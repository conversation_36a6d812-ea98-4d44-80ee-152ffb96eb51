import MyWidgetKit
import SwiftUI
import WidgetKit

public struct WidgetConfigView: View {
    public let widgetType: WidgetData.WidgetType
    private var theme: WidgetTheme {
        switch widgetType {
        case .dailyQuote:
            return .dailyQuote
        default:
            return .dailyQuote
        }
    }

    @State private var content: String = ""
    @State private var useImageBackground: Bool = false
    @State private var backgroundImage: UIImage?
    @State private var backgroundColor: Color
    @State private var textColor: Color = .black
    @State private var fontSize: Double = 16
    @State private var showBorder: Bool = false
    @State private var borderColor: Color = .gray
    @State private var showImagePicker: Bool = false
    @Environment(\.presentationMode) var presentationMode
    @State private var widgetconfig: WidgetCommonConfig?

    public init(widgetType: WidgetData.WidgetType) {
        self.widgetType = widgetType
        switch widgetType {
        case .dailyQuote:
            _backgroundColor = State(initialValue: WidgetTheme.dailyQuote.colors.background)
        default:
            _backgroundColor = State(initialValue: WidgetTheme.dailyQuote.colors.background)
        }
    }

    public var body: some View {
        Form {
            contentSection
            backgroundSection
            styleSection
            previewSection
            saveButtonSection
        }
        .navigationTitle("配置每日一句小组件")
        .tint(theme.colors.primary)
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(image: $backgroundImage)
        }
        .onAppear { loadExistingData() }
        .onChange(of: backgroundImage) { newValue in
            if let pngData = newValue?.pngData() {
                self.widgetconfig?.background = .imageData(pngData)
            }
        }
        .onChange(of: backgroundColor) { new in
            self.widgetconfig?.background = .color(WidgetColor.fromColor(new))
        }
    }



    @ViewBuilder
    private var contentSection: some View {
        Section {
            TextEditor(text: $content)
                .frame(height: 100)
                .foregroundStyle(theme.colors.gradient)
        } header: {
            Text("内容设置")
                .foregroundStyle(theme.colors.gradient)
        }
    }

    @ViewBuilder
    private var backgroundSection: some View {
        Section {
            Toggle("使用图片背景", isOn: $useImageBackground)
                .tint(theme.colors.primary)

            if useImageBackground {
                Button(backgroundImage == nil ? "选择背景图片" : "更换背景图片") {
                    showImagePicker = true
                }
                .foregroundStyle(theme.colors.gradient)

                if let image = backgroundImage {
                    Image(uiImage: image)
                        .resizable()
                        .scaledToFit()
                        .frame(height: 100)
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                }
            } else {
                ColorPicker("背景颜色", selection: $backgroundColor)
            }
        } header: {
            Text("背景设置")
                .foregroundStyle(theme.colors.gradient)
        }
    }

    @ViewBuilder
    private var styleSection: some View {
        Section {
            ColorPicker("文字颜色", selection: $textColor)

            VStack(alignment: .leading) {
                Text("字体大小: \(Int(fontSize))")
                    .foregroundStyle(theme.colors.gradient)
                Slider(value: $fontSize, in: 12 ... 32, step: 1)
                    .tint(theme.colors.primary)
            }

            Toggle("显示边框", isOn: $showBorder)
                .tint(theme.colors.primary)

            if showBorder {
                ColorPicker("边框颜色", selection: $borderColor)
            }
        } header: {
            Text("样式设置")
                .foregroundStyle(theme.colors.gradient)
        }
    }

    @ViewBuilder
    private var previewSection: some View {
        Section {
            WidgetPreviewContainer(base: .systemSmall) {
                DailyQuoteWidgetView(data: DailyQuoteWidgetViewData(quote: content, background: .color(WidgetColor.fromColor(.red)), date: Date(), config: self.widgetconfig))
            }
        } header: {
            Text("预览")
                .foregroundStyle(theme.colors.gradient)
        }
    }



    @ViewBuilder
    private var saveButtonSection: some View {
        Button("保存") {
            saveWidget()
        }
        .foregroundStyle(theme.colors.gradient)
    }



    private func loadExistingData() {
        if let existingData = WidgetDataManager.shared.loadWidgetData(),
           existingData.type == widgetType
        {
            if let quoteData = existingData.dailyQuote {
                content = quoteData.content
                loadStyle(quoteData.style)
            }
        }

        let appConfig = AppGroupDataManager.shared.read(WidgetCommonConfig.self, for: .dailyQuote, property: .config)
        self.widgetconfig = appConfig
    }

    private func loadStyle(_ style: DailyQuoteData.QuoteStyle) {
        switch style.background {
        case .color(let wrapper):
            useImageBackground = false
            backgroundColor = wrapper.color
        case .image(let data):
            useImageBackground = true
            backgroundImage = UIImage(data: data)
        }

        textColor = style.textSwiftUIColor
        fontSize = style.fontSize
        showBorder = style.showBorder
        borderColor = style.borderSwiftUIColor
    }

    private func saveWidget() {
        var widgetData = WidgetData(type: widgetType)

//        if widgetType == .countdown {
//            let style = CountdownData.CountdownStyle(
//                background: useImageBackground ? .image(backgroundImage?.jpegData(compressionQuality: 0.8) ?? Data()) : .color(ColorWrapper(color: backgroundColor)),
//                textColor: ColorWrapper(color: textColor),
//                fontSize: fontSize,
//                showBorder: showBorder,
//                borderColor: ColorWrapper(color: borderColor)
//            )
//            widgetData.countdown = CountdownData(title: title, targetDate: targetDate, style: style)
//        } else {
//            let style = DailyQuoteData.QuoteStyle(
//                background: useImageBackground ? .image(backgroundImage?.jpegData(compressionQuality: 0.8) ?? Data()) : .color(ColorWrapper(color: backgroundColor)),
//                textColor: ColorWrapper(color: textColor),
//                fontSize: fontSize,
//                showBorder: showBorder,
//                borderColor: ColorWrapper(color: borderColor)
//            )
//            widgetData.dailyQuote = DailyQuoteData(content: content, style: style)
//        }
//
        WidgetDataManager.shared.saveWidgetData(widgetData)
        var widgetBackground: WidgetBackground
        if let bgImage = backgroundImage?.compressForWidgetOptimized() {
            AppGroupDataManager.shared.saveAuto(bgImage, for: .dailyQuote, property: .backgroundImage)
            widgetBackground = .imageFile(AppGroupDataManager.shared.fileName(for: .dailyQuote, property: .backgroundImage))
        } else  {
            widgetBackground = .color(WidgetColor.fromColor(backgroundColor))
        }


        let config = WidgetCommonConfig(widgetType: .dailyQuote, background: widgetBackground)
        AppGroupDataManager.shared.save(config, for: .dailyQuote, property: .config)
        let appConfig = AppGroupDataManager.shared.read(WidgetCommonConfig.self, for: .dailyQuote, property: .config)

        print(config)
//        presentationMode.wrappedValue.dismiss()


        WidgetCenter.shared.reloadAllTimelines()
    }
}

// 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var image: UIImage?
    @Environment(\.presentationMode) private var presentationMode

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey: Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.image = image
            }
            parent.presentationMode.wrappedValue.dismiss()
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.presentationMode.wrappedValue.dismiss()
        }
    }
}
