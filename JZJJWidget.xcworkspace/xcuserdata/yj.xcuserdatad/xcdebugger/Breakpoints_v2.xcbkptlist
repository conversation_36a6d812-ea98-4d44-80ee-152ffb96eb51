<?xml version="1.0" encoding="UTF-8"?>
<Bucket
   uuid = "2F4587C5-16C8-4C54-AE70-CF8351F7F86F"
   type = "0"
   version = "2.0">
   <Breakpoints>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "2939AFD5-7C99-41DC-8751-855FA8A8B1CD"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "KeyBoardExtension/KeyboardImageLoader.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "161"
            endingLineNumber = "161"
            landmarkName = "loadIndividualKeyImage(for:keyConfig:themeId:state:isBuiltIn:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "64FBFD5B-1585-4DFF-B874-C8C0A453C8A4"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackImageManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "246"
            endingLineNumber = "246"
            landmarkName = "loadImageFromFile(at:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "0827A5F9-7D60-4762-9DFE-CC494107287F"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "234"
            endingLineNumber = "234"
            landmarkName = "applyThemePack(id:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "B74EF21F-C55A-4357-996B-777B6AF07AF5"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1226"
            endingLineNumber = "1226"
            landmarkName = "generateIndividualKeyConfigs(for:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
      <BreakpointProxy
         BreakpointExtensionID = "Xcode.Breakpoint.FileBreakpoint">
         <BreakpointContent
            uuid = "15D2920F-C362-4BC8-BBC9-91C5C5719E0E"
            shouldBeEnabled = "Yes"
            ignoreCount = "0"
            continueAfterRunningActions = "No"
            filePath = "MyWidgetKit/Sources/MyWidgetKit/ThemePack/ThemePackManager.swift"
            startingColumnNumber = "9223372036854775807"
            endingColumnNumber = "9223372036854775807"
            startingLineNumber = "1230"
            endingLineNumber = "1230"
            landmarkName = "generateIndividualKeyConfigs(for:)"
            landmarkType = "7">
         </BreakpointContent>
      </BreakpointProxy>
   </Breakpoints>
</Bucket>
