import Foundation
import WidgetKit

public enum AppGroup: String {
    case yourGroupName = "group.com.ort.JZJJWidgetAPP.group" // 替换为实际的App Group名称

    public static var containerURL: URL {
        return FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: "group.com.ort.JZJJWidgetAPP.group")!
    }
}

@available(iOS 16.0, *)
final class WidgetDataManager: @unchecked Sendable {
    static let shared = WidgetDataManager()

    private let userDefaults = UserDefaults(suiteName: "group.com.ort.JZJJWidgetAPP.group")

    func saveWidgetData(_ data: WidgetData) {
        guard let encoded = try? JSONEncoder().encode(data) else { return }
        userDefaults?.set(encoded, forKey: "widgetData")
        WidgetCenter.shared.reloadAllTimelines()

        print("AppGroup.containerURL: \(AppGroup.containerURL)")
    }

    func loadWidgetData() -> WidgetData? {
        guard let data = userDefaults?.data(forKey: "widgetData"),
              let widgetData = try? JSONDecoder().decode(WidgetData.self, from: data) else { return nil }
        return widgetData
    }
}
