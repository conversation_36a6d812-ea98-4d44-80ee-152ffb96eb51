import SwiftUI
import WidgetKit

/// 设备信息小组件视图
@available(iOS 16.0, *)
public struct DeviceInfoWidgetView: View {
    /// 配置数据
    private let data: DeviceInfoWidgetData

    /// 小组件尺寸
    private let family: WidgetFamily

    /// 初始化方法
    public init(data: DeviceInfoWidgetData, family: WidgetFamily) {
        self.data = data
        self.family = family
    }

    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 根据不同尺寸显示不同布局
                switch family {
                case .systemSmall:
                    smallWidgetLayout
                case .systemMedium:
                    mediumWidgetLayout
                case .systemLarge:
                    largeWidgetLayout
                default:
                    // 默认使用中尺寸布局
                    mediumWidgetLayout
                }
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
            .adaptiveBackground(content: {
                data.background.backgroundView(width: geometry.size.width, height: geometry.size.height)
            })
            // 添加 widgetURL，点击整个小组件时打开
            .widgetURL(createWidgetURL())
        }
    }

    // MARK: - 小尺寸布局

    private var smallWidgetLayout: some View {
        VStack(alignment: alignment) {
            // 设备名称
            if data.showDeviceName {
                Text(data.deviceName)
                    .font(.custom(data.fontName, size: data.fontSize + 2, relativeTo: .headline))
                    .foregroundColor(data.fontColor.toColor())
                    .lineLimit(1)
                    .padding(.bottom, 4)
            }

            // 存储使用百分比
            if data.showStorageInfo {
                storageProgressView
                    .frame(height: 20)
                    .padding(.vertical, 4)

                Text("\(Int(data.storagePercentage))% 已使用")
                    .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                    .foregroundColor(data.fontColor.toColor())
            }

            Spacer(minLength: 0)

            // 电池信息
            if data.showBatteryInfo {
                batteryInfoView
            }
        }
        .padding(12)
    }

    // MARK: - 中尺寸布局

    private var mediumWidgetLayout: some View {
        HStack {
            // 左侧信息
            VStack(alignment: alignment) {
                if data.showDeviceName {
                    Text(data.deviceName)
                        .font(.custom(data.fontName, size: data.fontSize + 2, relativeTo: .headline))
                        .foregroundColor(data.fontColor.toColor())
                        .lineLimit(1)
                }

                if data.showOSInfo {
                    Text(data.deviceName)
                        .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                        .foregroundColor(data.fontColor.toColor())
                        .padding(.bottom, 4)
                }

                Spacer(minLength: 0)

                if data.showBatteryInfo {
                    batteryInfoView
                }
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal)

            // 右侧存储信息
            if data.showStorageInfo {
                VStack(alignment: alignment) {
                    storageProgressView
                        .frame(height: 20)
                        .padding(.bottom, 4)

                    Text("\(Int(data.storagePercentage))% 已使用")
                        .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                        .foregroundColor(data.fontColor.toColor())

                    Spacer(minLength: 0)

                    Text("可用: \(data.formattedFreeStorage)")
                        .font(.custom(data.fontName, size: data.fontSize, relativeTo: .caption))
                        .foregroundColor(data.fontColor.toColor())

                    Text("总计: \(data.formattedTotalStorage)")
                        .font(.custom(data.fontName, size: data.fontSize, relativeTo: .caption))
                        .foregroundColor(data.fontColor.toColor())
                }
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal)
            }
        }
        .padding(12)
    }

    // MARK: - 大尺寸布局

    private var largeWidgetLayout: some View {
        VStack(alignment: alignment) {
            // 顶部设备信息
            HStack {
                VStack(alignment: alignment) {
                    if data.showDeviceName {
                        Text(data.deviceName)
                            .font(.custom(data.fontName, size: data.fontSize + 4, relativeTo: .title3))
                            .foregroundColor(data.fontColor.toColor())
                            .lineLimit(1)
                    }

                    if data.showOSInfo {
                        Text(data.deviceName)
                            .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                            .foregroundColor(data.fontColor.toColor())
                    }
                }
                .frame(maxWidth: .infinity, alignment: .leading)

                if data.showBatteryInfo {
                    batteryInfoView
                }
            }
            .padding(.bottom, 16)

            // 存储信息
            if data.showStorageInfo {
                VStack(alignment: alignment) {
                    Text("存储空间")
                        .font(.custom(data.fontName, size: data.fontSize + 2, relativeTo: .headline))
                        .foregroundColor(data.fontColor.toColor())
                        .padding(.bottom, 4)

                    // 大尺寸使用环形进度
                    if data.progressStyle == .circle {
                        circleProgressView
                            .frame(height: 120)
                            .padding(.vertical, 8)
                    } else if data.progressStyle == .pie {
                        pieChartView
                            .frame(height: 120)
                            .padding(.vertical, 8)
                    } else {
                        storageProgressView
                            .frame(height: 24)
                            .padding(.vertical, 8)
                    }

                    // 详细存储信息
                    HStack {
                        VStack(alignment: .leading) {
                            Text("总存储空间:")
                                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                                .foregroundColor(data.fontColor.toColor())

                            Text("已用空间:")
                                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                                .foregroundColor(data.fontColor.toColor())

                            Text("可用空间:")
                                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                                .foregroundColor(data.fontColor.toColor())
                        }

                        Spacer()

                        VStack(alignment: .trailing) {
                            Text(data.formattedTotalStorage)
                                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                                .foregroundColor(data.fontColor.toColor())

                            Text(data.formattedUsedStorage)
                                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                                .foregroundColor(data.fontColor.toColor())

                            Text(data.formattedFreeStorage)
                                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                                .foregroundColor(data.fontColor.toColor())
                        }
                    }
                    .padding(.horizontal)
                    .padding(.top, 8)
                }
            }



            Spacer(minLength: 0)

            // 更新时间
            Text("更新于: \(formattedDate)")
                .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption2))
                .foregroundColor(data.fontColor.toColor().opacity(0.6))
        }
        .padding(16)
    }

    // MARK: - 辅助视图

    /// 存储进度条视图
    private var storageProgressView: some View {
        ProgressView(value: data.storagePercentage / 100)
            .progressViewStyle(LinearProgressViewStyle(tint: progressColor))
            .frame(height: 8)
    }

    /// 环形进度视图
    private var circleProgressView: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(data.fontColor.toColor().opacity(0.2), lineWidth: 10)

            // 进度圆环
            Circle()
                .trim(from: 0, to: CGFloat(data.storagePercentage / 100))
                .stroke(
                    progressColor,
                    style: StrokeStyle(
                        lineWidth: 10,
                        lineCap: .round
                    )
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeOut, value: data.storagePercentage)

            VStack {
                Text("\(Int(data.storagePercentage))%")
                    .font(.custom(data.fontName, size: data.fontSize + 8, relativeTo: .title))
                    .foregroundColor(data.fontColor.toColor())

                Text("已使用")
                    .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                    .foregroundColor(data.fontColor.toColor())
            }
        }
    }

    /// 饼图视图
    private var pieChartView: some View {
        ZStack {
            // 使用兼容 iOS 16 的方式创建饼图
            Circle()
                .trim(from: 0, to: CGFloat(data.storagePercentage / 100))
                .foregroundColor(progressColor)
                .rotationEffect(.degrees(-90))

            Circle()
                .trim(from: CGFloat(data.storagePercentage / 100), to: 1)
                .foregroundColor(data.fontColor.toColor().opacity(0.2))
                .rotationEffect(.degrees(-90))

            VStack {
                Text("\(Int(data.storagePercentage))%")
                    .font(.custom(data.fontName, size: data.fontSize + 8, relativeTo: .title))
                    .foregroundColor(data.fontColor.toColor())

                Text("已使用")
                    .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                    .foregroundColor(data.fontColor.toColor())
            }
        }
    }

    /// 电池信息视图
    private var batteryInfoView: some View {
        // 使用模拟数据，避免主线程隔离问题
        let level = 80
        let state = "使用电池中"

        return HStack(spacing: 4) {
            Image(systemName: batteryIconName(for: level))
                .foregroundColor(batteryColor(for: level))

            Text("\(level)% \(state)")
                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .subheadline))
                .foregroundColor(data.fontColor.toColor())
        }
    }

    // MARK: - 辅助方法

    /// 创建小组件URL
    private func createWidgetURL() -> URL? {
        // 构建URL，包含必要参数
        let urlString = "jzjjwidget://widget?type=deviceInfo"
        return URL(string: urlString)
    }

    /// 获取对齐方式
    private var alignment: HorizontalAlignment {
        switch data.textAlignment {
        case .leading:
            return .leading
        case .center:
            return .center
        case .trailing:
            return .trailing
        }
    }

    /// 获取进度条颜色
    private var progressColor: Color {
        switch data.storageStatus {
        case .good:
            return .green
        case .warning:
            return .yellow
        case .critical:
            return .red
        }
    }

    /// 获取电池图标名称
    private func batteryIconName(for level: Int) -> String {
        if level <= 20 {
            return "battery.25"
        } else if level <= 50 {
            return "battery.50"
        } else if level <= 75 {
            return "battery.75"
        } else {
            return "battery.100"
        }
    }

    /// 获取电池颜色
    private func batteryColor(for level: Int) -> Color {
        if level <= 20 {
            return .red
        } else if level <= 50 {
            return .yellow
        } else {
            return .green
        }
    }

    /// 格式化日期
    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: data.lastUpdated)
    }
}
