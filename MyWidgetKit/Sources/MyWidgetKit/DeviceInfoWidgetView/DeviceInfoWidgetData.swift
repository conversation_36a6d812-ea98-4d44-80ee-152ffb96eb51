import SwiftUI
import WidgetKit

/// 存储空间使用状态
public enum StorageUsageStatus: String, Codable, Hashable {
    case good = "good"       // 良好（>70%可用）
    case warning = "warning" // 警告（30-70%可用）
    case critical = "critical" // 危险（<30%可用）

    /// 获取状态颜色
    public var color: Color {
        switch self {
        case .good:
            return .green
        case .warning:
            return .yellow
        case .critical:
            return .red
        }
    }

    /// 根据剩余空间百分比获取状态
    public static func fromPercentage(_ percentage: Double) -> StorageUsageStatus {
        if percentage >= 70 {
            return .good
        } else if percentage >= 30 {
            return .warning
        } else {
            return .critical
        }
    }
}

/// 设备信息小组件数据模型
public struct DeviceInfoWidgetData: WidgetPreviewableData, Codable, Hashable {
    // Hashable协议实现
    public func hash(into hasher: inout Hasher) {
        hasher.combine(deviceName)
        hasher.combine(totalStorage)
        hasher.combine(usedStorage)
        hasher.combine(freeStorage)
        hasher.combine(storagePercentage)
        hasher.combine(fontName)
        hasher.combine(fontSize)
        hasher.combine(showDeviceName)
        hasher.combine(showOSInfo)
        hasher.combine(showBatteryInfo)
        hasher.combine(showStorageInfo)
        hasher.combine(progressStyle)
        hasher.combine(textAlignment)
        hasher.combine(lastUpdated)
    }

    public static func == (lhs: DeviceInfoWidgetData, rhs: DeviceInfoWidgetData) -> Bool {
        return lhs.deviceName == rhs.deviceName &&
               lhs.totalStorage == rhs.totalStorage &&
               lhs.usedStorage == rhs.usedStorage &&
               lhs.freeStorage == rhs.freeStorage &&
               lhs.storagePercentage == rhs.storagePercentage &&
               lhs.fontName == rhs.fontName &&
               lhs.fontSize == rhs.fontSize &&
               lhs.showDeviceName == rhs.showDeviceName &&
               lhs.showOSInfo == rhs.showOSInfo &&
               lhs.showBatteryInfo == rhs.showBatteryInfo &&
               lhs.showStorageInfo == rhs.showStorageInfo &&
               lhs.progressStyle == rhs.progressStyle &&
               lhs.textAlignment == rhs.textAlignment &&
               lhs.lastUpdated == rhs.lastUpdated
    }
    /// 设备名称
    public var deviceName: String

    /// 总存储空间（字节）
    public var totalStorage: Int64

    /// 已用存储空间（字节）
    public var usedStorage: Int64

    /// 剩余存储空间（字节）
    public var freeStorage: Int64

    /// 存储使用百分比（0-100）
    public var storagePercentage: Double

    /// 背景设置
    public var background: WidgetBackground

    /// 字体名称
    public var fontName: String

    /// 字体大小
    public var fontSize: Double

    /// 字体颜色
    public var fontColor: WidgetColor

    /// 强调色（用于进度条等）
    public var accentColor: WidgetColor

    /// 显示设置
    public var showDeviceName: Bool

    /// 显示操作系统信息
    public var showOSInfo: Bool

    /// 显示电池信息
    public var showBatteryInfo: Bool

    /// 显示存储信息
    public var showStorageInfo: Bool

    /// 进度条样式
    public var progressStyle: ProgressStyle

    /// 文本对齐方式
    public var textAlignment: TextAlignment

    /// 最后更新时间
    public var lastUpdated: Date

    /// 进度条样式
    public enum ProgressStyle: String, Codable, Hashable, CaseIterable {
        case bar = "bar"           // 条形进度条
        case circle = "circle"     // 环形进度条
        case pie = "pie"           // 饼图

        /// 获取样式名称
        public var displayName: String {
            switch self {
            case .bar:
                return "条形"
            case .circle:
                return "环形"
            case .pie:
                return "饼图"
            }
        }

        /// 获取样式图标
        public var icon: String {
            switch self {
            case .bar:
                return "rectangle.fill"
            case .circle:
                return "circle"
            case .pie:
                return "chart.pie.fill"
            }
        }
    }

    /// 文本对齐方式
    public enum TextAlignment: String, Codable, Hashable, CaseIterable {
        case leading = "leading"
        case center = "center"
        case trailing = "trailing"

        /// 获取对齐方式名称
        public var displayName: String {
            switch self {
            case .leading:
                return "左对齐"
            case .center:
                return "居中"
            case .trailing:
                return "右对齐"
            }
        }

        /// 获取对齐方式图标
        public var icon: String {
            switch self {
            case .leading:
                return "text.alignleft"
            case .center:
                return "text.aligncenter"
            case .trailing:
                return "text.alignright"
            }
        }

        /// 转换为SwiftUI的TextAlignment
        public var swiftUIAlignment: SwiftUI.TextAlignment {
            switch self {
            case .leading:
                return .leading
            case .center:
                return .center
            case .trailing:
                return .trailing
            }
        }
    }

    /// 获取存储使用状态
    public var storageStatus: StorageUsageStatus {
        let freePercentage = Double(freeStorage) / Double(totalStorage) * 100
        return StorageUsageStatus.fromPercentage(freePercentage)
    }

    /// 获取格式化的总存储空间
    public var formattedTotalStorage: String {
        ByteCountFormatter.string(fromByteCount: totalStorage, countStyle: .file)
    }

    /// 获取格式化的已用存储空间
    public var formattedUsedStorage: String {
        ByteCountFormatter.string(fromByteCount: usedStorage, countStyle: .file)
    }

    /// 获取格式化的剩余存储空间
    public var formattedFreeStorage: String {
        ByteCountFormatter.string(fromByteCount: freeStorage, countStyle: .file)
    }

    /// 初始化方法
    public init(
        deviceName: String,
        totalStorage: Int64 = 0,
        usedStorage: Int64 = 0,
        freeStorage: Int64 = 0,
        storagePercentage: Double = 0,
        background: WidgetBackground = .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
        fontName: String = "SF Pro",
        fontSize: Double = 14,
        fontColor: WidgetColor = WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
        accentColor: WidgetColor = WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
        showDeviceName: Bool = true,
        showOSInfo: Bool = true,
        showBatteryInfo: Bool = true,
        showStorageInfo: Bool = true,
        progressStyle: ProgressStyle = .bar,
        textAlignment: TextAlignment = .leading,
        lastUpdated: Date = Date()
    ) {
        self.deviceName = deviceName
        self.totalStorage = totalStorage
        self.usedStorage = usedStorage
        self.freeStorage = freeStorage
        self.storagePercentage = storagePercentage
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontColor = fontColor
        self.accentColor = accentColor
        self.showDeviceName = showDeviceName
        self.showOSInfo = showOSInfo
        self.showBatteryInfo = showBatteryInfo
        self.showStorageInfo = showStorageInfo
        self.progressStyle = progressStyle
        self.textAlignment = textAlignment
        self.lastUpdated = lastUpdated
    }
}
