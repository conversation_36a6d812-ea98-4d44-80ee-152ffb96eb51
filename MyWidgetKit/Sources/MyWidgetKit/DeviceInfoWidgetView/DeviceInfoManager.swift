import Foundation
import UIKit

/// 设备信息管理器
@MainActor
public final class DeviceInfoManager: @unchecked Sendable {
    /// 单例实例
    public static let shared = DeviceInfoManager()

    /// 私有初始化方法
    private init() {}

    /// 获取设备名称
    public func getDeviceName() -> String {
        return UIDevice.current.name
    }

    /// 获取操作系统信息
    public func getOSInfo() -> String {
        return "\(UIDevice.current.systemName) \(UIDevice.current.systemVersion)"
    }

    /// 获取电池信息
    public func getBatteryInfo() -> (level: Int, state: String) {
        UIDevice.current.isBatteryMonitoringEnabled = true
        let level = Int(UIDevice.current.batteryLevel * 100)
        let state = UIDevice.current.batteryState

        let stateString: String
        switch state {
        case .charging: stateString = "充电中"
        case .full: stateString = "已充满"
        case .unplugged: stateString = "使用电池中"
        case .unknown: stateString = "未知"
        @unknown default: stateString = "未知"
        }

        return (level >= 0 ? level : 0, stateString)
    }

    /// 获取存储空间信息
    public func getStorageInfo() -> (total: Int64, used: Int64, free: Int64, percentage: Double) {
        let fileManager = FileManager.default
        do {
            if let path = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first?.path {
                if let attributes = try? fileManager.attributesOfFileSystem(forPath: path) {
                    if let freeSize = attributes[.systemFreeSize] as? NSNumber,
                       let totalSize = attributes[.systemSize] as? NSNumber
                    {
                        let free = freeSize.int64Value
                        let total = totalSize.int64Value
                        let used = total - free
                        let percentage = Double(used) / Double(total) * 100

                        return (total, used, free, percentage)
                    }
                }
            }
        } catch {
            print("获取存储空间信息失败: \(error)")
        }

        // 返回默认值
        return (0, 0, 0, 0)
    }



    /// 获取完整的设备信息
    public func getDeviceInfo() -> DeviceInfoWidgetData {
        // 获取存储信息
        let storageInfo = getStorageInfo()

        // 获取电池信息
        let batteryInfo = getBatteryInfo()

        // 创建设备信息数据
        return DeviceInfoWidgetData(
            deviceName: UIDevice.current.name,
            totalStorage: storageInfo.total,
            usedStorage: storageInfo.used,
            freeStorage: storageInfo.free,
            storagePercentage: storageInfo.percentage,
            background: .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
            fontName: "SF Pro",
            fontSize: 14,
            fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
            accentColor: WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
            showDeviceName: true,
            showOSInfo: true,
            showBatteryInfo: true,
            showStorageInfo: true,
            progressStyle: .bar,
            textAlignment: .leading,
            lastUpdated: Date()
        )
    }
}


