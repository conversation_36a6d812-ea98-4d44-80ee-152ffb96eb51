import SwiftUI
import Foundation

/// 每日一言小组件统一配置
/// 合并了 WidgetCommonConfig 和 DailyQuoteData 的功能
@available(iOS 16.0, *)
public struct DailyQuoteWidgetConfig: Codable, Sendable {
    // 内容
    public var content: String
    
    // 背景设置
    public var background: WidgetBackground
    
    // 字体设置
    public var fontName: String
    public var fontSize: Double
    private var textColor: ColorWrapper
    
    // 边框设置
    public var showBorder: Bool
    private var borderColor: ColorWrapper
    
    // 更新时间
    public var lastUpdated: Date
    
    // 颜色访问器
    public var fontColor: Color {
        get { textColor.color }
        set { textColor = ColorWrapper(color: newValue) }
    }
    
    public var borderSwiftUIColor: Color {
        get { borderColor.color }
        set { borderColor = ColorWrapper(color: newValue) }
    }
    
    // 初始化方法
    public init(
        content: String = "",
        background: WidgetBackground = .color(WidgetColor.fromColor(.white)),
        fontName: String = "PingFangSC",
        fontSize: Double = 16,
        fontColor: Color = .black,
        showBorder: Bool = false,
        borderColor: Color = .gray,
        lastUpdated: Date = Date()
    ) {
        self.content = content
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        self.textColor = ColorWrapper(color: fontColor)
        self.showBorder = showBorder
        self.borderColor = ColorWrapper(color: borderColor)
        self.lastUpdated = lastUpdated
    }
    
    // 默认配置
    public static let `default` = DailyQuoteWidgetConfig()
    
    // 从旧模型转换
    public static func from(
        config: WidgetCommonConfig?,
        quoteData: DailyQuoteData?
    ) -> DailyQuoteWidgetConfig {
        let background = config?.background ?? .color(WidgetColor.fromColor(.white))
        
        if let quoteData = quoteData {
            return DailyQuoteWidgetConfig(
                content: quoteData.content,
                background: background,
                fontName: "PingFangSC", // 可以从 quoteData 中获取，如果有的话
                fontSize: quoteData.style.fontSize,
                fontColor: quoteData.style.textSwiftUIColor,
                showBorder: quoteData.style.showBorder,
                borderColor: quoteData.style.borderSwiftUIColor,
                lastUpdated: quoteData.lastUpdated
            )
        } else {
            return DailyQuoteWidgetConfig(
                content: "",
                background: background
            )
        }
    }
    
    // 转换为 DailyQuoteWidgetViewData
    public func toViewData(date: Date) -> DailyQuoteWidgetViewData {
        return DailyQuoteWidgetViewData(
            quote: content,
            background: background,
            date: date,
            config: nil // 不再需要单独的配置
        )
    }
}
