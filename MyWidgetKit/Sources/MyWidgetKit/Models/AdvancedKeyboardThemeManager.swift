//
//  AdvancedKeyboardThemeManager.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import Foundation

/// 高级键盘主题管理器
@MainActor
public class AdvancedKeyboardThemeManager: ObservableObject {

    // MARK: - 单例
    public static let shared = AdvancedKeyboardThemeManager()

    // MARK: - 发布属性
    @Published public var currentAdvancedTheme: AdvancedKeyboardTheme?
    @Published public var availableAdvancedThemes: [AdvancedKeyboardTheme] = []
    @Published public var isEditingMode: Bool = false
    @Published public var selectedKeyType: KeyType = .letter
    @Published public var selectedKeyValue: String?

    // MARK: - 私有属性
    private let dataManager = AppGroupDataManager.shared
    private let baseThemeManager = KeyboardThemeManager.shared

    // 存储键
    private let advancedThemesKey = "advanced_keyboard_themes"
    private let currentAdvancedThemeKey = "current_advanced_keyboard_theme"

    // MARK: - 初始化
    private init() {
        loadAdvancedThemes()
        loadCurrentAdvancedTheme()
    }

    // MARK: - 主题管理

    /// 创建新的高级主题
    public func createAdvancedTheme(
        name: String,
        description: String = "",
        baseTheme: KeyboardTheme? = nil
    ) -> AdvancedKeyboardTheme {
        let base = baseTheme ?? KeyboardTheme.defaultThemes[0]
        let advancedTheme = AdvancedKeyboardTheme(
            name: name,
            description: description,
            baseTheme: base
        )

        availableAdvancedThemes.append(advancedTheme)
        saveAdvancedThemes()

        return advancedTheme
    }

    /// 应用高级主题
    public func applyAdvancedTheme(_ theme: AdvancedKeyboardTheme) {
        currentAdvancedTheme = theme
        saveCurrentAdvancedTheme()

        // 同时更新基础主题
        baseThemeManager.setTheme(theme.baseTheme)

        // 发送通知给键盘扩展
        NotificationCenter.default.post(
            name: NSNotification.Name("AdvancedKeyboardThemeDidChange"),
            object: theme
        )
    }

    /// 删除高级主题
    public func deleteAdvancedTheme(_ theme: AdvancedKeyboardTheme) {
        availableAdvancedThemes.removeAll { $0.id == theme.id }

        // 如果删除的是当前主题，切换到默认主题
        if currentAdvancedTheme?.id == theme.id {
            currentAdvancedTheme = nil
            saveCurrentAdvancedTheme()
        }

        saveAdvancedThemes()
    }

    /// 复制高级主题
    public func duplicateAdvancedTheme(_ theme: AdvancedKeyboardTheme) -> AdvancedKeyboardTheme {
        var newTheme = theme
        newTheme = AdvancedKeyboardTheme(
            name: "\(theme.name) 副本",
            description: theme.description,
            baseTheme: theme.baseTheme,
            globalKeySpacing: theme.globalKeySpacing,
            globalKeyHeight: theme.globalKeyHeight,
            enableHapticFeedback: theme.enableHapticFeedback,
            enableSoundFeedback: theme.enableSoundFeedback,
            enableKeyAnimations: theme.enableKeyAnimations,
            animationDuration: theme.animationDuration,
            enableGradientEffects: theme.enableGradientEffects,
            enableParallaxEffect: theme.enableParallaxEffect
        )

        // 复制按键类型配置
        newTheme.keyTypeConfigs = theme.keyTypeConfigs
        newTheme.individualKeyConfigs = theme.individualKeyConfigs

        availableAdvancedThemes.append(newTheme)
        saveAdvancedThemes()

        return newTheme
    }

    // MARK: - 按键配置管理

    /// 更新按键类型配置
    public func updateKeyTypeConfig(
        for keyType: KeyType,
        in theme: AdvancedKeyboardTheme,
        config: KeyTypeConfig
    ) {
        guard let index = availableAdvancedThemes.firstIndex(where: { $0.id == theme.id }) else {
            return
        }

        availableAdvancedThemes[index].keyTypeConfigs[keyType] = config
        availableAdvancedThemes[index].updatedAt = Date()

        // 如果是当前主题，同时更新
        if currentAdvancedTheme?.id == theme.id {
            currentAdvancedTheme?.keyTypeConfigs[keyType] = config
            currentAdvancedTheme?.updatedAt = Date()
        }

        saveAdvancedThemes()
        saveCurrentAdvancedTheme()
    }

    /// 更新单个按键配置
    public func updateIndividualKeyConfig(
        for keyValue: String,
        in theme: AdvancedKeyboardTheme,
        config: KeyConfig
    ) {
        guard let index = availableAdvancedThemes.firstIndex(where: { $0.id == theme.id }) else {
            return
        }

        availableAdvancedThemes[index].individualKeyConfigs[keyValue] = config
        availableAdvancedThemes[index].updatedAt = Date()

        // 如果是当前主题，同时更新
        if currentAdvancedTheme?.id == theme.id {
            currentAdvancedTheme?.individualKeyConfigs[keyValue] = config
            currentAdvancedTheme?.updatedAt = Date()
        }

        saveAdvancedThemes()
        saveCurrentAdvancedTheme()
    }

    /// 移除单个按键配置（回退到类型配置）
    public func removeIndividualKeyConfig(
        for keyValue: String,
        in theme: AdvancedKeyboardTheme
    ) {
        guard let index = availableAdvancedThemes.firstIndex(where: { $0.id == theme.id }) else {
            return
        }

        availableAdvancedThemes[index].individualKeyConfigs.removeValue(forKey: keyValue)
        availableAdvancedThemes[index].updatedAt = Date()

        // 如果是当前主题，同时更新
        if currentAdvancedTheme?.id == theme.id {
            currentAdvancedTheme?.individualKeyConfigs.removeValue(forKey: keyValue)
            currentAdvancedTheme?.updatedAt = Date()
        }

        saveAdvancedThemes()
        saveCurrentAdvancedTheme()
    }

    /// 批量应用配置到按键类型
    public func applyConfigToKeyType(
        keyType: KeyType,
        in theme: AdvancedKeyboardTheme,
        config: KeyTypeConfig
    ) {
        // 更新类型配置
        updateKeyTypeConfig(for: keyType, in: theme, config: config)

        // 移除该类型下所有单个按键的配置，让它们使用类型配置
        guard let index = availableAdvancedThemes.firstIndex(where: { $0.id == theme.id }) else {
            return
        }

        let affectedKeys = config.affectedKeys
        for keyValue in affectedKeys {
            availableAdvancedThemes[index].individualKeyConfigs.removeValue(forKey: keyValue)

            if currentAdvancedTheme?.id == theme.id {
                currentAdvancedTheme?.individualKeyConfigs.removeValue(forKey: keyValue)
            }
        }

        saveAdvancedThemes()
        saveCurrentAdvancedTheme()
    }

    // MARK: - 配置获取

    /// 获取按键的有效配置
    public func getEffectiveKeyConfig(
        for keyValue: String,
        keyType: KeyType,
        in theme: AdvancedKeyboardTheme
    ) -> KeyConfig {
        // 优先使用单个按键配置
        if let individualConfig = theme.individualKeyConfigs[keyValue] {
            return individualConfig
        }

        // 使用按键类型配置
        if let typeConfig = theme.keyTypeConfigs[keyType] {
            return KeyConfig(
                keyType: keyType,
                keyValue: keyValue,
                backgroundColor: typeConfig.defaultBackgroundColor,
                pressedColor: typeConfig.defaultPressedColor,
                textColor: typeConfig.defaultTextColor,
                borderColor: typeConfig.defaultBorderColor,
                fontSize: typeConfig.defaultFontSize,
                fontWeight: typeConfig.defaultFontWeight,
                cornerRadius: typeConfig.defaultCornerRadius,
                borderWidth: typeConfig.defaultBorderWidth
            )
        }

        // 回退到默认配置
        return KeyConfig(keyType: keyType, keyValue: keyValue)
    }

    /// 获取按键类型的所有按键配置
    public func getKeyConfigsForType(
        _ keyType: KeyType,
        in theme: AdvancedKeyboardTheme
    ) -> [KeyConfig] {
        guard let typeConfig = theme.keyTypeConfigs[keyType] else {
            return []
        }

        return typeConfig.affectedKeys.map { keyValue in
            getEffectiveKeyConfig(for: keyValue, keyType: keyType, in: theme)
        }
    }

    // MARK: - 公共保存方法

    /// 保存高级主题（更新现有主题或添加新主题）
    public func saveAdvancedTheme(_ theme: AdvancedKeyboardTheme) {
        if let index = availableAdvancedThemes.firstIndex(where: { $0.id == theme.id }) {
            // 更新现有主题
            availableAdvancedThemes[index] = theme
        } else {
            // 添加新主题
            availableAdvancedThemes.append(theme)
        }

        // 如果是当前主题，同时更新当前主题
        if currentAdvancedTheme?.id == theme.id {
            currentAdvancedTheme = theme
        }

        saveAdvancedThemes()
        saveCurrentAdvancedTheme()
    }

    // MARK: - 数据持久化

    private func saveAdvancedThemes() {
        do {
            let data = try JSONEncoder().encode(availableAdvancedThemes)
            dataManager.userDefaults?.set(data, forKey: advancedThemesKey)
            dataManager.userDefaults?.synchronize()
        } catch {
            print("❌ 保存高级主题失败: \(error)")
        }
    }

    private func loadAdvancedThemes() {
        guard let data = dataManager.userDefaults?.data(forKey: advancedThemesKey) else {
            availableAdvancedThemes = []
            return
        }

        do {
            availableAdvancedThemes = try JSONDecoder().decode([AdvancedKeyboardTheme].self, from: data)
        } catch {
            print("❌ 加载高级主题失败: \(error)")
            availableAdvancedThemes = []
        }
    }

    private func saveCurrentAdvancedTheme() {
        do {
            if let theme = currentAdvancedTheme {
                let data = try JSONEncoder().encode(theme)
                dataManager.userDefaults?.set(data, forKey: currentAdvancedThemeKey)
            } else {
                dataManager.userDefaults?.removeObject(forKey: currentAdvancedThemeKey)
            }
            dataManager.userDefaults?.synchronize()
        } catch {
            print("❌ 保存当前高级主题失败: \(error)")
        }
    }

    private func loadCurrentAdvancedTheme() {
        guard let data = dataManager.userDefaults?.data(forKey: currentAdvancedThemeKey) else {
            currentAdvancedTheme = nil
            return
        }

        do {
            currentAdvancedTheme = try JSONDecoder().decode(AdvancedKeyboardTheme.self, from: data)
        } catch {
            print("❌ 加载当前高级主题失败: \(error)")
            currentAdvancedTheme = nil
        }
    }

    // MARK: - 导入导出

    /// 导出主题配置
    public func exportTheme(_ theme: AdvancedKeyboardTheme) -> Data? {
        do {
            return try JSONEncoder().encode(theme)
        } catch {
            print("❌ 导出主题失败: \(error)")
            return nil
        }
    }

    /// 导入主题配置
    public func importTheme(from data: Data) -> AdvancedKeyboardTheme? {
        do {
            var theme = try JSONDecoder().decode(AdvancedKeyboardTheme.self, from: data)

            // 生成新的ID避免冲突
            theme = AdvancedKeyboardTheme(
                name: "\(theme.name) (导入)",
                description: theme.description,
                baseTheme: theme.baseTheme,
                globalKeySpacing: theme.globalKeySpacing,
                globalKeyHeight: theme.globalKeyHeight,
                enableHapticFeedback: theme.enableHapticFeedback,
                enableSoundFeedback: theme.enableSoundFeedback,
                enableKeyAnimations: theme.enableKeyAnimations,
                animationDuration: theme.animationDuration,
                enableGradientEffects: theme.enableGradientEffects,
                enableParallaxEffect: theme.enableParallaxEffect
            )

            availableAdvancedThemes.append(theme)
            saveAdvancedThemes()

            return theme
        } catch {
            print("❌ 导入主题失败: \(error)")
            return nil
        }
    }
}
