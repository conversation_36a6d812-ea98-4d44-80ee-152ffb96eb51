import SwiftUI
import WidgetKit

// MARK: - 学习显示模式
public enum LearningDisplayMode: String, Codable, CaseIterable {
    case progress = "进度条"
    case circular = "环形图"
    case statistics = "统计视图"
}

// MARK: - 学习目标
public struct LearningGoal: Identifiable, Codable, Equatable {
    public let id: String
    public var name: String
    public var subject: String
    public var totalUnits: Int
    public var completedUnits: Int
    public var timeSpent: Int // 以分钟为单位
    public var targetDate: Date
    public var color: Color

    public var progress: Double {
        return Double(completedUnits) / Double(totalUnits)
    }

    // 编码和解码 Color
    enum CodingKeys: String, CodingKey {
        case id, name, subject, totalUnits, completedUnits, timeSpent, targetDate
        case colorR, colorG, colorB, colorA
    }

    public init(id: String, name: String, subject: String, totalUnits: Int, completedUnits: Int, timeSpent: Int, targetDate: Date, color: Color) {
        self.id = id
        self.name = name
        self.subject = subject
        self.totalUnits = totalUnits
        self.completedUnits = completedUnits
        self.timeSpent = timeSpent
        self.targetDate = targetDate
        self.color = color
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        subject = try container.decode(String.self, forKey: .subject)
        totalUnits = try container.decode(Int.self, forKey: .totalUnits)
        completedUnits = try container.decode(Int.self, forKey: .completedUnits)
        timeSpent = try container.decode(Int.self, forKey: .timeSpent)
        targetDate = try container.decode(Date.self, forKey: .targetDate)

        let r = try container.decode(Double.self, forKey: .colorR)
        let g = try container.decode(Double.self, forKey: .colorG)
        let b = try container.decode(Double.self, forKey: .colorB)
        let a = try container.decode(Double.self, forKey: .colorA)

        color = Color(.sRGB, red: r, green: g, blue: b, opacity: a)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(subject, forKey: .subject)
        try container.encode(totalUnits, forKey: .totalUnits)
        try container.encode(completedUnits, forKey: .completedUnits)
        try container.encode(timeSpent, forKey: .timeSpent)
        try container.encode(targetDate, forKey: .targetDate)

        let uiColor = UIColor(color)
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)

        try container.encode(r, forKey: .colorR)
        try container.encode(g, forKey: .colorG)
        try container.encode(b, forKey: .colorB)
        try container.encode(a, forKey: .colorA)
    }

    public static func == (lhs: LearningGoal, rhs: LearningGoal) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - 学习进度小组件配置
public struct LearningProgressConfig: Codable {
    public var goalIds: [String]
    public var displayMode: LearningDisplayMode
    public var showPercentage: Bool
    public var showTimeSpent: Bool
    public var maxGoalsCount: Int
    public var background: WidgetBackground
    public var fontName: String
    public var fontSize: Double
    public var fontColor: Color
    public var accentColor: Color
    public var lastUpdated: Date

    // 编码和解码 Color
    enum CodingKeys: String, CodingKey {
        case goalIds, displayMode, showPercentage, showTimeSpent, maxGoalsCount, background, fontName, fontSize, lastUpdated
        case fontColorR, fontColorG, fontColorB, fontColorA
        case accentColorR, accentColorG, accentColorB, accentColorA
    }

    public init(goalIds: [String], displayMode: LearningDisplayMode, showPercentage: Bool, showTimeSpent: Bool, maxGoalsCount: Int, background: WidgetBackground, fontName: String, fontSize: Double, fontColor: Color, accentColor: Color, lastUpdated: Date) {
        self.goalIds = goalIds
        self.displayMode = displayMode
        self.showPercentage = showPercentage
        self.showTimeSpent = showTimeSpent
        self.maxGoalsCount = maxGoalsCount
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontColor = fontColor
        self.accentColor = accentColor
        self.lastUpdated = lastUpdated
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        goalIds = try container.decode([String].self, forKey: .goalIds)
        displayMode = try container.decode(LearningDisplayMode.self, forKey: .displayMode)
        showPercentage = try container.decode(Bool.self, forKey: .showPercentage)
        showTimeSpent = try container.decode(Bool.self, forKey: .showTimeSpent)
        maxGoalsCount = try container.decode(Int.self, forKey: .maxGoalsCount)
        background = try container.decode(WidgetBackground.self, forKey: .background)
        fontName = try container.decode(String.self, forKey: .fontName)
        fontSize = try container.decode(Double.self, forKey: .fontSize)
        lastUpdated = try container.decode(Date.self, forKey: .lastUpdated)

        let fontR = try container.decode(Double.self, forKey: .fontColorR)
        let fontG = try container.decode(Double.self, forKey: .fontColorG)
        let fontB = try container.decode(Double.self, forKey: .fontColorB)
        let fontA = try container.decode(Double.self, forKey: .fontColorA)

        fontColor = Color(.sRGB, red: fontR, green: fontG, blue: fontB, opacity: fontA)

        let accentR = try container.decode(Double.self, forKey: .accentColorR)
        let accentG = try container.decode(Double.self, forKey: .accentColorG)
        let accentB = try container.decode(Double.self, forKey: .accentColorB)
        let accentA = try container.decode(Double.self, forKey: .accentColorA)

        accentColor = Color(.sRGB, red: accentR, green: accentG, blue: accentB, opacity: accentA)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(goalIds, forKey: .goalIds)
        try container.encode(displayMode, forKey: .displayMode)
        try container.encode(showPercentage, forKey: .showPercentage)
        try container.encode(showTimeSpent, forKey: .showTimeSpent)
        try container.encode(maxGoalsCount, forKey: .maxGoalsCount)
        try container.encode(background, forKey: .background)
        try container.encode(fontName, forKey: .fontName)
        try container.encode(fontSize, forKey: .fontSize)
        try container.encode(lastUpdated, forKey: .lastUpdated)

        let uiFontColor = UIColor(fontColor)
        var fontR: CGFloat = 0
        var fontG: CGFloat = 0
        var fontB: CGFloat = 0
        var fontA: CGFloat = 0
        uiFontColor.getRed(&fontR, green: &fontG, blue: &fontB, alpha: &fontA)

        try container.encode(fontR, forKey: .fontColorR)
        try container.encode(fontG, forKey: .fontColorG)
        try container.encode(fontB, forKey: .fontColorB)
        try container.encode(fontA, forKey: .fontColorA)

        let uiAccentColor = UIColor(accentColor)
        var accentR: CGFloat = 0
        var accentG: CGFloat = 0
        var accentB: CGFloat = 0
        var accentA: CGFloat = 0
        uiAccentColor.getRed(&accentR, green: &accentG, blue: &accentB, alpha: &accentA)

        try container.encode(accentR, forKey: .accentColorR)
        try container.encode(accentG, forKey: .accentColorG)
        try container.encode(accentB, forKey: .accentColorB)
        try container.encode(accentA, forKey: .accentColorA)
    }
}

// MARK: - 学习进度小组件数据
public struct LearningProgressWidgetData: WidgetPreviewableData {
    public var goals: [LearningGoal]
    public var displayMode: LearningDisplayMode
    public var showPercentage: Bool
    public var showTimeSpent: Bool
    public var maxGoalsCount: Int
    public var background: WidgetBackground
    public var fontName: String
    public var fontSize: Double
    public var fontColor: Color
    public var accentColor: Color

    public init(goals: [LearningGoal], displayMode: LearningDisplayMode, showPercentage: Bool, showTimeSpent: Bool, maxGoalsCount: Int, background: WidgetBackground, fontName: String, fontSize: Double, fontColor: Color, accentColor: Color) {
        self.goals = goals
        self.displayMode = displayMode
        self.showPercentage = showPercentage
        self.showTimeSpent = showTimeSpent
        self.maxGoalsCount = maxGoalsCount
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontColor = fontColor
        self.accentColor = accentColor
    }
}

// MARK: - 学习数据管理器
public class LearningDataManager {
    nonisolated(unsafe) public static let shared = LearningDataManager()

    private let userDefaults = UserDefaults(suiteName: AppGroupConstants.appGroupId)
    private let learningGoalsKey = "learningGoals"

    private init() {}

    public func getLearningGoals() -> [LearningGoal] {
        guard let data = userDefaults?.data(forKey: learningGoalsKey),
              let goals = try? JSONDecoder().decode([LearningGoal].self, from: data) else {
            return createDefaultGoals()
        }
        return goals
    }

    public func saveLearningGoals(_ goals: [LearningGoal]) {
        guard let data = try? JSONEncoder().encode(goals) else { return }
        userDefaults?.set(data, forKey: learningGoalsKey)
    }

    private func createDefaultGoals() -> [LearningGoal] {
        let goals = [
            LearningGoal(
                id: UUID().uuidString,
                name: "Swift编程",
                subject: "编程",
                totalUnits: 100,
                completedUnits: 65,
                timeSpent: 1200,
                targetDate: Date().addingTimeInterval(30 * 24 * 60 * 60),
                color: .blue
            ),
            LearningGoal(
                id: UUID().uuidString,
                name: "高等数学",
                subject: "数学",
                totalUnits: 50,
                completedUnits: 20,
                timeSpent: 800,
                targetDate: Date().addingTimeInterval(60 * 24 * 60 * 60),
                color: .green
            ),
            LearningGoal(
                id: UUID().uuidString,
                name: "英语口语",
                subject: "语言",
                totalUnits: 30,
                completedUnits: 10,
                timeSpent: 500,
                targetDate: Date().addingTimeInterval(45 * 24 * 60 * 60),
                color: .orange
            )
        ]

        saveLearningGoals(goals)
        return goals
    }
}

// MARK: - AppGroupDataManager 扩展
extension AppGroupDataManager {

    public func getLearningProgressConfig() -> LearningProgressConfig? {
        
        let learningProgressConfigKey = "learningProgressConfig"
        guard let data = userDefaults?.data(forKey: learningProgressConfigKey),
              let config = try? JSONDecoder().decode(LearningProgressConfig.self, from: data) else {
            return nil
        }
        return config
    }

    public func saveLearningProgressConfig(_ config: LearningProgressConfig) {
        
        let learningProgressConfigKey = "learningProgressConfig"
        guard let data = try? JSONEncoder().encode(config) else { return }
        userDefaults?.set(data, forKey: learningProgressConfigKey)
    }
}
