//
//  KeyboardThemeModels.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import UIKit
import Foundation

// MARK: - 键盘主题类型
public enum KeyboardThemeType: String, Codable, CaseIterable, Identifiable {
    case light = "浅色"
    case dark = "深色"
    case colorful = "彩色"
    case gradient = "渐变"
    case custom = "自定义"

    public var id: String { rawValue }

    public var name: String {
        return self.rawValue
    }

    public var description: String {
        switch self {
        case .light:
            return "简洁明亮的浅色主题"
        case .dark:
            return "优雅的深色主题"
        case .colorful:
            return "活泼的彩色主题"
        case .gradient:
            return "美丽的渐变主题"
        case .custom:
            return "个性化自定义主题"
        }
    }
}

// MARK: - 键盘按键样式
public enum KeyboardKeyStyle: String, Codable, CaseIterable {
    case standard = "标准"
    case rounded = "圆角"
    case circular = "圆形"
    case flat = "扁平"

    public var name: String {
        return self.rawValue
    }

    public var cornerRadius: CGFloat {
        switch self {
        case .standard:
            return 6
        case .rounded:
            return 12
        case .circular:
            return 20
        case .flat:
            return 0
        }
    }
}

// MARK: - 键盘主题配置
public struct KeyboardTheme: Codable, Identifiable, Equatable {
    public let id: String
    public var name: String
    public var type: KeyboardThemeType
    public var keyStyle: KeyboardKeyStyle

    // 颜色配置
    public var backgroundColor: WidgetColor
    public var keyBackgroundColor: WidgetColor
    public var keyPressedColor: WidgetColor
    public var textColor: WidgetColor
    public var specialKeyColor: WidgetColor
    public var borderColor: WidgetColor

    // 图片配置
    public var hasBackgroundImage: Bool
    public var hasKeyImage: Bool
    public var backgroundImagePath: String?
    public var keyImagePath: String?
    public var isBuiltInImageTheme: Bool
    public var imageOpacity: Double
    public var imageBlendMode: String

    // 字体配置
    public var fontName: String
    public var fontSize: Double
    public var fontWeight: FontWeight

    // 布局配置
    public var keySpacing: Double
    public var keyHeight: Double
    public var showBorder: Bool
    public var borderWidth: Double

    // 特效配置
    public var enableShadow: Bool
    public var shadowColor: WidgetColor
    public var shadowRadius: Double
    public var enableHaptic: Bool
    public var enableSound: Bool

    // 创建时间
    public var createdAt: Date
    public var updatedAt: Date

    public init(
        id: String = UUID().uuidString,
        name: String,
        type: KeyboardThemeType,
        keyStyle: KeyboardKeyStyle = .rounded,
        backgroundColor: WidgetColor = WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
        keyBackgroundColor: WidgetColor = WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
        keyPressedColor: WidgetColor = WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
        textColor: WidgetColor = WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
        specialKeyColor: WidgetColor = WidgetColor(red: 0.68, green: 0.68, blue: 0.7, alpha: 1.0),
        borderColor: WidgetColor = WidgetColor(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0),
        hasBackgroundImage: Bool = false,
        hasKeyImage: Bool = false,
        backgroundImagePath: String? = nil,
        keyImagePath: String? = nil,
        isBuiltInImageTheme: Bool = false,
        imageOpacity: Double = 1.0,
        imageBlendMode: String = "normal",
        fontName: String = "SF Pro",
        fontSize: Double = 16,
        fontWeight: FontWeight = .medium,
        keySpacing: Double = 6,
        keyHeight: Double = 44,
        showBorder: Bool = true,
        borderWidth: Double = 1,
        enableShadow: Bool = true,
        shadowColor: WidgetColor = WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.1),
        shadowRadius: Double = 2,
        enableHaptic: Bool = true,
        enableSound: Bool = true
    ) {
        self.id = id
        self.name = name
        self.type = type
        self.keyStyle = keyStyle
        self.backgroundColor = backgroundColor
        self.keyBackgroundColor = keyBackgroundColor
        self.keyPressedColor = keyPressedColor
        self.textColor = textColor
        self.specialKeyColor = specialKeyColor
        self.borderColor = borderColor
        self.hasBackgroundImage = hasBackgroundImage
        self.hasKeyImage = hasKeyImage
        self.backgroundImagePath = backgroundImagePath
        self.keyImagePath = keyImagePath
        self.isBuiltInImageTheme = isBuiltInImageTheme
        self.imageOpacity = imageOpacity
        self.imageBlendMode = imageBlendMode
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontWeight = fontWeight
        self.keySpacing = keySpacing
        self.keyHeight = keyHeight
        self.showBorder = showBorder
        self.borderWidth = borderWidth
        self.enableShadow = enableShadow
        self.shadowColor = shadowColor
        self.shadowRadius = shadowRadius
        self.enableHaptic = enableHaptic
        self.enableSound = enableSound
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    // MARK: - 预设主题
    public static var defaultThemes: [KeyboardTheme] {
        return [
            // 暖橙主题（默认主题）
            KeyboardTheme(
                name: "暖橙",
                type: .colorful,
                backgroundColor: WidgetColor(red: 1.0, green: 0.97, blue: 0.96, alpha: 1.0), // 暖橙背景
                keyBackgroundColor: WidgetColor(red: 1.0, green: 0.34, blue: 0.13, alpha: 1.0), // FF5722
                keyPressedColor: WidgetColor(red: 1.0, green: 0.54, blue: 0.40, alpha: 1.0), // FF8A65
                textColor: WidgetColor(red: 0.24, green: 0.15, blue: 0.14, alpha: 1.0), // 3E2723
                specialKeyColor: WidgetColor(red: 1.0, green: 0.80, blue: 0.74, alpha: 1.0), // FFCCBC
                borderColor: WidgetColor(red: 1.0, green: 0.80, blue: 0.74, alpha: 1.0) // FFCCBC
            ),

            // 浅色主题
            KeyboardTheme(
                name: "经典浅色",
                type: .light,
                backgroundColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
                keyBackgroundColor: WidgetColor(red: 0.95, green: 0.95, blue: 0.95, alpha: 1.0),
                textColor: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0)
            ),

            // 深色主题
            KeyboardTheme(
                name: "经典深色",
                type: .dark,
                backgroundColor: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
                keyBackgroundColor: WidgetColor(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0),
                textColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
            ),

            // 彩色主题
            KeyboardTheme(
                name: "活力彩色",
                type: .colorful,
                backgroundColor: WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 0.1),
                keyBackgroundColor: WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
                textColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
            ),

            // 渐变主题
            KeyboardTheme(
                name: "梦幻渐变",
                type: .gradient,
                backgroundColor: WidgetColor(red: 0.5, green: 0.0, blue: 0.5, alpha: 0.2),
                keyBackgroundColor: WidgetColor(red: 0.5, green: 0.0, blue: 0.5, alpha: 1.0),
                textColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
            )
        ]
    }

    // MARK: - 内置图片主题
    public static var builtInImageThemes: [KeyboardTheme] {
        return [
            // 科技风图片主题
            KeyboardTheme(
                name: "科技风",
                type: .custom,
                backgroundColor: WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 0.3),
                keyBackgroundColor: WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 0.8),
                textColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
                hasBackgroundImage: true,
                hasKeyImage: true,
                backgroundImagePath: "tech",
                keyImagePath: "tech",
                isBuiltInImageTheme: true,
                imageOpacity: 0.8
            ),

            // 自然风图片主题
            KeyboardTheme(
                name: "自然风",
                type: .custom,
                backgroundColor: WidgetColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 0.3),
                keyBackgroundColor: WidgetColor(red: 0.0, green: 0.5, blue: 0.0, alpha: 0.8),
                textColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
                hasBackgroundImage: true,
                hasKeyImage: true,
                backgroundImagePath: "nature",
                keyImagePath: "nature",
                isBuiltInImageTheme: true,
                imageOpacity: 0.8
            ),

            // 简约风图片主题
            KeyboardTheme(
                name: "简约风",
                type: .custom,
                backgroundColor: WidgetColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 0.3),
                keyBackgroundColor: WidgetColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 0.8),
                textColor: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
                hasBackgroundImage: true,
                hasKeyImage: true,
                backgroundImagePath: "minimal",
                keyImagePath: "minimal",
                isBuiltInImageTheme: true,
                imageOpacity: 0.6
            ),

            // 渐变风图片主题
            KeyboardTheme(
                name: "渐变风",
                type: .custom,
                backgroundColor: WidgetColor(red: 0.5, green: 0.0, blue: 0.5, alpha: 0.3),
                keyBackgroundColor: WidgetColor(red: 0.5, green: 0.0, blue: 0.5, alpha: 0.8),
                textColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
                hasBackgroundImage: true,
                hasKeyImage: true,
                backgroundImagePath: "gradient",
                keyImagePath: "gradient",
                isBuiltInImageTheme: true,
                imageOpacity: 0.9
            ),

            // 深色风图片主题
            KeyboardTheme(
                name: "深色风",
                type: .custom,
                backgroundColor: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.8),
                keyBackgroundColor: WidgetColor(red: 0.1, green: 0.1, blue: 0.1, alpha: 0.9),
                textColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
                hasBackgroundImage: true,
                hasKeyImage: true,
                backgroundImagePath: "dark",
                keyImagePath: "dark",
                isBuiltInImageTheme: true,
                imageOpacity: 0.7
            )
        ]
    }

    // MARK: - 所有主题
    public static var allThemes: [KeyboardTheme] {
        return defaultThemes + builtInImageThemes
    }
}

// MARK: - 字体权重
public enum FontWeight: String, Codable, CaseIterable, Sendable {
    case ultraLight = "ultraLight"
    case thin = "thin"
    case light = "light"
    case regular = "regular"
    case medium = "medium"
    case semibold = "semibold"
    case bold = "bold"
    case heavy = "heavy"
    case black = "black"

    public var name: String {
        switch self {
        case .ultraLight: return "超细"
        case .thin: return "细"
        case .light: return "轻"
        case .regular: return "常规"
        case .medium: return "中等"
        case .semibold: return "半粗"
        case .bold: return "粗"
        case .heavy: return "重"
        case .black: return "黑"
        }
    }

    public var displayName: String {
        return name
    }

    public var fontWeight: Font.Weight {
        switch self {
        case .ultraLight: return .ultraLight
        case .thin: return .thin
        case .light: return .light
        case .regular: return .regular
        case .medium: return .medium
        case .semibold: return .semibold
        case .bold: return .bold
        case .heavy: return .heavy
        case .black: return .black
        }
    }

    public var uiKitWeight: UIFont.Weight {
        switch self {
        case .ultraLight: return .ultraLight
        case .thin: return .thin
        case .light: return .light
        case .regular: return .regular
        case .medium: return .medium
        case .semibold: return .semibold
        case .bold: return .bold
        case .heavy: return .heavy
        case .black: return .black
        }
    }
}

// MARK: - 键盘主题管理器
@MainActor
public class KeyboardThemeManager: ObservableObject {
    @Published public var currentTheme: KeyboardTheme
    @Published public var availableThemes: [KeyboardTheme]
    @Published public var customImageThemes: [KeyboardTheme] = []

    private let dataManager = AppGroupDataManager.shared
    private let imageManager = KeyboardImageManager.shared

    public static let shared = KeyboardThemeManager()

    private init() {
        // 加载保存的主题，如果没有则使用默认主题
        if let savedTheme = dataManager.loadKeyboardTheme() {
            currentTheme = savedTheme
        } else {
            currentTheme = KeyboardTheme.defaultThemes[0]
        }

        // 加载可用主题列表（包括所有类型）
        availableThemes = KeyboardTheme.allThemes
        loadCustomImageThemes()
    }

    /// 切换主题
    public func setTheme(_ theme: KeyboardTheme) {
        currentTheme = theme
        dataManager.saveKeyboardTheme(theme)

        // 发送通知给键盘扩展
        NotificationCenter.default.post(
            name: NSNotification.Name("KeyboardThemeDidChange"),
            object: theme
        )
    }

    /// 添加自定义主题
    public func addCustomTheme(_ theme: KeyboardTheme) {
        availableThemes.append(theme)
        if theme.hasBackgroundImage || theme.hasKeyImage {
            customImageThemes.append(theme)
            saveCustomImageThemes()
        }
    }

    /// 删除自定义主题
    public func removeCustomTheme(_ theme: KeyboardTheme) {
        availableThemes.removeAll { $0.id == theme.id }
        customImageThemes.removeAll { $0.id == theme.id }

        // 删除图片文件
        if theme.hasBackgroundImage || theme.hasKeyImage {
            imageManager.deleteCustomTheme(theme.id)
            saveCustomImageThemes()
        }
    }

    /// 创建自定义图片主题
    public func createCustomImageTheme(
        name: String,
        backgroundImage: UIImage?,
        keyImage: UIImage?,
        baseTheme: KeyboardTheme? = nil
    ) async -> KeyboardTheme? {
        let themeId = UUID().uuidString
        var hasBackground = false
        var hasKey = false

        // 保存背景图片
        if let bgImage = backgroundImage {
            if await imageManager.processAndSaveCustomImage(bgImage, for: themeId, type: .background) {
                hasBackground = true
            }
        }

        // 保存按键图片
        if let keyImg = keyImage {
            if await imageManager.processAndSaveCustomImage(keyImg, for: themeId, type: .key) {
                hasKey = true
            }
        }

        // 创建主题
        let base = baseTheme ?? KeyboardTheme.defaultThemes[0]
        let customTheme = KeyboardTheme(
            id: themeId,
            name: name,
            type: .custom,
            keyStyle: base.keyStyle,
            backgroundColor: base.backgroundColor,
            keyBackgroundColor: base.keyBackgroundColor,
            keyPressedColor: base.keyPressedColor,
            textColor: base.textColor,
            specialKeyColor: base.specialKeyColor,
            borderColor: base.borderColor,
            hasBackgroundImage: hasBackground,
            hasKeyImage: hasKey,
            backgroundImagePath: hasBackground ? themeId : nil,
            keyImagePath: hasKey ? themeId : nil,
            isBuiltInImageTheme: false,
            imageOpacity: 0.8,
            fontName: base.fontName,
            fontSize: base.fontSize,
            fontWeight: base.fontWeight,
            keySpacing: base.keySpacing,
            keyHeight: base.keyHeight,
            showBorder: base.showBorder,
            borderWidth: base.borderWidth,
            enableShadow: base.enableShadow,
            shadowColor: base.shadowColor,
            shadowRadius: base.shadowRadius,
            enableHaptic: base.enableHaptic,
            enableSound: base.enableSound
        )

        addCustomTheme(customTheme)
        return customTheme
    }

    /// 获取主题的背景图片
    public func getBackgroundImage(for theme: KeyboardTheme) async -> UIImage? {
        guard theme.hasBackgroundImage,
              let imagePath = theme.backgroundImagePath else {
            return nil
        }

        return await imageManager.loadImage(
            for: imagePath,
            type: .background,
            isBuiltIn: theme.isBuiltInImageTheme
        )
    }

    /// 获取主题的按键图片
    public func getKeyImage(for theme: KeyboardTheme) async -> UIImage? {
        guard theme.hasKeyImage,
              let imagePath = theme.keyImagePath else {
            return nil
        }

        return await imageManager.loadImage(
            for: imagePath,
            type: .key,
            isBuiltIn: theme.isBuiltInImageTheme
        )
    }

    // MARK: - 私有方法
    private func loadCustomImageThemes() {
        let customIds = imageManager.getCustomThemeIds()
        // 这里可以从持久化存储加载自定义主题配置
        // 暂时为空，后续可以扩展
    }

    private func saveCustomImageThemes() {
        // 这里可以保存自定义主题列表到持久化存储
        // 暂时为空，后续可以扩展
    }
}

// MARK: - 简化的键盘主题（用于键盘扩展）
public struct SimplifiedKeyboardTheme: Codable {
    public let id: String
    public var name: String
    public var type: String
    public var keyStyle: String

    // 颜色配置（使用RGB值存储）
    public var backgroundColor: ColorData
    public var keyBackgroundColor: ColorData
    public var keyPressedColor: ColorData
    public var textColor: ColorData
    public var specialKeyColor: ColorData
    public var borderColor: ColorData

    // 图片配置
    public var hasBackgroundImage: Bool
    public var hasKeyImage: Bool
    public var backgroundImagePath: String?
    public var keyImagePath: String?
    public var isBuiltInImageTheme: Bool
    public var imageOpacity: Double
    public var imageBlendMode: String

    // 字体配置
    public var fontName: String
    public var fontSize: Double
    public var fontWeight: String

    // 布局配置
    public var keySpacing: Double
    public var keyHeight: Double
    public var showBorder: Bool
    public var borderWidth: Double

    // 特效配置
    public var enableShadow: Bool
    public var shadowColor: ColorData
    public var shadowRadius: Double
    public var enableHaptic: Bool
    public var enableSound: Bool

    // 创建时间
    public var createdAt: Date
    public var updatedAt: Date
}

// MARK: - 颜色数据结构
public struct ColorData: Codable {
    public let red: Double
    public let green: Double
    public let blue: Double
    public let alpha: Double

    public init(red: Double, green: Double, blue: Double, alpha: Double) {
        self.red = red
        self.green = green
        self.blue = blue
        self.alpha = alpha
    }
}
