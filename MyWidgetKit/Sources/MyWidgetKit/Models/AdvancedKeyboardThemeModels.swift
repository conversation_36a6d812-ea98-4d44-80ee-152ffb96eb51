//
//  AdvancedKeyboardThemeModels.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import Foundation

// MARK: - 按键类型枚举
public enum KeyType: String, CaseIterable, Codable, Sendable {
    case letter = "letter"           // 字母键
    case number = "number"           // 数字键
    case function = "function"       // 功能键（删除、换行等）
    case space = "space"            // 空格键
    case shift = "shift"            // 大小写切换键
    case symbol = "symbol"          // 符号键
    case punctuation = "punctuation" // 标点符号键

    public var displayName: String {
        switch self {
        case .letter: return "字母键"
        case .number: return "数字键"
        case .function: return "功能键"
        case .space: return "空格键"
        case .shift: return "切换键"
        case .symbol: return "符号键"
        case .punctuation: return "标点键"
        }
    }

    public var icon: String {
        switch self {
        case .letter: return "textformat.abc"
        case .number: return "textformat.123"
        case .function: return "function"
        case .space: return "space"
        case .shift: return "shift"
        case .symbol: return "at"
        case .punctuation: return "questionmark"
        }
    }
}

// MARK: - 单个按键配置
public struct KeyConfig: Codable, Identifiable, Equatable, Sendable {
    public let id: String
    public var keyType: KeyType
    public var keyValue: String // 按键显示的文字

    // 颜色配置
    public var backgroundColor: WidgetColor
    public var pressedColor: WidgetColor
    public var textColor: WidgetColor
    public var borderColor: WidgetColor

    // 字体配置
    public var fontName: String
    public var fontSize: Double
    public var fontWeight: FontWeight

    // 视觉效果配置
    public var cornerRadius: Double
    public var borderWidth: Double
    public var shadowEnabled: Bool
    public var shadowColor: WidgetColor
    public var shadowRadius: Double
    public var shadowOffset: CGSize

    // 按键尺寸配置
    public var widthMultiplier: Double // 宽度倍数（相对于标准按键）
    public var heightMultiplier: Double // 高度倍数

    // 图片配置
    public var hasCustomImage: Bool // 是否使用自定义图片
    public var normalImagePath: String? // 正常状态图片路径
    public var pressedImagePath: String? // 按下状态图片路径
    public var hoverImagePath: String? // 悬停状态图片路径（iPad支持）
    public var imageOpacity: Double // 图片透明度
    public var imageBlendMode: String // 图片混合模式
    public var hideTextWhenImageExists: Bool // 当有自定义图片时是否隐藏文字

    // 创建和更新时间
    public var createdAt: Date
    public var updatedAt: Date

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case id
        case keyType = "key_type"
        case keyValue = "key_value"
        case backgroundColor = "background_color"
        case pressedColor = "pressed_color"
        case textColor = "text_color"
        case borderColor = "border_color"
        case fontName = "font_name"
        case fontSize = "font_size"
        case fontWeight = "font_weight"
        case cornerRadius = "corner_radius"
        case borderWidth = "border_width"
        case shadowEnabled = "shadow_enabled"
        case shadowColor = "shadow_color"
        case shadowRadius = "shadow_radius"
        case shadowOffset = "shadow_offset"
        case widthMultiplier = "width_multiplier"
        case heightMultiplier = "height_multiplier"
        case hasCustomImage = "has_custom_image"
        case normalImagePath = "normal_image_path"
        case pressedImagePath = "pressed_image_path"
        case hoverImagePath = "hover_image_path"
        case imageOpacity = "image_opacity"
        case imageBlendMode = "image_blend_mode"
        case hideTextWhenImageExists = "hide_text_when_image_exists"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    public init(
        id: String = UUID().uuidString,
        keyType: KeyType,
        keyValue: String,
        backgroundColor: WidgetColor = WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
        pressedColor: WidgetColor = WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
        textColor: WidgetColor = WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
        borderColor: WidgetColor = WidgetColor(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0),
        fontName: String = "SF Pro",
        fontSize: Double = 16,
        fontWeight: FontWeight = .medium,
        cornerRadius: Double = 8,
        borderWidth: Double = 1,
        shadowEnabled: Bool = true,
        shadowColor: WidgetColor = WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.1),
        shadowRadius: Double = 2,
        shadowOffset: CGSize = CGSize(width: 0, height: 1),
        widthMultiplier: Double = 1.0,
        heightMultiplier: Double = 1.0,
        hasCustomImage: Bool = false,
        normalImagePath: String? = nil,
        pressedImagePath: String? = nil,
        hoverImagePath: String? = nil,
        imageOpacity: Double = 1.0,
        imageBlendMode: String = "normal",
        hideTextWhenImageExists: Bool = false
    ) {
        self.id = id
        self.keyType = keyType
        self.keyValue = keyValue
        self.backgroundColor = backgroundColor
        self.pressedColor = pressedColor
        self.textColor = textColor
        self.borderColor = borderColor
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontWeight = fontWeight
        self.cornerRadius = cornerRadius
        self.borderWidth = borderWidth
        self.shadowEnabled = shadowEnabled
        self.shadowColor = shadowColor
        self.shadowRadius = shadowRadius
        self.shadowOffset = shadowOffset
        self.widthMultiplier = widthMultiplier
        self.heightMultiplier = heightMultiplier
        self.hasCustomImage = hasCustomImage
        self.normalImagePath = normalImagePath
        self.pressedImagePath = pressedImagePath
        self.hoverImagePath = hoverImagePath
        self.imageOpacity = imageOpacity
        self.imageBlendMode = imageBlendMode
        self.hideTextWhenImageExists = hideTextWhenImageExists
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    // MARK: - Custom Codable Implementation

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        keyType = try container.decode(KeyType.self, forKey: .keyType)
        keyValue = try container.decode(String.self, forKey: .keyValue)
        backgroundColor = try container.decode(WidgetColor.self, forKey: .backgroundColor)
        pressedColor = try container.decode(WidgetColor.self, forKey: .pressedColor)
        textColor = try container.decode(WidgetColor.self, forKey: .textColor)
        borderColor = try container.decode(WidgetColor.self, forKey: .borderColor)
        fontName = try container.decode(String.self, forKey: .fontName)
        fontSize = try container.decode(Double.self, forKey: .fontSize)
        fontWeight = try container.decode(FontWeight.self, forKey: .fontWeight)
        cornerRadius = try container.decode(Double.self, forKey: .cornerRadius)
        borderWidth = try container.decode(Double.self, forKey: .borderWidth)
        shadowEnabled = try container.decode(Bool.self, forKey: .shadowEnabled)
        shadowColor = try container.decode(WidgetColor.self, forKey: .shadowColor)
        shadowRadius = try container.decode(Double.self, forKey: .shadowRadius)
        shadowOffset = try container.decode(CGSize.self, forKey: .shadowOffset)
        widthMultiplier = try container.decode(Double.self, forKey: .widthMultiplier)
        heightMultiplier = try container.decode(Double.self, forKey: .heightMultiplier)
        hasCustomImage = try container.decode(Bool.self, forKey: .hasCustomImage)
        normalImagePath = try container.decodeIfPresent(String.self, forKey: .normalImagePath)
        pressedImagePath = try container.decodeIfPresent(String.self, forKey: .pressedImagePath)
        hoverImagePath = try container.decodeIfPresent(String.self, forKey: .hoverImagePath)
        imageOpacity = try container.decode(Double.self, forKey: .imageOpacity)
        imageBlendMode = try container.decode(String.self, forKey: .imageBlendMode)
        hideTextWhenImageExists = try container.decodeIfPresent(Bool.self, forKey: .hideTextWhenImageExists) ?? false
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        updatedAt = try container.decode(Date.self, forKey: .updatedAt)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(keyType, forKey: .keyType)
        try container.encode(keyValue, forKey: .keyValue)
        try container.encode(backgroundColor, forKey: .backgroundColor)
        try container.encode(pressedColor, forKey: .pressedColor)
        try container.encode(textColor, forKey: .textColor)
        try container.encode(borderColor, forKey: .borderColor)
        try container.encode(fontName, forKey: .fontName)
        try container.encode(fontSize, forKey: .fontSize)
        try container.encode(fontWeight, forKey: .fontWeight)
        try container.encode(cornerRadius, forKey: .cornerRadius)
        try container.encode(borderWidth, forKey: .borderWidth)
        try container.encode(shadowEnabled, forKey: .shadowEnabled)
        try container.encode(shadowColor, forKey: .shadowColor)
        try container.encode(shadowRadius, forKey: .shadowRadius)
        try container.encode(shadowOffset, forKey: .shadowOffset)
        try container.encode(widthMultiplier, forKey: .widthMultiplier)
        try container.encode(heightMultiplier, forKey: .heightMultiplier)
        try container.encode(hasCustomImage, forKey: .hasCustomImage)
        try container.encodeIfPresent(normalImagePath, forKey: .normalImagePath)
        try container.encodeIfPresent(pressedImagePath, forKey: .pressedImagePath)
        try container.encodeIfPresent(hoverImagePath, forKey: .hoverImagePath)
        try container.encode(imageOpacity, forKey: .imageOpacity)
        try container.encode(imageBlendMode, forKey: .imageBlendMode)
        try container.encode(hideTextWhenImageExists, forKey: .hideTextWhenImageExists)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
    }
}

// MARK: - 按键类型配置模板
public struct KeyTypeConfig: Codable, Identifiable, Equatable, Sendable {
    public let id: String
    public var keyType: KeyType
    public var name: String
    public var description: String

    // 默认配置
    public var defaultBackgroundColor: WidgetColor
    public var defaultPressedColor: WidgetColor
    public var defaultTextColor: WidgetColor
    public var defaultBorderColor: WidgetColor
    public var defaultFontSize: Double
    public var defaultFontWeight: FontWeight
    public var defaultCornerRadius: Double
    public var defaultBorderWidth: Double

    // 应用到的按键列表
    public var affectedKeys: [String] // 按键值列表

    public var updatedAt: Date

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case id, name, description
        case keyType = "key_type"
        case defaultBackgroundColor = "default_background_color"
        case defaultPressedColor = "default_pressed_color"
        case defaultTextColor = "default_text_color"
        case defaultBorderColor = "default_border_color"
        case defaultFontSize = "default_font_size"
        case defaultFontWeight = "default_font_weight"
        case defaultCornerRadius = "default_corner_radius"
        case defaultBorderWidth = "default_border_width"
        case affectedKeys = "affected_keys"
        case updatedAt = "updated_at"
    }

    public init(
        keyType: KeyType,
        name: String? = nil,
        description: String? = nil,
        defaultBackgroundColor: WidgetColor = WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
        defaultPressedColor: WidgetColor = WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
        defaultTextColor: WidgetColor = WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
        defaultBorderColor: WidgetColor = WidgetColor(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0),
        defaultFontSize: Double = 16,
        defaultFontWeight: FontWeight = .medium,
        defaultCornerRadius: Double = 8,
        defaultBorderWidth: Double = 1,
        affectedKeys: [String] = []
    ) {
        self.id = UUID().uuidString
        self.keyType = keyType
        self.name = name ?? keyType.displayName
        self.description = description ?? "配置\(keyType.displayName)的外观"
        self.defaultBackgroundColor = defaultBackgroundColor
        self.defaultPressedColor = defaultPressedColor
        self.defaultTextColor = defaultTextColor
        self.defaultBorderColor = defaultBorderColor
        self.defaultFontSize = defaultFontSize
        self.defaultFontWeight = defaultFontWeight
        self.defaultCornerRadius = defaultCornerRadius
        self.defaultBorderWidth = defaultBorderWidth
        self.affectedKeys = affectedKeys
        self.updatedAt = Date()
    }
}

// MARK: - 高级键盘主题
public struct AdvancedKeyboardTheme: Codable, Identifiable, Equatable {
    public let id: String
    public var name: String
    public var description: String
    public var baseTheme: KeyboardTheme // 基础主题

    // 按键类型配置
    public var keyTypeConfigs: [KeyType: KeyTypeConfig]

    // 单个按键配置（覆盖类型配置）
    public var individualKeyConfigs: [String: KeyConfig]

    // 全局设置
    public var globalKeySpacing: Double
    public var globalKeyHeight: Double
    public var enableHapticFeedback: Bool
    public var enableSoundFeedback: Bool

    // 高级效果
    public var enableKeyAnimations: Bool
    public var animationDuration: Double
    public var enableGradientEffects: Bool
    public var enableParallaxEffect: Bool

    // 创建和更新时间
    public var createdAt: Date
    public var updatedAt: Date

    public init(
        id: String = UUID().uuidString,
        name: String,
        description: String = "",
        baseTheme: KeyboardTheme,
        globalKeySpacing: Double = 6,
        globalKeyHeight: Double = 44,
        enableHapticFeedback: Bool = true,
        enableSoundFeedback: Bool = true,
        enableKeyAnimations: Bool = true,
        animationDuration: Double = 0.1,
        enableGradientEffects: Bool = false,
        enableParallaxEffect: Bool = false
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.baseTheme = baseTheme
        self.globalKeySpacing = globalKeySpacing
        self.globalKeyHeight = globalKeyHeight
        self.enableHapticFeedback = enableHapticFeedback
        self.enableSoundFeedback = enableSoundFeedback
        self.enableKeyAnimations = enableKeyAnimations
        self.animationDuration = animationDuration
        self.enableGradientEffects = enableGradientEffects
        self.enableParallaxEffect = enableParallaxEffect
        self.createdAt = Date()
        self.updatedAt = Date()

        // 初始化默认按键类型配置
        self.keyTypeConfigs = Self.createDefaultKeyTypeConfigs()
        self.individualKeyConfigs = [:]
    }

    /// 完整初始化方法（包含配置）
    public init(
        id: String,
        name: String,
        description: String,
        baseTheme: KeyboardTheme,
        keyTypeConfigs: [KeyType: KeyTypeConfig],
        individualKeyConfigs: [String: KeyConfig],
        globalKeySpacing: Double,
        globalKeyHeight: Double,
        enableHapticFeedback: Bool,
        enableSoundFeedback: Bool,
        enableKeyAnimations: Bool,
        animationDuration: Double,
        enableGradientEffects: Bool,
        enableParallaxEffect: Bool
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.baseTheme = baseTheme
        self.keyTypeConfigs = keyTypeConfigs
        self.individualKeyConfigs = individualKeyConfigs
        self.globalKeySpacing = globalKeySpacing
        self.globalKeyHeight = globalKeyHeight
        self.enableHapticFeedback = enableHapticFeedback
        self.enableSoundFeedback = enableSoundFeedback
        self.enableKeyAnimations = enableKeyAnimations
        self.animationDuration = animationDuration
        self.enableGradientEffects = enableGradientEffects
        self.enableParallaxEffect = enableParallaxEffect
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    // 创建默认按键类型配置
    private static func createDefaultKeyTypeConfigs() -> [KeyType: KeyTypeConfig] {
        var configs: [KeyType: KeyTypeConfig] = [:]

        // 字母键配置
        configs[.letter] = KeyTypeConfig(
            keyType: .letter,
            affectedKeys: Array("ABCDEFGHIJKLMNOPQRSTUVWXYZ").map { String($0) }
        )

        // 数字键配置
        configs[.number] = KeyTypeConfig(
            keyType: .number,
            defaultBackgroundColor: WidgetColor(red: 0.95, green: 0.95, blue: 0.95, alpha: 1.0),
            affectedKeys: Array("1234567890").map { String($0) }
        )

        // 功能键配置
        configs[.function] = KeyTypeConfig(
            keyType: .function,
            defaultBackgroundColor: WidgetColor(red: 0.68, green: 0.68, blue: 0.7, alpha: 1.0),
            defaultTextColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
            affectedKeys: ["删除", "换行", "123", "ABC", "切换键盘"]
        )

        // 空格键配置
        configs[.space] = KeyTypeConfig(
            keyType: .space,
            defaultBackgroundColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
            affectedKeys: ["空格"]
        )

        // 切换键配置
        configs[.shift] = KeyTypeConfig(
            keyType: .shift,
            defaultBackgroundColor: WidgetColor(red: 0.68, green: 0.68, blue: 0.7, alpha: 1.0),
            defaultTextColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
            affectedKeys: ["⇧"]
        )

        // 符号键配置
        configs[.symbol] = KeyTypeConfig(
            keyType: .symbol,
            defaultBackgroundColor: WidgetColor(red: 0.9, green: 0.9, blue: 0.9, alpha: 1.0),
            affectedKeys: ["@", "#", "$", "%", "&", "*", "+", "="]
        )

        // 标点符号键配置
        configs[.punctuation] = KeyTypeConfig(
            keyType: .punctuation,
            defaultBackgroundColor: WidgetColor(red: 0.9, green: 0.9, blue: 0.9, alpha: 1.0),
            affectedKeys: [".", ",", "?", "!", ";", ":", "'", "\""]
        )

        return configs
    }

    // MARK: - Custom Codable Implementation

    private enum CodingKeys: String, CodingKey {
        case id, name, description, baseTheme
        case keyTypeConfigs, individualKeyConfigs
        case globalKeySpacing, globalKeyHeight
        case enableHapticFeedback, enableSoundFeedback
        case enableKeyAnimations, animationDuration
        case enableGradientEffects, enableParallaxEffect
        case createdAt, updatedAt
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)
        baseTheme = try container.decode(KeyboardTheme.self, forKey: .baseTheme)

        // 解码 keyTypeConfigs 字典
        let keyTypeConfigsDict = try container.decode([String: KeyTypeConfig].self, forKey: .keyTypeConfigs)
        var configs: [KeyType: KeyTypeConfig] = [:]
        for (key, config) in keyTypeConfigsDict {
            if let keyType = KeyType(rawValue: key) {
                configs[keyType] = config
            }
        }
        keyTypeConfigs = configs

        individualKeyConfigs = try container.decode([String: KeyConfig].self, forKey: .individualKeyConfigs)
        globalKeySpacing = try container.decode(Double.self, forKey: .globalKeySpacing)
        globalKeyHeight = try container.decode(Double.self, forKey: .globalKeyHeight)
        enableHapticFeedback = try container.decode(Bool.self, forKey: .enableHapticFeedback)
        enableSoundFeedback = try container.decode(Bool.self, forKey: .enableSoundFeedback)
        enableKeyAnimations = try container.decode(Bool.self, forKey: .enableKeyAnimations)
        animationDuration = try container.decode(Double.self, forKey: .animationDuration)
        enableGradientEffects = try container.decode(Bool.self, forKey: .enableGradientEffects)
        enableParallaxEffect = try container.decode(Bool.self, forKey: .enableParallaxEffect)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        updatedAt = try container.decode(Date.self, forKey: .updatedAt)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(description, forKey: .description)
        try container.encode(baseTheme, forKey: .baseTheme)

        // 编码 keyTypeConfigs 为字典格式
        var keyTypeConfigsDict: [String: KeyTypeConfig] = [:]
        for (keyType, config) in keyTypeConfigs {
            keyTypeConfigsDict[keyType.rawValue] = config
        }
        try container.encode(keyTypeConfigsDict, forKey: .keyTypeConfigs)

        try container.encode(individualKeyConfigs, forKey: .individualKeyConfigs)
        try container.encode(globalKeySpacing, forKey: .globalKeySpacing)
        try container.encode(globalKeyHeight, forKey: .globalKeyHeight)
        try container.encode(enableHapticFeedback, forKey: .enableHapticFeedback)
        try container.encode(enableSoundFeedback, forKey: .enableSoundFeedback)
        try container.encode(enableKeyAnimations, forKey: .enableKeyAnimations)
        try container.encode(animationDuration, forKey: .animationDuration)
        try container.encode(enableGradientEffects, forKey: .enableGradientEffects)
        try container.encode(enableParallaxEffect, forKey: .enableParallaxEffect)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
    }
}
