import SwiftUI
import WidgetKit

// MARK: - 应用启动器图标样式
public enum AppIconStyle: String, Codable, CaseIterable {
    case standard = "标准"
    case rounded = "圆角"
    case circular = "圆形"
    case squircle = "方圆形"
    case custom = "自定义"
    
    public var name: String {
        return self.rawValue
    }
}

// MARK: - 应用启动器布局类型
public enum AppLauncherLayoutType: String, Codable, CaseIterable {
    case grid = "网格"
    case list = "列表"
    case dock = "底部栏"
    case floating = "浮动"
    
    public var name: String {
        return self.rawValue
    }
}

// MARK: - 应用启动器项目
public struct AppLauncherItem: Identifiable, Codable, Equatable {
    public var id: String
    public var name: String
    public var iconName: String
    public var urlScheme: String
    public var color: Color
    public var position: Int
    public var groupName: String?
    
    // 编码和解码 Color
    enum CodingKeys: String, CodingKey {
        case id, name, iconName, urlScheme, position, groupName
        case colorR, colorG, colorB, colorA
    }
    
    public init(id: String = UUID().uuidString, 
                name: String, 
                iconName: String, 
                urlScheme: String, 
                color: Color, 
                position: Int, 
                groupName: String? = nil) {
        self.id = id
        self.name = name
        self.iconName = iconName
        self.urlScheme = urlScheme
        self.color = color
        self.position = position
        self.groupName = groupName
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        iconName = try container.decode(String.self, forKey: .iconName)
        urlScheme = try container.decode(String.self, forKey: .urlScheme)
        position = try container.decode(Int.self, forKey: .position)
        groupName = try container.decodeIfPresent(String.self, forKey: .groupName)
        
        // 解码颜色
        let r = try container.decode(Double.self, forKey: .colorR)
        let g = try container.decode(Double.self, forKey: .colorG)
        let b = try container.decode(Double.self, forKey: .colorB)
        let a = try container.decode(Double.self, forKey: .colorA)
        color = Color(.sRGB, red: r, green: g, blue: b, opacity: a)
    }
    
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(iconName, forKey: .iconName)
        try container.encode(urlScheme, forKey: .urlScheme)
        try container.encode(position, forKey: .position)
        try container.encodeIfPresent(groupName, forKey: .groupName)
        
        // 编码颜色
        let uiColor = UIColor(color)
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)
        
        try container.encode(Double(r), forKey: .colorR)
        try container.encode(Double(g), forKey: .colorG)
        try container.encode(Double(b), forKey: .colorB)
        try container.encode(Double(a), forKey: .colorA)
    }
}

// MARK: - 应用启动器小组件数据
public struct AppLauncherWidgetData: Codable, Equatable {
    public var items: [AppLauncherItem]
    public var iconStyle: AppIconStyle
    public var layoutType: AppLauncherLayoutType
    public var background: WidgetBackground
    public var fontName: String
    public var fontSize: Double
    public var fontColor: Color
    public var showLabels: Bool
    public var columns: Int
    public var rows: Int
    public var spacing: Double
    public var lastUpdated: Date
    
    // 编码和解码 Color
    enum CodingKeys: String, CodingKey {
        case items, iconStyle, layoutType, background, fontName, fontSize, showLabels, columns, rows, spacing, lastUpdated
        case fontColorR, fontColorG, fontColorB, fontColorA
    }
    
    public init(items: [AppLauncherItem] = [],
                iconStyle: AppIconStyle = .rounded,
                layoutType: AppLauncherLayoutType = .grid,
                background: WidgetBackground = .color(WidgetColor.fromColor(.white)),
                fontName: String = "SF Pro",
                fontSize: Double = 12,
                fontColor: Color = .black,
                showLabels: Bool = true,
                columns: Int = 4,
                rows: Int = 2,
                spacing: Double = 10,
                lastUpdated: Date = Date()) {
        self.items = items
        self.iconStyle = iconStyle
        self.layoutType = layoutType
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontColor = fontColor
        self.showLabels = showLabels
        self.columns = columns
        self.rows = rows
        self.spacing = spacing
        self.lastUpdated = lastUpdated
    }
    
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        items = try container.decode([AppLauncherItem].self, forKey: .items)
        iconStyle = try container.decode(AppIconStyle.self, forKey: .iconStyle)
        layoutType = try container.decode(AppLauncherLayoutType.self, forKey: .layoutType)
        background = try container.decode(WidgetBackground.self, forKey: .background)
        fontName = try container.decode(String.self, forKey: .fontName)
        fontSize = try container.decode(Double.self, forKey: .fontSize)
        showLabels = try container.decode(Bool.self, forKey: .showLabels)
        columns = try container.decode(Int.self, forKey: .columns)
        rows = try container.decode(Int.self, forKey: .rows)
        spacing = try container.decode(Double.self, forKey: .spacing)
        lastUpdated = try container.decode(Date.self, forKey: .lastUpdated)
        
        // 解码颜色
        let r = try container.decode(Double.self, forKey: .fontColorR)
        let g = try container.decode(Double.self, forKey: .fontColorG)
        let b = try container.decode(Double.self, forKey: .fontColorB)
        let a = try container.decode(Double.self, forKey: .fontColorA)
        fontColor = Color(.sRGB, red: r, green: g, blue: b, opacity: a)
    }
    
    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(items, forKey: .items)
        try container.encode(iconStyle, forKey: .iconStyle)
        try container.encode(layoutType, forKey: .layoutType)
        try container.encode(background, forKey: .background)
        try container.encode(fontName, forKey: .fontName)
        try container.encode(fontSize, forKey: .fontSize)
        try container.encode(showLabels, forKey: .showLabels)
        try container.encode(columns, forKey: .columns)
        try container.encode(rows, forKey: .rows)
        try container.encode(spacing, forKey: .spacing)
        try container.encode(lastUpdated, forKey: .lastUpdated)
        
        // 编码颜色
        let uiColor = UIColor(fontColor)
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)
        
        try container.encode(Double(r), forKey: .fontColorR)
        try container.encode(Double(g), forKey: .fontColorG)
        try container.encode(Double(b), forKey: .fontColorB)
        try container.encode(Double(a), forKey: .fontColorA)
    }
}
