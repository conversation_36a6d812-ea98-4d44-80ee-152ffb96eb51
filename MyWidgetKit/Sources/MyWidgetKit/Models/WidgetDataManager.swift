import Foundation
import WidgetKit

/// 小组件数据管理器
@available(iOS 16.0, *)
public final class WidgetDataManager: @unchecked Sendable {
    public static let shared = WidgetDataManager()

    private init() {}

    private let userDefaults = UserDefaults(suiteName: "group.com.ort.JZJJWidgetAPP.group")

    /// 保存小组件数据
    public func saveWidgetData(_ data: WidgetData) {
        guard let encoded = try? JSONEncoder().encode(data) else { return }
        userDefaults?.set(encoded, forKey: "widgetData")
        WidgetCenter.shared.reloadAllTimelines()
    }

    /// 加载小组件数据
    public func loadWidgetData() -> WidgetData? {
        guard let data = userDefaults?.data(forKey: "widgetData"),
              let widgetData = try? JSONDecoder().decode(WidgetData.self, from: data) else { return nil }
        return widgetData
    }
}
