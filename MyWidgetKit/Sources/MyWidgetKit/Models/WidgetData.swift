import Foundation
import SwiftUI

/// 颜色包装器，用于在 Codable 中存储 Color
@available(iOS 16.0, *)
public struct ColorWrapper: Codable, Sendable {
    public let red: CGFloat
    public let green: CGFloat
    public let blue: CGFloat
    public let alpha: CGFloat

    public init(color: Color) {
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        self.red = red
        self.green = green
        self.blue = blue
        self.alpha = alpha
    }

    public var color: Color {
        Color(.sRGB, red: red, green: green, blue: blue, opacity: alpha)
    }
}

/// 每日一言数据模型
@available(iOS 16.0, *)
public struct DailyQuoteData: Codable, Sendable {
    public var content: String
    public var style: QuoteStyle
    public var lastUpdated: Date

    public struct QuoteStyle: Codable, Sendable {
        public enum Background: Codable, Sendable {
            case color(ColorWrapper)
            case image(Data)

            public var isImage: Bool {
                switch self {
                case .image: return true
                case .color: return false
                }
            }

            public var color: Color? {
                switch self {
                case let .color(wrapper): return wrapper.color
                case .image: return nil
                }
            }

            public var imageData: Data? {
                switch self {
                case let .image(data): return data
                case .color: return nil
                }
            }

            private enum CodingKeys: String, CodingKey {
                case type, color, imageData
            }

            public init(from decoder: Decoder) throws {
                let container = try decoder.container(keyedBy: CodingKeys.self)
                let type = try container.decode(String.self, forKey: .type)

                switch type {
                case "color":
                    let colorWrapper = try container.decode(ColorWrapper.self, forKey: .color)
                    self = .color(colorWrapper)
                case "image":
                    let data = try container.decode(Data.self, forKey: .imageData)
                    self = .image(data)
                default:
                    throw DecodingError.dataCorruptedError(
                        forKey: .type,
                        in: container,
                        debugDescription: "Invalid type: \(type)"
                    )
                }
            }

            public func encode(to encoder: Encoder) throws {
                var container = encoder.container(keyedBy: CodingKeys.self)

                switch self {
                case let .color(colorWrapper):
                    try container.encode("color", forKey: .type)
                    try container.encode(colorWrapper, forKey: .color)
                case let .image(data):
                    try container.encode("image", forKey: .type)
                    try container.encode(data, forKey: .imageData)
                }
            }
        }

        public private(set) var background: Background
        public private(set) var textColor: ColorWrapper
        public var fontSize: Double
        public var showBorder: Bool
        public private(set) var borderColor: ColorWrapper

        public var textSwiftUIColor: Color {
            get { textColor.color }
            set { textColor = ColorWrapper(color: newValue) }
        }

        public var borderSwiftUIColor: Color {
            get { borderColor.color }
            set { borderColor = ColorWrapper(color: newValue) }
        }

        public init(background: Background = .color(ColorWrapper(color: .white)),
                    textColor: Color = .black,
                    fontSize: Double = 16,
                    showBorder: Bool = false,
                    borderColor: Color = .gray)
        {
            self.background = background
            self.textColor = ColorWrapper(color: textColor)
            self.fontSize = fontSize
            self.showBorder = showBorder
            self.borderColor = ColorWrapper(color: borderColor)
        }

        @available(iOS 16.0, *)
        public static let `default`: QuoteStyle = {
            let style = QuoteStyle()
            return style
        }()
    }

    public init(content: String, style: QuoteStyle, lastUpdated: Date) {
        self.content = content
        self.style = style
        self.lastUpdated = lastUpdated
    }

    public static func createDefault() -> DailyQuoteData {
        DailyQuoteData(
            content: "",
            style: .default,
            lastUpdated: Date()
        )
    }
}

/// 小组件数据模型
@available(iOS 16.0, *)
public struct WidgetData: Codable, Sendable {
    public enum WidgetType: String, Codable, Sendable {
        case dailyQuote
        case qrImage
    }

    public let type: WidgetType
    public var dailyQuote: DailyQuoteData?
    public let content: String
    private(set) var backgroundColor: ColorWrapper
    private(set) var textColor: ColorWrapper
    public var fontSize: Double
    public var showBorder: Bool
    private(set) var borderColor: ColorWrapper
    public let lastUpdated: Date

    public var backgroundSwiftUIColor: Color {
        get { backgroundColor.color }
        set { backgroundColor = ColorWrapper(color: newValue) }
    }

    public var textSwiftUIColor: Color {
        get { textColor.color }
        set { textColor = ColorWrapper(color: newValue) }
    }

    public var borderSwiftUIColor: Color {
        get { borderColor.color }
        set { borderColor = ColorWrapper(color: newValue) }
    }

    public init(type: WidgetType, content: String,
                backgroundColor: Color = .white,
                textColor: Color = .black,
                fontSize: Double = 16,
                showBorder: Bool = false,
                borderColor: Color = .gray,
                lastUpdated: Date = Date())
    {
        self.type = type
        self.content = content
        self.backgroundColor = ColorWrapper(color: backgroundColor)
        self.textColor = ColorWrapper(color: textColor)
        self.fontSize = fontSize
        self.showBorder = showBorder
        self.borderColor = ColorWrapper(color: borderColor)
        self.lastUpdated = lastUpdated
    }

    public init(type: WidgetType) {
        self.type = type
        content = ""
        backgroundColor = ColorWrapper(color: .white)
        textColor = ColorWrapper(color: .black)
        fontSize = 16
        showBorder = false
        borderColor = ColorWrapper(color: .gray)
        lastUpdated = Date()

        switch type {
        case .dailyQuote:
            dailyQuote = DailyQuoteData.createDefault()
        case .qrImage:
            dailyQuote = nil
        }
    }

    @available(iOS 16.0, *)
    public static let placeholder: WidgetData = {
        let data = WidgetData(
            type: .dailyQuote,
            content: "每日一句占位内容",
            backgroundColor: .white,
            textColor: .black,
            fontSize: 16,
            showBorder: false,
            borderColor: .gray
        )
        return data
    }()
}


