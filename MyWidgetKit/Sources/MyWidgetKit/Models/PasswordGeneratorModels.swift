import SwiftUI
import WidgetKit
import Security

// MARK: - 密码复杂度选项
public enum PasswordComplexity: String, Codable, CaseIterable, Identifiable {
    case simple = "简单"      // 仅数字
    case medium = "中等"      // 数字+小写字母
    case strong = "强"        // 数字+大小写字母
    case veryStrong = "超强"  // 数字+大小写字母+特殊字符
    
    public var id: String { rawValue }
    
    // 获取复杂度描述
    public var description: String {
        switch self {
        case .simple:
            return "仅包含数字"
        case .medium:
            return "包含数字和小写字母"
        case .strong:
            return "包含数字和大小写字母"
        case .veryStrong:
            return "包含数字、大小写字母和特殊字符"
        }
    }
    
    // 获取复杂度图标
    public var icon: String {
        switch self {
        case .simple:
            return "1.circle"
        case .medium:
            return "2.circle"
        case .strong:
            return "3.circle"
        case .veryStrong:
            return "4.circle"
        }
    }
    
    // 获取复杂度颜色
    public var color: Color {
        switch self {
        case .simple:
            return .red
        case .medium:
            return .orange
        case .strong:
            return .blue
        case .veryStrong:
            return .green
        }
    }
    
    // 获取字符集
    public var characterSet: String {
        switch self {
        case .simple:
            return "0123456789"
        case .medium:
            return "0123456789abcdefghijklmnopqrstuvwxyz"
        case .strong:
            return "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"
        case .veryStrong:
            return "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ!@#$%^&*()-_=+[]{}|;:,.<>?/"
        }
    }
}

// MARK: - 密码生成器配置
public struct PasswordGeneratorConfig: Codable, WidgetPreviewableData {
    // 密码长度
    public var passwordLength: Int
    
    // 密码复杂度
    public var complexity: PasswordComplexity
    
    // 自动复制到剪贴板
    public var autoCopyToClipboard: Bool
    
    // 显示密码
    public var showPassword: Bool
    
    // 背景设置
    public var background: WidgetBackground
    
    // 字体设置
    public var fontName: String
    public var fontSize: CGFloat
    public var fontColor: WidgetColor
    
    // 强调色
    public var accentColor: WidgetColor
    
    // 当前生成的密码
    public var currentPassword: String
    
    // 最后更新时间
    public var lastUpdated: Date
    
    // 初始化方法
    public init(
        passwordLength: Int = 12,
        complexity: PasswordComplexity = .strong,
        autoCopyToClipboard: Bool = true,
        showPassword: Bool = true,
        background: WidgetBackground = .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
        fontName: String = "SF Pro",
        fontSize: CGFloat = 14,
        fontColor: WidgetColor = WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
        accentColor: WidgetColor = WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
        currentPassword: String = "",
        lastUpdated: Date = Date()
    ) {
        self.passwordLength = passwordLength
        self.complexity = complexity
        self.autoCopyToClipboard = autoCopyToClipboard
        self.showPassword = showPassword
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontColor = fontColor
        self.accentColor = accentColor
        self.currentPassword = currentPassword.isEmpty ? PasswordGenerator.generatePassword(length: passwordLength, complexity: complexity) : currentPassword
        self.lastUpdated = lastUpdated
    }
    
    // 创建默认配置
    public static func createDefault() -> PasswordGeneratorConfig {
        return PasswordGeneratorConfig(
            passwordLength: 12,
            complexity: .strong,
            autoCopyToClipboard: true,
            showPassword: true,
            background: .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
            fontName: "SF Pro",
            fontSize: 14,
            fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
            accentColor: WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
            currentPassword: PasswordGenerator.generatePassword(length: 12, complexity: .strong),
            lastUpdated: Date()
        )
    }
}

// MARK: - 密码生成工具
public class PasswordGenerator {
    // 生成随机密码
    public static func generatePassword(length: Int, complexity: PasswordComplexity) -> String {
        let characterSet = complexity.characterSet
        var password = ""
        
        // 使用Security框架的SecRandomCopyBytes生成加密安全的随机密码
        var randomData = Data(count: length)
        let result = randomData.withUnsafeMutableBytes { mutableBytes in
            SecRandomCopyBytes(kSecRandomDefault, length, mutableBytes.baseAddress!)
        }
        
        // 检查是否成功生成随机数据
        guard result == errSecSuccess else {
            // 如果失败，使用备用方法
            return generateFallbackPassword(length: length, complexity: complexity)
        }
        
        // 将随机数据转换为密码字符
        for i in 0..<length {
            let randomIndex = Int(randomData[i]) % characterSet.count
            let index = characterSet.index(characterSet.startIndex, offsetBy: randomIndex)
            password.append(characterSet[index])
        }
        
        return password
    }
    
    // 备用密码生成方法
    private static func generateFallbackPassword(length: Int, complexity: PasswordComplexity) -> String {
        let characterSet = complexity.characterSet
        var password = ""
        
        for _ in 0..<length {
            let randomIndex = Int.random(in: 0..<characterSet.count)
            let index = characterSet.index(characterSet.startIndex, offsetBy: randomIndex)
            password.append(characterSet[index])
        }
        
        return password
    }
    
    // 评估密码强度
    public static func evaluatePasswordStrength(_ password: String) -> Double {
        // 简单的密码强度评估算法
        var strength = 0.0
        
        // 长度评分
        strength += min(Double(password.count) * 0.5, 50.0)
        
        // 字符多样性评分
        let hasDigits = password.rangeOfCharacter(from: .decimalDigits) != nil
        let hasLowercase = password.rangeOfCharacter(from: .lowercaseLetters) != nil
        let hasUppercase = password.rangeOfCharacter(from: .uppercaseLetters) != nil
        let hasSymbols = password.rangeOfCharacter(from: .punctuationCharacters) != nil
        
        if hasDigits { strength += 10.0 }
        if hasLowercase { strength += 10.0 }
        if hasUppercase { strength += 15.0 }
        if hasSymbols { strength += 15.0 }
        
        return min(strength, 100.0)
    }
}
