import AppIntents
import WidgetKit
import SwiftUI
import Foundation
import UIKit

/// 应用启动器项目点击意图
@available(iOS 16.0, *)
struct AppLauncherItemIntent: AppIntent {
    static var title: LocalizedStringResource { "打开应用" }
    static var description: IntentDescription { IntentDescription("打开选定的应用") }

    @Parameter(title: "项目ID")
    var itemId: String

    @Parameter(title: "项目名称")
    var itemName: String

    @Parameter(title: "URL Scheme")
    var urlScheme: String

    @Parameter(title: "小组件类型")
    var widgetType: String

    init() {
        self.itemId = ""
        self.itemName = ""
        self.urlScheme = ""
        self.widgetType = ""
    }

    init(itemId: String, itemName: String, urlScheme: String, widgetType: String = "appLauncher") {
        self.itemId = itemId
        self.itemName = itemName
        self.urlScheme = urlScheme
        self.widgetType = widgetType
    }

    func perform() async throws -> some IntentResult {
        // 打印调试信息
        print("AppLauncherItemIntent.perform() 被调用")
        print("itemId: \(itemId), itemName: \(itemName), urlScheme: \(urlScheme), widgetType: \(widgetType)")

        // 始终构建指向我们自己APP的URL，包含所有必要信息
        // 对所有参数进行URL编码
        let encodedName = itemName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let encodedUrlScheme = urlScheme.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""

        // 构建预览URL，包含所有必要参数（包括原始的urlScheme）
        let previewURLString = "jzjjwidget://preview?id=\(itemId)&name=\(encodedName)&type=\(widgetType)&urlScheme=\(encodedUrlScheme)"

        // 打印调试信息
        print("构建预览URL: \(previewURLString)")

        if let previewURL = URL(string: previewURLString) {
            // 打印调试信息
            print("尝试打开预览URL: \(previewURL)")
            await openURL(previewURL)
            return .result()
        } else {
            // 打印调试信息
            print("无法创建有效的URL对象，URL字符串: \(previewURLString)")

            // 尝试构建一个更简单的URL作为备用方案
            let fallbackURLString = "jzjjwidget://preview?id=\(itemId)"
            if let fallbackURL = URL(string: fallbackURLString) {
                print("尝试打开备用URL: \(fallbackURL)")
                await openURL(fallbackURL)
                return .result()
            }
        }

        // 如果无法构建URL，则抛出错误
        throw AppLauncherError.invalidURL("无法构建有效的URL")
    }

    @MainActor
    private func openURL(_ url: URL) async {
        print("尝试打开URL: \(url)")

        // 使用更可靠的方式打开URL
        if UIApplication.shared.canOpenURL(url) {
            print("可以打开URL: \(url)")
            do {
                let success = await UIApplication.shared.open(url, options: [:])
                print("打开URL结果: \(success ? "成功" : "失败")")
            } catch {
                print("打开URL时发生错误: \(error.localizedDescription)")
                // 尝试使用备用方法
                fallbackOpenURL(url)
            }
        } else {
            print("无法打开URL: \(url)，应用可能未注册该URL Scheme")
            // 尝试使用备用方法
            fallbackOpenURL(url)
        }
    }

    // 备用方法：使用更简单的URL格式
    @MainActor
    private func fallbackOpenURL(_ url: URL) {
        print("使用备用方法打开URL")

        // 1. 尝试使用更简单的URL格式
        let simpleURLString = "jzjjwidget://preview?id=\(itemId)"
        if let simpleURL = URL(string: simpleURLString) {
            print("尝试打开简单URL: \(simpleURL)")
            UIApplication.shared.open(simpleURL, options: [:]) { success in
                print("打开简单URL结果: \(success ? "成功" : "失败")")
            }
            return
        }

        // 2. 尝试使用通用URL格式
        let universalURLString = "jzjjwidget://"
        if let universalURL = URL(string: universalURLString) {
            print("尝试打开通用URL: \(universalURL)")
            UIApplication.shared.open(universalURL, options: [:]) { success in
                print("打开通用URL结果: \(success ? "成功" : "失败")")
            }
            return
        }
    }
}

/// 应用启动器错误类型
@available(iOS 16.0, *)
enum AppLauncherError: Error {
    case invalidURL(String)

    var localizedDescription: String {
        switch self {
        case .invalidURL(let message):
            return message
        }
    }
}
