import AppIntents
import SwiftUI
import WidgetKit

/// 应用快捷启动器小组件视图
public struct AppLauncherWidgetView: View {
    // MARK: - 属性

    public var data: AppLauncherWidgetData
    public var family: WidgetFamily

    // MARK: - 初始化

    public init(data: AppLauncherWidgetData, family: WidgetFamily) {
        self.data = data
        self.family = family
    }

    // MARK: - 视图主体

    public var body: some View {
        
        GeometryReader { geometry in
            //        ZStack {
            // 内容
            VStack(spacing: 0) {
                switch data.layoutType {
                case .grid:
                    gridLayout
                case .list:
                    listLayout
                case .dock:
                    dockLayout
                case .floating:
                    floatingLayout
                }
            }
            
            .frame(width: geometry.size.width, height: geometry.size.height)
            .adaptiveBackground(content: {
                // 背景 - 使用明确的尺寸
                data.background.backgroundView(width: widgetWidth, height: widgetHeight)
            })
            //        }
            //        .frame(width: widgetWidth, height: widgetHeight)
            // 添加 widgetURL，点击整个小组件时打开
            .widgetURL(createWidgetURL())
        }
    }

    // 创建小组件URL
    private func createWidgetURL() -> URL? {
        // 构建URL，包含必要参数
        let urlString = "jzjjwidget://widget?type=appLauncher"
        return URL(string: urlString)
    }

    // MARK: - 网格布局

    private var gridLayout: some View {
        LazyVGrid(
            columns: Array(repeating: GridItem(.flexible(), spacing: data.spacing), count: data.columns),
            spacing: data.spacing
        ) {
            // 根据行数和列数限制显示的项目数量
            let maxItems = data.rows * data.columns
            ForEach(visibleItems.prefix(maxItems)) { item in
                appIconView(for: item)
            }
        }
    }

    // MARK: - 列表布局

    private var listLayout: some View {
        VStack(spacing: data.spacing) {
            ForEach(visibleItems) { item in
                HStack {
                    appIconView(for: item, showLabel: false)
                        .frame(width: 40, height: 40)

                    if data.showLabels {
                        Text(item.name)
                            .font(.custom(data.fontName, size: data.fontSize))
                            .foregroundColor(data.fontColor)
                            .lineLimit(1)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(data.fontColor.opacity(0.5))
                        .font(.system(size: 14))
                }
                .padding(.vertical, 4)
                .background(Color.white.opacity(0.1))
                .cornerRadius(8)
            }
        }
    }

    // MARK: - 底部栏布局

    private var dockLayout: some View {
        HStack(spacing: data.spacing) {
            ForEach(visibleItems.prefix(5)) { item in
                appIconView(for: item)
            }
        }
        .padding(.horizontal)
    }

    // MARK: - 浮动布局

    private var floatingLayout: some View {
        ZStack {
            ForEach(visibleItems.prefix(6)) { item in
                appIconView(for: item, showLabel: false)
                    .frame(width: 50, height: 50)
                    .position(
                        x: CGFloat.random(in: 20...widgetWidth-20),
                        y: CGFloat.random(in: 20...widgetHeight-20)
                    )
            }
        }
        .frame(width: widgetWidth, height: widgetHeight)
    }

    // MARK: - 应用图标视图

    private func appIconView(for item: AppLauncherItem, showLabel: Bool = true) -> some View {
        // 创建通用的内容视图
        let contentView = VStack(spacing: 4) {
            // 图标
            ZStack {
                // 图标背景
                iconBackground(for: item)

                // 图标
                Image(systemName: item.iconName)
                    .font(.system(size: iconSize * 0.6))
                    .foregroundColor(.white)
            }
            .frame(width: iconSize, height: iconSize)
            .contentShape(Rectangle()) // 确保整个区域可点击

            // 标签
            if data.showLabels && showLabel {
                Text(item.name)
                    .font(.custom(data.fontName, size: data.fontSize))
                    .foregroundColor(data.fontColor)
                    .lineLimit(1)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .frame(maxWidth: .infinity)
        .contentShape(Rectangle()) // 确保整个区域可点击

        // 根据iOS版本使用不同的按钮实现
        let intent = AppLauncherItemIntent(
            itemId: item.id,
            itemName: item.name,
            urlScheme: item.urlScheme,
            widgetType: "appLauncher"
        )

        // 使用AnyView来统一返回类型
        return createButton(with: contentView, intent: intent)
    }

    // 创建按钮的辅助方法
    @ViewBuilder
    private func createButton(with content: some View, intent: AppLauncherItemIntent) -> some View {
        // 构建URL，包含必要参数
        let encodedName = intent.itemName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let encodedUrlScheme = intent.urlScheme.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let urlString = "jzjjwidget://app?id=\(intent.itemId)&name=\(encodedName)&urlScheme=\(encodedUrlScheme)&type=\(intent.widgetType)"

        // 使用Link组件，点击时打开URL
        if let url = URL(string: urlString) {
            Link(destination: url) {
                content
            }
            .buttonStyle(PlainButtonStyle()) // 使用PlainButtonStyle避免默认的按钮样式
            .widgetAccentable() // 添加点击时的视觉反馈
        } else {
            // 如果URL无效，则显示普通视图
            content
                .widgetAccentable() // 添加点击时的视觉反馈
        }
    }

    // MARK: - 按钮点击处理

    private func handleButtonTap(intent: AppLauncherItemIntent) {
        // 使用分离的异步方法
        performIntentAsync(intent)
    }

    private func performIntentAsync(_ intent: AppLauncherItemIntent) {
        // 打印调试信息
        print("performIntentAsync 被调用，intent: \(intent)")

        // 使用普通Task而不是detached任务，以确保在小组件环境中正常工作
        _Concurrency.Task {
            do {
                print("开始执行intent.perform()")
                try await intent.perform()
                print("intent.perform()执行完成")
            } catch {
                print("执行意图失败: \(error.localizedDescription)")

                // 如果主要方法失败，使用备用方案
                fallbackOpenURL(intent)
            }
        }
    }

    // 备用方案：如果主要方法失败，尝试直接打开URL
    private func fallbackOpenURL(_ intent: AppLauncherItemIntent) {
        print("使用备用方案打开URL")

        // 构建指向我们自己APP的URL，包含所有必要信息
        let encodedName = intent.itemName.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        let encodedUrlScheme = intent.urlScheme.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""

        // 构建预览URL，包含所有必要参数（包括原始的urlScheme）
        let previewURLString = "jzjjwidget://preview?id=\(intent.itemId)&name=\(encodedName)&type=\(intent.widgetType)&urlScheme=\(encodedUrlScheme)"

        if let previewURL = URL(string: previewURLString) {
            print("备用方案：尝试打开预览URL: \(previewURL)")
            openURLDirectly(previewURL)
        } else {
            // 如果构建预览URL失败，尝试更简单的URL
            let simpleURLString = "jzjjwidget://preview?id=\(intent.itemId)"
            if let simpleURL = URL(string: simpleURLString) {
                print("备用方案：尝试打开简单URL: \(simpleURL)")
                openURLDirectly(simpleURL)
            }
        }
    }

    // 直接打开URL的辅助方法
    private func openURLDirectly(_ url: URL) {
        #if os(iOS)
        // 在iOS上，使用UIApplication打开URL
        DispatchQueue.main.async {
            print("尝试直接打开URL: \(url)")

            if UIApplication.shared.canOpenURL(url) {
                print("可以打开URL: \(url)")
                UIApplication.shared.open(url, options: [:]) { success in
                    print("直接打开URL结果: \(success ? "成功" : "失败")")

                    // 如果打开失败，尝试使用备用方法
                    if !success {
                        self.tryFallbackURLs()
                    }
                }
            } else {
                print("无法打开URL: \(url)，应用可能未注册该URL Scheme")
                // 尝试使用备用方法
                self.tryFallbackURLs()
            }
        }
        #endif
    }

    // 尝试使用备用URL
    private func tryFallbackURLs() {
        #if os(iOS)
        DispatchQueue.main.async {
            print("尝试使用备用URL")

            // 1. 尝试使用最简单的URL格式
            let simpleURLString = "jzjjwidget://"
            if let simpleURL = URL(string: simpleURLString) {
                print("尝试打开最简单URL: \(simpleURL)")
                if UIApplication.shared.canOpenURL(simpleURL) {
                    UIApplication.shared.open(simpleURL, options: [:]) { success in
                        print("打开最简单URL结果: \(success ? "成功" : "失败")")
                    }
                    return
                }
            }

            // 2. 尝试使用其他常见的URL格式
            let alternativeURLs = [
                "jzjjwidget-app://",
                "jzjjwidgetapp://",
                "jzjj://",
                "jzjjwidget://widget"
            ]

            for urlString in alternativeURLs {
                if let url = URL(string: urlString) {
                    print("尝试打开备用URL: \(url)")
                    if UIApplication.shared.canOpenURL(url) {
                        UIApplication.shared.open(url, options: [:]) { success in
                            print("打开备用URL结果: \(success ? "成功" : "失败")")
                        }
                        return
                    }
                }
            }

            print("所有备用URL都无法打开")
        }
        #endif
    }

    // MARK: - 图标背景

    @ViewBuilder
    private func iconBackground(for item: AppLauncherItem) -> some View {
        switch data.iconStyle {
        case .standard:
            RoundedRectangle(cornerRadius: 4)
                .fill(item.color)
        case .rounded:
            RoundedRectangle(cornerRadius: 12)
                .fill(item.color)
        case .circular:
            Circle()
                .fill(item.color)
        case .squircle:
            RoundedRectangle(cornerRadius: 16)
                .fill(item.color)
        case .custom:
            RoundedRectangle(cornerRadius: 8)
                .fill(
                    LinearGradient(
                        colors: [item.color, item.color.opacity(0.7)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .shadow(color: item.color.opacity(0.3), radius: 2, x: 0, y: 2)
        }
    }

    // MARK: - 辅助计算属性

    // 根据小组件尺寸获取可见项目
    private var visibleItems: [AppLauncherItem] {
        let maxItems: Int

        switch family {
        case .systemSmall:
            maxItems = data.layoutType == .list ? 3 : 4
        case .systemMedium:
            maxItems = data.layoutType == .list ? 4 : 8
        case .systemLarge:
            maxItems = data.layoutType == .list ? 8 : 12
        default:
            maxItems = 4
        }

        return Array(data.items.prefix(maxItems))
    }

    // 根据小组件尺寸获取列数
    private var columnsForFamily: Int {
        switch family {
        case .systemSmall:
            return 2
        case .systemMedium:
            return 4
        case .systemLarge:
            return 4
        default:
            return 2
        }
    }

    // 图标尺寸
    private var iconSize: CGFloat {
        switch family {
        case .systemSmall:
            return data.layoutType == .grid ? 40 : 30
        case .systemMedium:
            return data.layoutType == .grid ? 45 : 35
        case .systemLarge:
            return data.layoutType == .grid ? 50 : 40
        default:
            return 40
        }
    }

    // 小组件宽度
    private var widgetWidth: CGFloat {
        switch family {
        case .systemSmall:
            return WidgetPreviewSizeHelper.widgetSize(for: .systemSmall).width
        case .systemMedium:
            return WidgetPreviewSizeHelper.widgetSize(for: .systemMedium).width
        case .systemLarge:
            return WidgetPreviewSizeHelper.widgetSize(for: .systemLarge).width
        default:
            return 150
        }
    }

    // 小组件高度
    private var widgetHeight: CGFloat {
        switch family {
        case .systemSmall:
            return WidgetPreviewSizeHelper.widgetSize(for: .systemSmall).height
        case .systemMedium:
            return WidgetPreviewSizeHelper.widgetSize(for: .systemMedium).height
        case .systemLarge:
            return WidgetPreviewSizeHelper.widgetSize(for: .systemLarge).height
        default:
            return 150
        }
    }
}
