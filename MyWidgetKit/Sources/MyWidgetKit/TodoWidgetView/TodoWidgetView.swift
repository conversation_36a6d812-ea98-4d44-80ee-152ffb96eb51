import SwiftUI
import WidgetKit

/// 任务清单小组件视图
@available(iOS 16.0, *)
public struct TodoWidgetView: View {
    public let data: TodoWidgetData
    public let family: WidgetFamily

    public init(data: TodoWidgetData, family: WidgetFamily) {
        self.data = data
        self.family = family
    }

    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 根据不同尺寸显示不同布局
                switch family {
                case .systemSmall:
                    smallWidgetLayout
                case .systemMedium:
                    mediumWidgetLayout
                case .systemLarge:
                    largeWidgetLayout
                default:
                    // 默认使用中尺寸布局
                    mediumWidgetLayout
                }
            }
            .adaptiveBackground {
                // 背景
                switch data.background {
                case let .color(widgetColor):
                    widgetColor.toColor()
                case let .imageData(data):
                    Image(uiImage: UIImage(data: data) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageFile(path):
                    Image(uiImage: UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageURL(url):
                    if #available(iOS 15.0, *) {
                        AsyncImage(url: URL(string: url)!) { image in
                            image.resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .clipped()
                        } placeholder: {
                            Color.gray
                        }
                    } else {
                        Color.gray
                    }
                case let .packageImage(imageName):
                    // 使用包内图片资源
                    Image(imageName, bundle: .module)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                }
            }
        }
    }

    // 小尺寸布局
    private var smallWidgetLayout: some View {
        VStack(spacing: 6) {
            // 简化的头部
            smallHeaderView

            // 任务列表
            let tasks = data.filteredTasks()
            if !tasks.isEmpty {
                smallTaskListView(tasks: tasks)
            } else {
                smallEmptyStateView
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 10)
        // 添加轻微的阴影效果，增强视觉层次感
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
        // 添加圆角效果，使小组件更符合iOS设计语言
        .clipShape(RoundedRectangle(cornerRadius: 13, style: .continuous))
    }

    // 中尺寸布局
    private var mediumWidgetLayout: some View {
        VStack(spacing: 0) {
            headerView

            let tasks = data.filteredTasks()
            if !tasks.isEmpty {
                taskListView(tasks: tasks)
            } else {
                emptyStateView
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        // 添加轻微的阴影效果
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
        // 添加圆角效果
        .clipShape(RoundedRectangle(cornerRadius: 13, style: .continuous))
    }

    // 大尺寸布局
    private var largeWidgetLayout: some View {
        VStack(spacing: 0) {
            headerView

            let tasks = data.filteredTasks()
            if !tasks.isEmpty {
                largeTaskListView(tasks: tasks)
            } else {
                largeEmptyStateView
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        // 添加轻微的阴影效果
        .shadow(color: Color.black.opacity(0.05), radius: 1, x: 0, y: 1)
        // 添加圆角效果
        .clipShape(RoundedRectangle(cornerRadius: 13, style: .continuous))
        // 添加轻微的渐变背景，增强视觉层次感
        .background(
            RoundedRectangle(cornerRadius: 13, style: .continuous)
                .fill(
                    LinearGradient(
                        gradient: Gradient(colors: [
                            data.fontColor.opacity(0.03),
                            data.fontColor.opacity(0.01)
                        ]),
                        startPoint: .top,
                        endPoint: .bottom
                    )
                )
        )
    }

    // 头部视图
    private var headerView: some View {
        HStack {
            VStack(alignment: .leading, spacing: 2) {
                Text(data.category?.name ?? "所有任务")
                    .font(.custom(data.fontName, size: data.fontSize + 2, relativeTo: .headline))
                    .fontWeight(.semibold)
                    .foregroundColor(data.fontColor)

                if let tasks = data.tasks {
                    let completed = tasks.filter { $0.isCompleted }.count
                    Text("\(completed)/\(tasks.count) 已完成")
                        .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                        .foregroundColor(data.fontColor.opacity(0.7))
                }
            }

            Spacer()

            if let category = data.category {
                Image(systemName: category.icon)
                    .font(.system(size: family == .systemSmall ? 16 : 18))
                    .foregroundColor(category.color)
            }
        }
        .padding(.bottom, 8)
    }

    // 任务列表视图
    private func taskListView(tasks: [Task]) -> some View {
        VStack(spacing: 0) {
            ForEach(tasks) { task in
                taskRow(task: task)

                if task.id != tasks.last?.id {
                    Divider()
                        .background(data.fontColor.opacity(0.2))
                        .padding(.vertical, 4)
                }
            }

            if family != .systemSmall && tasks.count < (data.tasks?.count ?? 0) {
                HStack {
                    Spacer()
                    Text("查看更多...")
                        .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                        .foregroundColor(data.fontColor.opacity(0.6))
                        .padding(.top, 8)
                }
            }
        }
    }

    // 单个任务行
    private func taskRow(task: Task) -> some View {
        HStack(spacing: 12) {
            // 完成状态指示器
            Circle()
                .strokeBorder(task.priority.color, lineWidth: 1.5)
                .background(
                    Circle()
                        .fill(task.isCompleted ? task.priority.color.opacity(0.3) : Color.clear)
                )
                .frame(width: 16, height: 16)
                .overlay(
                    Image(systemName: "checkmark")
                        .font(.system(size: 8, weight: .bold))
                        .foregroundColor(task.priority.color)
                        .opacity(task.isCompleted ? 1 : 0)
                )
                // 添加轻微的阴影效果，增强视觉层次感
                .shadow(color: task.priority.color.opacity(0.2), radius: 1, x: 0, y: 1)

            // 任务标题
            Text(task.title)
                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .body))
                .fontWeight(task.isCompleted ? .regular : .medium)
                .foregroundColor(task.isCompleted ? data.fontColor.opacity(0.6) : data.fontColor)
                .strikethrough(task.isCompleted)
                .lineLimit(1)

            Spacer()

            // 优先级指示器（仅在中尺寸显示）
            if family == .systemMedium {
                Image(systemName: task.priority.icon)
                    .font(.system(size: 12))
                    .foregroundColor(task.priority.color)
                    .opacity(0.8)
                    .padding(.trailing, 4)
            }

            // 截止日期
            if let dueDate = task.dueDate {
                HStack(spacing: 4) {
                    Image(systemName: "calendar")
                        .font(.system(size: data.fontSize - 4))

                    if family != .systemSmall {
                        Text(formatDate(dueDate))
                            .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                    }
                }
                .foregroundColor(task.isOverdue ? .red : data.fontColor.opacity(0.7))
            }
        }
        .padding(.vertical, 6)
        .contentShape(Rectangle()) // 确保整行可点击
        // 添加轻微的背景效果，增强视觉层次感
        .background(
            RoundedRectangle(cornerRadius: 6)
                .fill(task.isCompleted ?
                      data.fontColor.opacity(0.03) :
                      task.priority.color.opacity(0.05))
                .opacity(task.isCompleted ? 0.5 : 1)
        )
        .padding(.vertical, 2)
    }

    // 空状态视图 - 中尺寸
    private var emptyStateView: some View {
        VStack(spacing: 8) {
            Image(systemName: "checkmark.circle")
                .font(.system(size: 24))
                .foregroundColor(data.fontColor.opacity(0.5))

            Text("暂无任务")
                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .body))
                .foregroundColor(data.fontColor.opacity(0.7))

            Text("点击添加新任务")
                .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                .foregroundColor(data.fontColor.opacity(0.5))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // 空状态视图 - 小尺寸
    private var smallEmptyStateView: some View {
        VStack(spacing: 4) {
            Image(systemName: "checkmark.circle")
                .font(.system(size: 18))
                .foregroundColor(data.fontColor.opacity(0.5))

            Text("暂无任务")
                .font(.custom(data.fontName, size: data.fontSize - 1, relativeTo: .subheadline))
                .foregroundColor(data.fontColor.opacity(0.7))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // 空状态视图 - 大尺寸
    private var largeEmptyStateView: some View {
        VStack(spacing: 12) {
            Image(systemName: "checkmark.circle")
                .font(.system(size: 32))
                .foregroundColor(data.fontColor.opacity(0.5))

            Text("暂无任务")
                .font(.custom(data.fontName, size: data.fontSize + 2, relativeTo: .title3))
                .foregroundColor(data.fontColor.opacity(0.7))

            Text("点击添加新任务")
                .font(.custom(data.fontName, size: data.fontSize, relativeTo: .body))
                .foregroundColor(data.fontColor.opacity(0.5))

            Text("或在应用中创建新的任务分类")
                .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                .foregroundColor(data.fontColor.opacity(0.5))
                .padding(.top, 4)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    // 小尺寸头部视图
    private var smallHeaderView: some View {
        HStack {
            // 分类名称和完成进度
            VStack(alignment: .leading, spacing: 0) {
                Text(data.category?.name ?? "所有任务")
                    .font(.custom(data.fontName, size: data.fontSize - 1, relativeTo: .subheadline))
                    .fontWeight(.medium)
                    .foregroundColor(data.fontColor)
                    .lineLimit(1)

                if let tasks = data.tasks, !tasks.isEmpty {
                    let completed = tasks.filter { $0.isCompleted }.count
                    Text("\(completed)/\(tasks.count)")
                        .font(.custom(data.fontName, size: data.fontSize - 3, relativeTo: .caption2))
                        .foregroundColor(data.fontColor.opacity(0.7))
                }
            }

            Spacer()

            // 分类图标
            if let category = data.category {
                Image(systemName: category.icon)
                    .font(.system(size: 14))
                    .foregroundColor(category.color)
            }
        }
        .padding(.bottom, 4)
    }

    // 小尺寸任务列表
    private func smallTaskListView(tasks: [Task]) -> some View {
        // 只显示最多3个任务
        let displayTasks = Array(tasks.prefix(3))

        return VStack(spacing: 2) {
            ForEach(displayTasks) { task in
                smallTaskRow(task: task)

                if task.id != displayTasks.last?.id {
                    Divider()
                        .background(data.fontColor.opacity(0.1))
                        .padding(.vertical, 2)
                }
            }

            // 如果有更多任务，显示提示
            if tasks.count > 3 {
                Text("+\(tasks.count - 3) 更多")
                    .font(.custom(data.fontName, size: data.fontSize - 3, relativeTo: .caption2))
                    .foregroundColor(data.fontColor.opacity(0.6))
                    .frame(maxWidth: .infinity, alignment: .trailing)
                    .padding(.top, 2)
            }
        }
    }

    // 小尺寸任务行
    private func smallTaskRow(task: Task) -> some View {
        HStack(spacing: 8) {
            // 简化的完成状态指示器
            Circle()
                .strokeBorder(task.priority.color, lineWidth: 1)
                .background(
                    Circle()
                        .fill(task.isCompleted ? task.priority.color.opacity(0.2) : Color.clear)
                )
                .frame(width: 12, height: 12)
                .overlay(
                    Image(systemName: "checkmark")
                        .font(.system(size: 6, weight: .bold))
                        .foregroundColor(task.priority.color)
                        .opacity(task.isCompleted ? 1 : 0)
                )
                // 添加微小的阴影效果
                .shadow(color: task.priority.color.opacity(0.15), radius: 0.5, x: 0, y: 0.5)

            // 任务标题
            Text(task.title)
                .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                .fontWeight(task.isCompleted ? .regular : .medium)
                .foregroundColor(task.isCompleted ? data.fontColor.opacity(0.6) : data.fontColor)
                .strikethrough(task.isCompleted)
                .lineLimit(1)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 优先级指示点（仅显示颜色点，不显示图标）
            Circle()
                .fill(task.priority.color.opacity(task.isCompleted ? 0.3 : 0.7))
                .frame(width: 6, height: 6)
        }
        .padding(.vertical, 3)
        .padding(.horizontal, 2)
        // 添加轻微的背景效果
        .background(
            RoundedRectangle(cornerRadius: 4)
                .fill(task.isCompleted ?
                      data.fontColor.opacity(0.02) :
                      task.priority.color.opacity(0.03))
        )
        .contentShape(Rectangle()) // 确保整行可点击
    }

    // 大尺寸任务列表
    private func largeTaskListView(tasks: [Task]) -> some View {
        VStack(spacing: 0) {
            // 分组标题
            HStack {
                Text("待办任务")
                    .font(.custom(data.fontName, size: data.fontSize, relativeTo: .headline))
                    .fontWeight(.medium)
                    .foregroundColor(data.fontColor)

                Spacer()
            }
            .padding(.top, 8)
            .padding(.bottom, 4)

            // 未完成任务
            let pendingTasks = tasks.filter { !$0.isCompleted }
            if !pendingTasks.isEmpty {
                ForEach(pendingTasks) { task in
                    largeTaskRow(task: task)

                    if task.id != pendingTasks.last?.id {
                        Divider()
                            .background(data.fontColor.opacity(0.2))
                            .padding(.vertical, 4)
                    }
                }
            } else {
                Text("没有待办任务")
                    .font(.custom(data.fontName, size: data.fontSize - 1, relativeTo: .subheadline))
                    .foregroundColor(data.fontColor.opacity(0.5))
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 8)
            }

            // 已完成任务分组
            if data.showCompleted {
                let completedTasks = tasks.filter { $0.isCompleted }
                if !completedTasks.isEmpty {
                    Divider()
                        .background(data.fontColor.opacity(0.2))
                        .padding(.vertical, 8)

                    HStack {
                        Text("已完成")
                            .font(.custom(data.fontName, size: data.fontSize, relativeTo: .headline))
                            .fontWeight(.medium)
                            .foregroundColor(data.fontColor)

                        Spacer()

                        Text("\(completedTasks.count)")
                            .font(.custom(data.fontName, size: data.fontSize - 1, relativeTo: .subheadline))
                            .foregroundColor(data.fontColor.opacity(0.7))
                    }
                    .padding(.bottom, 4)

                    ForEach(completedTasks.prefix(3)) { task in
                        largeTaskRow(task: task)

                        if task.id != completedTasks.prefix(3).last?.id {
                            Divider()
                                .background(data.fontColor.opacity(0.2))
                                .padding(.vertical, 4)
                        }
                    }

                    // 如果有更多已完成任务
                    if completedTasks.count > 3 {
                        Text("查看全部 \(completedTasks.count) 个已完成任务")
                            .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                            .foregroundColor(data.fontColor.opacity(0.6))
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.top, 8)
                    }
                }
            }
        }
    }

    // 大尺寸任务行
    private func largeTaskRow(task: Task) -> some View {
        HStack(alignment: .top, spacing: 12) {
            // 完成状态指示器
            Circle()
                .strokeBorder(task.priority.color, lineWidth: 1.5)
                .background(
                    Circle()
                        .fill(task.isCompleted ? task.priority.color.opacity(0.3) : Color.clear)
                )
                .frame(width: 18, height: 18)
                .overlay(
                    Image(systemName: "checkmark")
                        .font(.system(size: 10, weight: .bold))
                        .foregroundColor(task.priority.color)
                        .opacity(task.isCompleted ? 1 : 0)
                )
                .padding(.top, 2)
                // 添加轻微的阴影效果
                .shadow(color: task.priority.color.opacity(0.2), radius: 1, x: 0, y: 1)

            VStack(alignment: .leading, spacing: 4) {
                // 任务标题
                Text(task.title)
                    .font(.custom(data.fontName, size: data.fontSize, relativeTo: .body))
                    .fontWeight(task.isCompleted ? .regular : .medium)
                    .foregroundColor(task.isCompleted ? data.fontColor.opacity(0.6) : data.fontColor)
                    .strikethrough(task.isCompleted)
                    .lineLimit(1)

                // 任务详情
                HStack(spacing: 8) {
                    // 优先级
                    Label {
                        Text(task.priority.name)
                            .lineLimit(1)
                    } icon: {
                        Image(systemName: task.priority.icon)
                            .foregroundColor(task.priority.color)
                    }
                    .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                    .foregroundColor(task.priority.color)
                    .padding(.vertical, 2)
                    .padding(.horizontal, 4)
                    .background(
                        Capsule()
                            .fill(task.priority.color.opacity(0.1))
                    )

                    // 截止日期
                    if let dueDate = task.dueDate {
                        Label {
                            Text(formatDate(dueDate))
                                .lineLimit(1)
                        } icon: {
                            Image(systemName: "calendar")
                                .foregroundColor(task.isOverdue ? .red : data.fontColor.opacity(0.7))
                        }
                        .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                        .foregroundColor(task.isOverdue ? .red : data.fontColor.opacity(0.7))
                    }

                    // 分类
                    if let category = data.category, task.categoryId == category.id {
                        Label {
                            Text(category.name)
                                .lineLimit(1)
                        } icon: {
                            Image(systemName: category.icon)
                                .foregroundColor(category.color)
                        }
                        .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                        .foregroundColor(category.color)
                    }
                }

                // 如果有备注，显示备注
                if let notes = task.notes, !notes.isEmpty {
                    Text(notes)
                        .font(.custom(data.fontName, size: data.fontSize - 2, relativeTo: .caption))
                        .foregroundColor(data.fontColor.opacity(0.6))
                        .lineLimit(1)
                        .padding(.top, 2)
                }
            }
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 4)
        // 添加轻微的背景效果
        .background(
            RoundedRectangle(cornerRadius: 8)
                .fill(task.isCompleted ?
                      data.fontColor.opacity(0.03) :
                      task.priority.color.opacity(0.05))
                .opacity(task.isCompleted ? 0.5 : 1)
        )
        .contentShape(Rectangle()) // 确保整行可点击
        .padding(.vertical, 2)
    }

    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd"
        return formatter.string(from: date)
    }
}

// 预览
@available(iOS 16.0, *)
struct TodoWidgetView_Previews: PreviewProvider {
    static var previews: some View {
        let category = TaskCategory(name: "工作", color: .blue, icon: "briefcase")
        let tasks = Task.createSampleTasks(for: category)

        let data = TodoWidgetData(
            category: category,
            showCompleted: true,
            maxTaskCount: 5,
            priorityFilter: .all,
            background: .color(WidgetColor.fromColor(.white)),
            fontName: "SF Pro",
            fontSize: 15,
            fontColor: .black,
            tasks: tasks
        )

        Group {
            TodoWidgetView(data: data, family: .systemSmall)
                .previewContext(WidgetPreviewContext(family: .systemSmall))
                .previewDisplayName("Small")

            TodoWidgetView(data: data, family: .systemMedium)
                .previewContext(WidgetPreviewContext(family: .systemMedium))
                .previewDisplayName("Medium")

            TodoWidgetView(data: data, family: .systemLarge)
                .previewContext(WidgetPreviewContext(family: .systemLarge))
                .previewDisplayName("Large")
        }
    }
}
