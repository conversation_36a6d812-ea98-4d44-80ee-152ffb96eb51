import Foundation
import SwiftUI
import WidgetKit

/// 任务清单小组件配置
@available(iOS 16.0, *)
public struct TodoWidgetConfig: Codable, Sendable {
    public var categoryId: String?
    public var showCompleted: Bool
    public var maxTaskCount: Int
    public var priorityFilter: PriorityFilter
    public var background: WidgetBackground
    public var fontName: String
    public var fontSize: Double
    private var textColor: ColorWrapper
    public var lastUpdated: Date

    public var fontColor: Color {
        get { textColor.color }
        set { textColor = ColorWrapper(color: newValue) }
    }

    public init(
        categoryId: String? = nil,
        showCompleted: Bool = false,
        maxTaskCount: Int = 5,
        priorityFilter: PriorityFilter = .all,
        background: WidgetBackground = .color(WidgetColor.fromColor(.white)),
        fontName: String = "SF Pro",
        fontSize: Double = 15,
        fontColor: Color = .black,
        lastUpdated: Date = Date()
    ) {
        self.categoryId = categoryId
        self.showCompleted = showCompleted
        self.maxTaskCount = maxTaskCount
        self.priorityFilter = priorityFilter
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        textColor = ColorWrapper(color: fontColor)
        self.lastUpdated = lastUpdated
    }

    // 默认配置
    public static let `default` = TodoWidgetConfig()
}

/// 任务清单小组件数据模型
@available(iOS 16.0, *)
public struct TodoWidgetData: WidgetPreviewableData {
    public var category: TaskCategory?
    public var showCompleted: Bool
    public var maxTaskCount: Int
    public var priorityFilter: PriorityFilter
    public var background: WidgetBackground
    public var fontName: String
    public var fontSize: Double
    public var fontColor: Color
    public var tasks: [Task]?

    public init(
        category: TaskCategory? = nil,
        showCompleted: Bool = false,
        maxTaskCount: Int = 5,
        priorityFilter: PriorityFilter = .all,
        background: WidgetBackground = .color(WidgetColor.fromColor(.white)),
        fontName: String = "SF Pro",
        fontSize: Double = 15,
        fontColor: Color = .black,
        tasks: [Task]? = nil
    ) {
        self.category = category
        self.showCompleted = showCompleted
        self.maxTaskCount = maxTaskCount
        self.priorityFilter = priorityFilter
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontColor = fontColor
        self.tasks = tasks
    }

    // 从配置创建数据
    @MainActor public static func from(config: TodoWidgetConfig) -> TodoWidgetData {
        var category: TaskCategory? = nil
        var tasks: [Task]? = nil

        if let categoryIdString = config.categoryId, let categoryId = UUID(uuidString: categoryIdString) {
            // 从 AppGroupDataManager 直接读取分类和任务
            if let categories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories) {
                category = categories.first { $0.id == categoryId }
            }

            if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
                tasks = allTasks.filter { $0.categoryId == categoryId }
            }
        } else {
            // 读取所有任务
            tasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks)
        }

        return TodoWidgetData(
            category: category,
            showCompleted: config.showCompleted,
            maxTaskCount: config.maxTaskCount,
            priorityFilter: config.priorityFilter,
            background: config.background,
            fontName: config.fontName,
            fontSize: config.fontSize,
            fontColor: config.fontColor,
            tasks: tasks
        )
    }

    // 提供一个非主线程可以安全访问的方法
    public static func fromNonisolated(config: TodoWidgetConfig) -> TodoWidgetData {
        var category: TaskCategory? = nil
        var tasks: [Task]? = nil

        if let categoryIdString = config.categoryId, let categoryId = UUID(uuidString: categoryIdString) {
            // 从 AppGroupDataManager 直接读取分类和任务
            if let categories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories) {
                category = categories.first { $0.id == categoryId }
            }

            if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
                tasks = allTasks.filter { $0.categoryId == categoryId }
            }
        } else {
            // 读取所有任务
            tasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks)
        }

        return TodoWidgetData(
            category: category,
            showCompleted: config.showCompleted,
            maxTaskCount: config.maxTaskCount,
            priorityFilter: config.priorityFilter,
            background: config.background,
            fontName: config.fontName,
            fontSize: config.fontSize,
            fontColor: config.fontColor,
            tasks: tasks
        )
    }

    // 过滤任务
    public func filteredTasks() -> [Task] {
        guard var taskList = tasks else {
            // 如果没有任务，创建一个示例任务以避免显示"暂无任务"
            if let categories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories),
               let firstCategory = categories.first {
                print("没有找到任务，创建示例任务")
                return Task.createSampleTasks(for: firstCategory)
            }
            return []
        }

        // 如果任务列表为空，直接返回，不进行筛选
        if taskList.isEmpty {
            print("任务列表为空")
            return []
        }

        print("过滤前的任务数量: \(taskList.count)")

        // 按完成状态筛选
        if !showCompleted {
            taskList = taskList.filter { !$0.isCompleted }
            print("按完成状态筛选后的任务数量: \(taskList.count)")
        }

        // 按优先级筛选
        let taskListBeforePriorityFilter = taskList
        switch priorityFilter {
        case .all:
            break // 不做筛选
        case .high:
            taskList = taskList.filter { $0.priority == .high }
            print("按高优先级筛选后的任务数量: \(taskList.count)")
        case .mediumAndHigh:
            taskList = taskList.filter { $0.priority == .high || $0.priority == .medium }
            print("按中高优先级筛选后的任务数量: \(taskList.count)")
        }

        // 如果筛选后没有任务，则忽略优先级筛选，使用所有任务
        if taskList.isEmpty && !taskListBeforePriorityFilter.isEmpty {
            print("优先级筛选后没有任务，忽略优先级筛选，使用所有任务")
            taskList = taskListBeforePriorityFilter
        }

        // 排序: 未完成 > 优先级 > 截止日期
        taskList.sort { task1, task2 in
            if task1.isCompleted != task2.isCompleted {
                return !task1.isCompleted
            }

            if task1.priority.rawValue != task2.priority.rawValue {
                return task1.priority.rawValue > task2.priority.rawValue
            }

            if let date1 = task1.dueDate, let date2 = task2.dueDate {
                return date1 < date2
            }

            return task1.createdAt < task2.createdAt
        }

        // 限制数量
        if taskList.count > maxTaskCount {
            taskList = Array(taskList.prefix(maxTaskCount))
        }
        print("taskList = \(taskList)")
        return taskList
    }
}
