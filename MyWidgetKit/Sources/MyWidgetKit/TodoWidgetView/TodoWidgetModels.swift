import Foundation
import SwiftUI
import WidgetKit

/// 任务分类模型
@available(iOS 16.0, *)
public struct TaskCategory: Identifiable, Codable, Sendable, Equatable {
    public let id: UUID
    public var name: String
    private var colorWrapper: ColorWrapper
    public var icon: String // SF Symbol名称

    public var color: Color {
        get { colorWrapper.color }
        set { colorWrapper = ColorWrapper(color: newValue) }
    }

    public init(id: UUID = UUID(), name: String, color: Color, icon: String) {
        self.id = id
        self.name = name
        colorWrapper = ColorWrapper(color: color)
        self.icon = icon
    }

    public static func == (lhs: TaskCategory, rhs: TaskCategory) -> Bool {
        return lhs.id == rhs.id
    }

    // 默认分类
    public static let defaultCategories: [TaskCategory] = [
        TaskCategory(name: "工作", color: .blue, icon: "briefcase"),
        TaskCategory(name: "个人", color: .green, icon: "person"),
        TaskCategory(name: "购物", color: .orange, icon: "cart"),
        TaskCategory(name: "学习", color: .purple, icon: "book"),
        TaskCategory(name: "健康", color: .red, icon: "heart"),
    ]
}

/// 任务优先级
@available(iOS 16.0, *)
public enum TaskPriority: Int, Codable, CaseIterable, Sendable {
    case low = 0
    case medium = 1
    case high = 2

    public var color: Color {
        switch self {
        case .low: return .green
        case .medium: return .orange
        case .high: return .red
        }
    }

    public var name: String {
        switch self {
        case .low: return "低"
        case .medium: return "中"
        case .high: return "高"
        }
    }

    public var icon: String {
        switch self {
        case .low: return "arrow.down.circle"
        case .medium: return "equal.circle"
        case .high: return "exclamationmark.circle"
        }
    }
}

/// 任务模型
@available(iOS 16.0, *)
public struct Task: Identifiable, Codable, Sendable, Equatable {
    public let id: UUID
    public var title: String
    public var isCompleted: Bool
    public var priority: TaskPriority
    public var dueDate: Date?
    public var categoryId: UUID
    public var notes: String?
    public var createdAt: Date

    public var isOverdue: Bool {
        guard let dueDate = dueDate else { return false }
        return !isCompleted && dueDate < Date()
    }

    public init(id: UUID = UUID(), title: String, isCompleted: Bool = false, priority: TaskPriority = .medium, dueDate: Date? = nil, categoryId: UUID, notes: String? = nil, createdAt: Date = Date()) {
        self.id = id
        self.title = title
        self.isCompleted = isCompleted
        self.priority = priority
        self.dueDate = dueDate
        self.categoryId = categoryId
        self.notes = notes
        self.createdAt = createdAt
    }

    public static func == (lhs: Task, rhs: Task) -> Bool {
        return lhs.id == rhs.id
    }

    // 创建示例任务
    public static func createSampleTasks(for category: TaskCategory) -> [Task] {
        switch category.name {
        case "工作":
            return [
                Task(title: "完成项目报告", priority: .high, dueDate: Date().addingTimeInterval(86400), categoryId: category.id),
                Task(title: "准备周会演示", priority: .medium, dueDate: Date().addingTimeInterval(172_800), categoryId: category.id),
                Task(title: "回复客户邮件", isCompleted: true, priority: .medium, categoryId: category.id),
                Task(title: "更新项目文档", priority: .low, categoryId: category.id),
            ]
        case "个人":
            return [
                Task(title: "打电话给妈妈", priority: .medium, categoryId: category.id),
                Task(title: "整理衣柜", priority: .low, categoryId: category.id),
                Task(title: "预约理发", priority: .low, dueDate: Date().addingTimeInterval(432_000), categoryId: category.id),
            ]
        case "购物":
            return [
                Task(title: "购买生日礼物", priority: .high, dueDate: Date().addingTimeInterval(259_200), categoryId: category.id),
                Task(title: "采购周末食材", priority: .medium, dueDate: Date().addingTimeInterval(86400), categoryId: category.id),
                Task(title: "订购新手机壳", isCompleted: true, priority: .low, categoryId: category.id),
            ]
        case "学习":
            return [
                Task(title: "完成在线课程", priority: .high, dueDate: Date().addingTimeInterval(345_600), categoryId: category.id),
                Task(title: "阅读新书籍", priority: .medium, categoryId: category.id),
                Task(title: "练习编程", priority: .medium, dueDate: Date().addingTimeInterval(86400), categoryId: category.id),
                Task(title: "复习笔记", isCompleted: true, priority: .low, categoryId: category.id),
            ]
        case "健康":
            return [
                Task(title: "30分钟跑步", priority: .high, categoryId: category.id),
                Task(title: "预约体检", priority: .medium, dueDate: Date().addingTimeInterval(604_800), categoryId: category.id),
                Task(title: "准备健康餐食", priority: .medium, categoryId: category.id),
                Task(title: "喝足8杯水", isCompleted: true, priority: .low, categoryId: category.id),
            ]
        default:
            return [
                Task(title: "示例任务1", priority: .medium, categoryId: category.id),
                Task(title: "示例任务2", priority: .low, categoryId: category.id),
            ]
        }
    }
}

/// 优先级筛选选项
@available(iOS 16.0, *)
public enum PriorityFilter: Int, Codable, CaseIterable, Sendable {
    case all = 0
    case high = 1
    case mediumAndHigh = 2

    public var displayName: String {
        switch self {
        case .all: return "全部"
        case .high: return "仅高优先级"
        case .mediumAndHigh: return "中高优先级"
        }
    }
}

/// 任务管理器
@available(iOS 16.0, *)
@MainActor public final class TaskManager: @unchecked Sendable {
    public static let shared = TaskManager()

    private init() {
        loadData()
    }

    public var categories: [TaskCategory] = []
    public var tasks: [Task] = []

    // 加载数据
    private func loadData() {
        // 从AppGroupDataManager加载数据
        if let savedCategories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories) {
            categories = savedCategories
        } else {
            categories = TaskCategory.defaultCategories
            saveCategories()
        }

        if let savedTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            tasks = savedTasks
        } else {
            // 创建示例任务
            tasks = []
            for category in categories {
                tasks.append(contentsOf: Task.createSampleTasks(for: category))
            }
            saveTasks()
        }
    }

    // 保存分类
    public func saveCategories() {
        AppGroupDataManager.shared.save(categories, for: .todoList, property: .categories)
    }

    // 保存任务
    public func saveTasks() {
        AppGroupDataManager.shared.save(tasks, for: .todoList, property: .tasks)
        WidgetCenter.shared.reloadAllTimelines()
    }

    // 获取特定分类的任务
    public func tasks(for categoryId: UUID?) -> [Task] {
        if let categoryId = categoryId {
            return tasks.filter { $0.categoryId == categoryId }
        } else {
            return tasks
        }
    }

    // 获取分类
    public func category(with id: UUID) -> TaskCategory? {
        return categories.first { $0.id == id }
    }

    // 提供一个非主线程可以安全访问的方法来获取任务
    public nonisolated func getTasksForWidget(categoryId: UUID?) -> [Task] {
        // 由于这是nonisolated方法，我们不能直接访问tasks属性
        // 所以我们从AppGroupDataManager直接读取
        if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            if let categoryId = categoryId {
                return allTasks.filter { $0.categoryId == categoryId }
            } else {
                return allTasks
            }
        }
        return []
    }

    // 提供一个非主线程可以安全访问的方法来获取分类
    public nonisolated func getCategoryForWidget(with id: UUID) -> TaskCategory? {
        // 从AppGroupDataManager直接读取
        if let categories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories) {
            return categories.first { $0.id == id }
        }
        return nil
    }
}
