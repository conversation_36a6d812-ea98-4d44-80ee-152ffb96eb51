//
//  BackgroundSelectViewExample.swift
//  MyWidgetKit
//
//  Created by yj<PERSON>g on 2025/5/20.
//

import SwiftUI

/// 背景选择视图示例
@available(iOS 16.0, *)
public struct BackgroundSelectViewExample: View {
    @State private var background: WidgetBackground = .color(WidgetColor.fromColor(.white))
    
    public init() {}
    
    public var body: some View {
        VStack(spacing: 20) {
            // 预览区域
            ZStack {
                // 背景
                background.backgroundView(width: 300, height: 200)
                
                // 内容
                VStack {
                    Text("背景预览")
                        .font(.headline)
                        .padding()
                        .background(Color.white.opacity(0.7))
                        .cornerRadius(8)
                }
            }
            .frame(width: 300, height: 200)
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.gray.opacity(0.3), lineWidth: 1)
            )
            .shadow(radius: 3)
            
            // 背景选择器
            BackgroundSelectView(
                allowColor: true,
                allowImage: true,
                allowPackageImage: true,
                packageImageNames: ["background1", "background2", "background3", "background4", "background5"],
                packageImageDisplayNames: ["渐变蓝", "渐变紫", "星空", "大理石", "抽象"],
                onSelection: { selection in
                    background = selection.toWidgetBackground()
                }
            )
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(12)
            
            // 当前选择的背景类型
            Text("当前背景类型: \(backgroundTypeDescription)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
    
    private var backgroundTypeDescription: String {
        switch background {
        case .color:
            return "纯色背景"
        case .imageData:
            return "图片背景 (数据)"
        case .imageFile:
            return "图片背景 (文件)"
        case .imageURL:
            return "图片背景 (URL)"
        case .packageImage(let name):
            return "内置背景图片: \(name)"
        }
    }
}

// 预览
@available(iOS 16.0, *)
struct BackgroundSelectViewExample_Previews: PreviewProvider {
    static var previews: some View {
        BackgroundSelectViewExample()
    }
}
