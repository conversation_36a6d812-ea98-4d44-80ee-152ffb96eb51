import SwiftUI

/// 资源图片视图 - 演示如何在 Swift Package 中使用图片资源
@available(iOS 16.0, *)
public struct ResourceImageView: View {
    /// 图片名称
    let imageName: String
    
    /// 内容模式
    let contentMode: ContentMode
    
    /// 初始化方法
    /// - Parameters:
    ///   - imageName: 图片名称
    ///   - contentMode: 内容模式
    public init(imageName: String, contentMode: ContentMode = .fill) {
        self.imageName = imageName
        self.contentMode = contentMode
    }
    
    public var body: some View {
        // 方式 1：直接使用 Image 初始化器
        // 这种方式适用于 Assets.xcassets 中的图片或根目录下的图片
        Image(imageName, bundle: .module)
            .resizable()
            .aspectRatio(contentMode: contentMode)
            .accessibilityLabel("Package resource image: \(imageName)")
    }
    
    /// 从 Images 子目录加载图片
    /// - Parameter name: 图片名称（不含扩展名）
    /// - Returns: 图片视图
    public static func fromImagesDirectory(name: String, contentMode: ContentMode = .fill) -> some View {
        // 方式 2：使用 Bundle.module.url 获取特定路径的图片
        Group {
            if let imageURL = Bundle.module.url(forResource: name, withExtension: "png", subdirectory: "Images"),
               let uiImage = UIImage(contentsOfFile: imageURL.path) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: contentMode)
                    .accessibilityLabel("Image from Images directory: \(name)")
            } else {
                // 图片加载失败时的占位视图
                Rectangle()
                    .fill(Color.gray.opacity(0.3))
                    .overlay(
                        Image(systemName: "photo")
                            .foregroundColor(.gray)
                    )
                    .accessibilityLabel("Failed to load image: \(name)")
            }
        }
    }
    
    /// 从 UIImage 创建图片视图
    /// - Parameter uiImage: UIImage 对象
    /// - Returns: 图片视图
    public static func fromUIImage(_ uiImage: UIImage, contentMode: ContentMode = .fill) -> some View {
        Image(uiImage: uiImage)
            .resizable()
            .aspectRatio(contentMode: contentMode)
            .accessibilityLabel("Image from UIImage")
    }
}

// 预览
@available(iOS 16.0, *)
struct ResourceImageView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 20) {
            // 注意：这些预览只有在实际添加了相应图片资源后才能正常显示
            ResourceImageView(imageName: "background1")
                .frame(width: 200, height: 200)
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .previewDisplayName("From Assets")
            
            ResourceImageView.fromImagesDirectory(name: "background2")
                .frame(width: 200, height: 200)
                .clipShape(RoundedRectangle(cornerRadius: 16))
                .previewDisplayName("From Images directory")
        }
        .padding()
        .previewLayout(.sizeThatFits)
    }
}
