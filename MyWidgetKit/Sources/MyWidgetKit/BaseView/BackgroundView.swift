import SwiftUI

/// 背景视图组件，支持颜色或图片背景
@available(iOS 16.0, *)
public struct BackgroundView: View {
    /// 背景配置
    let background: WidgetBackground

    /// 初始化方法
    /// - Parameter background: 背景配置
    public init(background: WidgetBackground) {
        self.background = background
    }

    public var body: some View {
        EmptyView()
            .widgetBackground {
                switch background {
                case let .color(widgetColor):
                    widgetColor.toColor()
                case let .imageData(data):
                    if let uiImage = UIImage(data: data) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } else {
                        Color.white // 图片数据无效时的默认颜色
                    }
                case let .imageFile(path):
                    if let fileURL = URL(string: path),
                       let uiImage = UIImage(contentsOfFile: fileURL.path) {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } else {
                        Color.white // 图片文件无效时的默认颜色
                    }
                case let .imageURL(urlString):
                    if let url = URL(string: urlString) {
                        if #available(iOS 15.0, *) {
                            AsyncImage(url: url) { phase in
                                switch phase {
                                case .success(let image):
                                    image
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                case .failure(_):
                                    Color.gray // 加载失败时的默认颜色
                                case .empty:
                                    Color.gray.opacity(0.3) // 加载中的默认颜色
                                @unknown default:
                                    Color.gray
                                }
                            }
                        } else {
                            // iOS 14 兼容处理
                            Color.gray
                        }
                    } else {
                        Color.white // URL无效时的默认颜色
                    }
                case let .packageImage(name):
                    // 使用包内图片资源
                    Image(name, bundle: .module)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                }
            }
    }
}

/// 预览提供者
@available(iOS 16.0, *)
struct BackgroundView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 颜色背景预览
            BackgroundView(background: .color(WidgetColor.fromColor(.blue)))
                .previewLayout(.fixed(width: 200, height: 200))
                .previewDisplayName("颜色背景")

            // 图片背景预览（使用示例图片数据）
            if let image = UIImage(systemName: "photo"),
               let data = image.pngData() {
                BackgroundView(background: .imageData(data))
                    .previewLayout(.fixed(width: 200, height: 200))
                    .previewDisplayName("图片背景")
            }
        }
    }
}
