import SwiftUI
import WidgetKit

/// 包资源背景视图 - 使用包内图片资源作为小组件背景
@available(iOS 16.0, *)
public struct PackageResourceBackgroundView: View {
    /// 背景类型
    public enum BackgroundType {
        /// 内置图片资源
        case packageImage(String)
        /// 颜色
        case color(Color)
        /// 渐变色
        case gradient([Color])
    }
    
    /// 背景类型
    let backgroundType: BackgroundType
    
    /// 初始化方法
    /// - Parameter backgroundType: 背景类型
    public init(backgroundType: BackgroundType) {
        self.backgroundType = backgroundType
    }
    
    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 根据背景类型显示不同的背景
                switch backgroundType {
                case .packageImage(let imageName):
                    // 使用包内图片资源
                    Image(imageName, bundle: .module)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                
                case .color(let color):
                    // 纯色背景
                    color
                
                case .gradient(let colors):
                    // 渐变色背景
                    LinearGradient(
                        gradient: Gradient(colors: colors),
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                }
            }
        }
        .widgetBackground {
            // 使用 widgetBackground 修饰符确保兼容 iOS 17+
            switch backgroundType {
            case .packageImage(let imageName):
                Image(imageName, bundle: .module)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
            
            case .color(let color):
                color
            
            case .gradient(let colors):
                LinearGradient(
                    gradient: Gradient(colors: colors),
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            }
        }
    }
}

// 预览
@available(iOS 16.0, *)
struct PackageResourceBackgroundView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 图片背景预览
            PackageResourceBackgroundView(backgroundType: .packageImage("background1"))
                .previewContext(WidgetPreviewContext(family: .systemSmall))
                .previewDisplayName("Package Image")
            
            // 颜色背景预览
            PackageResourceBackgroundView(backgroundType: .color(.blue))
                .previewContext(WidgetPreviewContext(family: .systemSmall))
                .previewDisplayName("Color")
            
            // 渐变背景预览
            PackageResourceBackgroundView(backgroundType: .gradient([.blue, .purple]))
                .previewContext(WidgetPreviewContext(family: .systemSmall))
                .previewDisplayName("Gradient")
        }
    }
}
