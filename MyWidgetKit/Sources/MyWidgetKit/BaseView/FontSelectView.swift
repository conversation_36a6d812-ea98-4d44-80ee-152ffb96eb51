//
//  FontSelectView.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/13.
//

import PhotosUI
import SwiftUI

/// 字体选择结果结构体
public struct FontSelection {
    public let font: Font
    public let fontSize: CGFloat
    public let fontColor: Color

    /// 字体名称
    public var fontName: String {
        // 从 font 中提取字体名称，简化处理
        // 实际应用中可能需要更复杂的逻辑
        return "SF Pro"
    }

    public init(font: Font, fontSize: CGFloat, fontColor: Color) {
        self.font = font
        self.fontSize = fontSize
        self.fontColor = fontColor
    }
}

/// 字体选项结构体
public struct FontOption {
    public let name: String
    public let type: FontType
    public let weight: Font.Weight?
    public enum FontType {
        case system
        case custom
    }
}

/// 颜色选择组件，支持自定义颜色、禁用、动画、初始选中
public struct ColorSelectView: View {
    /// 预设颜色数组
    public let colors: [Color]
    /// 颜色名称数组
    public let colorNames: [String]
    /// 初始选中索引，-1表示自定义颜色
    public let initialSelectedIndex: Int
    /// 初始自定义颜色
    public let initialCustomColor: Color
    /// 禁用的颜色索引
    public let disabledIndices: Set<Int>
    /// 是否显示自定义颜色
    public let showCustomColor: Bool
    /// 颜色选中回调 (index, color)
    public let onColorSelected: (Int, Color) -> Void

    /// 当前选中索引，-1为自定义
    @State private var selectedIndex: Int
    /// 当前自定义颜色
    @State private var customColor: Color

    public init(colors: [Color] = [.white, .black, .gray, .red, .blue, .green, .orange, .purple, .brown], colorNames: [String] = ["白色", "黑色", "灰色", "红色", "蓝色", "绿色", "橙色", "紫色", "棕色"], initialSelectedIndex: Int = 0, initialCustomColor: Color = .black, disabledIndices: [Int] = [], showCustomColor: Bool = true, onColorSelected: @escaping (Int, Color) -> Void) {
        self.colors = colors
        self.colorNames = colorNames
        self.initialSelectedIndex = initialSelectedIndex
        self.initialCustomColor = initialCustomColor
        self.disabledIndices = Set(disabledIndices)
        self.showCustomColor = showCustomColor
        self.onColorSelected = onColorSelected
        _selectedIndex = State(initialValue: initialSelectedIndex)
        _customColor = State(initialValue: initialCustomColor)
    }

    public var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                if showCustomColor {
                    // 自定义颜色放最前面
                    VStack {
                        ColorPicker("自定义颜色", selection: $customColor, supportsOpacity: false)
                            .labelsHidden()
                            .frame(width: 40, height: 24)
                            .onChange(of: customColor) { newColor in
                                selectedIndex = -1
                                onColorSelected(-1, newColor)
                            }
                        Text("自定义")
                            .font(.caption)
                            .foregroundColor(.primary)
                    }
                    .padding(4)
                    .background(selectedIndex == -1 ? Color.blue.opacity(0.1) : Color.clear)
                    .cornerRadius(8)
                    .scaleEffect(selectedIndex == -1 ? 1.08 : 1.0)
                    .animation(.easeInOut(duration: 0.18), value: selectedIndex)
                }
                ForEach(colors.indices, id: \.self) { index in
                    Button(action: {
                        selectedIndex = index
                        customColor = colors[index] // 选中预设色时同步ColorPicker
                        onColorSelected(index, colors[index])
                    }) {
                        VStack {
                            Circle()
                                .fill(colors[index])
                                .frame(width: 24, height: 24)
                                .overlay(
                                    Circle().stroke(
                                        selectedIndex == index ? Color.blue :
                                            (colors[index] == .white ? Color.gray.opacity(0.5) : Color.clear),
                                        lineWidth: 2
                                    )
                                )
                            Text(colorNames[index])
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                        .padding(4)
                        .background(selectedIndex == index ? Color.blue.opacity(0.1) : Color.clear)
                        .cornerRadius(8)
                        .scaleEffect(selectedIndex == index ? 1.08 : 1.0)
                        .animation(.easeInOut(duration: 0.18), value: selectedIndex)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .disabled(disabledIndices.contains(index))
                    .opacity(disabledIndices.contains(index) ? 0.4 : 1.0)
                }
            }
        }
        // 保证外部设置自定义颜色时，ColorPicker同步
        .onChange(of: initialCustomColor) { newColor in
            if selectedIndex == -1 && customColor != newColor {
                customColor = newColor
            }
        }
    }
}
 

public struct FontSelectView: View {
    /// 支持的字体列表
    let fontOptions: [FontOption] = [
        FontOption(name: "", type: .system, weight: .regular),
        FontOption(name: "", type: .system, weight: .bold),
        FontOption(name: "Arial", type: .custom, weight: nil),
        FontOption(name: "Helvetica", type: .custom, weight: nil),
        FontOption(name: "Times New Roman", type: .custom, weight: nil),
        FontOption(name: "Comic Sans MS", type: .custom, weight: nil),
        FontOption(name: "Arial Unicode MS", type: .custom, weight: nil),
        FontOption(name: "Courier New", type: .custom, weight: nil),
        FontOption(name: "Verdana", type: .custom, weight: nil),
        FontOption(name: "Trebuchet MS", type: .custom, weight: nil),
        FontOption(name: "Georgia", type: .custom, weight: nil),
        FontOption(name: "Impact", type: .custom, weight: nil),
        FontOption(name: "Webdings", type: .custom, weight: nil),
        FontOption(name: "Wingdings", type: .custom, weight: nil),
        FontOption(name: "Wingdings 2", type: .custom, weight: nil),
        FontOption(name: "Wingdings 3", type: .custom, weight: nil),
    ]
    /// 支持的字体名称，与 fontOptions 顺序一致
    let fontNames: [String] = [
        "系统常规",
        "系统加粗",
        "Arial",
        "Helvetica",
        "Times New Roman",
        "Comic Sans MS",
        "Arial Unicode MS",
        "Courier New",
        "Verdana",
        "Trebuchet MS",
        "Georgia",
        "Impact",
        "Webdings",
        "Wingdings",
        "Wingdings 2",
        "Wingdings 3",
    ]
    /// 支持的字号选项
    let fontSizes: [CGFloat] = [12, 14, 16, 18, 20, 24, 28, 32]
    /// 支持的颜色选项
    let fontColors: [Color] = [.white, .black, .gray, .red, .blue, .green, .orange, .purple, .brown]
    let fontColorNames: [String] = ["白色", "黑色", "灰色", "红色", "蓝色", "绿色", "橙色", "紫色", "棕色"]

    /// 当前选中的字体索引
    @State private var selectedFontIndex: Int
    /// 当前选中的字号索引
    @State private var selectedFontSizeIndex: Int
    /// 当前选中的颜色索引
    @State private var selectedFontColorIndex: Int
    /// 当前自定义颜色
    @State private var customColor: Color

    /// 初始选中项
    private let initialFontIndex: Int
    private let initialFontSizeIndex: Int
    private let initialFontColorIndex: Int
    private let initialCustomColor: Color

    /// 禁用项索引
    private let disabledFontIndices: Set<Int>
    private let disabledFontSizeIndices: Set<Int>
    private let disabledFontColorIndices: Set<Int>

    /// 控制各部分显示
    public let showFontPicker: Bool
    public let showFontSizePicker: Bool
    public let showFontColorPicker: Bool

    /// 选中回调
    public var onSelectionChanged: ((FontSelection) -> Void)? = nil

    /// 初始化方法，参数可选
    public init(
        showFontPicker: Bool = true,
        showFontSizePicker: Bool = true,
        showFontColorPicker: Bool = true,
        onSelectionChanged: ((FontSelection) -> Void)? = nil,
        // 新增初始选中项参数
        initialFontIndex: Int = 0,
        initialFontSizeIndex: Int = 3,
        initialFontColorIndex: Int = 0,
        initialCustomColor: Color = .black,
        // 新增禁用项参数
        disabledFontIndices: [Int] = [],
        disabledFontSizeIndices: [Int] = [],
        disabledFontColorIndices: [Int] = []
    ) {
        self.showFontPicker = showFontPicker
        self.showFontSizePicker = showFontSizePicker
        self.showFontColorPicker = showFontColorPicker
        self.onSelectionChanged = onSelectionChanged
        _selectedFontIndex = State(initialValue: initialFontIndex)
        _selectedFontSizeIndex = State(initialValue: initialFontSizeIndex)
        _selectedFontColorIndex = State(initialValue: initialFontColorIndex)
        _customColor = State(initialValue: initialCustomColor)
        self.initialFontIndex = initialFontIndex
        self.initialFontSizeIndex = initialFontSizeIndex
        self.initialFontColorIndex = initialFontColorIndex
        self.initialCustomColor = initialCustomColor
        self.disabledFontIndices = Set(disabledFontIndices)
        self.disabledFontSizeIndices = Set(disabledFontSizeIndices)
        self.disabledFontColorIndices = Set(disabledFontColorIndices)
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Spacer()
                // 重置按钮
                Button(action: {
                    // 恢复初始选中项
                    selectedFontIndex = initialFontIndex
                    selectedFontSizeIndex = initialFontSizeIndex
                    selectedFontColorIndex = initialFontColorIndex
                    customColor = initialCustomColor
                    triggerCallback()
                }) {
                    Text("重置")
                        .font(.subheadline)
                        .foregroundColor(.blue)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(Color.blue.opacity(0.08))
                        .cornerRadius(8)
                }
            }
            if showFontPicker {
                HStack {
                    Text("请选择字体：")
                        .font(.headline)
                    Spacer()
                    // 实时显示当前字体
                    Text(fontNames[selectedFontIndex])
                        .font(makeFont(option: fontOptions[selectedFontIndex], size: 16))
                        .foregroundColor(.secondary)
                }
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(fontOptions.indices, id: \.self) { index in
                            let font = makeFont(option: fontOptions[index], size: fontSizes[selectedFontSizeIndex])
                            let color = (selectedFontColorIndex == -1) ? customColor : (selectedFontColorIndex < fontColors.count ? fontColors[selectedFontColorIndex] : .black)
                            Button(action: {
                                selectedFontIndex = index
                                triggerCallback()
                            }) {
                                HStack {
                                    Text(fontNames[index])
                                        .font(font)
                                        .foregroundColor(color)
                                    if selectedFontIndex == index {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(.blue)
                                    }
                                }
                                .padding(6)
                                .background(selectedFontIndex == index ? Color.blue.opacity(0.1) : Color.clear)
                                .cornerRadius(8)
                                // 动画反馈
                                .scaleEffect(selectedFontIndex == index ? 1.08 : 1.0)
                                .animation(.easeInOut(duration: 0.18), value: selectedFontIndex)
                            }
                            .buttonStyle(PlainButtonStyle())
                            .disabled(disabledFontIndices.contains(index))
                            .opacity(disabledFontIndices.contains(index) ? 0.4 : 1.0)
                        }
                    }
                }
            }
            if showFontSizePicker {
                HStack {
                    Text("请选择字号：")
                        .font(.headline)
                    Spacer()
                    // 实时显示当前字号
                    Text("\(Int(fontSizes[selectedFontSizeIndex]))")
                        .font(.system(size: 16))
                        .foregroundColor(.secondary)
                }
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 16) {
                        ForEach(fontSizes.indices, id: \.self) { index in
                            Button(action: {
                                selectedFontSizeIndex = index
                                triggerCallback()
                            }) {
                                Text("\(Int(fontSizes[index]))")
                                    .font(.system(size: fontSizes[index]))
                                    .foregroundColor(selectedFontSizeIndex == index ? .blue : .primary)
                                    .padding(6)
                                    .background(selectedFontSizeIndex == index ? Color.blue.opacity(0.1) : Color.clear)
                                    .cornerRadius(8)
                                    // 动画反馈
                                    .scaleEffect(selectedFontSizeIndex == index ? 1.08 : 1.0)
                                    .animation(.easeInOut(duration: 0.18), value: selectedFontSizeIndex)
                            }
                            .buttonStyle(PlainButtonStyle())
                            .disabled(disabledFontSizeIndices.contains(index))
                            .opacity(disabledFontSizeIndices.contains(index) ? 0.4 : 1.0)
                        }
                    }
                }
            }
            if showFontColorPicker {
                HStack {
                    Text("请选择颜色：")
                        .font(.headline)
                    Spacer()
                    // 实时显示当前颜色
                    if selectedFontColorIndex == -1 {
                        HStack(spacing: 4) {
                            Text("自定义")
                                .font(.subheadline)
                                .foregroundColor(customColor)
                            Circle().fill(customColor).frame(width: 20, height: 20)
                        }
                    } else {
                        HStack(spacing: 4) {
                            Text(fontColorNames[selectedFontColorIndex])
                                .font(.subheadline)
                                .foregroundColor(fontColors[selectedFontColorIndex])
                            Circle().fill(fontColors[selectedFontColorIndex]).frame(width: 20, height: 20)
                        }
                    }
                }
                // 用 ColorSelectView 替换原有颜色选择区
                ColorSelectView(
                    colors: fontColors,
                    colorNames: fontColorNames,
                    initialSelectedIndex: initialFontColorIndex,
                    initialCustomColor: initialCustomColor,
                    disabledIndices: Array(disabledFontColorIndices),
                    showCustomColor: true
                ) { index, color in
                    selectedFontColorIndex = index
                    customColor = color
                    triggerCallback()
                }
            }
        }
        .padding()
    }

    /// 触发回调，返回当前选中项
    private func triggerCallback() {
        guard let onSelectionChanged = onSelectionChanged else { return }
        let font = makeFont(option: fontOptions[selectedFontIndex], size: fontSizes[selectedFontSizeIndex])
        let color: Color = (selectedFontColorIndex == -1) ? customColor : fontColors[selectedFontColorIndex]
        let selection = FontSelection(font: font, fontSize: fontSizes[selectedFontSizeIndex], fontColor: color)
        onSelectionChanged(selection)
    }

    // 生成 Font
    private func makeFont(option: FontOption, size: CGFloat) -> Font {
        switch option.type {
        case .system:
            return .system(size: size, weight: option.weight ?? .regular)
        case .custom:
            return .custom(option.name, size: size)
        }
    }
}

// 预览
struct FontSelectView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            FontSelectView(
                showFontPicker: true,
                showFontSizePicker: true,
                showFontColorPicker: true,
                onSelectionChanged: { selection in
                    // 预览时回调处理
                    print(selection)
                }
            )

            ColorSelectView { index, color in
                print(index, color)
            }

//            FontSelectView(
//                showFontPicker: true,
//                showFontSizePicker: false,
//                showFontColorPicker: true
//            )
//            FontSelectView(
//                showFontPicker: false,
//                showFontSizePicker: true,
//                showFontColorPicker: true
//            )
//            FontSelectView(
//                showFontPicker: true,
//                showFontSizePicker: false,
//                showFontColorPicker: true
//            )
//            FontSelectView(
//                showFontPicker: true,
//                showFontSizePicker: true,
//                showFontColorPicker: false
//            )
        }
    }
}

struct BackgroundSelectView_Previews: PreviewProvider {
    static var previews: some View {
        BackgroundSelectView(
            allowColor: true,
            allowImage: true,
            initialColor: .red, onSelection: {
                _ in
            }
        )
    }
}
