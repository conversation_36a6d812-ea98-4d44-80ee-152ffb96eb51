//
//  BackgroundSelectView.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/20.
//

import PhotosUI
import SwiftUI

/// 背景选择类型：颜色、图片或内置背景图片
@available(iOS 16.0, *)
public enum BackgroundSelection {
    case color(Color)
    case image(UIImage)
    case packageImage(String)

    /// 获取颜色值，如果是图片则返回nil
    public var colorValue: Color? {
        switch self {
        case let .color(color):
            return color
        case .image, .packageImage:
            return nil
        }
    }

    /// 获取图片值，如果是颜色或内置背景图片则返回nil
    public var imageValue: UIImage? {
        switch self {
        case let .image(image):
            return image
        case .color, .packageImage:
            return nil
        }
    }

    /// 获取内置背景图片名称，如果是颜色或普通图片则返回nil
    public var packageImageName: String? {
        switch self {
        case let .packageImage(name):
            return name
        case .color, .image:
            return nil
        }
    }

    /// 转换为WidgetBackground
    public func toWidgetBackground() -> WidgetBackground {
        switch self {
        case let .color(color):
            let widgetColor = WidgetColor.fromColor(color)
            print("转换颜色背景: \(color) -> \(widgetColor)")
            return .color(widgetColor)
        case let .image(image):
            if let data = image.pngData() {
                print("转换图片背景: \(image.size) -> 数据大小: \(data.count)")
                return .imageData(data)
            } else {
                print("图片转换失败，使用默认白色背景")
                return .color(WidgetColor.fromColor(.white))
            }
        case let .packageImage(name):
            print("转换内置背景图片: \(name)")
            return .packageImage(name)
        }
    }
}

/// 背景选择组件，支持颜色/图片/内置背景图片三选一
@available(iOS 16.0, *)
public struct BackgroundSelectView: View {
    /// 是否允许颜色选择
    public let allowColor: Bool
    /// 是否允许图片选择
    public let allowImage: Bool
    /// 是否允许内置背景图片选择
    public let allowPackageImage: Bool
    /// 预设颜色数组
    public let colors: [Color]
    /// 颜色名称数组
    public let colorNames: [String]
    /// 内置背景图片名称数组
    public let packageImageNames: [String]
    /// 内置背景图片显示名称数组
    public let packageImageDisplayNames: [String]
    /// 初始颜色
    public let initialColor: Color
    /// 初始图片
    public let initialImage: UIImage?
    /// 初始内置背景图片名称
    public let initialPackageImageName: String?
    /// 选中回调
    public let onSelection: (BackgroundSelection) -> Void

    /// 当前选中类型
    private enum SelectionType: Equatable {
        case image
        case packageImage(String)
        case customColor
        case presetColor(Int)
    }

    @State private var selection: SelectionType = .presetColor(0)
    @State private var selectedImage: UIImage?
    @State private var selectedPackageImageName: String?
    @State private var customColor: Color = .white
    @State private var showImagePicker: Bool = false
    @State private var showPackageImagePicker: Bool = false

    public init(
        allowColor: Bool = true,
        allowImage: Bool = true,
        allowPackageImage: Bool = true,
        colors: [Color] = [.white, .black, .gray, .red, .blue, .green, .orange, .purple, .brown],
        colorNames: [String] = ["白色", "黑色", "灰色", "红色", "蓝色", "绿色", "橙色", "紫色", "棕色"],
        packageImageNames: [String] = ["background1", "background2", "background3", "background4", "background5"],
        packageImageDisplayNames: [String] = ["背景1", "背景2", "背景3", "背景4", "背景5"],
        initialColor: Color = .white,
        initialImage: UIImage? = nil,
        initialPackageImageName: String? = nil,
        onSelection: @escaping (BackgroundSelection) -> Void
    ) {
        self.allowColor = allowColor
        self.allowImage = allowImage
        self.allowPackageImage = allowPackageImage
        self.colors = colors
        self.colorNames = colorNames
        self.packageImageNames = packageImageNames
        self.packageImageDisplayNames = packageImageDisplayNames
        self.initialColor = initialColor
        self.initialImage = initialImage
        self.initialPackageImageName = initialPackageImageName
        self.onSelection = onSelection

        // 初始化默认值
        self.customColor = initialColor

        // 默认优先级：内置背景图片 > 图片 > 自定义色 > 预设色
        if let name = initialPackageImageName, allowPackageImage {
            self.selection = .packageImage(name)
            self.selectedPackageImageName = name
        } else if let img = initialImage, allowImage {
            self.selection = .image
            self.selectedImage = img
        } else if allowColor && !colors.contains(initialColor) {
            self.selection = .customColor
        } else if allowColor {
            if let idx = colors.firstIndex(where: { $0.description == initialColor.description }) {
                self.selection = .presetColor(idx)
            } else {
                self.selection = .presetColor(0)
            }
        } else {
            // 默认选择
            self.selection = .presetColor(0)
            self.customColor = colors.first ?? .white
        }
    }

    // 计算属性，用于判断是否选择了内置背景图片
    private var isPackageImageSelected: Bool {
        if case .packageImage = selection {
            return true
        }
        return false
    }

    public var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 背景类型选择
            HStack {
                Text("背景类型：")
                    .font(.headline)
                Spacer()
            }

            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    // 1. 图片选择按钮
                    if allowImage {
                        Button(action: {
                            showImagePicker = true
                        }) {
                            VStack {
                                if let img = selectedImage, selection == .image {
                                    Image(uiImage: img)
                                        .resizable()
                                        .scaledToFill()
                                        .frame(width: 40, height: 40)
                                        .clipShape(RoundedRectangle(cornerRadius: 8))
                                        .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color.blue, lineWidth: 2))
                                } else {
                                    Image(systemName: "photo.on.rectangle")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 32, height: 32)
                                        .foregroundColor(.blue)
                                    Text("图片")
                                        .font(.caption)
                                        .foregroundColor(.primary)
                                }
                            }
                            .padding(8)
                            .background(selection == .image ? Color.blue.opacity(0.1) : Color.clear)
                            .cornerRadius(8)
                            .scaleEffect(selection == .image ? 1.08 : 1.0)
                            .animation(.easeInOut(duration: 0.18), value: selection)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .sheet(isPresented: $showImagePicker) {
                            ImagePicker(selectedImage: $selectedImage, onImageSelected: { image in
                                selection = .image
                                onSelection(.image(image))
                            })
                        }
                    }

                    // 2. 内置背景图片选择按钮
                    if allowPackageImage {
                        Button(action: {
                            showPackageImagePicker = true
                        }) {
                            VStack {
                                if let name = selectedPackageImageName, case .packageImage = selection {
                                    Image(name, bundle: .module)
                                        .resizable()
                                        .scaledToFill()
                                        .frame(width: 40, height: 40)
                                        .clipShape(RoundedRectangle(cornerRadius: 8))
                                        .overlay(RoundedRectangle(cornerRadius: 8).stroke(Color.blue, lineWidth: 2))
                                } else {
                                    Image(systemName: "photo.stack")
                                        .resizable()
                                        .scaledToFit()
                                        .frame(width: 32, height: 32)
                                        .foregroundColor(.blue)
                                    Text("内置背景")
                                        .font(.caption)
                                        .foregroundColor(.primary)
                                }
                            }
                            .padding(8)
                            .background(isPackageImageSelected ? Color.blue.opacity(0.1) : Color.clear)
                            .cornerRadius(8)
                            .scaleEffect(isPackageImageSelected ? 1.08 : 1.0)
                            .animation(.easeInOut(duration: 0.18), value: selection)
                        }
                        .buttonStyle(PlainButtonStyle())
                        .sheet(isPresented: $showPackageImagePicker) {
                            PackageImagePicker(
                                imageNames: packageImageNames,
                                displayNames: packageImageDisplayNames,
                                selectedImageName: $selectedPackageImageName,
                                onImageSelected: { name in
                                    selection = .packageImage(name)
                                    print("BackgroundSelectView - 选择了内置背景图片: \(name)")
                                    let bgSelection = BackgroundSelection.packageImage(name)
                                    print("BackgroundSelectView - 创建了BackgroundSelection: \(bgSelection)")
                                    onSelection(bgSelection)
                                }
                            )
                        }
                    }

                    // 3. 自定义颜色
                    if allowColor {
                        VStack {
                            ColorPicker("自定义颜色", selection: $customColor, supportsOpacity: false)
                                .labelsHidden()
                                .frame(width: 40, height: 40)
                                .onChange(of: customColor) { newColor in
                                    selection = .customColor
                                    onSelection(.color(newColor))
                                }
                            Text("自定义")
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                        .padding(8)
                        .background(selection == .customColor ? Color.blue.opacity(0.1) : Color.clear)
                        .cornerRadius(8)
                        .scaleEffect(selection == .customColor ? 1.08 : 1.0)
                        .animation(.easeInOut(duration: 0.18), value: selection)
                    }

                    // 4. 预设颜色
                    if allowColor {
                        ForEach(colors.indices, id: \.self) { idx in
                            Button(action: {
                                selection = .presetColor(idx)
                                onSelection(.color(colors[idx]))
                                selectedImage = nil
                                selectedPackageImageName = nil
                                customColor = colors[idx] // 选中预设色时同步ColorPicker
                            }) {
                                VStack {
                                    Circle()
                                        .fill(colors[idx])
                                        .frame(width: 28, height: 28)
                                        .overlay(
                                            Circle().stroke(selection == .presetColor(idx) ? Color.blue : Color.clear, lineWidth: 2)
                                        )
                                    Text(colorNames[idx])
                                        .font(.caption)
                                        .foregroundColor(.primary)
                                }
                                .padding(8)
                                .background(selection == .presetColor(idx) ? Color.blue.opacity(0.1) : Color.clear)
                                .cornerRadius(8)
                                .scaleEffect(selection == .presetColor(idx) ? 1.08 : 1.0)
                                .animation(.easeInOut(duration: 0.18), value: selection)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                }
                .padding(.vertical, 8)
            }
        }
        .onAppear {
            // 初始化选中状态
            if let img = initialImage, allowImage {
                selectedImage = img
            }
            if let name = initialPackageImageName, allowPackageImage {
                selectedPackageImageName = name
            }
        }
    }
}

// MARK: - 图片选择器

@available(iOS 16.0, *)
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    var onImageSelected: (UIImage) -> Void

    func makeUIViewController(context: Context) -> PHPickerViewController {
        var config = PHPickerConfiguration()
        config.filter = .images
        config.selectionLimit = 1
        let picker = PHPickerViewController(configuration: config)
        picker.delegate = context.coordinator
        return picker
    }

    func updateUIViewController(_ uiViewController: PHPickerViewController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, PHPickerViewControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func picker(_ picker: PHPickerViewController, didFinishPicking results: [PHPickerResult]) {
            picker.dismiss(animated: true)

            guard let provider = results.first?.itemProvider else { return }

            if provider.canLoadObject(ofClass: UIImage.self) {
                provider.loadObject(ofClass: UIImage.self) { [weak self] imageObject, _ in
                    guard let self = self else { return }

                    // 在非主线程上创建图片副本
                    if let originalImage = imageObject as? UIImage {
                        // 尝试创建图片的本地副本
                        let localImageCopy: UIImage

                        if let pngData = originalImage.pngData(), let pngImage = UIImage(data: pngData) {
                            localImageCopy = pngImage
                        } else if let jpegData = originalImage.jpegData(compressionQuality: 0.9), let jpegImage = UIImage(data: jpegData) {
                            localImageCopy = jpegImage
                        } else {
                            // 如果无法创建数据副本，使用系统API创建新的图片实例
                            if let cgImage = originalImage.cgImage {
                                localImageCopy = UIImage(cgImage: cgImage, scale: originalImage.scale, orientation: originalImage.imageOrientation)
                            } else {
                                // 最后的备选方案
                                localImageCopy = originalImage
                            }
                        }

                        // 在主线程上更新UI
                        DispatchQueue.main.async {
                            self.parent.selectedImage = localImageCopy
                            self.parent.onImageSelected(localImageCopy)
                        }
                    }
                }
            }
        }
    }
}

// MARK: - 内置背景图片选择器

@available(iOS 16.0, *)
struct PackageImagePicker: View {
    let imageNames: [String]
    let displayNames: [String]
    @Binding var selectedImageName: String?
    var onImageSelected: (String) -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            ScrollView {
                LazyVGrid(columns: [GridItem(.adaptive(minimum: 150))], spacing: 16) {
                    ForEach(imageNames.indices, id: \.self) { index in
                        let name = imageNames[index]
                        let displayName = index < displayNames.count ? displayNames[index] : name

                        Button {
                            selectedImageName = name
                            print("选择了内置背景图片: \(name)")
                            onImageSelected(name)
                            dismiss()
                        } label: {
                            VStack {
                                Image(name, bundle: .module)
                                    .resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: 150, height: 150)
                                    .cornerRadius(12)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(selectedImageName == name ? Color.blue : Color.clear, lineWidth: 3)
                                    )

                                Text(displayName)
                                    .font(.caption)
                                    .foregroundColor(.primary)
                            }
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding()
            }
            .navigationTitle("选择内置背景")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
    }
}
