//
//  ThemeManager.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/15.
//

import Combine
import SwiftUI

/// 主题管理器
@MainActor
public class ThemeManager: ObservableObject {
    // 当前主题
    @Published public var currentTheme: AppTheme

    // 用户默认设置键
    private let themeKey = "app_selected_theme"

    // 单例
    public static let shared = ThemeManager()

    // 初始化
    private init() {
        // 从用户默认设置中读取主题，如果没有则使用默认主题
        if let savedThemeName = UserDefaults.standard.string(forKey: themeKey),
           let savedTheme = AppTheme.allCases.first(where: { $0.rawValue == savedThemeName })
        {
            currentTheme = savedTheme
        } else {
            // 使用暖橙作为默认主题
            currentTheme = .warmOrange
        }
    }

    // 切换主题
    public func switchTheme(to theme: AppTheme) {
        currentTheme = theme
        // 保存到用户默认设置
        UserDefaults.standard.set(theme.rawValue, forKey: themeKey)
        // 发送主题变化通知
        NotificationCenter.default.post(name: NSNotification.Name("ThemeDidChangeNotification"), object: nil)
    }

    // 根据系统外观自动切换主题
    public func applySystemTheme() {
        if UITraitCollection.current.userInterfaceStyle == .dark {
            switchTheme(to: .iosDark)
        } else {
            switchTheme(to: .iosLight)
        }
    }

    // 获取所有可用主题
    public func getAllThemes() -> [AppTheme] {
        return AppTheme.allCases
    }

    // MARK: - 便捷访问属性

    /// 当前主题的颜色
    public var colors: ThemeColors {
        return currentTheme.colors
    }

    /// 当前主题的字体
    public var fonts: ThemeFonts {
        return currentTheme.fonts
    }

    /// 颜色选项
    public var colorOptions: [Color] {
        return [.white, .black, .gray, .red, .blue, .green, .orange, .purple, .brown]
    }

    /// 颜色名称
    public var colorNames: [String] {
        return ["白色", "黑色", "灰色", "红色", "蓝色", "绿色", "橙色", "紫色", "棕色"]
    }
}
