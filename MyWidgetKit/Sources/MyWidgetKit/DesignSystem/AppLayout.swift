//
//  AppLayout.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/15.
//

import SwiftUI
import WidgetKit

/// 应用统一布局系统
public enum AppLayout {
    // 间距
    public enum Spacing {
        public static let nano: CGFloat = 2
        public static let tiny: CGFloat = 4
        public static let extraSmall: CGFloat = 6
        public static let small: CGFloat = 8
        public static let medium: CGFloat = 16
        public static let large: CGFloat = 24
        public static let xLarge: CGFloat = 32
        public static let xxLarge: CGFloat = 48
        public static let xxxLarge: CGFloat = 64
    }

    // 圆角
    public enum CornerRadius {
        public static let none: CGFloat = 0
        public static let small: CGFloat = 6
        public static let medium: CGFloat = 12
        public static let large: CGFloat = 16
        public static let xLarge: CGFloat = 24
        public static let circle: CGFloat = 9999

        // 根据容器大小自动计算圆角
        public static func adaptive(for size: CGFloat) -> CGFloat {
            return min(size * 0.1, large)
        }
    }

    // 阴影
    public enum ShadowStyle {
        // 轻微阴影
        public static let subtle: (Color, CGFloat, CGFloat, CGFloat) = (.black.opacity(0.05), 2, 0, 1)
        // 小阴影
        public static let small: (Color, CGFloat, CGFloat, CGFloat) = (.black.opacity(0.08), 4, 0, 2)
        // 中等阴影
        public static let medium: (Color, CGFloat, CGFloat, CGFloat) = (.black.opacity(0.12), 8, 0, 4)
        // 大阴影
        public static let large: (Color, CGFloat, CGFloat, CGFloat) = (.black.opacity(0.16), 16, 0, 8)
        // 强调阴影
        public static let emphasis: (Color, CGFloat, CGFloat, CGFloat) = (.black.opacity(0.2), 24, 0, 12)
        // 浮动阴影
        public static let floating: (Color, CGFloat, CGFloat, CGFloat) = (.black.opacity(0.14), 12, 0, 6)
        // 聚焦阴影
        public static func focus(color: Color) -> (Color, CGFloat, CGFloat, CGFloat) {
            return (color.opacity(0.3), 8, 0, 4)
        }
    }

    // 边框
    public enum Border {
        public static let hairline: CGFloat = 0.5
        public static let thin: CGFloat = 1
        public static let medium: CGFloat = 2
        public static let thick: CGFloat = 3
        public static let heavy: CGFloat = 4
    }

    // 容器尺寸
    public enum ContainerSize {
        public static let xSmall: CGFloat = 80
        public static let small: CGFloat = 120
        public static let medium: CGFloat = 160
        public static let large: CGFloat = 200
        public static let xLarge: CGFloat = 240
        public static let xxLarge: CGFloat = 300

        // 自适应容器尺寸
        public static func adaptive(for screenWidth: CGFloat) -> CGFloat {
            if screenWidth < 375 {
                return small
            } else if screenWidth < 428 {
                return medium
            } else {
                return large
            }
        }
    }

    // 图标尺寸
    public enum IconSize {
        public static let tiny: CGFloat = 12
        public static let small: CGFloat = 16
        public static let medium: CGFloat = 24
        public static let large: CGFloat = 32
        public static let xLarge: CGFloat = 48
        public static let xxLarge: CGFloat = 64
    }

    // 小组件尺寸 - 基于 iOS 16+ 的尺寸规范
    public enum WidgetSize {
        // 小尺寸小组件
        public static let smallWidth: CGFloat = 170
        public static let smallHeight: CGFloat = 170

        // 中尺寸小组件
        public static let mediumWidth: CGFloat = 364
        public static let mediumHeight: CGFloat = 170

        // 大尺寸小组件
        public static let largeWidth: CGFloat = 364
        public static let largeHeight: CGFloat = 382

        // 额外大尺寸小组件 (iOS 16+)
        public static let xLargeWidth: CGFloat = 364
        public static let xLargeHeight: CGFloat = 382 * 2

        // 获取指定类型小组件的尺寸
        public static func size(for family: WidgetFamily) -> CGSize {
            switch family {
            case .systemSmall:
                return CGSize(width: smallWidth, height: smallHeight)
            case .systemMedium:
                return CGSize(width: mediumWidth, height: mediumHeight)
            case .systemLarge:
                return CGSize(width: largeWidth, height: largeHeight)
            case .systemExtraLarge:
                return CGSize(width: xLargeWidth, height: xLargeHeight)
            default:
                return CGSize(width: smallWidth, height: smallHeight)
            }
        }
    }

    // 内边距
    public enum Padding {
        // 基础内边距
        public static let nano: EdgeInsets = .init(top: Spacing.nano, leading: Spacing.nano, bottom: Spacing.nano, trailing: Spacing.nano)
        public static let tiny: EdgeInsets = .init(top: Spacing.tiny, leading: Spacing.tiny, bottom: Spacing.tiny, trailing: Spacing.tiny)
        public static let extraSmall: EdgeInsets = .init(top: Spacing.extraSmall, leading: Spacing.extraSmall, bottom: Spacing.extraSmall, trailing: Spacing.extraSmall)
        public static let small: EdgeInsets = .init(top: Spacing.small, leading: Spacing.small, bottom: Spacing.small, trailing: Spacing.small)
        public static let medium: EdgeInsets = .init(top: Spacing.medium, leading: Spacing.medium, bottom: Spacing.medium, trailing: Spacing.medium)
        public static let large: EdgeInsets = .init(top: Spacing.large, leading: Spacing.large, bottom: Spacing.large, trailing: Spacing.large)
        public static let xLarge: EdgeInsets = .init(top: Spacing.xLarge, leading: Spacing.xLarge, bottom: Spacing.xLarge, trailing: Spacing.xLarge)

        // 方向性内边距
        public static let horizontalSmall: EdgeInsets = .init(top: 0, leading: Spacing.small, bottom: 0, trailing: Spacing.small)
        public static let horizontalMedium: EdgeInsets = .init(top: 0, leading: Spacing.medium, bottom: 0, trailing: Spacing.medium)
        public static let horizontalLarge: EdgeInsets = .init(top: 0, leading: Spacing.large, bottom: 0, trailing: Spacing.large)

        public static let verticalSmall: EdgeInsets = .init(top: Spacing.small, leading: 0, bottom: Spacing.small, trailing: 0)
        public static let verticalMedium: EdgeInsets = .init(top: Spacing.medium, leading: 0, bottom: Spacing.medium, trailing: 0)
        public static let verticalLarge: EdgeInsets = .init(top: Spacing.large, leading: 0, bottom: Spacing.large, trailing: 0)

        // 不对称内边距
        public static let card: EdgeInsets = .init(top: Spacing.medium, leading: Spacing.medium, bottom: Spacing.medium, trailing: Spacing.medium)
        public static let listItem: EdgeInsets = .init(top: Spacing.small, leading: Spacing.medium, bottom: Spacing.small, trailing: Spacing.medium)
        public static let button: EdgeInsets = .init(top: Spacing.small, leading: Spacing.medium, bottom: Spacing.small, trailing: Spacing.medium)
        public static let input: EdgeInsets = .init(top: Spacing.small, leading: Spacing.medium, bottom: Spacing.small, trailing: Spacing.medium)
    }

    // 动画持续时间
    public enum AnimationDuration {
        public static let quick: Double = 0.15
        public static let standard: Double = 0.25
        public static let medium: Double = 0.35
        public static let slow: Double = 0.5
    }

    // 动画曲线
    public enum AnimationCurve {
        public static let standard: Animation = .easeInOut(duration: AnimationDuration.standard)
        public static let emphasized: Animation = .spring(response: 0.3, dampingFraction: 0.7)
        public static let energetic: Animation = .spring(response: 0.25, dampingFraction: 0.5)
        public static let gentle: Animation = .easeOut(duration: AnimationDuration.medium)
        public static let quick: Animation = .easeOut(duration: AnimationDuration.quick)
    }

    // 设备适配
    public enum DeviceAdaptive {
        // 获取当前设备的安全区域
        @MainActor
        public static var safeAreaInsets: EdgeInsets {
            let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow })
            let safeAreaInsets = keyWindow?.safeAreaInsets ?? .zero
            return EdgeInsets(
                top: safeAreaInsets.top,
                leading: safeAreaInsets.left,
                bottom: safeAreaInsets.bottom,
                trailing: safeAreaInsets.right
            )
        }

        // 获取当前设备的屏幕尺寸
        @MainActor
        public static var screenSize: CGSize {
            return UIScreen.main.bounds.size
        }

        // 判断是否为小屏设备
        @MainActor
        public static var isSmallScreen: Bool {
            return UIScreen.main.bounds.width < 375
        }

        // 判断是否为大屏设备
        @MainActor
        public static var isLargeScreen: Bool {
            return UIScreen.main.bounds.width >= 428
        }
    }
}
