//
//  AppTheme.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/15.
//

import SwiftUI

/// 应用主题系统
public enum AppTheme: String, CaseIterable, Identifiable {
    case iosLight = "iOS 亮色"
    case iosDark = "iOS 暗色"
    case modernBlue = "现代蓝"
    case elegantPurple = "优雅紫"
    case warmOrange = "暖橙"
    case calmGreen = "静谧绿"
    // 新增主题
    case vibrantRed = "活力红"
    case oceanBreeze = "海洋风"
    case nightSky = "星空夜"

    public var id: String { rawValue }

    // 主题配色
    public var colors: ThemeColors {
        switch self {
        case .iosLight:
            return ThemeColors(
                primary: Color(hex: "007AFF"),
                secondary: Color(hex: "5AC8FA"),
                accent: Color(hex: "007AFF"),
                background: Color(hex: "F2F2F7"),
                surface: Color(hex: "FFFFFF"),
                surfaceVariant: Color(hex: "F9F9FB"),
                text: Color(hex: "000000"),
                subtext: Color(hex: "8E8E93"),
                border: Color(hex: "E5E5EA"),
                success: Color(hex: "34C759"),
                warning: Color(hex: "FF9500"),
                error: Color(hex: "FF3B30"),
                shadow: Color.black.opacity(0.1),
                divider: Color(hex: "E5E5EA")
            )
        case .iosDark:
            return ThemeColors(
                primary: Color(hex: "0A84FF"),
                secondary: Color(hex: "64D2FF"),
                accent: Color(hex: "0A84FF"),
                background: Color(hex: "1C1C1E"),
                surface: Color(hex: "2C2C2E"),
                surfaceVariant: Color(hex: "3A3A3C"),
                text: Color(hex: "FFFFFF"),
                subtext: Color(hex: "8E8E93"),
                border: Color(hex: "38383A"),
                success: Color(hex: "30D158"),
                warning: Color(hex: "FF9F0A"),
                error: Color(hex: "FF453A"),
                shadow: Color.black.opacity(0.3),
                divider: Color(hex: "38383A")
            )
        case .modernBlue:
            return ThemeColors(
                primary: Color(hex: "4285F4"),
                secondary: Color(hex: "5E97F6"),
                accent: Color(hex: "4285F4"),
                background: Color(hex: "F8F9FA"),
                surface: Color(hex: "FFFFFF"),
                surfaceVariant: Color(hex: "EEF1F8"),
                text: Color(hex: "202124"),
                subtext: Color(hex: "5F6368"),
                border: Color(hex: "DADCE0"),
                success: Color(hex: "0F9D58"),
                warning: Color(hex: "F4B400"),
                error: Color(hex: "DB4437"),
                shadow: Color.black.opacity(0.08),
                divider: Color(hex: "DADCE0")
            )
        case .elegantPurple:
            return ThemeColors(
                primary: Color(hex: "6200EE"),
                secondary: Color(hex: "BB86FC"),
                accent: Color(hex: "6200EE"),
                background: Color(hex: "F4F0F7"),
                surface: Color(hex: "FFFFFF"),
                surfaceVariant: Color(hex: "F9F5FC"),
                text: Color(hex: "1D1B20"),
                subtext: Color(hex: "7A757F"),
                border: Color(hex: "E6DDEF"),
                success: Color(hex: "03DAC5"),
                warning: Color(hex: "FFB74D"),
                error: Color(hex: "CF6679"),
                shadow: Color.black.opacity(0.08),
                divider: Color(hex: "E6DDEF")
            )
        case .warmOrange:
            return ThemeColors(
                primary: Color(hex: "FF5722"),
                secondary: Color(hex: "FF8A65"),
                accent: Color(hex: "FF5722"),
                background: Color(hex: "FFF8F6"),
                surface: Color(hex: "FFFFFF"),
                surfaceVariant: Color(hex: "FFF0EC"),
                text: Color(hex: "3E2723"),
                subtext: Color(hex: "8D6E63"),
                border: Color(hex: "FFCCBC"),
                success: Color(hex: "8BC34A"),
                warning: Color(hex: "FFC107"),
                error: Color(hex: "F44336"),
                shadow: Color.black.opacity(0.08),
                divider: Color(hex: "FFCCBC")
            )
        case .calmGreen:
            return ThemeColors(
                primary: Color(hex: "00897B"),
                secondary: Color(hex: "4DB6AC"),
                accent: Color(hex: "00897B"),
                background: Color(hex: "F1F8F5"),
                surface: Color(hex: "FFFFFF"),
                surfaceVariant: Color(hex: "E8F5F2"),
                text: Color(hex: "004D40"),
                subtext: Color(hex: "00796B"),
                border: Color(hex: "B2DFDB"),
                success: Color(hex: "4CAF50"),
                warning: Color(hex: "FFEB3B"),
                error: Color(hex: "F44336"),
                shadow: Color.black.opacity(0.08),
                divider: Color(hex: "E0F2F1")
            )
        // 新增主题配色
        case .vibrantRed:
            return ThemeColors(
                primary: Color(hex: "E53935"),
                secondary: Color(hex: "FF5252"),
                accent: Color(hex: "FF1744"),
                background: Color(hex: "FFEBEE"),
                surface: Color(hex: "FFFFFF"),
                surfaceVariant: Color(hex: "FFF5F5"),
                text: Color(hex: "212121"),
                subtext: Color(hex: "757575"),
                border: Color(hex: "FFCDD2"),
                success: Color(hex: "4CAF50"),
                warning: Color(hex: "FFC107"),
                error: Color(hex: "D50000"),
                shadow: Color.black.opacity(0.1),
                divider: Color(hex: "FFCDD2")
            )
        case .oceanBreeze:
            return ThemeColors(
                primary: Color(hex: "039BE5"),
                secondary: Color(hex: "4FC3F7"),
                accent: Color(hex: "00B0FF"),
                background: Color(hex: "E1F5FE"),
                surface: Color(hex: "FFFFFF"),
                surfaceVariant: Color(hex: "F3F9FD"),
                text: Color(hex: "01579B"),
                subtext: Color(hex: "0277BD"),
                border: Color(hex: "B3E5FC"),
                success: Color(hex: "26A69A"),
                warning: Color(hex: "FFD54F"),
                error: Color(hex: "EF5350"),
                shadow: Color.black.opacity(0.08),
                divider: Color(hex: "B3E5FC")
            )
        case .nightSky:
            return ThemeColors(
                primary: Color(hex: "3F51B5"),
                secondary: Color(hex: "7986CB"),
                accent: Color(hex: "536DFE"),
                background: Color(hex: "121212"),
                surface: Color(hex: "1E1E1E"),
                surfaceVariant: Color(hex: "2D2D2D"),
                text: Color(hex: "E0E0E0"),
                subtext: Color(hex: "9E9E9E"),
                border: Color(hex: "424242"),
                success: Color(hex: "66BB6A"),
                warning: Color(hex: "FFCA28"),
                error: Color(hex: "EF5350"),
                shadow: Color.black.opacity(0.3),
                divider: Color(hex: "424242")
            )
        }
    }

    // 主题字体
    public var fonts: ThemeFonts {
        switch self {
        case .iosLight, .iosDark:
            return ThemeFonts(
                titleLarge: Font.system(size: 28, weight: .bold, design: .default),
                titleMedium: Font.system(size: 22, weight: .semibold, design: .default),
                titleSmall: Font.system(size: 20, weight: .semibold, design: .default),
                headlineLarge: Font.system(size: 18, weight: .semibold, design: .default),
                headlineMedium: Font.system(size: 16, weight: .semibold, design: .default),
                headlineSmall: Font.system(size: 14, weight: .semibold, design: .default),
                bodyLarge: Font.system(size: 17, weight: .regular, design: .default),
                bodyMedium: Font.system(size: 15, weight: .regular, design: .default),
                bodySmall: Font.system(size: 13, weight: .regular, design: .default),
                captionLarge: Font.system(size: 12, weight: .regular, design: .default),
                captionMedium: Font.system(size: 11, weight: .regular, design: .default),
                captionSmall: Font.system(size: 10, weight: .regular, design: .default)
            )
        case .modernBlue, .elegantPurple, .oceanBreeze:
            return ThemeFonts(
                titleLarge: Font.system(size: 28, weight: .bold, design: .rounded),
                titleMedium: Font.system(size: 22, weight: .semibold, design: .rounded),
                titleSmall: Font.system(size: 20, weight: .semibold, design: .rounded),
                headlineLarge: Font.system(size: 18, weight: .semibold, design: .rounded),
                headlineMedium: Font.system(size: 16, weight: .semibold, design: .rounded),
                headlineSmall: Font.system(size: 14, weight: .semibold, design: .rounded),
                bodyLarge: Font.system(size: 17, weight: .regular, design: .rounded),
                bodyMedium: Font.system(size: 15, weight: .regular, design: .rounded),
                bodySmall: Font.system(size: 13, weight: .regular, design: .rounded),
                captionLarge: Font.system(size: 12, weight: .regular, design: .rounded),
                captionMedium: Font.system(size: 11, weight: .regular, design: .rounded),
                captionSmall: Font.system(size: 10, weight: .regular, design: .rounded)
            )
        case .warmOrange, .calmGreen:
            return ThemeFonts(
                titleLarge: Font.system(size: 28, weight: .bold, design: .serif),
                titleMedium: Font.system(size: 22, weight: .semibold, design: .serif),
                titleSmall: Font.system(size: 20, weight: .semibold, design: .serif),
                headlineLarge: Font.system(size: 18, weight: .semibold, design: .serif),
                headlineMedium: Font.system(size: 16, weight: .semibold, design: .serif),
                headlineSmall: Font.system(size: 14, weight: .semibold, design: .serif),
                bodyLarge: Font.system(size: 17, weight: .regular, design: .serif),
                bodyMedium: Font.system(size: 15, weight: .regular, design: .serif),
                bodySmall: Font.system(size: 13, weight: .regular, design: .serif),
                captionLarge: Font.system(size: 12, weight: .regular, design: .serif),
                captionMedium: Font.system(size: 11, weight: .regular, design: .serif),
                captionSmall: Font.system(size: 10, weight: .regular, design: .serif)
            )
        case .vibrantRed:
            return ThemeFonts(
                titleLarge: Font.system(size: 28, weight: .black, design: .default),
                titleMedium: Font.system(size: 22, weight: .bold, design: .default),
                titleSmall: Font.system(size: 20, weight: .bold, design: .default),
                headlineLarge: Font.system(size: 18, weight: .bold, design: .default),
                headlineMedium: Font.system(size: 16, weight: .semibold, design: .default),
                headlineSmall: Font.system(size: 14, weight: .semibold, design: .default),
                bodyLarge: Font.system(size: 17, weight: .medium, design: .default),
                bodyMedium: Font.system(size: 15, weight: .medium, design: .default),
                bodySmall: Font.system(size: 13, weight: .regular, design: .default),
                captionLarge: Font.system(size: 12, weight: .regular, design: .default),
                captionMedium: Font.system(size: 11, weight: .regular, design: .default),
                captionSmall: Font.system(size: 10, weight: .regular, design: .default)
            )
        case .nightSky:
            return ThemeFonts(
                titleLarge: Font.system(size: 28, weight: .bold, design: .monospaced),
                titleMedium: Font.system(size: 22, weight: .semibold, design: .monospaced),
                titleSmall: Font.system(size: 20, weight: .semibold, design: .monospaced),
                headlineLarge: Font.system(size: 18, weight: .semibold, design: .monospaced),
                headlineMedium: Font.system(size: 16, weight: .semibold, design: .monospaced),
                headlineSmall: Font.system(size: 14, weight: .semibold, design: .monospaced),
                bodyLarge: Font.system(size: 17, weight: .regular, design: .monospaced),
                bodyMedium: Font.system(size: 15, weight: .regular, design: .monospaced),
                bodySmall: Font.system(size: 13, weight: .regular, design: .monospaced),
                captionLarge: Font.system(size: 12, weight: .regular, design: .monospaced),
                captionMedium: Font.system(size: 11, weight: .regular, design: .monospaced),
                captionSmall: Font.system(size: 10, weight: .regular, design: .monospaced)
            )
        }
    }
}

// 主题颜色结构
public struct ThemeColors {
    public let primary: Color
    public let secondary: Color
    public let accent: Color
    public let background: Color
    public let surface: Color
    public let surfaceVariant: Color
    public let text: Color
    public let subtext: Color
    public let border: Color
    public let success: Color
    public let warning: Color
    public let error: Color
    public let shadow: Color
    public let divider: Color

    // 次要背景色 - 用于卡片、预览区域等
    public var secondaryBackground: Color {
        return surface
    }

    // 三级背景色 - 用于嵌套卡片、预览文本背景等
    public var tertiaryBackground: Color {
        return surfaceVariant
    }

    // 主色渐变
    public var gradient: LinearGradient {
        LinearGradient(
            colors: [primary, secondary],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // 强调色渐变
    public var accentGradient: LinearGradient {
        LinearGradient(
            colors: [accent, accent.opacity(0.7)],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }

    // 卡片背景渐变
    public var cardGradient: LinearGradient {
        LinearGradient(
            colors: [surface, surfaceVariant.opacity(0.5)],
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    // 新增：彩虹渐变
    public var rainbowGradient: LinearGradient {
        LinearGradient(
            colors: [
                Color(hex: "FF5252"),  // 红
                Color(hex: "FF9800"),  // 橙
                Color(hex: "FFEB3B"),  // 黄
                Color(hex: "4CAF50"),  // 绿
                Color(hex: "2196F3"),  // 蓝
                Color(hex: "673AB7")   // 紫
            ],
            startPoint: .leading,
            endPoint: .trailing
        )
    }
    
    // 新增：深浅渐变
    public var tintGradient: LinearGradient {
        LinearGradient(
            colors: [primary, primary.opacity(0.3)],
            startPoint: .top,
            endPoint: .bottom
        )
    }
    
    // 新增：对角线渐变
    public var diagonalGradient: LinearGradient {
        LinearGradient(
            colors: [primary, secondary, accent],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

// 主题字体结构
public struct ThemeFonts {
    // 标题字体
    public let titleLarge: Font
    public let titleMedium: Font
    public let titleSmall: Font

    // 标题字体
    public let headlineLarge: Font
    public let headlineMedium: Font
    public let headlineSmall: Font

    // 正文字体
    public let bodyLarge: Font
    public let bodyMedium: Font
    public let bodySmall: Font

    // 说明文字字体
    public let captionLarge: Font
    public let captionMedium: Font
    public let captionSmall: Font
}

// 颜色扩展，支持十六进制颜色代码
extension Color {
    init(hex: String) {
        let hex = hex.trimmingCharacters(in: CharacterSet.alphanumerics.inverted)
        var int: UInt64 = 0
        Scanner(string: hex).scanHexInt64(&int)
        let a, r, g, b: UInt64
        switch hex.count {
        case 3: // RGB (12-bit)
            (a, r, g, b) = (255, (int >> 8) * 17, (int >> 4 & 0xF) * 17, (int & 0xF) * 17)
        case 6: // RGB (24-bit)
            (a, r, g, b) = (255, int >> 16, int >> 8 & 0xFF, int & 0xFF)
        case 8: // ARGB (32-bit)
            (a, r, g, b) = (int >> 24, int >> 16 & 0xFF, int >> 8 & 0xFF, int & 0xFF)
        default:
            (a, r, g, b) = (1, 1, 1, 0)
        }
        self.init(
            .sRGB,
            red: Double(r) / 255,
            green: Double(g) / 255,
            blue: Double(b) / 255,
            opacity: Double(a) / 255
        )
    }
}
