//
//  AppTypography.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/15.
//

import SwiftUI

/// 应用统一字体系统
public enum AppTypography {
    // 字体大小
    public enum FontSize {
        public static let tiny: CGFloat = 12
        public static let small: CGFloat = 14
        public static let medium: CGFloat = 16
        public static let large: CGFloat = 18
        public static let xLarge: CGFloat = 20
        public static let xxLarge: CGFloat = 24
        public static let huge: CGFloat = 32

        // 小组件专用尺寸
        public static let widgetSmall: CGFloat = 12
        public static let widgetMedium: CGFloat = 14
        public static let widgetLarge: CGFloat = 16
        public static let widgetTitle: CGFloat = 18
    }

    // 字体权重
    public enum FontWeight {
        public static let light = Font.Weight.light
        public static let regular = Font.Weight.regular
        public static let medium = Font.Weight.medium
        public static let semibold = Font.Weight.semibold
        public static let bold = Font.Weight.bold
    }

    // 行高
    public enum LineHeight {
        public static let tight: CGFloat = 1.1
        public static let normal: CGFloat = 1.3
        public static let relaxed: CGFloat = 1.5
        public static let loose: CGFloat = 1.8
    }

    // 字体样式预设
    public static func titleLarge(theme: AppTheme) -> Font {
        return theme.fonts.titleLarge.weight(FontWeight.bold)
    }

    public static func titleMedium(theme: AppTheme) -> Font {
        return theme.fonts.titleMedium
    }

    public static func titleSmall(theme: AppTheme) -> Font {
        return theme.fonts.titleSmall
    }

    public static func bodyLarge(theme: AppTheme) -> Font {
        return theme.fonts.bodyLarge
    }

    public static func bodyMedium(theme: AppTheme) -> Font {
        return theme.fonts.bodyMedium
    }

    public static func bodySmall(theme: AppTheme) -> Font {
        return theme.fonts.bodySmall
    }

    // 说明文字字体
    public static func caption(theme: AppTheme) -> Font {
        return theme.fonts.captionLarge
    }

    public static func captionLarge(theme: AppTheme) -> Font {
        return theme.fonts.captionLarge
    }

    public static func captionMedium(theme: AppTheme) -> Font {
        return theme.fonts.captionMedium
    }

    public static func captionSmall(theme: AppTheme) -> Font {
        return theme.fonts.captionSmall
    }

    public static func accentLarge(theme: AppTheme) -> Font {
        return theme.fonts.headlineLarge
    }

    public static func accentMedium(theme: AppTheme) -> Font {
        return theme.fonts.headlineMedium
    }

    public static func accentSmall(theme: AppTheme) -> Font {
        return theme.fonts.headlineSmall
    }

    // 小组件专用字体
    public static func widgetTitle(theme _: AppTheme) -> Font {
        return Font.system(size: FontSize.widgetTitle, weight: FontWeight.semibold)
    }

    public static func widgetBody(theme _: AppTheme) -> Font {
        return Font.system(size: FontSize.widgetMedium, weight: FontWeight.regular)
    }

    public static func widgetAccent(theme _: AppTheme) -> Font {
        return Font.system(size: FontSize.widgetLarge, weight: FontWeight.medium)
    }
}
