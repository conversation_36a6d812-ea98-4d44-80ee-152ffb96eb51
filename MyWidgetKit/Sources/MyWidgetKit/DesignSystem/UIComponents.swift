//
//  UIComponents.swift
//  MyWidgetKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/15.
//

import SwiftUI
import WidgetKit

/// 现代化搜索栏组件
public struct SearchBar: View {
    // 属性
    @Binding var text: String
    var placeholder: String
    var theme: AppTheme
    var onSearch: ((String) -> Void)?

    // 状态
    @State private var isFocused: Bool = false
    @State private var showClearButton: Bool = false

    // 初始化
    public init(
        text: Binding<String>,
        placeholder: String,
        theme: AppTheme = ThemeManager.shared.currentTheme,
        onSearch: ((String) -> Void)? = nil
    ) {
        _text = text
        self.placeholder = placeholder
        self.theme = theme
        self.onSearch = onSearch
    }

    // 视图主体
    public var body: some View {
        HStack(spacing: AppLayout.Spacing.small) {
            // 搜索图标
            Image(systemName: "magnifyingglass")
                .font(.system(size: AppLayout.IconSize.small))
                .foregroundColor(isFocused ? theme.colors.accent : theme.colors.subtext)
                .padding(.leading, AppLayout.Spacing.medium)
                .animation(.easeInOut(duration: 0.2), value: isFocused)

            // 输入框
            TextField(placeholder, text: $text, onEditingChanged: { editing in
                withAnimation(.easeInOut(duration: 0.2)) {
                    isFocused = editing
                    showClearButton = !text.isEmpty && editing
                }
            }, onCommit: {
                onSearch?(text)
            })
            .font(theme.fonts.bodyMedium)
            .foregroundColor(theme.colors.text)
            .padding(.vertical, AppLayout.Spacing.medium)
            .accentColor(theme.colors.accent)
            .onChange(of: text) { newValue in
                withAnimation {
                    showClearButton = !newValue.isEmpty && isFocused
                }
            }

            // 清除按钮
            if showClearButton || (!isFocused && !text.isEmpty) {
                Button(action: {
                    text = ""
                    hapticFeedback(style: .light)
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: AppLayout.IconSize.small))
                        .foregroundColor(theme.colors.subtext)
                        .padding(.trailing, AppLayout.Spacing.medium)
                }
                .transition(.opacity)
                .animation(.easeInOut(duration: 0.2), value: showClearButton)
            }
        }
        .background(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                .fill(theme.colors.surfaceVariant)
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                        .stroke(isFocused ? theme.colors.accent : Color.clear, lineWidth: AppLayout.Border.thin)
                )
        )
        .animation(.easeInOut(duration: 0.2), value: isFocused)
    }

    // 触觉反馈
    private func hapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
}

/// 现代化分类按钮组件
public struct CategoryButton: View {
    // 属性
    var title: String
    var isSelected: Bool
    var theme: AppTheme
    var action: () -> Void

    // 状态
    @State private var isPressed: Bool = false

    // 初始化
    public init(title: String, isSelected: Bool, theme: AppTheme, action: @escaping () -> Void) {
        self.title = title
        self.isSelected = isSelected
        self.theme = theme
        self.action = action
    }

    // 视图主体
    public var body: some View {
        Button(action: {
            hapticFeedback(style: .light)
            action()
        }) {
            Text(title)
                .font(theme.fonts.bodyMedium)
                .fontWeight(isSelected ? .semibold : .regular)
                .foregroundColor(isSelected ? .white : theme.colors.text)
                .padding(.horizontal, AppLayout.Spacing.medium)
                .padding(.vertical, AppLayout.Spacing.small)
                .background(
                    Capsule()
                        .fill(isSelected ? theme.colors.accent : theme.colors.surfaceVariant)
                )
                .shadow(
                    color: isSelected ? theme.colors.accent.opacity(0.3) : Color.clear,
                    radius: 4,
                    x: 0,
                    y: 2
                )
                .scaleEffect(isPressed ? 0.95 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .animation(.easeInOut(duration: 0.2), value: isSelected)
        .simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = true
                    }
                }
                .onEnded { _ in
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = false
                    }
                }
        )
    }

    // 触觉反馈
    private func hapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
}

/// 现代化小组件预览容器
public struct ThemedWidgetPreviewContainer<Content: View>: View {
    // 属性
    var family: WidgetFamily
    var content: () -> Content
    var theme: AppTheme

    // 状态
    @State private var isHovered: Bool = false
    @State private var animateGradient: Bool = false

    // 初始化
    public init(
        family: WidgetFamily,
        theme: AppTheme = ThemeManager.shared.currentTheme,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.family = family
        self.theme = theme
        self.content = content
    }

    // 视图主体
    public var body: some View {
        ZStack {
            // 背景层
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                .fill(
                    LinearGradient(
                        colors: animateGradient ?
                            [theme.colors.surfaceVariant.opacity(0.7), theme.colors.surface] :
                            [theme.colors.surface, theme.colors.surfaceVariant.opacity(0.7)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                        .strokeBorder(theme.colors.border, lineWidth: AppLayout.Border.hairline)
                )

            // 内容层
            Group {
                switch family {
                case .systemSmall:
                    content()
                        .frame(width: AppLayout.WidgetSize.smallWidth, height: AppLayout.WidgetSize.smallHeight)
                case .systemMedium:
                    content()
                        .frame(width: AppLayout.WidgetSize.mediumWidth, height: AppLayout.WidgetSize.mediumHeight)
                case .systemLarge:
                    content()
                        .frame(width: AppLayout.WidgetSize.largeWidth, height: AppLayout.WidgetSize.largeHeight)
                case .systemExtraLarge:
                    content()
                        .frame(width: AppLayout.WidgetSize.xLargeWidth, height: AppLayout.WidgetSize.xLargeHeight)
                default:
                    content()
                        .frame(width: AppLayout.WidgetSize.smallWidth, height: AppLayout.WidgetSize.smallHeight)
                }
            }
            .clipShape(RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium))
            .padding(AppLayout.Spacing.medium)

            // 悬停效果
            if isHovered {
                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                    .strokeBorder(theme.colors.accent.opacity(0.3), lineWidth: AppLayout.Border.medium)
                    .transition(.opacity)
            }
        }
        .shadow(
            color: isHovered ? theme.colors.accent.opacity(0.2) : theme.colors.shadow,
            radius: isHovered ? 12 : 8,
            x: 0,
            y: isHovered ? 6 : 4
        )
        .scaleEffect(isHovered ? 1.02 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isHovered)
        .onHover { hovering in
            withAnimation {
                isHovered = hovering
            }
        }
        .onAppear {
            // 启动渐变动画
            withAnimation(Animation.linear(duration: 5).repeatForever(autoreverses: true)) {
                animateGradient.toggle()
            }
        }
    }
}

/// 现代化组件卡片
public struct EnhancedWidgetCard: View {
    // 属性
    let title: String
    let description: String
    let iconName: String
    let widgetFamily: WidgetFamily
    let theme: AppTheme
    let onTap: (() -> Void)?

    // 状态
    @State private var isPressed: Bool = false
    @State private var isHovered: Bool = false
    @State private var animateGradient: Bool = false

    // 初始化
    public init(
        title: String,
        description: String,
        iconName: String,
        widgetFamily: WidgetFamily,
        theme: AppTheme,
        onTap: (() -> Void)? = nil
    ) {
        self.title = title
        self.description = description
        self.iconName = iconName
        self.widgetFamily = widgetFamily
        self.theme = theme
        self.onTap = onTap
    }

    // 视图主体
    public var body: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            // 顶部区域：图标和标题
            HStack(alignment: .center, spacing: AppLayout.Spacing.medium) {
                // 图标容器
                ZStack {
                    Circle()
                        .fill(theme.colors.gradient)
                        .frame(width: AppLayout.IconSize.large, height: AppLayout.IconSize.large)

                    Image(systemName: iconName)
                        .font(.system(size: AppLayout.IconSize.medium))
                        .foregroundColor(.white)
                }
                .shadow(color: theme.colors.shadow.opacity(0.2), radius: 4, x: 0, y: 2)

                VStack(alignment: .leading, spacing: AppLayout.Spacing.tiny) {
                    Text(title)
                        .font(theme.fonts.headlineMedium)
                        .foregroundColor(theme.colors.text)

                    Text(description)
                        .font(theme.fonts.bodySmall)
                        .foregroundColor(theme.colors.subtext)
                        .lineLimit(2)
                        .fixedSize(horizontal: false, vertical: true)
                }

                Spacer()

                // 添加按钮
                Image(systemName: "plus.circle.fill")
                    .font(.system(size: AppLayout.IconSize.medium))
                    .foregroundColor(theme.colors.accent)
                    .opacity(isHovered ? 1.0 : 0.8)
                    .scaleEffect(isHovered ? 1.1 : 1.0)
                    .animation(.easeInOut(duration: 0.2), value: isHovered)
            }
            .padding(.horizontal, AppLayout.Spacing.medium)
            .padding(.top, AppLayout.Spacing.medium)

            // 预览区域
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                    .fill(
                        LinearGradient(
                            colors: [
                                theme.colors.surfaceVariant.opacity(0.7),
                                theme.colors.surface,
                            ],
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        )
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                            .strokeBorder(theme.colors.border, lineWidth: AppLayout.Border.hairline)
                    )

                // 预览内容
                getPreviewContent(for: title, family: widgetFamily, theme: theme)
                    .padding(AppLayout.Spacing.medium)
            }
            .frame(height: getPreviewHeight(for: widgetFamily))
            .padding(.horizontal, AppLayout.Spacing.medium)
            .padding(.bottom, AppLayout.Spacing.medium)
        }
        .background(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                .fill(theme.colors.surface)
        )
        .overlay(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                .strokeBorder(
                    isPressed ? theme.colors.accent.opacity(0.3) : theme.colors.border,
                    lineWidth: isPressed ? AppLayout.Border.medium : AppLayout.Border.hairline
                )
        )
        .shadow(
            color: isPressed ? theme.colors.accent.opacity(0.2) : theme.colors.shadow,
            radius: isPressed ? 4 : 8,
            x: 0,
            y: isPressed ? 2 : 4
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(AppLayout.AnimationCurve.emphasized, value: isPressed)
        .onHover { hovering in
            withAnimation(.easeInOut(duration: 0.2)) {
                isHovered = hovering
            }
        }
        .simultaneousGesture(
            TapGesture()
                .onEnded { _ in
                    hapticFeedback(style: .light)
                    withAnimation {
                        isPressed = true
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.2) {
                            withAnimation {
                                isPressed = false

                                // 如果提供了 onTap 回调，则调用它
                                if let onTap = onTap {
                                    onTap()
                                }
                            }
                        }
                    }
                }
        )
        .onAppear {
            // 启动渐变动画
            withAnimation(Animation.linear(duration: 3).repeatForever(autoreverses: true)) {
                animateGradient = true
            }
        }
    }

    // 获取预览内容
    @ViewBuilder
    private func getPreviewContent(for title: String, family _: WidgetFamily, theme: AppTheme) -> some View {
        switch title {
        case "每日灵感":
            VStack(spacing: AppLayout.Spacing.medium) {
                Spacer()

                Text("生活不止眼前的苟且，还有诗和远方的田野")
                    .font(.system(size: 16, weight: .medium, design: .serif))
                    .multilineTextAlignment(.center)
                    .foregroundStyle(theme.colors.gradient)
                    .padding(.horizontal)
                    .lineLimit(3)

                Text("— 北岛")
                    .font(.system(size: 14, weight: .regular, design: .serif))
                    .foregroundColor(theme.colors.subtext)

                Spacer()

                HStack {
                    Spacer()
                    Text(dateFormatter.string(from: Date()))
                        .font(theme.fonts.captionMedium)
                        .foregroundColor(theme.colors.subtext)
                }
            }

        case "时光提醒":
            VStack(spacing: AppLayout.Spacing.medium) {
                Spacer()

                Text("春节")
                    .font(theme.fonts.headlineMedium)
                    .foregroundColor(theme.colors.text)

                Text("还有 15 天")
                    .font(.system(size: 28, weight: .bold))
                    .foregroundStyle(theme.colors.gradient)

                ProgressView(value: 0.75)
                    .progressViewStyle(LinearProgressViewStyle(tint: theme.colors.accent))
                    .frame(width: 120)

                Spacer()

                HStack {
                    Spacer()
                    Text(dateFormatter.string(from: Date()))
                        .font(theme.fonts.captionMedium)
                        .foregroundColor(theme.colors.subtext)
                }
            }

        case "二维码小组件":
            VStack(spacing: AppLayout.Spacing.medium) {
                Spacer()

                Image(systemName: "qrcode")
                    .font(.system(size: 80))
                    .foregroundColor(theme.colors.text)

                Text("扫一扫")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)

                Spacer()
            }

        default:
            VStack {
                Spacer()

                Image(systemName: iconName)
                    .font(.system(size: 40))
                    .foregroundStyle(theme.colors.gradient)

                Text(title)
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
                    .padding(.top, AppLayout.Spacing.small)

                Spacer()
            }
        }
    }

    // 获取预览高度
    private func getPreviewHeight(for family: WidgetFamily) -> CGFloat {
        switch family {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 170
        case .systemLarge:
            return 360
        default:
            return 170
        }
    }

    // 日期格式化
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy年MM月dd日"
        return formatter
    }

    // 触觉反馈
    private func hapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
}
