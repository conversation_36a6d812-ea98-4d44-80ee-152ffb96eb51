//
//  AppInteraction.swift
//  MyWidgetKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/15.
//

import SwiftUI

/// 应用统一交互系统
public enum AppInteraction {
    // 动画持续时间
    public enum AnimationDuration {
        public static let fast: Double = 0.15
        public static let normal: Double = 0.25
        public static let slow: Double = 0.4
    }

    // 动画曲线
    public enum AnimationCurve {
        public static func easeIn(duration: Double) -> Animation {
            return .easeIn(duration: duration)
        }

        public static func easeOut(duration: Double) -> Animation {
            return .easeOut(duration: duration)
        }

        public static func easeInOut(duration: Double) -> Animation {
            return .easeInOut(duration: duration)
        }

        public static func spring(duration: Double = 0.3) -> Animation {
            return .spring(response: duration, dampingFraction: 0.6)
        }
    }

    // 按钮样式
    public enum ButtonStyle {
        @MainActor public static func primary(theme: AppTheme) -> some SwiftUI.ButtonStyle {
            PrimaryButtonStyle(theme: theme)
        }

        @MainActor public static func secondary(theme: AppTheme) -> some SwiftUI.ButtonStyle {
            SecondaryButtonStyle(theme: theme)
        }

        @MainActor public static func ghost(theme: AppTheme) -> some SwiftUI.ButtonStyle {
            GhostButtonStyle(theme: theme)
        }
    }
}

// 主按钮样式
@MainActor
public struct PrimaryButtonStyle: ButtonStyle {
    let theme: AppTheme

    public func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, AppLayout.Spacing.medium)
            .padding(.vertical, AppLayout.Spacing.small)
            .background(theme.colors.accent)
            .foregroundColor(.white)
            .cornerRadius(AppLayout.CornerRadius.medium)
            .scaleEffect(configuration.isPressed ? 0.97 : 1)
            .opacity(configuration.isPressed ? 0.9 : 1)
            .animation(.easeOut(duration: AppInteraction.AnimationDuration.fast), value: configuration.isPressed)
    }
}

// 次要按钮样式
@MainActor
public struct SecondaryButtonStyle: ButtonStyle {
    let theme: AppTheme

    public func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, AppLayout.Spacing.medium)
            .padding(.vertical, AppLayout.Spacing.small)
            .background(theme.colors.surface)
            .foregroundColor(theme.colors.accent)
            .cornerRadius(AppLayout.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                    .stroke(theme.colors.accent, lineWidth: AppLayout.Border.thin)
            )
            .scaleEffect(configuration.isPressed ? 0.97 : 1)
            .opacity(configuration.isPressed ? 0.9 : 1)
            .animation(.easeOut(duration: AppInteraction.AnimationDuration.fast), value: configuration.isPressed)
    }
}

// 幽灵按钮样式
@MainActor
public struct GhostButtonStyle: ButtonStyle {
    let theme: AppTheme

    public func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .padding(.horizontal, AppLayout.Spacing.medium)
            .padding(.vertical, AppLayout.Spacing.small)
            .foregroundColor(theme.colors.accent)
            .background(Color.clear)
            .scaleEffect(configuration.isPressed ? 0.97 : 1)
            .opacity(configuration.isPressed ? 0.7 : 1)
            .animation(.easeOut(duration: AppInteraction.AnimationDuration.fast), value: configuration.isPressed)
    }
}

// 卡片点击效果修饰器
@MainActor
public struct CardPressModifier: ViewModifier {
    let isPressed: Bool

    public func body(content: Content) -> some View {
        content
            .scaleEffect(isPressed ? 0.98 : 1)
            .opacity(isPressed ? 0.9 : 1)
            .animation(.easeOut(duration: AppInteraction.AnimationDuration.fast), value: isPressed)
    }
}

// 视图扩展
public extension View {
    func cardPress(isPressed: Bool) -> some View {
        modifier(CardPressModifier(isPressed: isPressed))
    }
}
