import SwiftUI

/// 水波浪视图
public struct WaterWaveView: View {
    /// 填充百分比 (0-1)
    private let progress: Double
    
    /// 波浪颜色
    private let waveColors: [Color]
    
    /// 文本内容
    private let content: AnyView
    
    /// 动画状态
    @State private var waveOffset1: CGFloat = 0
    @State private var waveOffset2: CGFloat = 0
    @State private var waveOffset3: CGFloat = 0
    
    /// 初始化方法
    public init<Content: View>(
        progress: Double,
        waveColors: [Color] = [
            Color(red: 0, green: 0.6, blue: 1, opacity: 0.3),
            Color(red: 0, green: 0.5, blue: 0.9, opacity: 0.5),
            Color(red: 0, green: 0.4, blue: 0.8, opacity: 0.7)
        ],
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.progress = min(max(progress, 0), 1)
        self.waveColors = waveColors.count >= 3 ? waveColors : [
            Color(red: 0, green: 0.6, blue: 1, opacity: 0.3),
            Color(red: 0, green: 0.5, blue: 0.9, opacity: 0.5),
            Color(red: 0, green: 0.4, blue: 0.8, opacity: 0.7)
        ]
        self.content = AnyView(content())
    }
    
    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 波浪层
                ZStack(alignment: .bottom) {
                    // 第一层波浪
                    WavePath(
                        progress: progress,
                        waveHeight: 10,
                        offset: waveOffset1
                    )
                    .fill(waveColors[0])
                    .animation(
                        Animation.linear(duration: 2)
                            .repeatForever(autoreverses: false),
                        value: waveOffset1
                    )
                    
                    // 第二层波浪
                    WavePath(
                        progress: progress,
                        waveHeight: 15,
                        offset: waveOffset2
                    )
                    .fill(waveColors[1])
                    .animation(
                        Animation.linear(duration: 1.8)
                            .repeatForever(autoreverses: false),
                        value: waveOffset2
                    )
                    
                    // 第三层波浪
                    WavePath(
                        progress: progress,
                        waveHeight: 8,
                        offset: waveOffset3
                    )
                    .fill(waveColors[2])
                    .animation(
                        Animation.linear(duration: 1.5)
                            .repeatForever(autoreverses: false),
                        value: waveOffset3
                    )
                }
                .mask(Circle())
                
                // 内容层
                content
            }
            .onAppear {
                // 启动波浪动画
                withAnimation(.linear(duration: 2).repeatForever(autoreverses: false)) {
                    waveOffset1 = geometry.size.width
                }
                
                withAnimation(.linear(duration: 1.8).repeatForever(autoreverses: false)) {
                    waveOffset2 = geometry.size.width
                }
                
                withAnimation(.linear(duration: 1.5).repeatForever(autoreverses: false)) {
                    waveOffset3 = geometry.size.width
                }
            }
        }
    }
}

/// 波浪路径
struct WavePath: Shape {
    /// 填充百分比 (0-1)
    let progress: Double
    
    /// 波浪高度
    let waveHeight: CGFloat
    
    /// 波浪偏移量
    var offset: CGFloat
    
    /// 动画属性
    var animatableData: CGFloat {
        get { offset }
        set { offset = newValue }
    }
    
    func path(in rect: CGRect) -> Path {
        var path = Path()
        
        // 计算波浪底部位置
        let lowPoint = rect.height * (1 - CGFloat(progress))
        let width = rect.width
        
        // 起始点
        path.move(to: CGPoint(x: 0, y: rect.height))
        
        // 左侧边界
        path.addLine(to: CGPoint(x: 0, y: lowPoint))
        
        // 波浪曲线
        for x in stride(from: 0, to: width, by: 1) {
            let relativeX = x / width
            let normalizedX = x + offset
            let sine = sin(normalizedX / 20)
            let y = lowPoint + sine * waveHeight
            path.addLine(to: CGPoint(x: x, y: y))
        }
        
        // 右侧边界和底部
        path.addLine(to: CGPoint(x: width, y: lowPoint))
        path.addLine(to: CGPoint(x: width, y: rect.height))
        path.closeSubpath()
        
        return path
    }
}
