import SwiftUI
import WidgetKit

/// 水分摄入追踪器数据模型
public struct WaterIntakeWidgetData: WidgetPreviewableData, Codable, Hashable {
    /// 今日饮水量（毫升）
    public var currentIntake: Double

    /// 目标饮水量（毫升）
    public var targetIntake: Double

    /// 快捷添加按钮配置（毫升）
    public var quickAddOptions: [Double]

    /// 背景设置
    public var background: WidgetBackground

    /// 字体名称
    public var fontName: String

    /// 字体大小
    public var fontSize: CGFloat

    /// 字体颜色
    public var fontColor: WidgetColor

    /// 主题颜色（用于进度条等）
    public var accentColor: WidgetColor

    /// 主题
    public var theme: WaterIntakeTheme

    /// 是否使用渐变色
    public var useGradient: Bool

    /// 是否使用波浪效果（大尺寸）
    public var useWaveEffect: Bool

    /// 是否使用阴影效果
    public var useShadow: Bool

    /// 是否使用HealthKit
    public var useHealthKit: Bool

    /// 上次更新时间
    public var lastUpdated: Date

    /// 是否显示庆祝动画
    public var showCelebration: Bool

    /// 初始化方法
    public init(
        currentIntake: Double = 0,
        targetIntake: Double = 2000,
        quickAddOptions: [Double] = [100, 200, 300, 500],
        background: WidgetBackground = .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
        fontName: String = "PingFangSC-Regular",
        fontSize: CGFloat = 14,
        fontColor: WidgetColor = WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
        accentColor: WidgetColor = WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
        theme: WaterIntakeTheme = .oceanBlue,
        useGradient: Bool = true,
        useWaveEffect: Bool = true,
        useShadow: Bool = true,
        useHealthKit: Bool = true,
        lastUpdated: Date = Date(),
        showCelebration: Bool = false
    ) {
        self.currentIntake = currentIntake
        self.targetIntake = targetIntake
        self.quickAddOptions = quickAddOptions
        self.background = background
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontColor = fontColor
        self.accentColor = accentColor
        self.theme = theme
        self.useGradient = useGradient
        self.useWaveEffect = useWaveEffect
        self.useShadow = useShadow
        self.useHealthKit = useHealthKit
        self.lastUpdated = lastUpdated
        self.showCelebration = showCelebration
    }

    /// 获取主题渐变色
    public var themeGradientColors: [Color] {
        return theme.gradientColors
    }

    /// 获取波浪颜色
    public var waveColors: [Color] {
        return theme.waveColors
    }

    /// 完成百分比
    public var completionPercentage: Double {
        min(currentIntake / targetIntake, 1.0)
    }

    /// 格式化的当前饮水量
    public var formattedCurrentIntake: String {
        if currentIntake >= 1000 {
            return String(format: "%.1f L", currentIntake / 1000)
        } else {
            return "\(Int(currentIntake)) ml"
        }
    }

    /// 格式化的目标饮水量
    public var formattedTargetIntake: String {
        if targetIntake >= 1000 {
            return String(format: "%.1f L", targetIntake / 1000)
        } else {
            return "\(Int(targetIntake)) ml"
        }
    }

    /// 格式化的完成百分比
    public var formattedPercentage: String {
        return "\(Int(completionPercentage * 100))%"
    }

    /// 哈希方法
    public func hash(into hasher: inout Hasher) {
        hasher.combine(currentIntake)
        hasher.combine(targetIntake)
        hasher.combine(quickAddOptions)
        hasher.combine(fontName)
        hasher.combine(fontSize)
        hasher.combine(fontColor.red)
        hasher.combine(fontColor.green)
        hasher.combine(fontColor.blue)
        hasher.combine(accentColor.red)
        hasher.combine(accentColor.green)
        hasher.combine(accentColor.blue)
        hasher.combine(theme.rawValue)
        hasher.combine(useGradient)
        hasher.combine(useWaveEffect)
        hasher.combine(useShadow)
        hasher.combine(useHealthKit)
        hasher.combine(lastUpdated)
        hasher.combine(showCelebration)
    }
}

/// 水分摄入追踪器小组件入口
public struct WaterIntakeEntry: TimelineEntry {
    /// 日期
    public let date: Date

    /// 配置数据
    public let configuration: WaterIntakeWidgetData

    /// 初始化方法
    public init(date: Date, configuration: WaterIntakeWidgetData) {
        self.date = date
        self.configuration = configuration
    }
}
