import SwiftUI
import WidgetKit
#if os(iOS)
import AppIntents
#endif

/// 水分摄入追踪器小组件视图
public struct WaterIntakeWidgetView: View {
    /// 数据
    public let data: WaterIntakeWidgetData

    /// 小组件尺寸
    public let family: WidgetFamily

    /// 初始化方法
    public init(data: WaterIntakeWidgetData, family: WidgetFamily) {
        self.data = data
        self.family = family
    }

    /// 主视图
    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 根据不同尺寸显示不同布局
                switch family {
                case .systemSmall:
                    smallWidgetLayout
                case .systemMedium:
                    mediumWidgetLayout
                case .systemLarge:
                    largeWidgetLayout
                default:
                    // 默认使用中尺寸布局
                    mediumWidgetLayout
                }
            }
            .adaptiveBackground(content: {
                // 背景
                BackgroundView(background: data.background)

            })
            .frame(width: geometry.size.width, height: geometry.size.height)
        }
    }

    /// 小尺寸布局
    private var smallWidgetLayout: some View {
        VStack(spacing: 8) {
            // 标题
            Text("今日饮水")
                .font(.custom(data.fontName, size: data.fontSize))
                .foregroundColor(data.fontColor.toColor())
                .fontWeight(.medium)

            // 进度环
            ZStack {
                // 背景环
                Circle()
                    .stroke(data.accentColor.toColor().opacity(0.2), lineWidth: 8)

                // 进度环
                Circle()
                    .trim(from: 0, to: CGFloat(data.completionPercentage))
                    .stroke(data.accentColor.toColor(), style: StrokeStyle(lineWidth: 8, lineCap: .round))
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut, value: data.completionPercentage)

                // 中心文字
                VStack(spacing: 2) {
                    Text(data.formattedCurrentIntake)
                        .font(.custom(data.fontName, size: data.fontSize + 4))
                        .foregroundColor(data.fontColor.toColor())
                        .fontWeight(.bold)

                    Text(data.formattedPercentage)
                        .font(.custom(data.fontName, size: data.fontSize - 2))
                        .foregroundColor(data.fontColor.toColor().opacity(0.7))
                }
            }
            .padding(8)

            // 目标
            Text("目标: \(data.formattedTargetIntake)")
                .font(.custom(data.fontName, size: data.fontSize - 2))
                .foregroundColor(data.fontColor.toColor().opacity(0.7))
        }
        .padding()
    }

    /// 中尺寸布局
    private var mediumWidgetLayout: some View {
        HStack(spacing: 16) {
            // 左侧进度环
            ZStack {
                // 背景环
                Circle()
                    .stroke(data.accentColor.toColor().opacity(0.2), lineWidth: 10)

                // 进度环
                Circle()
                    .trim(from: 0, to: CGFloat(data.completionPercentage))
                    .stroke(data.accentColor.toColor(), style: StrokeStyle(lineWidth: 10, lineCap: .round))
                    .rotationEffect(.degrees(-90))
                    .animation(.easeInOut, value: data.completionPercentage)

                // 中心文字
                VStack(spacing: 2) {
                    Text(data.formattedPercentage)
                        .font(.custom(data.fontName, size: data.fontSize + 6))
                        .foregroundColor(data.fontColor.toColor())
                        .fontWeight(.bold)

                    Text("完成")
                        .font(.custom(data.fontName, size: data.fontSize - 2))
                        .foregroundColor(data.fontColor.toColor().opacity(0.7))
                }
            }
            .frame(width: 100, height: 100)

            // 右侧信息和快捷按钮
            VStack(alignment: .leading, spacing: 8) {
                // 标题
                Text("今日饮水")
                    .font(.custom(data.fontName, size: data.fontSize + 2))
                    .foregroundColor(data.fontColor.toColor())
                    .fontWeight(.medium)

                // 当前饮水量
                Text(data.formattedCurrentIntake)
                    .font(.custom(data.fontName, size: data.fontSize + 8))
                    .foregroundColor(data.fontColor.toColor())
                    .fontWeight(.bold)

                // 目标
                Text("目标: \(data.formattedTargetIntake)")
                    .font(.custom(data.fontName, size: data.fontSize))
                    .foregroundColor(data.fontColor.toColor().opacity(0.7))

                // 快捷添加按钮
                HStack(spacing: 8) {
                    ForEach(data.quickAddOptions.prefix(2), id: \.self) { amount in

                        #if os(iOS)
                        if #available(iOS 17.0, *) {
                            // iOS 17+ 使用新的 Button(intent:) API
                            Button(intent: AddWaterIntentAppIntent(amount: amount)) {
                                Text("+\(Int(amount))")
                                    .font(.custom(data.fontName, size: data.fontSize - 1))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(data.accentColor.toColor())
                                    .cornerRadius(12)
                            }
                            .buttonStyle(PlainButtonStyle())
                        } else {
                            // iOS 16 及以下版本使用 widgetURL
                            Button {
                                // 按钮操作由widgetURL处理
                            } label: {
                                Text("+\(Int(amount))")
                                    .font(.custom(data.fontName, size: data.fontSize - 1))
                                    .foregroundColor(.white)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(data.accentColor.toColor())
                                    .cornerRadius(12)
                            }
                            .widgetURL(URL(string: "jzjjwidget://addwater?amount=\(Int(amount))"))
                            .buttonStyle(PlainButtonStyle())
                        }
                        #endif
                    }
                }
            }
            .padding()
        }
    }

    /// 大尺寸布局
    private var largeWidgetLayout: some View {
        VStack(spacing: 16) {
            // 顶部信息
            HStack {
                // 标题
                VStack(alignment: .leading, spacing: 4) {
                    Text("今日饮水")
                        .font(.custom(data.fontName, size: data.fontSize + 4))
                        .foregroundColor(data.fontColor.toColor())
                        .fontWeight(.medium)

                    Text("目标: \(data.formattedTargetIntake)")
                        .font(.custom(data.fontName, size: data.fontSize))
                        .foregroundColor(data.fontColor.toColor().opacity(0.7))
                }

                Spacer()

                // 完成百分比
                Text(data.formattedPercentage)
                    .font(.custom(data.fontName, size: data.fontSize + 8))
                    .foregroundColor(data.fontColor.toColor())
                    .fontWeight(.bold)
            }

            // 进度条
            ProgressView(value: data.completionPercentage)
                .progressViewStyle(LinearProgressViewStyle(tint: data.accentColor.toColor()))
                .scaleEffect(x: 1, y: 2, anchor: .center)

            // 当前饮水量
            HStack {
                Text(data.formattedCurrentIntake)
                    .font(.custom(data.fontName, size: data.fontSize + 12))
                    .foregroundColor(data.fontColor.toColor())
                    .fontWeight(.bold)

                Spacer()

                // 更新时间
                Text("更新于 \(data.lastUpdated.formatted(.dateTime.hour().minute()))")
                    .font(.custom(data.fontName, size: data.fontSize - 2))
                    .foregroundColor(data.fontColor.toColor().opacity(0.6))
            }

            Spacer()

            // 快捷添加按钮
            HStack(spacing: 12) {
                ForEach(data.quickAddOptions, id: \.self) { amount in
                    #if os(iOS)
                    if #available(iOS 17.0, *) {
                        // iOS 17+ 使用新的 Button(intent:) API
                        Button(intent: AddWaterIntentAppIntent(amount: amount)) {
                            VStack {
                                Text("+\(Int(amount))")
                                    .font(.custom(data.fontName, size: data.fontSize + 2))
                                    .foregroundColor(.white)
                                    .fontWeight(.bold)

                                Text("ml")
                                    .font(.custom(data.fontName, size: data.fontSize - 2))
                                    .foregroundColor(.white.opacity(0.8))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(data.accentColor.toColor())
                            .cornerRadius(12)
                        }
                        .buttonStyle(PlainButtonStyle())
                    } else {
                        // iOS 16 及以下版本使用 widgetURL
                        Button {
                            // 按钮操作由widgetURL处理
                        } label: {
                            VStack {
                                Text("+\(Int(amount))")
                                    .font(.custom(data.fontName, size: data.fontSize + 2))
                                    .foregroundColor(.white)
                                    .fontWeight(.bold)

                                Text("ml")
                                    .font(.custom(data.fontName, size: data.fontSize - 2))
                                    .foregroundColor(.white.opacity(0.8))
                            }
                            .frame(maxWidth: .infinity)
                            .padding(.vertical, 8)
                            .background(data.accentColor.toColor())
                            .cornerRadius(12)
                        }
                        .widgetURL(URL(string: "jzjjwidget://addwater?amount=\(Int(amount))"))
                        .buttonStyle(PlainButtonStyle())
                    }
                    #endif
                }
            }
        }
        .padding()
    }
}
