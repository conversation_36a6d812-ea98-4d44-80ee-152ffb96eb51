import SwiftUI

/// 渐变进度环视图
public struct GradientCircularProgressView: View {
    /// 进度值 (0-1)
    private let progress: Double
    
    /// 渐变颜色
    private let gradientColors: [Color]
    
    /// 背景颜色
    private let backgroundColor: Color
    
    /// 线宽
    private let lineWidth: CGFloat
    
    /// 是否显示阴影
    private let showShadow: Bool
    
    /// 内容视图
    private let content: AnyView
    
    /// 初始化方法
    public init<Content: View>(
        progress: Double,
        gradientColors: [Color],
        backgroundColor: Color = Color.gray.opacity(0.2),
        lineWidth: CGFloat = 10,
        showShadow: Bool = true,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.progress = min(max(progress, 0), 1)
        self.gradientColors = gradientColors
        self.backgroundColor = backgroundColor
        self.lineWidth = lineWidth
        self.showShadow = showShadow
        self.content = AnyView(content())
    }
    
    public var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(backgroundColor, lineWidth: lineWidth)
            
            // 进度圆环
            Circle()
                .trim(from: 0, to: CGFloat(progress))
                .stroke(
                    LinearGradient(
                        gradient: Gradient(colors: gradientColors),
                        startPoint: .leading,
                        endPoint: .trailing
                    ),
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.5), value: progress)
                .shadow(color: showShadow ? gradientColors.last?.opacity(0.5) ?? Color.clear : Color.clear, 
                        radius: 3, x: 0, y: 1)
            
            // 内容
            content
        }
    }
}

/// 渐变进度条视图
public struct GradientLinearProgressView: View {
    /// 进度值 (0-1)
    private let progress: Double
    
    /// 渐变颜色
    private let gradientColors: [Color]
    
    /// 背景颜色
    private let backgroundColor: Color
    
    /// 高度
    private let height: CGFloat
    
    /// 是否显示阴影
    private let showShadow: Bool
    
    /// 初始化方法
    public init(
        progress: Double,
        gradientColors: [Color],
        backgroundColor: Color = Color.gray.opacity(0.2),
        height: CGFloat = 8,
        showShadow: Bool = true
    ) {
        self.progress = min(max(progress, 0), 1)
        self.gradientColors = gradientColors
        self.backgroundColor = backgroundColor
        self.height = height
        self.showShadow = showShadow
    }
    
    public var body: some View {
        GeometryReader { geometry in
            ZStack(alignment: .leading) {
                // 背景
                RoundedRectangle(cornerRadius: height / 2)
                    .fill(backgroundColor)
                    .frame(height: height)
                
                // 进度条
                RoundedRectangle(cornerRadius: height / 2)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: gradientColors),
                            startPoint: .leading,
                            endPoint: .trailing
                        )
                    )
                    .frame(width: geometry.size.width * CGFloat(progress), height: height)
                    .animation(.easeInOut(duration: 0.5), value: progress)
                    .shadow(color: showShadow ? gradientColors.last?.opacity(0.5) ?? Color.clear : Color.clear, 
                            radius: 2, x: 0, y: 1)
            }
        }
        .frame(height: height)
    }
}

/// 庆祝动画视图
public struct CelebrationView: View {
    /// 是否显示
    @Binding var isShowing: Bool
    
    /// 主题颜色
    let themeColors: [Color]
    
    /// 粒子数量
    let particleCount: Int
    
    /// 粒子状态
    @State private var particles: [ParticleState] = []
    
    /// 初始化方法
    public init(isShowing: Binding<Bool>, themeColors: [Color], particleCount: Int = 30) {
        self._isShowing = isShowing
        self.themeColors = themeColors
        self.particleCount = particleCount
    }
    
    public var body: some View {
        ZStack {
            ForEach(particles.indices, id: \.self) { index in
                Circle()
                    .fill(themeColors[index % themeColors.count])
                    .frame(width: particles[index].size, height: particles[index].size)
                    .position(particles[index].position)
                    .opacity(particles[index].opacity)
            }
        }
        .onAppear {
            if isShowing {
                generateParticles()
                animateParticles()
            }
        }
        .onChange(of: isShowing) { newValue in
            if newValue {
                generateParticles()
                animateParticles()
            }
        }
    }
    
    /// 生成粒子
    private func generateParticles() {
        particles = (0..<particleCount).map { _ in
            ParticleState(
                position: CGPoint(x: UIScreen.main.bounds.width / 2, y: UIScreen.main.bounds.height / 2),
                finalPosition: CGPoint(
                    x: CGFloat.random(in: 0...UIScreen.main.bounds.width),
                    y: CGFloat.random(in: 0...UIScreen.main.bounds.height)
                ),
                size: CGFloat.random(in: 3...8),
                opacity: 1
            )
        }
    }
    
    /// 动画粒子
    private func animateParticles() {
        for index in particles.indices {
            withAnimation(.easeOut(duration: Double.random(in: 0.5...1.5))) {
                particles[index].position = particles[index].finalPosition
            }
            
            withAnimation(.easeInOut(duration: Double.random(in: 0.5...1.5)).delay(0.5)) {
                particles[index].opacity = 0
            }
        }
        
        // 动画结束后隐藏
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            isShowing = false
        }
    }
}

/// 粒子状态
struct ParticleState {
    var position: CGPoint
    var finalPosition: CGPoint
    var size: CGFloat
    var opacity: Double
}
