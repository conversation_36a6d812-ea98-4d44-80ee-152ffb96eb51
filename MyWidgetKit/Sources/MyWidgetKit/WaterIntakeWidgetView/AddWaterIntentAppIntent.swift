import AppIntents
import WidgetKit
import Swift<PERSON>

@available(iOS 16.0, *)
struct AddWaterIntentAppIntent: AppIntent {
    static var title: LocalizedStringResource { "添加水分摄入" }
    static var description: IntentDescription { IntentDescription("记录水分摄入量") }

    @Parameter(title: "水量(ml)")
    var amount: Double

    init() {
        self.amount = 0
    }

    init(amount: Double) {
        self.amount = amount
    }

    func perform() async throws -> some IntentResult {
        // 1. 更新保存的数据
        if var config = AppGroupDataManager.shared.read(WaterIntakeWidgetData.self, for: .waterIntake, property: .config) {
            config.currentIntake += amount
            config.lastUpdated = Date()
            AppGroupDataManager.shared.save(config, for: .waterIntake, property: .config)

            // 2. 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
 

            return .result()
        }

        throw WaterIntakeError.updateFailed("无法更新水分摄入数据")
    }
}

// 定义水分摄入错误类型
@available(iOS 16.0, *)
enum WaterIntakeError: Error {
    case updateFailed(String)

    var localizedDescription: String {
        switch self {
        case .updateFailed(let message):
            return message
        }
    }
}
