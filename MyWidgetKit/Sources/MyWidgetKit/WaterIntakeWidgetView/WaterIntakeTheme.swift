import SwiftUI

/// 水分摄入追踪器主题
public enum WaterIntakeTheme: String, CaseIterable, Codable {
    /// 海洋蓝
    case oceanBlue = "oceanBlue"
    /// 活力橙
    case energyOrange = "energyOrange"
    /// 清新绿
    case freshGreen = "freshGreen"
    /// 紫罗兰
    case violet = "violet"
    /// 樱花粉
    case cherryBlossom = "cherryBlossom"
    
    /// 获取主题颜色
    public var accentColor: WidgetColor {
        switch self {
        case .oceanBlue:
            return WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1)
        case .energyOrange:
            return WidgetColor(red: 1, green: 0.6, blue: 0, alpha: 1)
        case .freshGreen:
            return WidgetColor(red: 0.2, green: 0.8, blue: 0.4, alpha: 1)
        case .violet:
            return WidgetColor(red: 0.6, green: 0.2, blue: 0.8, alpha: 1)
        case .cherryBlossom:
            return WidgetColor(red: 1, green: 0.4, blue: 0.6, alpha: 1)
        }
    }
    
    /// 获取主题渐变色
    public var gradientColors: [Color] {
        switch self {
        case .oceanBlue:
            return [Color(red: 0, green: 0.7, blue: 1), Color(red: 0, green: 0.3, blue: 0.8)]
        case .energyOrange:
            return [Color(red: 1, green: 0.8, blue: 0), Color(red: 1, green: 0.4, blue: 0)]
        case .freshGreen:
            return [Color(red: 0.4, green: 0.9, blue: 0.4), Color(red: 0, green: 0.7, blue: 0.3)]
        case .violet:
            return [Color(red: 0.8, green: 0.3, blue: 1), Color(red: 0.4, green: 0.1, blue: 0.6)]
        case .cherryBlossom:
            return [Color(red: 1, green: 0.6, blue: 0.8), Color(red: 0.9, green: 0.2, blue: 0.4)]
        }
    }
    
    /// 获取主题名称
    public var displayName: String {
        switch self {
        case .oceanBlue:
            return "海洋蓝"
        case .energyOrange:
            return "活力橙"
        case .freshGreen:
            return "清新绿"
        case .violet:
            return "紫罗兰"
        case .cherryBlossom:
            return "樱花粉"
        }
    }
    
    /// 获取主题图标名称
    public var iconName: String {
        switch self {
        case .oceanBlue:
            return "drop.fill"
        case .energyOrange:
            return "flame.fill"
        case .freshGreen:
            return "leaf.fill"
        case .violet:
            return "sparkles"
        case .cherryBlossom:
            return "heart.fill"
        }
    }
    
    /// 获取主题对应的波浪颜色
    public var waveColors: [Color] {
        switch self {
        case .oceanBlue:
            return [
                Color(red: 0, green: 0.6, blue: 1, opacity: 0.3),
                Color(red: 0, green: 0.5, blue: 0.9, opacity: 0.5),
                Color(red: 0, green: 0.4, blue: 0.8, opacity: 0.7)
            ]
        case .energyOrange:
            return [
                Color(red: 1, green: 0.7, blue: 0, opacity: 0.3),
                Color(red: 1, green: 0.6, blue: 0, opacity: 0.5),
                Color(red: 1, green: 0.5, blue: 0, opacity: 0.7)
            ]
        case .freshGreen:
            return [
                Color(red: 0.3, green: 0.8, blue: 0.4, opacity: 0.3),
                Color(red: 0.2, green: 0.7, blue: 0.3, opacity: 0.5),
                Color(red: 0.1, green: 0.6, blue: 0.2, opacity: 0.7)
            ]
        case .violet:
            return [
                Color(red: 0.7, green: 0.3, blue: 0.9, opacity: 0.3),
                Color(red: 0.6, green: 0.2, blue: 0.8, opacity: 0.5),
                Color(red: 0.5, green: 0.1, blue: 0.7, opacity: 0.7)
            ]
        case .cherryBlossom:
            return [
                Color(red: 1, green: 0.5, blue: 0.7, opacity: 0.3),
                Color(red: 0.9, green: 0.4, blue: 0.6, opacity: 0.5),
                Color(red: 0.8, green: 0.3, blue: 0.5, opacity: 0.7)
            ]
        }
    }
}

/// 扩展 WidgetColor 以支持主题
extension WidgetColor {
    /// 从主题创建颜色
    public static func fromTheme(_ theme: WaterIntakeTheme) -> WidgetColor {
        return theme.accentColor
    }
}
