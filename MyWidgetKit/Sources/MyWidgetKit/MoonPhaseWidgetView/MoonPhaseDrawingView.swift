import SwiftUI

/// 自定义月相绘制视图
public struct MoonPhaseDrawingView: View {
    /// 月相类型
    private let phase: MoonPhaseType

    /// 月相百分比
    private let percentage: Double

    /// 尺寸
    private let size: CGFloat

    /// 颜色
    private let color: Color

    /// 初始化方法
    public init(phase: MoonPhaseType, percentage: Double, size: CGFloat, color: Color) {
        self.phase = phase
        self.percentage = percentage
        self.size = size
        self.color = color
    }

    public var body: some View {
        ZStack {
            // 背景圆 - 使用完全透明的背景
            Circle()
                .fill(Color.clear)
                .frame(width: size, height: size)

            // 月相绘制
            moonPhaseShape
                .frame(width: size * 0.8, height: size * 0.8)
                .clipShape(Circle()) // 确保内容不会溢出圆形边界
        }
        .compositingGroup() // 确保整个组合被视为一个单元
        .drawingGroup() // 使用Metal渲染以提高性能
    }

    /// 月相形状
    @ViewBuilder
    private var moonPhaseShape: some View {
        switch phase {
        case .newMoon:
            // 新月 - 完全黑色圆形
            ZStack {
                Circle()
                    .fill(Color.black)
                Circle()
                    .stroke(color, lineWidth: 1)
            }

        case .waxingCrescent:
            // 蛾眉月 - 右侧为圆弧
            MoonCrescentShape(waxing: true, percentage: percentage / 100.0)
                .fill(color)
                .overlay(
                    Circle()
                        .stroke(color, lineWidth: 1)
                )

        case .firstQuarter:
            // 上弦月 - 右半圆
            HalfMoonShape(waxing: true)
                .fill(color)
                .overlay(
                    Circle()
                        .stroke(color, lineWidth: 1)
                )

        case .waxingGibbous:
            // 盈凸月 - 大部分可见，左侧为圆弧
            MoonGibbousShape(waxing: true, percentage: percentage / 100.0)
                .fill(color)
                .overlay(
                    Circle()
                        .stroke(color, lineWidth: 1)
                )

        case .fullMoon:
            // 满月 - 完全圆形
            Circle()
                .fill(color)

        case .waningGibbous:
            // 亏凸月 - 大部分可见，右侧为圆弧
            MoonGibbousShape(waxing: false, percentage: percentage / 100.0)
                .fill(color)
                .overlay(
                    Circle()
                        .stroke(color, lineWidth: 1)
                )

        case .lastQuarter:
            // 下弦月 - 左半圆
            HalfMoonShape(waxing: false)
                .fill(color)
                .overlay(
                    Circle()
                        .stroke(color, lineWidth: 1)
                )

        case .waningCrescent:
            // 残月 - 左侧为圆弧
            MoonCrescentShape(waxing: false, percentage: percentage / 100.0)
                .fill(color)
                .overlay(
                    Circle()
                        .stroke(color, lineWidth: 1)
                )
        }
    }
}

/// 半月形状
struct HalfMoonShape: Shape {
    /// 是否为盈月（右半圆）
    let waxing: Bool

    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2

        // 绘制半圆
        if waxing {
            // 上弦月 - 右半圆
            path.addArc(
                center: center,
                radius: radius,
                startAngle: Angle(degrees: -90),
                endAngle: Angle(degrees: 90),
                clockwise: false
            )
            path.addLine(to: center)
            path.closeSubpath()
        } else {
            // 下弦月 - 左半圆
            path.addArc(
                center: center,
                radius: radius,
                startAngle: Angle(degrees: 90),
                endAngle: Angle(degrees: 270),
                clockwise: false
            )
            path.addLine(to: center)
            path.closeSubpath()
        }

        return path
    }
}

/// 月牙形状
struct MoonCrescentShape: Shape {
    /// 是否为盈月
    let waxing: Bool

    /// 填充百分比
    let percentage: Double

    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2

        // 计算偏移量 (0.0-0.5范围)
        let normalizedPercentage = max(0.0, min(0.5, percentage))
        let offset = radius * (1.0 - normalizedPercentage * 2.0)

        // 计算控制点
        let offsetX = waxing ? -offset : offset

        // 绘制月牙形状
        // 外圆弧
        path.addArc(
            center: center,
            radius: radius,
            startAngle: Angle(degrees: 90),
            endAngle: Angle(degrees: 270),
            clockwise: false
        )

        // 内圆弧（反方向）
        path.addArc(
            center: CGPoint(x: center.x + offsetX, y: center.y),
            radius: radius,
            startAngle: Angle(degrees: 270),
            endAngle: Angle(degrees: 90),
            clockwise: true
        )

        path.closeSubpath()
        return path
    }
}

/// 凸月形状
struct MoonGibbousShape: Shape {
    /// 是否为盈月
    let waxing: Bool

    /// 填充百分比
    let percentage: Double

    func path(in rect: CGRect) -> Path {
        var path = Path()
        let center = CGPoint(x: rect.midX, y: rect.midY)
        let radius = min(rect.width, rect.height) / 2

        // 计算偏移量 (0.5-1.0范围)
        let normalizedPercentage = max(0.5, min(1.0, percentage))
        let offset = radius * (2.0 - normalizedPercentage * 2.0)

        // 计算控制点
        let offsetX = waxing ? offset : -offset

        // 绘制凸月形状
        // 外圆弧（半圆）
        path.addArc(
            center: center,
            radius: radius,
            startAngle: Angle(degrees: 270),
            endAngle: Angle(degrees: 90),
            clockwise: false
        )

        // 内圆弧（反方向）
        path.addArc(
            center: CGPoint(x: center.x + offsetX, y: center.y),
            radius: radius,
            startAngle: Angle(degrees: 90),
            endAngle: Angle(degrees: 270),
            clockwise: true
        )

        path.closeSubpath()
        return path
    }
}

#Preview {
    VStack {
        HStack {
            MoonPhaseDrawingView(phase: .newMoon, percentage: 0, size: 60, color: .white)
            MoonPhaseDrawingView(phase: .waxingCrescent, percentage: 25, size: 60, color: .white)
            MoonPhaseDrawingView(phase: .firstQuarter, percentage: 50, size: 60, color: .white)
            MoonPhaseDrawingView(phase: .waxingGibbous, percentage: 75, size: 60, color: .white)
        }
        HStack {
            MoonPhaseDrawingView(phase: .fullMoon, percentage: 100, size: 60, color: .white)
            MoonPhaseDrawingView(phase: .waningGibbous, percentage: 75, size: 60, color: .white)
            MoonPhaseDrawingView(phase: .lastQuarter, percentage: 50, size: 60, color: .white)
            MoonPhaseDrawingView(phase: .waningCrescent, percentage: 25, size: 60, color: .white)
        }
    }
    .padding()
    .background(Color.black)
}
