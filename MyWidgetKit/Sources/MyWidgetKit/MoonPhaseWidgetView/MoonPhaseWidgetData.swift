import SwiftUI
import WidgetKit

/// 月相类型
public enum MoonPhaseType: String, Codable, CaseIterable {
    case newMoon = "新月"
    case waxingCrescent = "蛾眉月"
    case firstQuarter = "上弦月"
    case waxingGibbous = "盈凸月"
    case fullMoon = "满月"
    case waningGibbous = "亏凸月"
    case lastQuarter = "下弦月"
    case waningCrescent = "残月"

    /// 获取月相图标名称
    public var iconName: String {
        switch self {
        case .newMoon: return "moon.circle"
        case .waxingCrescent: return "moon.circle.fill"
        case .firstQuarter: return "moon.fill"
        case .waxingGibbous: return "moon.fill"
        case .fullMoon: return "circle.fill"
        case .waningGibbous: return "moon.fill"
        case .lastQuarter: return "moon.fill"
        case .waningCrescent: return "moon.circle.fill"
        }
    }

    /// 获取月相填充比例（用于自定义绘制）
    public var fillPercentage: Double {
        switch self {
        case .newMoon: return 0.0
        case .waxingCrescent: return 0.25
        case .firstQuarter: return 0.5
        case .waxingGibbous: return 0.75
        case .fullMoon: return 1.0
        case .waningGibbous: return 0.75
        case .lastQuarter: return 0.5
        case .waningCrescent: return 0.25
        }
    }
}

/// 月相显示模式
public enum MoonPhaseDisplayMode: String, Codable, CaseIterable {
    case simple = "简约模式"
    case detailed = "详细模式"
}

/// 月相日历小组件数据模型
public struct MoonPhaseWidgetData: WidgetPreviewableData, Codable, Hashable {
    /// 背景设置
    public var background: WidgetBackground

    /// 字体颜色
    public var fontColor: WidgetColor

    /// 字体名称
    public var fontName: String

    /// 显示模式
    public var displayMode: MoonPhaseDisplayMode

    /// 是否显示农历日期
    public var showLunarDate: Bool

    /// 初始化方法
    public init(
        background: WidgetBackground = .color(WidgetColor(red: 0.05, green: 0.05, blue: 0.2, alpha: 1)),
        fontColor: WidgetColor = WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
        fontName: String = "",
        displayMode: MoonPhaseDisplayMode = .detailed,
        showLunarDate: Bool = true
    ) {
        self.background = background
        self.fontColor = fontColor
        self.fontName = fontName
        self.displayMode = displayMode
        self.showLunarDate = showLunarDate
    }

    // MARK: - Hashable

    /// 实现Hashable协议的hash方法
    public func hash(into hasher: inout Hasher) {
        // 由于WidgetBackground没有实现Hashable，我们使用自定义的哈希逻辑
        switch background {
        case let .color(color):
            hasher.combine("color")
            hasher.combine(color.red)
            hasher.combine(color.green)
            hasher.combine(color.blue)
            hasher.combine(color.alpha)
        case let .imageData(data):
            hasher.combine("imageData")
            hasher.combine(data.count) // 使用数据长度作为哈希值的一部分
        case let .imageURL(url):
            hasher.combine("imageURL")
            hasher.combine(url)
        case let .imageFile(path):
            hasher.combine("imageFile")
            hasher.combine(path)
        case let .packageImage(name):
            hasher.combine("packageImage")
            hasher.combine(name)
        }

        // 其他属性都是Hashable的，直接组合
        hasher.combine(fontColor.red)
        hasher.combine(fontColor.green)
        hasher.combine(fontColor.blue)
        hasher.combine(fontColor.alpha)
        hasher.combine(fontName)
        hasher.combine(displayMode)
        hasher.combine(showLunarDate)
    }
}

/// 月相日历小组件入口
public struct MoonPhaseWidgetEntry: TimelineEntry {
    /// 日期
    public let date: Date

    /// 配置数据
    public let configuration: MoonPhaseWidgetData

    /// 初始化方法
    public init(date: Date, configuration: MoonPhaseWidgetData) {
        self.date = date
        self.configuration = configuration
    }
}

/// 月相计算工具类
public class MoonPhaseCalculator {
    /// 计算指定日期的月相
    /// - Parameter date: 日期
    /// - Returns: 月相类型和月相百分比
    public static func calculateMoonPhase(for date: Date) -> (phase: MoonPhaseType, percentage: Double) {
        // 使用天文算法计算月相
        // 这里使用简化的算法，实际应用中可以使用更精确的天文算法

        // 1. 计算自2000年1月1日以来的天数
        let calendar = Calendar(identifier: .gregorian)
        let components = calendar.dateComponents([.year, .month, .day], from: date)
        let year = components.year!
        let month = components.month!
        let day = components.day!

        // 2. 使用简化的月相计算公式
        var c: Double = 0
        var e: Double = 0
        var jd: Double = 0

        if month <= 2 {
            c = Double(year - 1)
            e = Double(month + 12)
        } else {
            c = Double(year)
            e = Double(month)
        }

        jd = Double(365.25 * c) + Double(30.6001 * (e + 1)) + Double(day) + 1720994.5

        // 3. 计算月相角度
        let daysSinceNewMoon = (jd - 2451549.5) / 29.53
        let newMoons = daysSinceNewMoon - floor(daysSinceNewMoon)
        let angle = newMoons * 360 // 月相角度（0-360度）

        // 4. 计算月相百分比（0-100%）
        var percentage = 0.0
        if angle <= 180 {
            // 新月到满月
            percentage = angle / 180.0 * 100.0
        } else {
            // 满月到新月
            percentage = (360.0 - angle) / 180.0 * 100.0
        }

        // 5. 确定月相类型
        let phase: MoonPhaseType
        if angle < 22.5 {
            phase = .newMoon
        } else if angle < 67.5 {
            phase = .waxingCrescent
        } else if angle < 112.5 {
            phase = .firstQuarter
        } else if angle < 157.5 {
            phase = .waxingGibbous
        } else if angle < 202.5 {
            phase = .fullMoon
        } else if angle < 247.5 {
            phase = .waningGibbous
        } else if angle < 292.5 {
            phase = .lastQuarter
        } else if angle < 337.5 {
            phase = .waningCrescent
        } else {
            phase = .newMoon
        }

        return (phase, percentage)
    }

    /// 计算下一个满月日期
    /// - Parameter date: 当前日期
    /// - Returns: 下一个满月日期
    public static func nextFullMoon(after date: Date) -> Date {
        // 简化计算，实际应用中可以使用更精确的天文算法
        let (phase, percentage) = calculateMoonPhase(for: date)

        // 估算距离下一个满月的天数
        var daysToFullMoon: Double = 0

        switch phase {
        case .newMoon:
            daysToFullMoon = 14.5
        case .waxingCrescent:
            daysToFullMoon = 10.5
        case .firstQuarter:
            daysToFullMoon = 7.5
        case .waxingGibbous:
            daysToFullMoon = 3.5
        case .fullMoon:
            daysToFullMoon = 29.5
        case .waningGibbous:
            daysToFullMoon = 25.5
        case .lastQuarter:
            daysToFullMoon = 22.5
        case .waningCrescent:
            daysToFullMoon = 18.5
        }

        return Calendar.current.date(byAdding: .day, value: Int(daysToFullMoon), to: date)!
    }

    /// 计算下一个新月日期
    /// - Parameter date: 当前日期
    /// - Returns: 下一个新月日期
    public static func nextNewMoon(after date: Date) -> Date {
        // 简化计算，实际应用中可以使用更精确的天文算法
        let (phase, percentage) = calculateMoonPhase(for: date)

        // 估算距离下一个新月的天数
        var daysToNewMoon: Double = 0

        switch phase {
        case .newMoon:
            daysToNewMoon = 29.5
        case .waxingCrescent:
            daysToNewMoon = 25.5
        case .firstQuarter:
            daysToNewMoon = 22.5
        case .waxingGibbous:
            daysToNewMoon = 18.5
        case .fullMoon:
            daysToNewMoon = 14.5
        case .waningGibbous:
            daysToNewMoon = 10.5
        case .lastQuarter:
            daysToNewMoon = 7.5
        case .waningCrescent:
            daysToNewMoon = 3.5
        }

        return Calendar.current.date(byAdding: .day, value: Int(daysToNewMoon), to: date)!
    }
}
