import SwiftUI
import WidgetKit

/// 月相日历小组件视图
public struct MoonPhaseWidgetView: View {
    /// 配置数据
    private let data: MoonPhaseWidgetData

    /// 当前日期
    private let currentDate: Date

    /// 日期格式化器
    private let dateFormatter = DateFormatter()

    /// 农历日期格式化器
    private let lunarDateFormatter = DateFormatter()

    /// 是否在小组件环境中
    @Environment(\.isWidgetEnvironment) private var isWidgetEnvironment

    /// 当前月相信息
    private var moonPhaseInfo: (phase: MoonPhaseType, percentage: Double) {
        MoonPhaseCalculator.calculateMoonPhase(for: currentDate)
    }

    /// 下一个满月日期
    private var nextFullMoonDate: Date {
        MoonPhaseCalculator.nextFullMoon(after: currentDate)
    }

    /// 下一个新月日期
    private var nextNewMoonDate: Date {
        MoonPhaseCalculator.nextNewMoon(after: currentDate)
    }

    /// 初始化方法
    public init(data: MoonPhaseWidgetData, date: Date = Date()) {
        self.data = data
        self.currentDate = date

        // 配置日期格式化器
        dateFormatter.dateFormat = "yyyy年MM月dd日 EEEE"
        dateFormatter.locale = Locale(identifier: "zh_CN")

        // 配置农历日期格式化器
        lunarDateFormatter.dateFormat = "yyyy年MM月dd日"
        lunarDateFormatter.calendar = Calendar(identifier: .chinese)
        lunarDateFormatter.locale = Locale(identifier: "zh_CN")
    }

    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景 - 先设置一个统一的黑色背景
                Color.black
                    .frame(width: geometry.size.width, height: geometry.size.height)

               

                // 半透明背景覆盖层，提高文本可读性
                Rectangle()
                    .fill(Color.black.opacity(0.3))
                    .frame(width: geometry.size.width, height: geometry.size.height)

                // 内容
                VStack(spacing: 8) {
                    // 根据显示模式选择不同的布局
                    if data.displayMode == .simple {
                        simpleLayout(geometry: geometry)
                    } else {
                        detailedLayout(geometry: geometry)
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            }
            .adaptiveBackground(content: {
                // 背景图片或颜色
                Group {
                    switch data.background {
                    case let .color(widgetColor):
                        widgetColor.toColor()
                    case let .imageData(data):
                        if let image = UIImage(data: data) {
                            Image(uiImage: image)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } else {
                            Color.black
                        }
                    case let .imageFile(path):
                        if let image = UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) {
                            Image(uiImage: image)
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                        } else {
                            Color.black
                        }
                    case let .imageURL(url):
                        if #available(iOS 15.0, *) {
                            AsyncImage(url: URL(string: url)) { phase in
                                if let image = phase.image {
                                    image
                                        .resizable()
                                        .aspectRatio(contentMode: .fill)
                                } else {
                                    Color.black
                                }
                            }
                        } else {
                            Color.black
                        }
                    case let .packageImage(name):
                        Image(name, bundle: .module)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    }
                }
                .frame(width: geometry.size.width, height: geometry.size.height)
                .clipped()
            })
            .clipShape(RoundedRectangle(cornerRadius: isWidgetEnvironment ? 0 : 16))
            .background(Color.black) // 确保背景是黑色
        }
    }

    /// 简约布局
    private func simpleLayout(geometry: GeometryProxy) -> some View {
        VStack(spacing: 12) {
            // 月相图标
            moonPhaseIcon(size: min(geometry.size.width, geometry.size.height) * 0.4)

            // 月相名称和百分比
            VStack(spacing: 4) {
                Text(moonPhaseInfo.phase.rawValue)
                    .font(customFont(size: titleTextSize(for: geometry.size)))
                    .foregroundColor(data.fontColor.toColor())

                Text("满月度: \(Int(moonPhaseInfo.percentage))%")
                    .font(customFont(size: subtitleTextSize(for: geometry.size)))
                    .foregroundColor(data.fontColor.toColor().opacity(0.8))
            }

            // 当前日期
            Text(dateFormatter.string(from: currentDate))
                .font(customFont(size: dateTextSize(for: geometry.size)))
                .foregroundColor(data.fontColor.toColor().opacity(0.7))
                .lineLimit(1)
                .minimumScaleFactor(0.7)

            // 农历日期（可选）
            if data.showLunarDate {
                Text("农历 \(lunarDateFormatter.string(from: currentDate))")
                    .font(customFont(size: dateTextSize(for: geometry.size) * 0.9))
                    .foregroundColor(data.fontColor.toColor().opacity(0.6))
                    .lineLimit(1)
                    .minimumScaleFactor(0.7)
            }
        }
    }

    /// 详细布局
    private func detailedLayout(geometry: GeometryProxy) -> some View {
        VStack(spacing: 10) {
            // 顶部日期信息
            HStack {
                VStack(alignment: .leading, spacing: 2) {
                    Text(dateFormatter.string(from: currentDate))
                        .font(customFont(size: dateTextSize(for: geometry.size)))
                        .foregroundColor(data.fontColor.toColor())
                        .lineLimit(1)
                        .minimumScaleFactor(0.7)

                    if data.showLunarDate {
                        Text("农历 \(lunarDateFormatter.string(from: currentDate))")
                            .font(customFont(size: dateTextSize(for: geometry.size) * 0.8))
                            .foregroundColor(data.fontColor.toColor().opacity(0.7))
                            .lineLimit(1)
                            .minimumScaleFactor(0.7)
                    }
                }
                Spacer()
            }

            // 月相图标和信息
            HStack(spacing: 15) {
                // 月相图标
                moonPhaseIcon(size: min(geometry.size.width, geometry.size.height) * 0.3)

                // 月相信息
                VStack(alignment: .leading, spacing: 4) {
                    Text(moonPhaseInfo.phase.rawValue)
                        .font(customFont(size: titleTextSize(for: geometry.size)))
                        .foregroundColor(data.fontColor.toColor())

                    Text("满月度: \(Int(moonPhaseInfo.percentage))%")
                        .font(customFont(size: subtitleTextSize(for: geometry.size)))
                        .foregroundColor(data.fontColor.toColor().opacity(0.8))
                }

                Spacer()
            }

            Divider()
                .background(data.fontColor.toColor().opacity(0.3))
                .padding(.vertical, 4)

            // 下一个满月和新月信息
            VStack(alignment: .leading, spacing: 6) {
                // 下一个满月
                HStack(spacing: 8) {
                    // 满月图标
                    MoonPhaseDrawingView(
                        phase: .fullMoon,
                        percentage: 100,
                        size: subtitleTextSize(for: geometry.size) * 2,
                        color: data.fontColor.toColor()
                    )

                    VStack(alignment: .leading, spacing: 2) {
                        Text("下一个满月")
                            .font(customFont(size: subtitleTextSize(for: geometry.size) * 0.9))
                            .foregroundColor(data.fontColor.toColor().opacity(0.8))

                        Text(dateFormatter.string(from: nextFullMoonDate))
                            .font(customFont(size: dateTextSize(for: geometry.size) * 0.9))
                            .foregroundColor(data.fontColor.toColor().opacity(0.7))
                            .lineLimit(1)
                    }

                    Spacer()

                    // 剩余天数
                    let daysToFullMoon = Calendar.current.dateComponents([.day], from: currentDate, to: nextFullMoonDate).day ?? 0
                    Text("\(daysToFullMoon)天")
                        .font(customFont(size: subtitleTextSize(for: geometry.size)))
                        .foregroundColor(data.fontColor.toColor())
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(data.fontColor.toColor().opacity(0.2))
                        .cornerRadius(8)
                }

                // 下一个新月
                HStack(spacing: 8) {
                    // 新月图标
                    MoonPhaseDrawingView(
                        phase: .newMoon,
                        percentage: 0,
                        size: subtitleTextSize(for: geometry.size) * 2,
                        color: data.fontColor.toColor()
                    )

                    VStack(alignment: .leading, spacing: 2) {
                        Text("下一个新月")
                            .font(customFont(size: subtitleTextSize(for: geometry.size) * 0.9))
                            .foregroundColor(data.fontColor.toColor().opacity(0.8))

                        Text(dateFormatter.string(from: nextNewMoonDate))
                            .font(customFont(size: dateTextSize(for: geometry.size) * 0.9))
                            .foregroundColor(data.fontColor.toColor().opacity(0.7))
                            .lineLimit(1)
                    }

                    Spacer()

                    // 剩余天数
                    let daysToNewMoon = Calendar.current.dateComponents([.day], from: currentDate, to: nextNewMoonDate).day ?? 0
                    Text("\(daysToNewMoon)天")
                        .font(customFont(size: subtitleTextSize(for: geometry.size)))
                        .foregroundColor(data.fontColor.toColor())
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(data.fontColor.toColor().opacity(0.2))
                        .cornerRadius(8)
                }
            }

            // 月相变化图（仅在大尺寸小组件中显示）
            if geometry.size.height > 300 {
                moonPhaseChart(width: geometry.size.width - 40)
            }
        }
    }

    /// 月相图标
    private func moonPhaseIcon(size: CGFloat) -> some View {
        // 使用自定义月相绘制视图
        MoonPhaseDrawingView(
            phase: moonPhaseInfo.phase,
            percentage: moonPhaseInfo.percentage,
            size: size,
            color: data.fontColor.toColor()
        )
    }

    /// 月相变化图
    private func moonPhaseChart(width: CGFloat) -> some View {
        HStack(spacing: width / 8) {
            ForEach(0..<7) { index in
                let futureDate = Calendar.current.date(bySetting: .day, value: index + 1, of: currentDate)!
                let futurePhase = MoonPhaseCalculator.calculateMoonPhase(for: futureDate)

                VStack(spacing: 4) {
                    // 日期
                    Text("\(Calendar.current.component(.day, from: futureDate))日")
                        .font(customFont(size: 10))
                        .foregroundColor(data.fontColor.toColor().opacity(0.7))

                    // 月相图标 - 使用自定义月相绘制视图
                    MoonPhaseDrawingView(
                        phase: futurePhase.phase,
                        percentage: futurePhase.percentage,
                        size: 28,
                        color: data.fontColor.toColor()
                    )
                }
            }
        }
        .padding(.top, 8)
    }

    /// 获取自定义字体
    private func customFont(size: CGFloat) -> Font {
        if !data.fontName.isEmpty {
            return Font.custom(data.fontName, size: size)
        } else {
            return Font.system(size: size, weight: .medium, design: .rounded)
        }
    }

    /// 根据容器大小计算标题文本大小
    private func titleTextSize(for size: CGSize) -> CGFloat {
        let minDimension = min(size.width, size.height)

        // 根据容器大小调整字体大小
        if minDimension < 120 { // 小尺寸
            return 18
        } else if minDimension < 200 { // 中尺寸
            return 24
        } else { // 大尺寸
            return 30
        }
    }

    /// 根据容器大小计算副标题文本大小
    private func subtitleTextSize(for size: CGSize) -> CGFloat {
        let minDimension = min(size.width, size.height)

        // 根据容器大小调整字体大小
        if minDimension < 120 { // 小尺寸
            return 14
        } else if minDimension < 200 { // 中尺寸
            return 18
        } else { // 大尺寸
            return 22
        }
    }

    /// 根据容器大小计算日期文本大小
    private func dateTextSize(for size: CGSize) -> CGFloat {
        let minDimension = min(size.width, size.height)

        // 根据容器大小调整字体大小
        if minDimension < 120 { // 小尺寸
            return 10
        } else if minDimension < 200 { // 中尺寸
            return 14
        } else { // 大尺寸
            return 16
        }
    }
}
