import SwiftUI
import WidgetKit

/// 月相日历小组件的Timeline Provider
public struct MoonPhaseWidgetProvider: TimelineProvider {
    /// 默认配置
    private let defaultConfig = MoonPhaseWidgetData()
    
    /// 获取占位条目
    public func placeholder(in context: Context) -> MoonPhaseWidgetEntry {
        MoonPhaseWidgetEntry(date: Date(), configuration: defaultConfig)
    }
    
    /// 获取快照条目
    public func getSnapshot(in context: Context, completion: @escaping (MoonPhaseWidgetEntry) -> Void) {
        // 尝试从AppGroup读取配置
        let config = AppGroupDataManager.shared.read(MoonPhaseWidgetData.self, for: .moonPhase, property: .config) ?? defaultConfig
        
        // 创建条目
        let entry = MoonPhaseWidgetEntry(date: Date(), configuration: config)
        completion(entry)
    }
    
    /// 获取时间线
    public func getTimeline(in context: Context, completion: @escaping (Timeline<MoonPhaseWidgetEntry>) -> Void) {
        // 尝试从AppGroup读取配置
        let config = AppGroupDataManager.shared.read(MoonPhaseWidgetData.self, for: .moonPhase, property: .config) ?? defaultConfig
        
        // 创建时间线条目
        var entries: [MoonPhaseWidgetEntry] = []
        
        // 当前日期
        let currentDate = Date()
        
        // 创建未来24小时的条目，每小时更新一次
        for hourOffset in 0..<24 {
            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
            let entry = MoonPhaseWidgetEntry(date: entryDate, configuration: config)
            entries.append(entry)
        }
        
        // 创建时间线
        let timeline = Timeline(entries: entries, policy: .atEnd)
        completion(timeline)
    }
}

/// 月相日历小组件
public struct MoonPhaseWidget: Widget {
    /// 小组件配置
    private let kind: String = "MoonPhaseWidget"
    
    /// 小组件支持的尺寸
    private let supportedFamilies: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]
    
    /// 初始化方法
    public init() {}
    
    /// 小组件配置
    public var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: MoonPhaseWidgetProvider()) { entry in
            MoonPhaseWidgetView(data: entry.configuration, date: entry.date)
        }
        .configurationDisplayName("月相日历")
        .description("精确显示月相变化，预测满月和新月日期")
        .supportedFamilies(supportedFamilies)
    }
}
