//
//  UIImage++.swift
//  MyWidgetKit
//
//  Created by y<PERSON><PERSON><PERSON> on 2025/5/11.
//

import SwiftUI
import UIKit

public extension UIImage {
    /// 转换为Data
    func toData() -> Data? {
        return pngData()
    }

    /// 循环压缩图片使其在 Widget 中显示, 并且使其文件大小小于 60KB
    func compressForWidget(targetFileSize: Int = 60 * 1024) -> Data? {
        var image = self
        guard var data = image.pngData() else { return nil }
        var compression: CGFloat = 1.0
        while data.count > targetFileSize {
            compression *= 0.9
            if let resizedImage = image.resized(to: CGSize(width: image.size.width * compression, height: image.size.height * compression)) {
                if let resizedData = resizedImage.pngData() {
                    data = resizedData
                } else {
                    return nil
                }
            } else {
                return nil
            }
        }
        return data
    }

    internal func resized(to size: CGSize, preserveAspectRatio: Bool = false) -> UIImage? {
        if preserveAspectRatio {
            let aspectRatio = self.size.width / self.size.height
            var newSize = size

            if size.width / size.height > aspectRatio {
                // 宽度受限
                newSize.width = size.height * aspectRatio
            } else {
                // 高度受限
                newSize.height = size.width / aspectRatio
            }

            UIGraphicsBeginImageContextWithOptions(newSize, false, 1)
            draw(in: CGRect(origin: .zero, size: newSize))
        } else {
            UIGraphicsBeginImageContextWithOptions(size, true, 1)
            UIColor.white.setFill()
            UIRectFill(CGRect(origin: .zero, size: size))
            draw(in: CGRect(origin: .zero, size: size))
        }

        let result = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()
        return result
    }

    private func resizedToFit(maxArea: CGFloat, image: UIImage, preserveAspectRatio: Bool = true) -> UIImage? {
        let currentSize = image.size
        let currentArea = currentSize.width * currentSize.height
        guard currentArea > maxArea else {
            return image // 原图就满足条件
        }

        let scale = sqrt(maxArea / currentArea)
        let newSize = CGSize(width: currentSize.width * scale, height: currentSize.height * scale)
        return image.resized(to: newSize, preserveAspectRatio: preserveAspectRatio)
    }

    /// 优化的 Widget 图片压缩方法，使用自适应压缩策略
    func compressForWidgetOptimized(
        targetFileSize: Int = 60 * 1024,
        minQuality: CGFloat = 0.4,
        minSize: CGSize = CGSize(width: 338, height: 338),
        maxPixelArea: CGFloat = 988_574.4,
        preserveAspectRatio: Bool = true
    ) -> Data? {
        // 1. 首先控制最大像素面积
        var image = self
        if let resized = resizedToFit(maxArea: maxPixelArea, image: self, preserveAspectRatio: preserveAspectRatio) {
            image = resized
        }

        // 2. 使用二分法查找最佳压缩质量，比线性递减更高效
        var minQ: CGFloat = minQuality
        var maxQ: CGFloat = 1.0
        var bestData: Data?
        var bestQuality: CGFloat = 1.0

        // 最多尝试8次二分查找
        for _ in 0 ..< 8 {
            let midQ = (minQ + maxQ) / 2
            if let data = image.jpegData(compressionQuality: midQ) {
                if data.count <= targetFileSize {
                    // 当前质量可接受，尝试提高质量
                    bestData = data
                    bestQuality = midQ
                    minQ = midQ
                } else {
                    // 当前质量产生的文件过大，降低质量
                    maxQ = midQ
                }
            } else {
                break
            }

            // 如果区间已经很小，提前退出
            if (maxQ - minQ) < 0.05 {
                break
            }
        }

        // 3. 如果质量调整后仍然过大，则缩小尺寸
        var currentSize = image.size
        var currentData = bestData

        while currentData == nil || (currentData!.count > targetFileSize &&
            currentSize.width > minSize.width &&
            currentSize.height > minSize.height)
        {
            // 使用更智能的缩放比例 - 根据超出大小比例决定缩放程度
            let scaleFactor: CGFloat
            if let data = currentData {
                // 根据超出比例决定缩放程度，但不小于0.8
                scaleFactor = max(0.8, CGFloat(targetFileSize) / CGFloat(data.count))
            } else {
                scaleFactor = 0.9
            }

            currentSize = CGSize(
                width: currentSize.width * scaleFactor,
                height: currentSize.height * scaleFactor
            )

            if let resized = image.resized(to: currentSize, preserveAspectRatio: preserveAspectRatio) {
                image = resized
                currentData = image.jpegData(compressionQuality: bestQuality)
            } else {
                break
            }
        }

        // 4. 最后一次尝试 - 如果仍然过大，使用PNG可能会更小
        if let data = currentData, data.count > targetFileSize {
            if let pngData = image.pngData(), pngData.count <= targetFileSize {
                return pngData
            }
        }

        return currentData
    }

    /// 生成二维码 UIImage
    static func generateQRCode(from string: String, color: UIColor, backgroundColor: UIColor) -> UIImage? {
        let context = CIContext()
        let filter = CIFilter.qrCodeGenerator()

        let data = Data(string.utf8)
        filter.setValue(data, forKey: "inputMessage")

        let colorFilter = CIFilter(name: "CIFalseColor")!
        colorFilter.setDefaults()
        colorFilter.setValue(filter.outputImage, forKey: "inputImage")

        colorFilter.setValue(CIColor(color: color), forKey: "inputColor0")
        colorFilter.setValue(CIColor(color: backgroundColor), forKey: "inputColor1")

        if let outputImage = colorFilter.outputImage {
            let transformedImage = outputImage.transformed(by: CGAffineTransform(scaleX: 10, y: 10))
            if let cgImage = context.createCGImage(transformedImage, from: transformedImage.extent) {
                return UIImage(cgImage: cgImage)
            }
        }

        return nil
    }

    /// 生成二维码 UIImage
    static func generateQRCode(from string: String, color: Color, backgroundColor: Color) -> UIImage? {
        let uiColor = UIColor(color)
        let uiBackgroundColor = UIColor(backgroundColor)
        return generateQRCode(from: string, color: uiColor, backgroundColor: uiBackgroundColor)
    }
}
