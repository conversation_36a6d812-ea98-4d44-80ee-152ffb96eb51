//
//  File.swift
//  MyWidgetKit
//
//  Created by y<PERSON><PERSON><PERSON> on 2025/5/12.
//

import Foundation

public extension Date {
    public var isToday: Bool {
        return Calendar.current.isDateInToday(self)
    }
      
    public var week: Int {
        return Calendar.current.component(.weekday, from: self)
    }

}

public extension Date {
    public var calendar: Calendar {
        return Calendar(identifier: Calendar.current.identifier)
    }
     
    //年:2020
    public var year: Int {
        get {
            return calendar.component(.year, from: self)
        }
        set {
            guard newValue > 0 else { return }
            let currentYear = calendar.component(.year, from: self)
            let yearsToAdd = newValue - currentYear
            if let date = calendar.date(byAdding: .year, value: yearsToAdd, to: self) {
                self = date
            }
        }
    }
     
    //月份:2
    public var month: Int {
        get {
            return calendar.component(.month, from: self)
        }
        set {
            let allowedRange = calendar.range(of: .month, in: .year, for: self)!
            guard allowedRange.contains(newValue) else { return }
 
            let currentMonth = calendar.component(.month, from: self)
            let monthsToAdd = newValue - currentMonth
            if let date = calendar.date(byAdding: .month, value: monthsToAdd, to: self) {
                self = date
            }
        }
    }
     
    //天:10
    public var day: Int {
        get {
            return calendar.component(.day, from: self)
        }
        set {
            let allowedRange = calendar.range(of: .day, in: .month, for: self)!
            guard allowedRange.contains(newValue) else { return }
 
            let currentDay = calendar.component(.day, from: self)
            let daysToAdd = newValue - currentDay
            if let date = calendar.date(byAdding: .day, value: daysToAdd, to: self) {
                self = date
            }
        }
    }
     
}
 
 
//小组件时间刷新相关
public extension Date {
    //获取完整时间,2011:07:13
    public func getCurrentDayStartHour(_ isDayOf24Hours: Bool)-> Date {
        let components = DateComponents(year: self.year, month: self.month, day: self.day, hour: 0, minute: 0, second: 0)
        return Calendar.current.date(from: components)!
   }
}
