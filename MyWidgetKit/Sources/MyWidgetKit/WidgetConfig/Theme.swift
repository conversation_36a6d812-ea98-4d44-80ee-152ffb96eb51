import SwiftUI

public struct ThemeColor {
    public let primary: Color
    public let secondary: Color
    public let background: Color

    public var gradient: LinearGradient {
        LinearGradient(
            colors: [primary, secondary],
            startPoint: .topLeading,
            endPoint: .bottomTrailing
        )
    }
}

public enum WidgetTheme: String, CaseIterable {
    case dailyQuote = "每日灵感"
    case mood = "心情日记"
    case todo = "任务清单"
    case progress = "学习进度"
    case gallery = "精选图集"

    public static func theme(for title: String) -> Self {
        Self.allCases.first { $0.rawValue == title } ?? .dailyQuote
    }

    public var colors: ThemeColor {
        switch self {
        case .dailyQuote:
            return ThemeColor(primary: .blue, secondary: .purple, background: .white.opacity(0.9))
        case .mood:
            return ThemeColor(primary: .pink, secondary: .purple, background: .white.opacity(0.9))
        case .todo:
            return ThemeColor(primary: .indigo, secondary: .purple, background: .white.opacity(0.9))
        case .progress:
            return ThemeColor(primary: .purple, secondary: .red, background: .white.opacity(0.9))
        case .gallery:
            return ThemeColor(primary: .teal, secondary: .blue, background: .white.opacity(0.9))
        }
    }
}
