//
//  Untitled.swift
//  MyWidgetKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/11.
//
import Foundation
import SwiftUI
import UIKit
import WidgetKit

/// 通用 Widget 预览容器
public struct WidgetPreviewContainer<Content: View>: View {
    let base: WidgetFamily
    let content: () -> Content

    public init(
        base: WidgetFamily,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.base = base
        self.content = content
    }

    public var body: some View {
        // 获取当前设备下该类型 widget 的真实尺寸
        let size = WidgetPreviewSizeHelper.widgetSize(for: base)

        ZStack {
            content()
                .frame(width: size.width, height: size.height)
        }
        .frame(width: size.width, height: size.height)
        .clipped()
        .cornerRadius(20)
        .shadow(radius: 4)
    }
}
