//
//  File.swift
//  MyWidgetKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/12.
//

import Foundation
import UIKit
import WidgetKit

public enum WidgetPreviewSizeHelper {
    @MainActor
    public static func portraitScreenWidth() -> CGFloat {
        UIScreen.main.bounds.width
    }

    @MainActor
    public static func portraitScreenHeight() -> CGFloat {
        UIScreen.main.bounds.height
    }

    @MainActor
    public static func widgetSize(for base: WidgetFamily) -> CGSize {
        let width = portraitScreenWidth()
        let height = portraitScreenHeight()

        switch width {
        case 430...: // iPhone 14 Pro Max 及以上
            switch base {
            case .systemLarge:
                return CGSize(width: 375, height: 393)
            case .systemMedium:
                return CGSize(width: 375, height: 170)
            default:
                return CGSize(width: 170, height: 170)
            }
        case 428...: // iPhone 12 Pro Max 及以上
            switch base {
            case .systemLarge:
                return CGSize(width: 364, height: 382)
            case .systemMedium:
                return CGSize(width: 364, height: 170)
            default:
                return CGSize(width: 170, height: 170)
            }
        case 414:
            switch height {
            case 896: // iPhone 11 Pro Max
                switch base {
                case .systemLarge:
                    return CGSize(width: 360, height: 379)
                case .systemMedium:
                    return CGSize(width: 360, height: 169)
                default:
                    return CGSize(width: 169, height: 169)
                }
            default: // iPhone 8 Plus
                switch base {
                case .systemLarge:
                    return CGSize(width: 348, height: 351)
                case .systemMedium:
                    return CGSize(width: 348, height: 157)
                default:
                    return CGSize(width: 157, height: 157)
                }
            }
        // iPhone 16 Pro  Max
        case 402:
            switch height {
            case 896: // iPhone 11
                switch base {
                case .systemLarge:
                    return CGSize(width: 340, height: 351)
                case .systemMedium:
                    return CGSize(width: 340, height: 157)
                default:
                    return CGSize(width: 157, height: 157)
                }
            default: // iPhone 12 Pro Max
                switch base {
                case .systemLarge:
                    return CGSize(width: 340, height: 351)
                case .systemMedium:
                    return CGSize(width: 340, height: 157)
                default:
                    return CGSize(width: 157, height: 157)
                }
            }
        case 390: // iPhone 12/12 Pro
            switch base {
            case .systemLarge:
                return CGSize(width: 338, height: 354)
            case .systemMedium:
                return CGSize(width: 338, height: 158)
            default:
                return CGSize(width: 158, height: 158)
            }
        case 375:
            switch height {
            case 812: // iPhone X/12 mini
                switch base {
                case .systemLarge:
                    return CGSize(width: 329, height: 345)
                case .systemMedium:
                    return CGSize(width: 329, height: 155)
                default:
                    return CGSize(width: 155, height: 155)
                }
            default: // iPhone 8
                switch base {
                case .systemLarge:
                    return CGSize(width: 321, height: 324)
                case .systemMedium:
                    return CGSize(width: 321, height: 148)
                default:
                    return CGSize(width: 148, height: 148)
                }
            }
        default: // iPhone SE
            switch base {
            case .systemLarge:
                return CGSize(width: 292, height: 311)
            case .systemMedium:
                return CGSize(width: 292, height: 141)
            default:
                return CGSize(width: 141, height: 141)
            }
        }
    }
}
