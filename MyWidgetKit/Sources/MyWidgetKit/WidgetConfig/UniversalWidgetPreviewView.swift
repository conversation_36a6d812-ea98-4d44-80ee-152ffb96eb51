//
//  UniversalWidgetPreviewView.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/13.
//

import SwiftUI
import WidgetKit

public protocol WidgetPreviewableData {}

/// 通用小组件预览视图，支持主题适配和多种尺寸预览
public struct UniversalWidgetPreviewView<Data: WidgetPreviewableData>: View {
    // MARK: - 属性

    // 数据和内容
    public let data: Data
    @Binding public var previewSize: WidgetFamily
    public let content: (Data, WidgetFamily) -> AnyView

    // 主题相关
    public var accentColor: Color = .blue
    public var backgroundColor: Color = .init(.systemBackground)
    public var surfaceColor: Color = Color(.systemGray6).opacity(0.7)
    public var textColor: Color = .primary
    public var subtextColor: Color = .secondary
    public var borderColor: Color = Color.gray.opacity(0.2)
    public var shadowColor: Color = Color.black.opacity(0.1)

    // 自定义选项
    public var showSizeSelector: Bool = true
    public var showTitle: Bool = false
    public var title: String = "预览"
    public var cornerRadius: CGFloat = 22

    // 手势支持
    @State private var previewScale: CGFloat = 1.0
    public var enableZoomGesture: Bool = true

    // 动态高度计算 - 优化后的版本，减少额外空间
    private var dynamicPreviewHeight: CGFloat {
        let baseWidth = (UIScreen.main.bounds.width - 36) / 2.12
        let size = widgetSize(for: previewSize, containerWidth: baseWidth)

        // 根据不同尺寸返回不同高度，减少额外空间
        switch previewSize {
        case .systemSmall:
            return size.height + 20
        case .systemMedium:
            return size.height + 20
        case .systemLarge:
            return size.height + 30
        case .systemExtraLarge:
            return size.height + 40
        default:
            return size.height + 20
        }
    }

    // 紧凑模式下的动态高度计算
    public var compactPreviewHeight: CGFloat {
        let baseWidth = (UIScreen.main.bounds.width - 36) / 2.12
        let size = widgetSize(for: previewSize, containerWidth: baseWidth)

        // 紧凑模式下，几乎不添加额外空间
        return size.height + 10
    }

    // MARK: - 初始化方法

    /// 标准初始化方法
    public init(
        data: Data,
        previewSize: Binding<WidgetFamily>,
        @ViewBuilder content: @escaping (Data, WidgetFamily) -> AnyView
    ) {
        self.data = data
        _previewSize = previewSize
        self.content = content
    }

    /// 支持主题的初始化方法
    public init(
        data: Data,
        previewSize: Binding<WidgetFamily>,
        accentColor: Color = .blue,
        backgroundColor: Color = Color(.systemBackground),
        surfaceColor: Color = Color(.systemGray6).opacity(0.7),
        textColor: Color = .primary,
        subtextColor: Color = .secondary,
        showSizeSelector: Bool = true,
        showTitle: Bool = false,
        title: String = "预览",
        @ViewBuilder content: @escaping (Data, WidgetFamily) -> AnyView
    ) {
        self.data = data
        _previewSize = previewSize
        self.content = content
        self.accentColor = accentColor
        self.backgroundColor = backgroundColor
        self.surfaceColor = surfaceColor
        self.textColor = textColor
        self.subtextColor = subtextColor
        self.showSizeSelector = showSizeSelector
        self.showTitle = showTitle
        self.title = title
    }

    /// 紧凑模式初始化方法 - 适用于空间有限的场景
    public init(
        compact data: Data,
        previewSize: Binding<WidgetFamily>,
        accentColor: Color = .blue,
        backgroundColor: Color = Color(.systemBackground),
        surfaceColor: Color = Color(.systemGray6).opacity(0.7),
        textColor: Color = .primary,
        subtextColor: Color = .secondary,
        @ViewBuilder content: @escaping (Data, WidgetFamily) -> AnyView
    ) {
        self.data = data
        _previewSize = previewSize
        self.content = content
        self.accentColor = accentColor
        self.backgroundColor = backgroundColor
        self.surfaceColor = surfaceColor
        self.textColor = textColor
        self.subtextColor = subtextColor
        showSizeSelector = false
        showTitle = false
        cornerRadius = 14
        enableZoomGesture = true
    }

    // MARK: - 视图主体

    public var body: some View {
        VStack(spacing: 0) {
            // 标题（可选）
            if showTitle {
                Text(title)
                    .font(.headline)
                    .foregroundColor(textColor)
                    .frame(maxWidth: .infinity, alignment: .leading)
                    .padding(.horizontal, 20)
                    .padding(.top, 8)
                    .padding(.bottom, 4)
            }

            // 预览区
            previewAreaView
                .padding(.horizontal, 18)
                .padding(.bottom, 8)
                .padding(.top, 8)
        }
        .background(backgroundColor)
    }

    /// 紧凑模式视图 - 适用于空间有限的场景
    public var compactBody: some View {
        VStack(spacing: 0) {
            // 紧凑版预览区
            compactPreviewAreaView
                .padding(.horizontal, 12)
                .padding(.vertical, 4)
        }
        .background(backgroundColor)
    }

    /// 获取尺寸选择器视图 - 可以作为导航栏的 titleView 使用
    public var navBarSizeSelectorView: some View {
        HStack(spacing: 12) {
            ForEach([WidgetFamily.systemSmall, .systemMedium, .systemLarge], id: \.self) { size in
                Button(action: { previewSize = size }) {
                    VStack(spacing: 2) {
                        Image(systemName: sizeIcon(for: size))
                            .font(.system(size: 14, weight: .medium))
                        Text(sizeName(for: size))
                            .font(.caption2)
                    }
                    .foregroundColor(previewSize == size ? .white : textColor)
                    .padding(.vertical, 6)
                    .padding(.horizontal, 12)
                    .background(
                        ZStack {
                            if previewSize == size {
                                LinearGradient(
                                    gradient: Gradient(colors: [accentColor, accentColor.opacity(0.7)]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                                .shadow(color: accentColor.opacity(0.18), radius: 4, x: 0, y: 2)
                            } else {
                                surfaceColor
                                RoundedRectangle(cornerRadius: 10)
                                    .stroke(borderColor, lineWidth: 1)
                            }
                        }
                    )
                    .cornerRadius(10)
                    .scaleEffect(previewSize == size ? 1.05 : 1.0)
                    .animation(.easeInOut(duration: 0.18), value: previewSize)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
    }

    // MARK: - 子视图

    /// 尺寸选择器视图
    private var sizeSelectorView: some View {
        HStack(spacing: 16) {
            ForEach([WidgetFamily.systemSmall, .systemMedium, .systemLarge], id: \.self) { size in
                Button(action: { previewSize = size }) {
                    VStack(spacing: 4) {
                        Image(systemName: sizeIcon(for: size))
                            .font(.system(size: 18, weight: .medium))
                        Text(sizeName(for: size))
                            .font(.subheadline)
                    }
                    .foregroundColor(previewSize == size ? .white : textColor)
                    .padding(.vertical, 10)
                    .padding(.horizontal, 22)
                    .background(
                        ZStack {
                            if previewSize == size {
                                LinearGradient(
                                    gradient: Gradient(colors: [accentColor, accentColor.opacity(0.7)]),
                                    startPoint: .topLeading,
                                    endPoint: .bottomTrailing
                                )
                                .shadow(color: accentColor.opacity(0.18), radius: 8, x: 0, y: 4)
                            } else {
                                surfaceColor
                                RoundedRectangle(cornerRadius: 14)
                                    .stroke(borderColor, lineWidth: 1)
                            }
                        }
                    )
                    .cornerRadius(14)
                    .scaleEffect(previewSize == size ? 1.08 : 1.0)
                    .animation(.easeInOut(duration: 0.18), value: previewSize)
                }
                .buttonStyle(PlainButtonStyle())
            }
        }
        .padding(.horizontal, 20)
    }

    /// 预览区域视图 - 优化版本，减少内边距和阴影
    private var previewAreaView: some View {
        GeometryReader { geo in
            let baseWidth = (geo.size.width - 24) / 2.12 // 以 medium 的宽度为最大参考，减少边距
            let size = widgetSize(for: previewSize, containerWidth: baseWidth)
            ZStack {
                // 卡片背景 - 减小阴影和圆角
                RoundedRectangle(cornerRadius: 18, style: .continuous)
                    .fill(
                        LinearGradient(
                            gradient: Gradient(colors: [backgroundColor, surfaceColor]),
                            startPoint: .top,
                            endPoint: .bottom
                        )
                    )
                    .shadow(color: shadowColor, radius: 8, x: 0, y: 4) // 减小阴影

                // 预览内容 - 减少内边距
                WidgetPreviewContainer(base: previewSize) {
                    content(data, previewSize)
                }
                .frame(width: size.width, height: size.height)
                .scaleEffect(previewScale)
                .clipped()
                .cornerRadius(16)
                .padding(6) // 减少内边距
                .shadow(color: shadowColor.opacity(0.3), radius: 2, x: 0, y: 1) // 减小阴影
            }
            .gesture(
                enableZoomGesture ?
                    MagnificationGesture()
                    .onChanged { value in
                        previewScale = min(max(value.magnitude, 0.8), 1.2)
                    }
                    .onEnded { _ in
                        withAnimation {
                            previewScale = 1.0
                        }
                    } : nil
            )
        }
        .frame(height: dynamicPreviewHeight)
    }

    /// 紧凑版预览区域视图 - 可以在空间有限时使用
    public var compactPreviewAreaView: some View {
        GeometryReader { geo in
            let baseWidth = (geo.size.width - 16) / 2.12 // 进一步减少边距
            let size = widgetSize(for: previewSize, containerWidth: baseWidth)
            ZStack {
                // 简化背景
                RoundedRectangle(cornerRadius: 14, style: .continuous)
                    .fill(surfaceColor)
                    .shadow(color: shadowColor, radius: 4, x: 0, y: 2)

                // 预览内容 - 最小内边距
                WidgetPreviewContainer(base: previewSize) {
                    content(data, previewSize)
                }
                .frame(width: size.width, height: size.height)
                .scaleEffect(previewScale)
                .clipped()
                .cornerRadius(12)
                .padding(4) // 最小内边距
            }
            .gesture(
                enableZoomGesture ?
                    MagnificationGesture()
                    .onChanged { value in
                        previewScale = min(max(value.magnitude, 0.8), 1.2)
                    }
                    .onEnded { _ in
                        withAnimation {
                            previewScale = 1.0
                        }
                    } : nil
            )
        }
        .frame(height: compactPreviewHeight)
    }

    // MARK: - 辅助方法

    /// 获取尺寸对应的图标
    private func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        case .systemExtraLarge:
            return "square.grid.2x2"
        @unknown default:
            return "square"
        }
    }

    /// 获取尺寸对应的名称
    private func sizeName(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大"
        @unknown default:
            return "未知"
        }
    }

    /// 获取小组件的宽高比
    private func widgetAspectRatio(for family: WidgetFamily) -> CGFloat {
        switch family {
        case .systemSmall:
            return 1.0
        case .systemMedium:
            return 2.1
        case .systemLarge:
            return 1.0
        case .systemExtraLarge:
            return 2.1
        default:
            return 1.0
        }
    }

    /// 计算小组件尺寸
    private func widgetSize(for family: WidgetFamily, containerWidth: CGFloat) -> CGSize {
        // 以 small 为基准宽度
        let baseWidth = containerWidth
        switch family {
        case .systemSmall:
            return CGSize(width: baseWidth, height: baseWidth)
        case .systemMedium:
            return CGSize(width: baseWidth * 2.12, height: baseWidth)
        case .systemLarge:
            return CGSize(width: baseWidth * 2.12, height: baseWidth * 2.22)
        case .systemExtraLarge:
            return CGSize(width: baseWidth * 2.5, height: baseWidth * 2.5)
        default:
            return CGSize(width: baseWidth, height: baseWidth)
        }
    }
}
