//
//  WidgetCommonConfig.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/11.
//

import SwiftUI
import UIKit

/// Widget类型标识
public enum WidgetType: String, Codable, Sendable {
    case dailyQuote
    case clock
    case calendar
    case qrImage
    case todoList // 任务清单小组件
    case timeWidget // 时间小组件
    case waterIntake // 水分摄入追踪器
    case passwordGenerator // 随机密码生成器
    case pomodoro // 番茄时钟小组件
    case note // 快速笔记捕捉器
    case moonPhase // 月相日历小组件
    case appLauncher // 应用快捷启动器
    case deviceInfo // 设备信息小组件
    case keyboard // 键盘扩展
    // 可扩展更多
}

/// Widget属性key
@available(iOS 16.0, *)
public enum WidgetPropertyKey: String, Codable, Sendable {
    case config
    case image
    case backgroundImage
    case color
    case lastUpdate
    case userConfig
    case data // 添加 data 属性键，用于存储组件数据
    case categories // 任务分类
    case tasks // 任务列表
    case theme // 键盘主题
    // 可扩展更多

    /// 推荐的文件后缀
    public var fileExtension: String {
        switch self {
        case .image, .backgroundImage:
            return "png" // 或 "png"/"jpg" 视实际情况
        case .color, .lastUpdate, .userConfig, .config, .data, .categories, .tasks, .theme:
            return "json"
        }
    }
}

/// 通用背景配置
@available(iOS 16.0, *)
public enum WidgetBackground: Codable, Sendable, Equatable {
    case color(WidgetColor)
    case imageData(Data)
    case imageURL(String)
    case imageFile(String)
    case packageImage(String) // 新增：内置背景图片

    enum CodingKeys: String, CodingKey {
        case type, color, imageData, imageURL, imageFile, packageImage
    }

    public static func == (lhs: WidgetBackground, rhs: WidgetBackground) -> Bool {
        switch (lhs, rhs) {
        case let (.color(lhsColor), .color(rhsColor)):
            return lhsColor == rhsColor
        case let (.imageData(lhsData), .imageData(rhsData)):
            return lhsData == rhsData
        case let (.imageURL(lhsURL), .imageURL(rhsURL)):
            return lhsURL == rhsURL
        case let (.imageFile(lhsPath), .imageFile(rhsPath)):
            return lhsPath == rhsPath
        case let (.packageImage(lhsName), .packageImage(rhsName)):
            return lhsName == rhsName
        default:
            return false
        }
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        switch self {
        case let .color(color):
            try container.encode("color", forKey: .type)
            try container.encode(color, forKey: .color)
        case let .imageData(data):
            try container.encode("imageData", forKey: .type)
            try container.encode(data, forKey: .imageData)
        case let .imageURL(url):
            try container.encode("imageURL", forKey: .type)
            try container.encode(url, forKey: .imageURL)
        case let .imageFile(path):
            try container.encode("imageFile", forKey: .type)
            try container.encode(path, forKey: .imageFile)
        case let .packageImage(name):
            try container.encode("packageImage", forKey: .type)
            try container.encode(name, forKey: .packageImage)
        }
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        let type = try container.decode(String.self, forKey: .type)
        switch type {
        case "color":
            let color = try container.decode(WidgetColor.self, forKey: .color)
            self = .color(color)
        case "imageData":
            let data = try container.decode(Data.self, forKey: .imageData)
            self = .imageData(data)
        case "imageURL":
            let url = try container.decode(String.self, forKey: .imageURL)
            self = .imageURL(url)
        case "imageFile":
            let path = try container.decode(String.self, forKey: .imageFile)
            self = .imageFile(path)
        case "packageImage":
            let name = try container.decode(String.self, forKey: .packageImage)
            self = .packageImage(name)
        default:
            throw DecodingError.dataCorruptedError(forKey: .type, in: container, debugDescription: "Unknown WidgetBackground type")
        }
    }

    public var isImage: Bool {
        switch self {
        case .color: return false
        default: return true
        }
    }

    /// 根据当前的背景类型返回对应的视图模式
    /// - Returns: 返回一个AnyView，根据背景类型可能是Color、Image或Image(url: URL)
    /// 根据当前的背景类型返回对应的 View（Color 或 Image）
    @MainActor @ViewBuilder
    public func backgroundView() -> some View {
        ZStack {
            // 直接使用背景视图，不使用widgetBackground修饰符
            switch self {
            case let .color(color):
                color.toColor()
                    .ignoresSafeArea()
            case let .imageData(data):
                if let uiImage = UIImage(data: data) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .ignoresSafeArea()
                } else {
                    Color.white.ignoresSafeArea() // 图片数据无效时的默认颜色
                }
            case let .imageURL(url):
                if #available(iOS 15.0, *), let url = URL(string: url) {
                    AsyncImage(url: url) { phase in
                        switch phase {
                        case .success(let image):
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .ignoresSafeArea()
                        case .failure(_):
                            Color.gray.ignoresSafeArea() // 加载失败时的默认颜色
                        case .empty:
                            Color.gray.opacity(0.3).ignoresSafeArea() // 加载中的默认颜色
                        @unknown default:
                            Color.gray.ignoresSafeArea()
                        }
                    }
                } else {
                    // iOS 14 兼容处理
                    Color.gray.ignoresSafeArea()
                }
            case let .imageFile(path):
                if let uiImage = UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .ignoresSafeArea()
                } else {
                    Color.white.ignoresSafeArea() // 图片文件无效时的默认颜色
                }
            case let .packageImage(name):
                // 使用包内图片资源
                Image(name, bundle: .module)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .ignoresSafeArea()
            }

            // 添加一个空视图，使用widgetBackground修饰符
            EmptyView()
                .widgetBackground {
                    switch self {
                    case let .color(color):
                        color.toColor()
                    case let .imageData(data):
                        Image(uiImage: UIImage(data: data) ?? UIImage())
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    case let .imageURL(url):
                        if #available(iOS 15.0, *) {
                            AsyncImage(url: URL(string: url)!) { image in
                                image.resizable().aspectRatio(contentMode: .fill)
                            } placeholder: {
                                Color.gray
                            }
                        } else {
                            // iOS 14 占位，建议自定义图片加载
                            Color.gray
                        }
                    case let .imageFile(path):
                        // 使用 AppGroupDataManager 获取正确的文件路径
                        Image(uiImage: UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) ?? UIImage())
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    case let .packageImage(name):
                        // 使用包内图片资源
                        Image(name, bundle: .module)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    }
                }
        }
    }

    @MainActor @ViewBuilder
    public func backgroundView(width: CGFloat, height: CGFloat) -> some View {
        ZStack {
            // 直接使用背景视图，不使用widgetBackground修饰符
            switch self {
            case let .color(color):
                color.toColor()
                    .frame(width: width, height: height)
            case let .imageData(data):
                if let uiImage = UIImage(data: data) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: width, height: height)
                        .clipped()
                } else {
                    Color.white.frame(width: width, height: height) // 图片数据无效时的默认颜色
                }
            case let .imageURL(url):
                if #available(iOS 15.0, *), let url = URL(string: url) {
                    AsyncImage(url: url) { phase in
                        switch phase {
                        case .success(let image):
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: width, height: height)
                                .clipped()
                        case .failure(_):
                            Color.gray.frame(width: width, height: height) // 加载失败时的默认颜色
                        case .empty:
                            Color.gray.opacity(0.3).frame(width: width, height: height) // 加载中的默认颜色
                        @unknown default:
                            Color.gray.frame(width: width, height: height)
                        }
                    }
                } else {
                    // iOS 14 兼容处理
                    Color.gray.frame(width: width, height: height)
                }
            case let .imageFile(path):
                if let uiImage = UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: width, height: height)
                        .clipped()
                } else {
                    Color.white.frame(width: width, height: height) // 图片文件无效时的默认颜色
                }
            case let .packageImage(name):
                // 使用包内图片资源
                Image(name, bundle: .module)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: width, height: height)
                    .clipped()
            }

            // 添加一个空视图，使用widgetBackground修饰符
            EmptyView()
                .frame(width: width, height: height)
                .widgetBackground {
                    switch self {
                    case let .color(color):
                        color.toColor()
                    case let .imageData(data):
                        Image(uiImage: UIImage(data: data) ?? UIImage())
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: width, height: height)
                            .clipped()
                    case let .imageURL(url):
                        if #available(iOS 15.0, *) {
                            AsyncImage(url: URL(string: url)!) { image in
                                image.resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: width, height: height)
                                    .clipped()
                            } placeholder: {
                                Color.gray
                            }
                        } else {
                            Color.gray
                        }
                    case let .imageFile(path):
                        Image(uiImage: UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) ?? UIImage())
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: width, height: height)
                            .clipped()
                    case let .packageImage(name):
                        // 使用包内图片资源
                        Image(name, bundle: .module)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: width, height: height)
                            .clipped()
                    }
                }
        }
    }
}

/// 颜色
@available(iOS 16.0, *)
public struct WidgetColor: Codable, Equatable, Sendable {
    /// 红色
    public let red: Double
    /// 绿色
    public let green: Double
    /// 蓝色
    public let blue: Double
    /// 透明度
    public let alpha: Double

    /// 初始化
    public init(red: Double, green: Double, blue: Double, alpha: Double = 1.0) {
        self.red = red
        self.green = green
        self.blue = blue
        self.alpha = alpha
    }

    #if canImport(UIKit)
        /// 转换为UIColor
        public func toUIColor() -> UIColor {
            return UIColor(red: CGFloat(red), green: CGFloat(green), blue: CGFloat(blue), alpha: CGFloat(alpha))
        }

        /// 转换为CGColor
        public func toCGColor() -> CGColor {
            return UIColor(red: CGFloat(red), green: CGFloat(green), blue: CGFloat(blue), alpha: CGFloat(alpha)).cgColor
        }

        /// 从UIColor初始化
        public static func fromUIColor(_ color: UIColor) -> WidgetColor {
            var red: CGFloat = 0, green: CGFloat = 0, blue: CGFloat = 0, alpha: CGFloat = 0
            color.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
            return WidgetColor(red: Double(red), green: Double(green), blue: Double(blue), alpha: Double(alpha))
        }
    #endif

    /// 转换为Color
    public func toColor() -> Color {
        return Color(red: red, green: green, blue: blue, opacity: alpha)
    }

    public var color: Color {
        return toColor()
    }

    #if canImport(UIKit)
        /// 从Color初始化
        public static func fromColor(_ color: Color) -> WidgetColor {
            let uiColor = UIColor(color)
            return fromUIColor(uiColor)
        }
    #endif
}


/// 小组件渐变
public struct WidgetGradient: Codable, Equatable {
    public let colors: [WidgetColor]
    public let startPoint: UnitPoint
    public let endPoint: UnitPoint

    public init(colors: [WidgetColor], startPoint: UnitPoint, endPoint: UnitPoint) {
        self.colors = colors
        self.startPoint = startPoint
        self.endPoint = endPoint
    }

    // 编码和解码 UnitPoint
    private enum CodingKeys: String, CodingKey {
        case colors
        case startPointX, startPointY
        case endPointX, endPointY
    }

    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        colors = try container.decode([WidgetColor].self, forKey: .colors)

        let startX = try container.decode(Double.self, forKey: .startPointX)
        let startY = try container.decode(Double.self, forKey: .startPointY)
        startPoint = UnitPoint(x: startX, y: startY)

        let endX = try container.decode(Double.self, forKey: .endPointX)
        let endY = try container.decode(Double.self, forKey: .endPointY)
        endPoint = UnitPoint(x: endX, y: endY)
    }

    public func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)
        try container.encode(colors, forKey: .colors)

        try container.encode(startPoint.x, forKey: .startPointX)
        try container.encode(startPoint.y, forKey: .startPointY)

        try container.encode(endPoint.x, forKey: .endPointX)
        try container.encode(endPoint.y, forKey: .endPointY)
    }
}


@available(iOS 16.0, *)
public struct WidgetFont: Codable, Sendable {
    public var name: String? // 字体名
    public var size: CGFloat // 字体大小
    public var weight: CGFloat? // 字重
    public var color: WidgetColor // 字体颜色

    public init(name: String? = nil, size: CGFloat, weight: CGFloat? = nil, color: WidgetColor) {
        self.name = name
        self.size = size
        self.weight = weight
        self.color = color
    }
}

/// 组件预设位置
@available(iOS 16.0, *)
public enum WidgetDisplayPosition: String, Codable, Sendable {
    case topLeft
    case topRight
    case centerLeft
    case centerRight
    case bottomLeft
    case bottomRight
}

@available(iOS 16.0, *)
public struct WidgetCommonConfig: Codable, Sendable {
    // 用于标识该配置属于哪个组件
    public var widgetType: WidgetType

    // 组件显示位置
    public var displayPosition: WidgetDisplayPosition = .topLeft

    // 其他外观和内容属性
    public var backgroundColor: WidgetColor?
    public var backgroundImageName: String?
    public var backgroundImageData: Data?
    public var mainText: String?
    public var subText: String?
    public var mainTextFont: WidgetFont?
    public var subTextFont: WidgetFont?
    public var padding: CGFloat?
    public var cornerRadius: CGFloat?
    public var alignment: String?
    public var shadowOpacity: Float?
    public var shadowRadius: CGFloat?
    public var opacity: Float?
    public var borderColor: WidgetColor?
    public var borderWidth: CGFloat?
    public var extra: [String: String]?
    public var background: WidgetBackground? // 新增，通用背景配置

    public init(
        widgetType: WidgetType,
        displayPosition: WidgetDisplayPosition = .topLeft,
        backgroundColor: WidgetColor? = nil,
        backgroundImageName: String? = nil,
        backgroundImageData: Data? = nil,
        mainText: String? = nil,
        subText: String? = nil,
        mainTextFont: WidgetFont? = nil,
        subTextFont: WidgetFont? = nil,
        padding: CGFloat? = nil,
        cornerRadius: CGFloat? = nil,
        alignment: String? = nil,
        shadowOpacity: Float? = nil,
        shadowRadius: CGFloat? = nil,
        opacity: Float? = nil,
        borderColor: WidgetColor? = nil,
        borderWidth: CGFloat? = nil,
        extra: [String: String]? = nil,
        background: WidgetBackground? = nil
    ) {
        self.widgetType = widgetType
        self.displayPosition = displayPosition
        self.backgroundColor = backgroundColor
        self.backgroundImageName = backgroundImageName
        self.backgroundImageData = backgroundImageData
        self.mainText = mainText
        self.subText = subText
        self.mainTextFont = mainTextFont
        self.subTextFont = subTextFont
        self.padding = padding
        self.cornerRadius = cornerRadius
        self.alignment = alignment
        self.shadowOpacity = shadowOpacity
        self.shadowRadius = shadowRadius
        self.opacity = opacity
        self.borderColor = borderColor
        self.borderWidth = borderWidth
        self.extra = extra
        self.background = background
    }
}
