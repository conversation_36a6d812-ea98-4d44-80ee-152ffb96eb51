//
//  ThemePackImageManager.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import Foundation
#if canImport(UIKit)
import UIKit
#endif

/// 主题包图片管理器
public final class ThemePackImageManager: ObservableObject, @unchecked Sendable {

    // MARK: - 单例
    public static let shared = ThemePackImageManager()

    // MARK: - 私有属性
    private let fileManager = FileManager.default
    private let imageCache = NSCache<NSString, UIImage>()
    private let dataManager = AppGroupDataManager.shared

    // 线程安全的缓存键管理
    private let cacheKeysLock = NSLock()
    private var _cacheKeys: Set<String> = []

    private var cacheKeys: Set<String> {
        get {
            cacheKeysLock.lock()
            defer { cacheKeysLock.unlock() }
            return _cacheKeys
        }
        set {
            cacheKeysLock.lock()
            defer { cacheKeysLock.unlock() }
            _cacheKeys = newValue
        }
    }

    // 线程安全的缓存键操作
    private func addCacheKey(_ key: String) {
        cacheKeysLock.lock()
        defer { cacheKeysLock.unlock() }
        _cacheKeys.insert(key)
    }

    private func removeCacheKeys(matching predicate: (String) -> Bool) {
        cacheKeysLock.lock()
        defer { cacheKeysLock.unlock() }
        _cacheKeys = _cacheKeys.filter { !predicate($0) }
    }

    private func clearAllCacheKeys() {
        cacheKeysLock.lock()
        defer { cacheKeysLock.unlock() }
        _cacheKeys.removeAll()
    }

    // 图片规格常量
    public struct ImageSpecs {
        public static let keyboardBackgroundSize = CGSize(width: 1024, height: 512)
        public static let keyImageSize = CGSize(width: 64, height: 64)
        public static let maxBackgroundFileSize = 500 * 1024 // 500KB
        public static let maxKeyFileSize = 50 * 1024 // 50KB
    }

    // MARK: - 初始化
    private init() {
        setupImageCache()
    }

    // MARK: - 公共方法

    /// 获取键盘背景图片
    public func getKeyboardBackgroundImage(for themePackId: String, imagePath: String) async -> UIImage? {
        let cacheKey = NSString(string: "bg_\(themePackId)_\(imagePath)")

        // 检查内存缓存
        if let cachedImage = imageCache.object(forKey: cacheKey) {
            return cachedImage
        }

        // 从文件加载
        let fullPath = getThemePackResourcePath(themePackId: themePackId, resourcePath: imagePath)
        guard let image = await loadImageFromFile(at: fullPath) else {
            return nil
        }

        // 缓存图片
        imageCache.setObject(image, forKey: cacheKey)
        addCacheKey(cacheKey as String)
        return image
    }

    /// 获取按键背景图片
    public func getKeyImage(
        for themePackId: String,
        keyType: KeyType,
        state: KeyImageState = .normal,
        keyImages: KeyTypeImages?
    ) async -> UIImage? {

        let cacheKey = NSString(string: "key_\(themePackId)_\(keyType.rawValue)_\(state.rawValue)")

        // 检查内存缓存
        if let cachedImage = imageCache.object(forKey: cacheKey) {
            return cachedImage
        }

        // 获取图片路径
        guard let imagePath = getKeyImagePath(keyType: keyType, state: state, keyImages: keyImages) else {
            return nil
        }

        // 从文件加载
        let fullPath = getThemePackResourcePath(themePackId: themePackId, resourcePath: imagePath)
        guard let image = await loadImageFromFile(at: fullPath) else {
            return nil
        }

        // 缓存图片
        imageCache.setObject(image, forKey: cacheKey)
        addCacheKey(cacheKey as String)
        return image
    }

    /// 预加载主题包的所有图片
    public func preloadThemePackImages(for themePackId: String, imageConfig: ImageConfig) async {
        // 预加载背景图片
        if imageConfig.hasBackgroundImage, let backgroundPath = imageConfig.backgroundImagePath {
            _ = await getKeyboardBackgroundImage(for: themePackId, imagePath: backgroundPath)
        }

        // 预加载按键图片
        if imageConfig.hasKeyImage, let keyImages = imageConfig.keyImages {
            for keyType in KeyType.allCases {
                _ = await getKeyImage(for: themePackId, keyType: keyType, state: .normal, keyImages: keyImages)
                _ = await getKeyImage(for: themePackId, keyType: keyType, state: .pressed, keyImages: keyImages)
            }
        }
    }

    /// 清理指定主题包的图片缓存
    public func clearImageCache(for themePackId: String) {
        let keysToRemove = cacheKeys.filter { $0.contains(themePackId) }
        for key in keysToRemove {
            let nsKey = NSString(string: key)
            imageCache.removeObject(forKey: nsKey)
        }

        removeCacheKeys { $0.contains(themePackId) }
    }

    /// 清理所有图片缓存
    public func clearAllImageCache() {
        imageCache.removeAllObjects()
        clearAllCacheKeys()
    }

    /// 获取缓存统计信息
    public func getCacheStats() -> (count: Int, keys: [String]) {
        let keys = cacheKeys
        return (count: keys.count, keys: Array(keys))
    }

    /// 验证图片资源是否存在
    public func validateImageResources(for themePackId: String, imageConfig: ImageConfig) -> [String] {
        var missingResources: [String] = []

        // 验证背景图片
        if imageConfig.hasBackgroundImage, let backgroundPath = imageConfig.backgroundImagePath {
            let fullPath = getThemePackResourcePath(themePackId: themePackId, resourcePath: backgroundPath)
            if !fileManager.fileExists(atPath: fullPath.path) {
                missingResources.append("背景图片: \(backgroundPath)")
            }
        }

        // 验证按键图片
        if imageConfig.hasKeyImage, let keyImages = imageConfig.keyImages {
            for keyType in KeyType.allCases {
                if let normalPath = getKeyImagePath(keyType: keyType, state: .normal, keyImages: keyImages) {
                    let fullPath = getThemePackResourcePath(themePackId: themePackId, resourcePath: normalPath)
                    if !fileManager.fileExists(atPath: fullPath.path) {
                        missingResources.append("\(keyType.displayName)正常状态图片: \(normalPath)")
                    }
                }

                if let pressedPath = getKeyImagePath(keyType: keyType, state: .pressed, keyImages: keyImages) {
                    let fullPath = getThemePackResourcePath(themePackId: themePackId, resourcePath: pressedPath)
                    if !fileManager.fileExists(atPath: fullPath.path) {
                        missingResources.append("\(keyType.displayName)按下状态图片: \(pressedPath)")
                    }
                }
            }
        }

        return missingResources
    }

    // MARK: - 私有方法

    private func setupImageCache() {
        imageCache.countLimit = 100 // 最多缓存100张图片
        imageCache.totalCostLimit = 100 * 1024 * 1024 // 100MB内存限制
    }

    private func loadImageFromFile(at url: URL) async -> UIImage? {
        guard fileManager.fileExists(atPath: url.path) else {
            print("❌ 图片文件不存在: \(url.path)")
            print("   父目录: \(url.deletingLastPathComponent().path)")
            print("   父目录内容: \(listDirectoryContents(url.deletingLastPathComponent()))")
            return createFallbackImage()
        }

        do {
            let data = try Data(contentsOf: url)

            // 检查文件大小
            guard !data.isEmpty else {
                print("❌ 图片文件为空: \(url.lastPathComponent)")
                return createFallbackImage()
            }

            // 检查文件大小是否合理（避免过大的文件）
            let maxSize = 10 * 1024 * 1024 // 10MB
            guard data.count <= maxSize else {
                print("❌ 图片文件过大: \(url.lastPathComponent), 大小: \(data.count) bytes")
                return createFallbackImage()
            }

            guard let image = UIImage(data: data) else {
                print("❌ 无法解析图片数据: \(url.lastPathComponent)")
                print("   文件大小: \(data.count) bytes")
                print("   文件头: \(data.prefix(16).map { String(format: "%02x", $0) }.joined())")
                return createFallbackImage()
            }

            // 验证图片尺寸
            guard image.size.width > 0 && image.size.height > 0 else {
                print("❌ 图片尺寸无效: \(url.lastPathComponent), 尺寸: \(image.size)")
                return createFallbackImage()
            }

            print("✅ 成功加载图片: \(url.lastPathComponent), 尺寸: \(image.size), 大小: \(data.count) bytes")
            return image
        } catch {
            print("❌ 读取图片文件失败: \(url.lastPathComponent)")
            print("   错误: \(error.localizedDescription)")
            if let nsError = error as NSError? {
                print("   错误代码: \(nsError.code)")
                print("   错误域: \(nsError.domain)")
            }
            return createFallbackImage()
        }
    }

    private func getThemePackResourcePath(themePackId: String, resourcePath: String) -> URL {
        // 首先尝试内置主题包路径
        let builtInPath = dataManager.appGroup.containerURL
            .appendingPathComponent("theme-packs")
            .appendingPathComponent("built-in")
            .appendingPathComponent(themePackId)
            .appendingPathComponent(resourcePath)

        // 检查内置路径是否存在
        if fileManager.fileExists(atPath: builtInPath.path) {
            return builtInPath
        }

        // 如果内置路径不存在，尝试自定义路径
        let customPath = dataManager.appGroup.containerURL
            .appendingPathComponent("theme-packs")
            .appendingPathComponent("custom")
            .appendingPathComponent(themePackId)
            .appendingPathComponent(resourcePath)

        return customPath
    }

    private func getKeyImagePath(keyType: KeyType, state: KeyImageState, keyImages: KeyTypeImages?) -> String? {
        guard let keyImages = keyImages else { return nil }

        let stateImages: KeyStateImages? = {
            switch keyType {
            case .letter: return keyImages.letter
            case .number: return keyImages.number
            case .function: return keyImages.function
            case .space: return keyImages.space
            case .shift: return keyImages.shift
            case .symbol: return keyImages.symbol
            case .punctuation: return keyImages.punctuation
            }
        }()

        guard let stateImages = stateImages else { return nil }

        switch state {
        case .normal: return stateImages.normal
        case .pressed: return stateImages.pressed
        case .hover: return stateImages.hover
        }
    }

    /// 创建降级图片
    private func createFallbackImage() -> UIImage {
        let size = CGSize(width: 300, height: 200)
        let renderer = UIGraphicsImageRenderer(size: size)

        return renderer.image { context in
            // 背景色
            UIColor.systemGray6.setFill()
            context.fill(CGRect(origin: .zero, size: size))

            // 错误图标
            UIColor.systemRed.setStroke()
            context.cgContext.setLineWidth(3.0)

            // 绘制X标记
            let margin: CGFloat = 50
            context.cgContext.move(to: CGPoint(x: margin, y: margin))
            context.cgContext.addLine(to: CGPoint(x: size.width - margin, y: size.height - margin))
            context.cgContext.move(to: CGPoint(x: size.width - margin, y: margin))
            context.cgContext.addLine(to: CGPoint(x: margin, y: size.height - margin))
            context.cgContext.strokePath()

            // 添加文字
            let text = "图片加载失败"
            let attributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16, weight: .medium),
                .foregroundColor: UIColor.systemRed
            ]
            let attributedText = NSAttributedString(string: text, attributes: attributes)
            let textSize = attributedText.size()
            let textRect = CGRect(
                x: (size.width - textSize.width) / 2,
                y: (size.height - textSize.height) / 2,
                width: textSize.width,
                height: textSize.height
            )
            attributedText.draw(in: textRect)
        }
    }

    /// 列出目录内容（用于调试）
    private func listDirectoryContents(_ directory: URL) -> String {
        do {
            let contents = try fileManager.contentsOfDirectory(atPath: directory.path)
            if contents.isEmpty {
                return "目录为空"
            }
            return contents.prefix(10).joined(separator: ", ") + (contents.count > 10 ? "..." : "")
        } catch {
            return "无法读取目录: \(error.localizedDescription)"
        }
    }
}

/// 按键图片状态枚举
public enum KeyImageState: String, CaseIterable, Sendable {
    case normal = "normal"
    case pressed = "pressed"
    case hover = "hover"
}
