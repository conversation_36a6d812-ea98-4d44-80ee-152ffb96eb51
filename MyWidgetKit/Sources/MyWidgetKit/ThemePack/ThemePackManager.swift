//
//  ThemePackManager.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import Foundation
import UIKit

/// 主题包管理器
@MainActor
public class ThemePackManager: ObservableObject {

    // MARK: - 单例
    public static let shared = ThemePackManager()

    // MARK: - 发布属性
    @Published public var availableThemePacks: [ThemePackMetadata] = []
    @Published public var isLoading: Bool = false
    @Published public var loadingProgress: Double = 0.0
    @Published public var errorMessage: String?

    // MARK: - 私有属性
    private let dataManager = AppGroupDataManager.shared
    private let fileManager = FileManager.default
    private let imageCache = NSCache<NSString, UIImage>()
    private let imageManager = ThemePackImageManager.shared
    private let imageBridge = ThemePackImageBridge.shared

    // 路径配置
    private let bundleThemePacksPath = "BuiltInThemePacks"
    private let sharedThemePacksPath = "theme-packs"
    private let builtInSubPath = "built-in"
    private let customSubPath = "custom"
    private let cacheSubPath = "cache"
    private let metadataSubPath = "metadata"

    // 文件名常量
    private let themeConfigFileName = "theme.json"
    private let metadataFileName = "metadata.json"
    private let previewImageFileName = "preview.png"
    private let installedPacksFileName = "installed-packs.json"
    private let packVersionsFileName = "pack-versions.json"

    // MARK: - 初始化
    private init() {
        setupImageCache()
        _Concurrency.Task {
            await initializeThemePacks()
        }
    }

    // MARK: - 公共方法

    /// 初始化主题包系统
    public func initializeThemePacks() async {
        print("🚀 开始初始化主题包系统...")
        isLoading = true
        loadingProgress = 0.0
        errorMessage = nil

        do {
            // 1. 创建必要的目录结构
            print("📁 步骤1: 创建目录结构...")
            try createDirectoryStructure()
            loadingProgress = 0.1
            print("✅ 目录结构创建完成")

            // 2. 清理无效的主题包文件
            print("🧹 步骤2: 清理无效文件...")
            try cleanupInvalidThemePacks()
            loadingProgress = 0.2
            print("✅ 无效文件清理完成")

            // 3. 检查并复制内置主题包资源（不包括theme.json）
            print("📦 步骤3: 复制内置主题包资源...")
            try await copyBuiltInThemePacksIfNeeded()
            loadingProgress = 0.3
            print("✅ 内置主题包资源复制完成")

            // 4. 生成基础图片资源和配置文件
            print("🎨 步骤4: 生成图片资源和配置文件...")
            try await generateBasicImageResources()
            loadingProgress = 0.6
            print("✅ 图片资源和配置文件生成完成")

            // 5. 扫描并加载所有主题包
            print("🔍 步骤5: 扫描加载主题包...")
            try await loadAllThemePacks()
            loadingProgress = 0.8
            print("✅ 主题包扫描加载完成，共找到 \(availableThemePacks.count) 个主题包")

            // 6. 验证资源完整性
            print("🔍 步骤6: 验证资源完整性...")
            let validationResults = await validateThemePackResources()
            var hasIssues = false
            for (themeId, issues) in validationResults {
                if !issues.isEmpty {
                    hasIssues = true
                    print("⚠️ 主题包「\(themeId)」存在问题:")
                    for issue in issues {
                        print("  - \(issue)")
                    }
                } else {
                    print("✅ 主题包「\(themeId)」验证通过")
                }
            }
            loadingProgress = 0.85

            // 7. 同步图片到键盘扩展
            print("🔄 步骤7: 同步图片到键盘扩展...")
            await imageBridge.syncThemePackImagesToKeyboard()
            loadingProgress = 0.95
            print("✅ 图片同步完成")

            // 8. 更新安装记录
            print("📝 步骤8: 更新安装记录...")
            try updateInstalledPacksRecord()
            loadingProgress = 1.0
            print("✅ 安装记录更新完成")

            if hasIssues {
                print("⚠️ 初始化完成，但部分主题包存在问题，建议运行强制重新生成")
            } else {
                print("🎉 主题包系统初始化完全成功！")
            }

        } catch {
            errorMessage = "初始化主题包失败: \(error.localizedDescription)"
            print("❌ ThemePackManager初始化失败: \(error)")

            // 尝试降级处理
            print("🔄 尝试降级处理...")
            do {
                try await forceRegenerateBuiltInThemePacks()
                print("✅ 降级处理成功")
            } catch {
                print("❌ 降级处理也失败: \(error)")
            }
        }

        isLoading = false
        print("🏁 主题包系统初始化流程结束")
    }

    /// 获取主题包
    public func getThemePack(id: String) async throws -> ThemePackBundle? {
        print("🔍 开始获取主题包: \(id)")

        guard let metadata = availableThemePacks.first(where: { $0.id == id }) else {
            print("❌ 找不到主题包元数据: \(id)")
            return nil
        }

        let themePackPath = getThemePackPath(id: id, isBuiltIn: metadata.isBuiltIn)
        let configPath = themePackPath.appendingPathComponent(themeConfigFileName)

        print("📁 主题包路径: \(themePackPath.path)")
        print("📄 配置文件路径: \(configPath.path)")

        guard fileManager.fileExists(atPath: configPath.path) else {
            print("❌ 配置文件不存在: \(configPath.path)")

            // 列出目录内容进行调试
            if fileManager.fileExists(atPath: themePackPath.path) {
                do {
                    let contents = try fileManager.contentsOfDirectory(atPath: themePackPath.path)
                    print("📂 主题包目录内容: \(contents)")
                } catch {
                    print("❌ 无法读取主题包目录: \(error)")
                }
            } else {
                print("❌ 主题包目录不存在: \(themePackPath.path)")
            }

            throw ThemePackError.configFileNotFound(id)
        }

        do {
            let configData = try Data(contentsOf: configPath)
            print("✅ 成功读取配置文件，大小: \(configData.count) 字节")

            // 先尝试解析为基本JSON来调试
            if let jsonObject = try? JSONSerialization.jsonObject(with: configData, options: []) {
                print("📄 配置JSON预览: \(jsonObject)")
            }

            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601

            let themeConfig = try decoder.decode(ThemePackConfig.self, from: configData)
            print("✅ 成功解析主题配置: \(themeConfig.themeInfo.name)")

            return ThemePackBundle(metadata: metadata, config: themeConfig)
        } catch let decodingError as DecodingError {
            print("❌ 解析主题配置失败:")
            switch decodingError {
            case .keyNotFound(let key, let context):
                print("  缺少键: \(key.stringValue)")
                print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                print("  描述: \(context.debugDescription)")
            case .typeMismatch(let type, let context):
                print("  类型不匹配: 期望 \(type)")
                print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                print("  描述: \(context.debugDescription)")
            case .valueNotFound(let type, let context):
                print("  值不存在: 期望 \(type)")
                print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                print("  描述: \(context.debugDescription)")
            case .dataCorrupted(let context):
                print("  数据损坏:")
                print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                print("  描述: \(context.debugDescription)")
            @unknown default:
                print("  未知解码错误: \(decodingError)")
            }
            throw ThemePackError.invalidConfiguration("解析配置失败: \(decodingError.localizedDescription)")
        } catch {
            print("❌ 读取配置文件失败: \(error)")
            throw error
        }
    }

    /// 应用主题包
    public func applyThemePack(id: String) async throws {
        print("🎨 开始应用主题包: \(id)")

        guard let themePack = try await getThemePack(id: id) else {
            throw ThemePackError.themePackNotFound(id)
        }

        // 转换为AdvancedKeyboardTheme
        let advancedTheme = try convertToAdvancedTheme(from: themePack)

        // 应用主题
        let advancedThemeManager = AdvancedKeyboardThemeManager.shared
        advancedThemeManager.applyAdvancedTheme(advancedTheme)

        // 同时应用基础主题
        let baseTheme = convertToKeyboardTheme(from: themePack.config.baseTheme)
        let keyboardThemeManager = KeyboardThemeManager.shared
        keyboardThemeManager.setTheme(baseTheme)

        // 复制图片资源到键盘扩展可访问位置
        do {
            try await copyThemePackImagesToKeyboard(themePack: themePack)
            print("✅ 主题包图片资源复制成功")
        } catch {
            print("⚠️ 图片资源复制失败，将使用纯色主题: \(error.localizedDescription)")
            // 继续执行，使用纯色主题作为降级方案
        }

        // 同步图片到键盘扩展
        await imageBridge.syncThemePackImagesToKeyboard()

        print("✅ 主题包「\(themePack.metadata.name)」应用成功")
    }

    /// 获取预览图
    public func getPreviewImage(for themePackId: String) async -> UIImage? {
        let cacheKey = NSString(string: "preview_\(themePackId)")

        // 检查内存缓存
        if let cachedImage = imageCache.object(forKey: cacheKey) {
            return cachedImage
        }

        // 在主线程上获取元数据
        let metadata = await MainActor.run {
            availableThemePacks.first(where: { $0.id == themePackId })
        }

        guard let metadata = metadata else {
            return nil
        }

        let themePackPath = getThemePackPath(id: themePackId, isBuiltIn: metadata.isBuiltIn)
        let previewPath = themePackPath.appendingPathComponent(previewImageFileName)

        guard let image = UIImage(contentsOfFile: previewPath.path) else {
            return nil
        }

        // 缓存图片
        imageCache.setObject(image, forKey: cacheKey)
        return image
    }

    /// 刷新主题包列表
    public func refreshThemePacks() async {
        await initializeThemePacks()
    }

    /// 获取主题包背景图片
    public func getThemePackBackgroundImage(for themePackId: String) async -> UIImage? {
        // 在主线程上检查元数据
        let hasMetadata = await MainActor.run {
            availableThemePacks.first(where: { $0.id == themePackId }) != nil
        }

        guard hasMetadata else { return nil }

        // 获取主题包数据
        guard let themePack = try? await getThemePack(id: themePackId),
              let backgroundPath = themePack.config.baseTheme.images.backgroundImagePath else {
            return nil
        }

        // 在主线程上获取imageManager引用
        let manager = await MainActor.run { imageManager }

        return await manager.getKeyboardBackgroundImage(for: themePackId, imagePath: backgroundPath)
    }

    /// 获取按键图片
    public func getKeyImage(
        for themePackId: String,
        keyType: KeyType,
        state: KeyImageState = .normal
    ) async -> UIImage? {
        // 先获取主题包数据
        guard let themePack = try? await getThemePack(id: themePackId) else {
            return nil
        }

        // 提取需要的数据
        let keyImages = themePack.config.baseTheme.images.keyImages

        // 在主线程上获取imageManager引用
        let manager = await MainActor.run { imageManager }
let safeState = await MainActor.run { state }

        return await manager.getKeyImage(
            for: themePackId,
            keyType: keyType,
            state: safeState,
            keyImages: keyImages
        )
    }

    /// 预加载主题包图片
    public func preloadThemePackImages(for themePackId: String) async {
        // 先获取主题包数据
        guard let themePack = try? await getThemePack(id: themePackId) else {
            return
        }

        // 提取需要的数据
        let imageConfig = themePack.config.baseTheme.images

        // 在主线程上获取imageManager引用
        let manager = await MainActor.run { imageManager }

        await manager.preloadThemePackImages(
            for: themePackId,
            imageConfig: imageConfig
        )
    }

    /// 验证主题包图片资源
    public func validateThemePackImages(for themePackId: String) async -> [String] {
        // 先获取主题包数据
        guard let themePack = try? await getThemePack(id: themePackId) else {
            return ["无法加载主题包配置"]
        }

        // 提取需要的数据
        let imageConfig = themePack.config.baseTheme.images

        // 在主线程上获取imageManager引用
        let manager = await MainActor.run { imageManager }

        return manager.validateImageResources(
            for: themePackId,
            imageConfig: imageConfig
        )
    }

    /// 清理缓存
    public func clearCache() {
        imageCache.removeAllObjects()
        imageManager.clearAllImageCache()

        let cachePath = getSharedDirectory().appendingPathComponent(cacheSubPath)
        try? fileManager.removeItem(at: cachePath)
        try? fileManager.createDirectory(at: cachePath, withIntermediateDirectories: true)
    }

    /// 强制清理所有无效文件并重新初始化
    public func forceCleanupAndReinitialize() async {
        print("🧹 开始强制清理和重新初始化...")

        // 清理缓存
        clearCache()

        // 清理无效文件
        try? cleanupInvalidThemePacks()

        // 重新初始化
        await initializeThemePacks()

        print("✅ 强制清理和重新初始化完成")
    }

    /// 强制重新生成所有内置主题包资源
    public func forceRegenerateBuiltInThemePacks() async throws {
        print("🎨 开始强制重新生成内置主题包...")

        // 1. 删除现有的内置主题包目录
        let builtInDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(builtInSubPath)

        if fileManager.fileExists(atPath: builtInDir.path) {
            try? fileManager.removeItem(at: builtInDir)
            print("🗑️ 已删除现有内置主题包目录")
        }

        // 2. 重新创建目录
        try? fileManager.createDirectory(at: builtInDir, withIntermediateDirectories: true)

        // 3. 强制重新生成所有资源
        do {
            try await generateBasicImageResources()
            print("✅ 内置主题包重新生成完成")
        } catch {
            print("❌ 重新生成失败: \(error)")
        }

        // 4. 重新加载主题包列表
        try await loadAllThemePacks()

        // 5. 同步图片到键盘扩展
        await imageBridge.syncThemePackImagesToKeyboard()

        print("🎉 强制重新生成完成")
    }

    /// 验证主题包资源完整性
    public func validateThemePackResources() async -> [String: [String]] {
        var validationResults: [String: [String]] = [:]

        let builtInDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(builtInSubPath)

        let expectedThemes = ["classic-light", "nature-wood", "neon-cyber", "colorful-vibrant"]

        for themeId in expectedThemes {
            var issues: [String] = []
            let themeDir = builtInDir.appendingPathComponent(themeId)

            // 检查主题包目录
            if !fileManager.fileExists(atPath: themeDir.path) {
                issues.append("主题包目录不存在: \(themeDir.path)")
                validationResults[themeId] = issues
                continue
            }

            // 检查metadata.json
            let metadataPath = themeDir.appendingPathComponent("metadata.json")
            if !fileManager.fileExists(atPath: metadataPath.path) {
                issues.append("缺少metadata.json文件")
            }

            // 检查theme.json
            let configPath = themeDir.appendingPathComponent("theme.json")
            if !fileManager.fileExists(atPath: configPath.path) {
                issues.append("缺少theme.json文件")
            }

            // 检查预览图片
            let previewPath = themeDir.appendingPathComponent("preview.png")
            if !fileManager.fileExists(atPath: previewPath.path) {
                issues.append("缺少preview.png文件")
            }

            // 检查背景图片
            let bgPath = themeDir.appendingPathComponent("resources/backgrounds/keyboard-bg.png")
            if !fileManager.fileExists(atPath: bgPath.path) {
                issues.append("缺少背景图片: resources/backgrounds/keyboard-bg.png")
            }

            // 检查按键图片
            let keyTypes = ["letter", "number", "function", "space", "shift", "symbol", "punctuation"]
            for keyType in keyTypes {
                let normalPath = themeDir.appendingPathComponent("resources/keys/\(keyType)-key.png")
                let pressedPath = themeDir.appendingPathComponent("resources/keys/\(keyType)-key-pressed.png")

                if !fileManager.fileExists(atPath: normalPath.path) {
                    issues.append("缺少按键图片: resources/keys/\(keyType)-key.png")
                }

                if !fileManager.fileExists(atPath: pressedPath.path) {
                    issues.append("缺少按键图片: resources/keys/\(keyType)-key-pressed.png")
                }
            }

            validationResults[themeId] = issues
        }

        return validationResults
    }

    /// 打印主题包目录结构
    public func printThemePackStructure() {
        print("📁 主题包目录结构:")

        let baseDir = getSharedDirectory().appendingPathComponent(sharedThemePacksPath)
        print("基础目录: \(baseDir.path)")

        let builtInDir = baseDir.appendingPathComponent(builtInSubPath)
        print("内置主题包目录: \(builtInDir.path)")

        if let contents = try? fileManager.contentsOfDirectory(atPath: builtInDir.path) {
            for item in contents {
                let itemPath = builtInDir.appendingPathComponent(item)
                var isDirectory: ObjCBool = false

                if fileManager.fileExists(atPath: itemPath.path, isDirectory: &isDirectory) {
                    if isDirectory.boolValue {
                        print("  📦 \(item)/")
                        printDirectoryContents(at: itemPath, level: 2)
                    } else {
                        print("  📄 \(item)")
                    }
                }
            }
        } else {
            print("  ❌ 无法读取目录内容")
        }
    }

    private func printDirectoryContents(at directory: URL, level: Int) {
        let indent = String(repeating: "  ", count: level)

        if let contents = try? fileManager.contentsOfDirectory(atPath: directory.path) {
            for item in contents {
                let itemPath = directory.appendingPathComponent(item)
                var isDirectory: ObjCBool = false

                if fileManager.fileExists(atPath: itemPath.path, isDirectory: &isDirectory) {
                    if isDirectory.boolValue {
                        print("\(indent)📁 \(item)/")
                        if level < 4 { // 限制递归深度
                            printDirectoryContents(at: itemPath, level: level + 1)
                        }
                    } else {
                        print("\(indent)📄 \(item)")
                    }
                }
            }
        }
    }

    // MARK: - 私有方法

    /// 复制主题包图片资源到键盘扩展可访问位置
    private func copyThemePackImagesToKeyboard(themePack: ThemePackBundle) async throws {
        print("📋 开始复制主题包图片资源到键盘扩展目录...")

        let themeId = themePack.metadata.id
        let isBuiltIn = themePack.metadata.isBuiltIn

        // 源路径：根据是否为内置主题包选择正确的源路径
        guard let sourceThemePackPath = getThemePackSourcePath(id: themeId, isBuiltIn: isBuiltIn) else {
            throw ThemePackError.resourceNotFound("无法找到主题包「\(themeId)」的源路径")
        }

        let sourceResourcesPath = sourceThemePackPath.appendingPathComponent("resources")

        print("📁 源路径: \(sourceThemePackPath.path)")
        print("📁 资源路径: \(sourceResourcesPath.path)")

        // 验证源资源目录是否存在
        guard fileManager.fileExists(atPath: sourceResourcesPath.path) else {
            print("❌ 源资源目录不存在: \(sourceResourcesPath.path)")
            throw ThemePackError.resourceNotFound("主题包「\(themeId)」的资源目录不存在")
        }

        // 目标路径：键盘扩展可访问的目录
        let keyboardThemesDir = getSharedDirectory().appendingPathComponent("keyboard_themes")
        let targetSubDir = isBuiltIn ? "built-in" : "custom"
        let targetThemeDir = keyboardThemesDir.appendingPathComponent(targetSubDir)

        print("📁 目标路径: \(targetThemeDir.path)")
        print("📁 目标子目录: \(targetSubDir)")
        print("📁 注意：图片将直接复制到 \(targetSubDir) 目录下，覆盖当前主题")

        // 创建目标目录结构（直接在built-in目录下，不需要子目录）
        try fileManager.createDirectory(at: targetThemeDir, withIntermediateDirectories: true)

        // 复制背景图片（重命名为主题ID_background.png）
        try await copyBackgroundImages(from: sourceResourcesPath, to: targetThemeDir, themeId: themeId)

        // 复制按键图片（重命名为主题ID_key.png等）
        try await copyKeyImages(from: sourceResourcesPath, to: targetThemeDir, themeId: themeId)

        // 复制个性化按键图片（custom目录）
        try await copyCustomKeyImages(from: sourceResourcesPath, to: targetThemeDir, themeId: themeId)

        print("✅ 主题包「\(themeId)」图片资源复制完成")
    }

    /// 复制背景图片
    private func copyBackgroundImages(from sourceResourcesPath: URL, to targetThemeDir: URL, themeId: String) async throws {
        // Bundle中使用通用文件名
        let sourceBgPath = sourceResourcesPath.appendingPathComponent("backgrounds/keyboard-bg.png")
        // 复制后使用主题特定的文件名
        let targetFileName = "\(themeId)_background.png"
        let targetBgPath = targetThemeDir.appendingPathComponent(targetFileName)

        guard fileManager.fileExists(atPath: sourceBgPath.path) else {
            print("⚠️ Bundle中背景图片不存在: \(sourceBgPath.path)")
            throw ThemePackError.copyFailed("Bundle中背景图片文件不存在: keyboard-bg.png")
        }

        // 如果目标文件已存在，先删除
        if fileManager.fileExists(atPath: targetBgPath.path) {
            try fileManager.removeItem(at: targetBgPath)
        }

        try fileManager.copyItem(at: sourceBgPath, to: targetBgPath)
        print("  ✅ 背景图片复制成功: keyboard-bg.png -> \(targetFileName)")
    }

    /// 复制按键图片
    private func copyKeyImages(from sourceResourcesPath: URL, to targetThemeDir: URL, themeId: String) async throws {
        let keyTypes = ["letter", "number", "function", "space", "shift", "symbol", "punctuation"]
        let states = ["", "-pressed"] // 正常状态和按下状态

        var copiedCount = 0
        var failedFiles: [String] = []

        for keyType in keyTypes {
            for state in states {
                // Bundle中使用通用文件名
                let sourceFileName = "\(keyType)-key\(state).png"
                let sourceKeyPath = sourceResourcesPath.appendingPathComponent("keys/\(sourceFileName)")

                // 复制后使用主题特定的文件名
                let targetFileName = state.isEmpty ?
                    "\(themeId)_\(keyType)_key.png" :
                    "\(themeId)_\(keyType)_key_pressed.png"
                let targetKeyPath = targetThemeDir.appendingPathComponent(targetFileName)

                if fileManager.fileExists(atPath: sourceKeyPath.path) {
                    do {
                        // 如果目标文件已存在，先删除
                        if fileManager.fileExists(atPath: targetKeyPath.path) {
                            try fileManager.removeItem(at: targetKeyPath)
                        }

                        try fileManager.copyItem(at: sourceKeyPath, to: targetKeyPath)
                        copiedCount += 1
                        print("  ✅ 复制: \(sourceFileName) -> \(targetFileName)")
                    } catch {
                        failedFiles.append(sourceFileName)
                        print("  ❌ 复制失败: \(sourceFileName) - \(error.localizedDescription)")
                    }
                } else {
                    failedFiles.append(sourceFileName)
                    print("  ⚠️ Bundle中源文件不存在: \(sourceFileName)")
                    print("      期望路径: \(sourceKeyPath.path)")
                }
            }
        }

        print("  ✅ 按键图片复制完成: \(copiedCount)个文件成功")

        if !failedFiles.isEmpty {
            print("  ⚠️ 部分文件复制失败: \(failedFiles.joined(separator: ", "))")
            print("  💡 请确保Bundle中包含通用命名的文件: letter-key.png, letter-key-pressed.png 等")
            // 不抛出错误，允许部分文件缺失的情况下继续使用主题
        }
    }

    /// 复制个性化按键图片（custom目录）
    private func copyCustomKeyImages(from sourceResourcesPath: URL, to targetThemeDir: URL, themeId: String) async throws {
        let customKeysPath = sourceResourcesPath.appendingPathComponent("keys/custom")

        // 检查custom目录是否存在
        guard fileManager.fileExists(atPath: customKeysPath.path) else {
            print("  ℹ️ 没有找到custom目录，跳过个性化按键图片复制")
            return
        }

        print("  🎨 开始复制个性化按键图片...")

        do {
            let customFiles = try fileManager.contentsOfDirectory(atPath: customKeysPath.path)
            let pngFiles = customFiles.filter { $0.hasSuffix(".png") }

            var copiedCount = 0
            var failedFiles: [String] = []

            for fileName in pngFiles {
                let sourceCustomPath = customKeysPath.appendingPathComponent(fileName)
                // 保持原始文件名，但添加主题ID前缀以避免冲突
                let targetFileName = "\(themeId)_custom_\(fileName)"
                let targetCustomPath = targetThemeDir.appendingPathComponent(targetFileName)

                do {
                    // 如果目标文件已存在，先删除
                    if fileManager.fileExists(atPath: targetCustomPath.path) {
                        try fileManager.removeItem(at: targetCustomPath)
                    }

                    try fileManager.copyItem(at: sourceCustomPath, to: targetCustomPath)
                    copiedCount += 1
                    print("    ✅ 复制个性化图片: \(fileName) -> \(targetFileName)")
                } catch {
                    failedFiles.append(fileName)
                    print("    ❌ 复制个性化图片失败: \(fileName) - \(error.localizedDescription)")
                }
            }

            print("  ✅ 个性化按键图片复制完成: \(copiedCount)个文件成功")
            if !failedFiles.isEmpty {
                print("  ⚠️ 部分个性化图片复制失败: \(failedFiles.joined(separator: ", "))")
            }

        } catch {
            print("  ❌ 读取custom目录失败: \(error.localizedDescription)")
        }
    }

    private func setupImageCache() {
        imageCache.countLimit = 50 // 最多缓存50张图片
        imageCache.totalCostLimit = 50 * 1024 * 1024 // 50MB内存限制
    }

    private func createDirectoryStructure() throws {
        let sharedDir = getSharedDirectory()
        let themePacksDir = sharedDir.appendingPathComponent(sharedThemePacksPath)
        let keyboardThemesDir = sharedDir.appendingPathComponent("keyboard_themes")

        let directories = [
            // 主题包目录结构
            themePacksDir.appendingPathComponent(builtInSubPath),
            themePacksDir.appendingPathComponent(customSubPath),
            themePacksDir.appendingPathComponent(cacheSubPath),
            themePacksDir.appendingPathComponent(metadataSubPath),
            themePacksDir.appendingPathComponent(cacheSubPath).appendingPathComponent("previews"),
            themePacksDir.appendingPathComponent(cacheSubPath).appendingPathComponent("processed"),

            // 键盘扩展可访问的目录结构
            keyboardThemesDir,
            keyboardThemesDir.appendingPathComponent("built-in"),
            keyboardThemesDir.appendingPathComponent("custom")
        ]

        for directory in directories {
            try fileManager.createDirectory(at: directory, withIntermediateDirectories: true)
            print("📁 创建目录: \(directory.lastPathComponent)")
        }

        print("✅ 目录结构创建完成")
    }

    private func cleanupInvalidThemePacks() throws {
        let builtInDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(builtInSubPath)

        let customDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(customSubPath)

        // 清理内置主题包目录中的无效文件
        try cleanupInvalidFilesInDirectory(builtInDir)

        // 清理自定义主题包目录中的无效文件
        try cleanupInvalidFilesInDirectory(customDir)
    }

    private func cleanupInvalidFilesInDirectory(_ directory: URL) throws {
        guard fileManager.fileExists(atPath: directory.path) else {
            return
        }

        let contents = try fileManager.contentsOfDirectory(atPath: directory.path)

        for item in contents {
            // 检查是否为无效文件
            if item.hasPrefix(".") ||
               item.hasSuffix(".md") ||
               item.hasSuffix(".txt") ||
               item.hasSuffix(".json") ||
               item.hasSuffix(".plist") {

                let itemPath = directory.appendingPathComponent(item)

                // 检查是否为文件（而不是目录）
                var isDirectory: ObjCBool = false
                if fileManager.fileExists(atPath: itemPath.path, isDirectory: &isDirectory),
                   !isDirectory.boolValue {
                    try fileManager.removeItem(at: itemPath)
                    print("🗑️ 清理无效文件: \(item)")
                }
            }
        }
    }

    private func generateBasicImageResources() async throws {
        print("🎨 开始生成内置主题包配置文件...")

        let themes: [(id: String, name: String, description: String, category: String, style: String, bg: UIColor, key: UIColor)] = [
            (id: "classic-light", name: "经典浅色", description: "iOS系统风格的经典浅色主题", category: "classic", style: "light",
             bg: UIColor(red: 0.96, green: 0.96, blue: 0.97, alpha: 1.0),
             key: UIColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)),
            (id: "nature-wood", name: "自然木质", description: "温暖自然的木质纹理主题", category: "nature", style: "warm",
             bg: UIColor(red: 0.55, green: 0.27, blue: 0.07, alpha: 1.0),
             key: UIColor(red: 0.87, green: 0.72, blue: 0.53, alpha: 1.0)),
            (id: "neon-cyber", name: "霓虹赛博", description: "未来科技感的霓虹赛博主题", category: "cyber", style: "dark",
             bg: UIColor(red: 0.08, green: 0.08, blue: 0.12, alpha: 1.0),
             key: UIColor(red: 0.16, green: 0.16, blue: 0.24, alpha: 1.0)),
            (id: "colorful-vibrant", name: "彩色活泼", description: "活泼明亮的彩色主题，充满活力的渐变色彩", category: "colorful", style: "vibrant",
             bg: UIColor(red: 0.98, green: 0.95, blue: 1.0, alpha: 1.0),
             key: UIColor(red: 1.0, green: 0.98, blue: 0.95, alpha: 1.0))
        ]

        let keyTypes = ["letter", "number", "function", "space", "shift", "symbol", "punctuation"]

        for theme in themes {
            print("📦 处理主题包: \(theme.name) (\(theme.id))")

            // 创建主题包根目录
            let themePackDir = getSharedDirectory()
                .appendingPathComponent(sharedThemePacksPath)
                .appendingPathComponent(builtInSubPath)
                .appendingPathComponent(theme.id)

            let resourcesDir = themePackDir.appendingPathComponent("resources")

            // 检查是否已经从Bundle复制了资源
            let bundleResourcesExist = checkBundleResourcesExist(for: theme.id)
            let appGroupResourcesExist = fileManager.fileExists(atPath: resourcesDir.path)

            print("  🔍 Bundle资源存在: \(bundleResourcesExist)")
            print("  🔍 App Groups资源存在: \(appGroupResourcesExist)")

            // 只有在Bundle中没有资源且App Groups中也没有资源时，才生成图片
            if !bundleResourcesExist && !appGroupResourcesExist {
                print("  🎨 Bundle中无资源，生成默认图片...")

                // 创建目录结构
                try fileManager.createDirectory(at: resourcesDir.appendingPathComponent("backgrounds"),
                                              withIntermediateDirectories: true)
                try fileManager.createDirectory(at: resourcesDir.appendingPathComponent("keys"),
                                              withIntermediateDirectories: true)

                // 生成背景图片
                let bgImage = createSolidColorImage(size: CGSize(width: 1024, height: 512), color: theme.bg)
                let bgPath = resourcesDir.appendingPathComponent("backgrounds/keyboard-bg.png")
                if let bgData = bgImage.pngData() {
                    try bgData.write(to: bgPath)
                    print("    ✅ 生成背景图片: backgrounds/keyboard-bg.png")
                }

                // 生成按键图片
                for keyType in keyTypes {
                    // 正常状态
                    let keyImage = createSolidColorImage(size: CGSize(width: 64, height: 64), color: theme.key)
                    let keyPath = resourcesDir.appendingPathComponent("keys/\(keyType)-key.png")
                    if let keyData = keyImage.pngData() {
                        try keyData.write(to: keyPath)
                    }

                    // 按下状态
                    let pressedColor = UIColor(red: theme.key.cgColor.components![0] * 0.9,
                                             green: theme.key.cgColor.components![1] * 0.9,
                                             blue: theme.key.cgColor.components![2] * 0.9,
                                             alpha: 1.0)
                    let pressedImage = createSolidColorImage(size: CGSize(width: 64, height: 64), color: pressedColor)
                    let pressedPath = resourcesDir.appendingPathComponent("keys/\(keyType)-key-pressed.png")
                    if let pressedData = pressedImage.pngData() {
                        try pressedData.write(to: pressedPath)
                    }
                }
                print("    ✅ 生成按键图片: \(keyTypes.count * 2)个文件")
            } else {
                print("  ⏭️ 跳过图片生成，使用Bundle中的真实图片资源")
            }

            // 总是生成预览图片（如果不存在）
            let previewPath = themePackDir.appendingPathComponent("preview.png")
            if !fileManager.fileExists(atPath: previewPath.path) {
                // 首先尝试从Bundle复制预览图片
                var previewCopied = false
                if let bundleSourcePath = getThemePackSourcePath(id: theme.id, isBuiltIn: true) {
                    let bundlePreviewPath = bundleSourcePath.appendingPathComponent("preview.png")
                    if fileManager.fileExists(atPath: bundlePreviewPath.path) {
                        do {
                            try fileManager.copyItem(at: bundlePreviewPath, to: previewPath)
                            print("  ✅ 从Bundle复制预览图片: preview.png")
                            previewCopied = true
                        } catch {
                            print("  ⚠️ 从Bundle复制预览图片失败: \(error)")
                        }
                    }
                }

                // 如果Bundle中没有预览图片，则生成默认预览图片
                if !previewCopied {
                    let previewImage = createSolidColorImage(size: CGSize(width: 300, height: 200), color: theme.bg)
                    if let previewData = previewImage.pngData() {
                        try previewData.write(to: previewPath)
                        print("  ✅ 生成预览图片: preview.png")
                    }
                }
            }

            // 总是生成metadata.json和theme.json（确保配置文件是最新的）
            try generateMetadataFile(for: theme, at: themePackDir)
            try generateThemeConfigFile(for: theme, at: themePackDir)

            print("  🎉 主题包「\(theme.name)」处理完成")
        }

        print("🎉 所有内置主题包处理完成")
    }

    /// 检查Bundle中是否存在主题包资源
    private func checkBundleResourcesExist(for themeId: String) -> Bool {
        guard let bundleSourcePath = getThemePackSourcePath(id: themeId, isBuiltIn: true) else {
            return false
        }

        let resourcesPath = bundleSourcePath.appendingPathComponent("resources")
        let backgroundsPath = resourcesPath.appendingPathComponent("backgrounds")
        let keysPath = resourcesPath.appendingPathComponent("keys")

        // 检查关键资源文件是否存在
        let bgExists = fileManager.fileExists(atPath: backgroundsPath.appendingPathComponent("keyboard-bg.png").path)
        let letterKeyExists = fileManager.fileExists(atPath: keysPath.appendingPathComponent("letter-key.png").path)

        return bgExists && letterKeyExists
    }

    private func createSolidColorImage(size: CGSize, color: UIColor) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            color.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
    }

    private func generateMetadataFile(for theme: (id: String, name: String, description: String, category: String, style: String, bg: UIColor, key: UIColor), at directory: URL) throws {
        let metadata = ThemePackMetadata(
            id: theme.id,
            name: theme.name,
            description: theme.description,
            version: "1.0.0",
            category: theme.category,
            style: theme.style,
            author: "JZJJWidget Team",
            createdAt: Date(),
            updatedAt: Date(),
            compatibility: CompatibilityInfo(
                minIOSVersion: "13.0",
                minAppVersion: "1.0.0",
                supportedDevices: ["iPhone", "iPad"]
            ),
            resources: ResourceInfo(
                previewImage: "preview.png",
                backgroundImages: ["resources/backgrounds/keyboard-bg.png"],
                keyImages: [
                    "resources/keys/letter-key.png",
                    "resources/keys/number-key.png",
                    "resources/keys/function-key.png",
                    "resources/keys/space-key.png",
                    "resources/keys/shift-key.png",
                    "resources/keys/symbol-key.png",
                    "resources/keys/punctuation-key.png"
                ],
                totalSize: 1024768,
                compressedSize: 512384
            ),
            features: FeatureInfo(
                supportsDarkMode: theme.style == "dark",
                hasAnimations: true,
                hasSounds: true,
                hasHaptics: true,
                customFonts: false
            ),
            tags: [theme.category, theme.style, "内置"],
            rating: 4.8,
            downloadCount: 0,
            isPremium: false,
            isBuiltIn: true
        )

        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]

        let data = try encoder.encode(metadata)
        let metadataPath = directory.appendingPathComponent(metadataFileName)
        try data.write(to: metadataPath)

        print("  ✅ 元数据文件: metadata.json")
    }

    private func generateThemeConfigFile(for theme: (id: String, name: String, description: String, category: String, style: String, bg: UIColor, key: UIColor), at directory: URL) throws {
        // 转换UIColor为WidgetColor
        let bgColor = WidgetColor(
            red: Double(theme.bg.cgColor.components![0]),
            green: Double(theme.bg.cgColor.components![1]),
            blue: Double(theme.bg.cgColor.components![2]),
            alpha: Double(theme.bg.cgColor.components![3])
        )

        let keyColor = WidgetColor(
            red: Double(theme.key.cgColor.components![0]),
            green: Double(theme.key.cgColor.components![1]),
            blue: Double(theme.key.cgColor.components![2]),
            alpha: Double(theme.key.cgColor.components![3])
        )

        let pressedColor = WidgetColor(
            red: Double(theme.key.cgColor.components![0]) * 0.9,
            green: Double(theme.key.cgColor.components![1]) * 0.9,
            blue: Double(theme.key.cgColor.components![2]) * 0.9,
            alpha: 1.0
        )

        let textColor = theme.style == "dark" ?
            WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0) :
            WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0)

        let specialKeyColor = WidgetColor(
            red: Double(theme.key.cgColor.components![0]) * 0.8,
            green: Double(theme.key.cgColor.components![1]) * 0.8,
            blue: Double(theme.key.cgColor.components![2]) * 0.8,
            alpha: 1.0
        )

        let borderColor = WidgetColor(
            red: Double(theme.key.cgColor.components![0]) * 0.7,
            green: Double(theme.key.cgColor.components![1]) * 0.7,
            blue: Double(theme.key.cgColor.components![2]) * 0.7,
            alpha: 1.0
        )

        let shadowColor = WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.2)

        let themeConfig = ThemePackConfig(
            schemaVersion: "1.0.0",
            themeInfo: ThemeInfo(
                id: theme.id,
                name: theme.name,
                description: theme.description
            ),
            baseTheme: BaseThemeConfig(
                id: theme.id,
                name: theme.name,
                type: theme.style,
                keyStyle: "rounded",
                colors: ColorConfig(
                    background: bgColor,
                    keyBackground: keyColor,
                    keyPressed: pressedColor,
                    text: textColor,
                    specialKey: specialKeyColor,
                    border: borderColor
                ),
                images: ImageConfig(
                    hasBackgroundImage: true,
                    hasKeyImage: true,
                    backgroundImagePath: "resources/backgrounds/keyboard-bg.png",
                    keyImagePath: "resources/keys/letter-key.png",
                    keyImages: KeyTypeImages(
                        letter: KeyStateImages(
                            normal: "resources/keys/letter-key.png",
                            pressed: "resources/keys/letter-key-pressed.png"
                        ),
                        number: KeyStateImages(
                            normal: "resources/keys/number-key.png",
                            pressed: "resources/keys/number-key-pressed.png"
                        ),
                        function: KeyStateImages(
                            normal: "resources/keys/function-key.png",
                            pressed: "resources/keys/function-key-pressed.png"
                        ),
                        space: KeyStateImages(
                            normal: "resources/keys/space-key.png",
                            pressed: "resources/keys/space-key-pressed.png"
                        ),
                        shift: KeyStateImages(
                            normal: "resources/keys/shift-key.png",
                            pressed: "resources/keys/shift-key-pressed.png"
                        ),
                        symbol: KeyStateImages(
                            normal: "resources/keys/symbol-key.png",
                            pressed: "resources/keys/symbol-key-pressed.png"
                        ),
                        punctuation: KeyStateImages(
                            normal: "resources/keys/punctuation-key.png",
                            pressed: "resources/keys/punctuation-key-pressed.png"
                        )
                    ),
                    isBuiltInImageTheme: true,
                    imageOpacity: 0.8,
                    imageBlendMode: "normal"
                ),
                typography: TypographyConfig(
                    fontName: "SF Pro",
                    fontSize: 16,
                    fontWeight: "medium"
                ),
                layout: LayoutConfig(
                    keySpacing: 6,
                    keyHeight: 44,
                    showBorder: true,
                    borderWidth: 1
                ),
                effects: EffectConfig(
                    enableShadow: true,
                    shadowColor: shadowColor,
                    shadowRadius: 2,
                    enableHaptic: true,
                    enableSound: true
                )
            ),
            advancedConfig: AdvancedThemeConfig(
                globalSettings: GlobalSettings(
                    keySpacing: 6,
                    keyHeight: 44,
                    enableHapticFeedback: true,
                    enableSoundFeedback: true,
                    enableKeyAnimations: true,
                    animationDuration: 0.1,
                    enableGradientEffects: false,
                    enableParallaxEffect: false
                ),
                keyTypeConfigs: [
                    KeyType.letter.rawValue: KeyTypeConfig(
                        keyType: .letter,
                        name: "字母键",
                        description: "字母按键配置",
                        defaultBackgroundColor: keyColor,
                        defaultPressedColor: pressedColor,
                        defaultTextColor: textColor,
                        defaultBorderColor: borderColor,
                        defaultFontSize: 16,
                        defaultFontWeight: .medium,
                        defaultCornerRadius: 8,
                        defaultBorderWidth: 0,
                        affectedKeys: Array("ABCDEFGHIJKLMNOPQRSTUVWXYZ").map { String($0) }
                    ),
                    KeyType.number.rawValue: KeyTypeConfig(
                        keyType: .number,
                        name: "数字键",
                        description: "数字按键配置",
                        defaultBackgroundColor: keyColor,
                        defaultPressedColor: pressedColor,
                        defaultTextColor: textColor,
                        defaultBorderColor: borderColor,
                        defaultFontSize: 16,
                        defaultFontWeight: .medium,
                        defaultCornerRadius: 8,
                        defaultBorderWidth: 0,
                        affectedKeys: Array("1234567890").map { String($0) }
                    ),
                    KeyType.function.rawValue: KeyTypeConfig(
                        keyType: .function,
                        name: "功能键",
                        description: "功能按键配置",
                        defaultBackgroundColor: specialKeyColor,
                        defaultPressedColor: WidgetColor(
                            red: Double(specialKeyColor.red) * 0.9,
                            green: Double(specialKeyColor.green) * 0.9,
                            blue: Double(specialKeyColor.blue) * 0.9,
                            alpha: 1.0
                        ),
                        defaultTextColor: textColor,
                        defaultBorderColor: borderColor,
                        defaultFontSize: 14,
                        defaultFontWeight: .medium,
                        defaultCornerRadius: 8,
                        defaultBorderWidth: 0,
                        affectedKeys: ["shift", "delete", "enter", "123", "globe"]
                    ),
                    KeyType.space.rawValue: KeyTypeConfig(
                        keyType: .space,
                        name: "空格键",
                        description: "空格按键配置",
                        defaultBackgroundColor: keyColor,
                        defaultPressedColor: pressedColor,
                        defaultTextColor: textColor,
                        defaultBorderColor: borderColor,
                        defaultFontSize: 16,
                        defaultFontWeight: .medium,
                        defaultCornerRadius: 8,
                        defaultBorderWidth: 0,
                        affectedKeys: ["space"]
                    )
                ],
                individualKeyConfigs: generateIndividualKeyConfigs(for: theme.id),
                createdAt: Date(),
                updatedAt: Date()
            ),
            validation: ValidationInfo(
                checksum: "auto-generated",
                fileCount: 16, // 1背景 + 14按键图片 + 1预览图
                totalSize: 1024768
            )
        )

        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601
        encoder.outputFormatting = [.prettyPrinted, .sortedKeys]

        let data = try encoder.encode(themeConfig)
        let configPath = directory.appendingPathComponent(themeConfigFileName)
        try data.write(to: configPath)

        print("  ✅ 配置文件: theme.json")
    }

    /// 为特定主题生成个性化按键配置
    private func generateIndividualKeyConfigs(for themeId: String) -> [String: KeyConfig] {
        // 只为 colorful-vibrant 主题生成个性化配置
        guard themeId == "colorful-vibrant" else {
            return [:]
        }

        var configs: [String: KeyConfig] = [:]

        // 字母 A 键配置
        configs["A"] = KeyConfig(
            id: "key-A",
            keyType: .letter,
            keyValue: "A",
            backgroundColor: WidgetColor(red: 1.0, green: 0.6, blue: 0.8, alpha: 1.0),
            pressedColor: WidgetColor(red: 1.0, green: 0.3, blue: 0.6, alpha: 1.0),
            textColor: WidgetColor(red: 0.1, green: 0.1, blue: 0.2, alpha: 1.0),
            borderColor: WidgetColor(red: 0.9, green: 0.4, blue: 0.8, alpha: 0.8),
            fontName: "SF Pro",
            fontSize: 18,
            fontWeight: .bold,
            cornerRadius: 12,
            borderWidth: 2,
            shadowEnabled: true,
            shadowColor: WidgetColor(red: 1.0, green: 0.4, blue: 0.8, alpha: 0.3),
            shadowRadius: 4,
            shadowOffset: CGSize(width: 0, height: 2),
            widthMultiplier: 1.0,
            heightMultiplier: 1.0,
            hasCustomImage: true,
            normalImagePath: "resources/keys/custom/A-key.png",
            pressedImagePath: "resources/keys/custom/A-key-pressed.png",
            hoverImagePath: nil,
            imageOpacity: 0.9,
            imageBlendMode: "normal",
            hideTextWhenImageExists: true  // A键图片已包含文字，隐藏文本
        )

        // 数字 1 键配置
        configs["1"] = KeyConfig(
            id: "key-1",
            keyType: .number,
            keyValue: "1",
            backgroundColor: WidgetColor(red: 0.2, green: 0.9, blue: 0.7, alpha: 1.0),
            pressedColor: WidgetColor(red: 0.1, green: 0.8, blue: 0.6, alpha: 1.0),
            textColor: WidgetColor(red: 0.0, green: 0.2, blue: 0.1, alpha: 1.0),
            borderColor: WidgetColor(red: 0.3, green: 0.7, blue: 1.0, alpha: 0.8),
            fontName: "SF Pro",
            fontSize: 20,
            fontWeight: .semibold,
            cornerRadius: 10,
            borderWidth: 2,
            shadowEnabled: true,
            shadowColor: WidgetColor(red: 0.2, green: 0.9, blue: 0.7, alpha: 0.2),
            shadowRadius: 3,
            shadowOffset: CGSize(width: 1, height: 1),
            widthMultiplier: 1.0,
            heightMultiplier: 1.0,
            hasCustomImage: true,
            normalImagePath: "resources/keys/custom/1-key.png",
            pressedImagePath: "resources/keys/custom/1-key-pressed.png",
            hoverImagePath: nil,
            imageOpacity: 0.85,
            imageBlendMode: "normal",
            hideTextWhenImageExists: true  // 1键图片已包含文字，隐藏文本
        )

        // 空格键配置
        configs["空格"] = KeyConfig(
            id: "key-space",
            keyType: .space,
            keyValue: "空格",
            backgroundColor: WidgetColor(red: 0.9, green: 0.6, blue: 1.0, alpha: 1.0),
            pressedColor: WidgetColor(red: 0.8, green: 0.4, blue: 0.9, alpha: 1.0),
            textColor: WidgetColor(red: 0.3, green: 0.1, blue: 0.4, alpha: 1.0),
            borderColor: WidgetColor(red: 0.7, green: 0.3, blue: 0.9, alpha: 0.8),
            fontName: "SF Pro",
            fontSize: 14,
            fontWeight: .medium,
            cornerRadius: 15,
            borderWidth: 3,
            shadowEnabled: true,
            shadowColor: WidgetColor(red: 0.9, green: 0.6, blue: 1.0, alpha: 0.25),
            shadowRadius: 5,
            shadowOffset: CGSize(width: 0, height: 3),
            widthMultiplier: 3.0,
            heightMultiplier: 1.0,
            hasCustomImage: true,
            normalImagePath: "resources/keys/custom/space-key.png",
            pressedImagePath: "resources/keys/custom/space-key-pressed.png",
            hoverImagePath: nil,
            imageOpacity: 0.8,
            imageBlendMode: "multiply",
            hideTextWhenImageExists: false  // 空格键图片可能只是背景，显示文本
        )

        // Shift 键配置
        configs["⇧"] = KeyConfig(
            id: "key-shift",
            keyType: .function,
            keyValue: "⇧",
            backgroundColor: WidgetColor(red: 1.0, green: 0.3, blue: 0.6, alpha: 1.0),
            pressedColor: WidgetColor(red: 0.9, green: 0.2, blue: 0.5, alpha: 1.0),
            textColor: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
            borderColor: WidgetColor(red: 0.8, green: 0.1, blue: 0.4, alpha: 0.9),
            fontName: "SF Pro",
            fontSize: 16,
            fontWeight: .bold,
            cornerRadius: 8,
            borderWidth: 2,
            shadowEnabled: true,
            shadowColor: WidgetColor(red: 1.0, green: 0.3, blue: 0.6, alpha: 0.4),
            shadowRadius: 3,
            shadowOffset: CGSize(width: 1, height: 2),
            widthMultiplier: 1.5,
            heightMultiplier: 1.0,
            hasCustomImage: true,
            normalImagePath: "resources/keys/custom/shift-key.png",
            pressedImagePath: "resources/keys/custom/shift-key-pressed.png",
            hoverImagePath: nil,
            imageOpacity: 0.9,
            imageBlendMode: "overlay",
            hideTextWhenImageExists: true  // Shift键图片已包含符号，隐藏文本
        )

        return configs
    }

    /// 调试个性化按键图片复制状态
    public func debugCustomKeyImagesCopy(themeId: String = "colorful-vibrant") async {
        print("\n🔍 调试个性化按键图片复制状态: \(themeId)")

        // 1. 检查Bundle源路径
        guard let bundleSourcePath = getThemePackSourcePath(id: themeId, isBuiltIn: true) else {
            print("❌ 无法获取Bundle源路径")
            return
        }

        let bundleResourcesPath = bundleSourcePath.appendingPathComponent("resources")
        let bundleCustomPath = bundleResourcesPath.appendingPathComponent("keys/custom")

        print("📁 Bundle源路径: \(bundleSourcePath.path)")
        print("📁 Bundle资源路径: \(bundleResourcesPath.path)")
        print("📁 Bundle custom路径: \(bundleCustomPath.path)")

        // 2. 检查Bundle中的custom目录和文件
        print("\n🔍 检查Bundle中的custom目录:")
        print("  custom目录存在: \(fileManager.fileExists(atPath: bundleCustomPath.path))")

        if fileManager.fileExists(atPath: bundleCustomPath.path) {
            do {
                let customFiles = try fileManager.contentsOfDirectory(atPath: bundleCustomPath.path)
                let pngFiles = customFiles.filter { $0.hasSuffix(".png") }
                print("  custom目录文件数量: \(customFiles.count)")
                print("  PNG文件数量: \(pngFiles.count)")
                print("  PNG文件列表: \(pngFiles.joined(separator: ", "))")

                // 检查具体文件
                for fileName in pngFiles {
                    let filePath = bundleCustomPath.appendingPathComponent(fileName)
                    let fileExists = fileManager.fileExists(atPath: filePath.path)
                    print("    \(fileExists ? "✅" : "❌") \(fileName)")
                }
            } catch {
                print("  ❌ 读取custom目录失败: \(error)")
            }
        }

        // 3. 检查目标路径
        let keyboardThemesDir = getSharedDirectory().appendingPathComponent("keyboard_themes")
        let targetThemeDir = keyboardThemesDir.appendingPathComponent("built-in")

        print("\n📁 目标路径: \(targetThemeDir.path)")
        print("  目标目录存在: \(fileManager.fileExists(atPath: targetThemeDir.path))")

        // 4. 检查已复制的个性化图片
        print("\n🔍 检查已复制的个性化图片:")
        do {
            let targetFiles = try fileManager.contentsOfDirectory(atPath: targetThemeDir.path)
            let customFiles = targetFiles.filter { $0.contains("_custom_") }
            print("  目标目录文件总数: \(targetFiles.count)")
            print("  个性化图片文件数量: \(customFiles.count)")

            if !customFiles.isEmpty {
                print("  个性化图片文件列表:")
                for fileName in customFiles {
                    print("    ✅ \(fileName)")
                }
            } else {
                print("  ⚠️ 没有找到个性化图片文件")
            }
        } catch {
            print("  ❌ 读取目标目录失败: \(error)")
        }

        // 5. 手动测试复制流程
        print("\n🧪 手动测试个性化图片复制:")
        do {
            try await copyCustomKeyImages(from: bundleResourcesPath, to: targetThemeDir, themeId: themeId)
            print("✅ 手动复制测试完成")
        } catch {
            print("❌ 手动复制测试失败: \(error)")
        }

        // 6. 再次检查复制结果
        print("\n🔍 复制后再次检查:")
        do {
            let targetFiles = try fileManager.contentsOfDirectory(atPath: targetThemeDir.path)
            let customFiles = targetFiles.filter { $0.contains("_custom_") }
            print("  复制后个性化图片文件数量: \(customFiles.count)")

            for fileName in customFiles {
                let filePath = targetThemeDir.appendingPathComponent(fileName)
                let fileSize = try fileManager.attributesOfItem(atPath: filePath.path)[.size] as? Int64 ?? 0
                print("    ✅ \(fileName) (大小: \(fileSize) 字节)")
            }
        } catch {
            print("  ❌ 复制后检查失败: \(error)")
        }

        print("\n🏁 个性化图片复制调试完成")
    }

    private func copyBuiltInThemePacksIfNeeded() async throws {
        guard let bundlePath = Bundle.main.path(forResource: bundleThemePacksPath, ofType: nil),
              let bundleThemePacks = try? fileManager.contentsOfDirectory(atPath: bundlePath) else {
            print("⚠️ 未找到内置主题包目录")
            return
        }

        let builtInDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(builtInSubPath)

        for themePackName in bundleThemePacks {
            // 跳过非主题包文件
            if themePackName.hasPrefix(".") ||
               themePackName.hasSuffix(".md") ||
               themePackName.hasSuffix(".txt") ||
               themePackName.hasSuffix(".json") ||
               themePackName.hasSuffix(".plist") {
                print("⏭️ 跳过非主题包文件: \(themePackName)")
                continue
            }

            let sourcePath = URL(fileURLWithPath: bundlePath).appendingPathComponent(themePackName)

            // 确保源路径是目录
            var isDirectory: ObjCBool = false
            guard fileManager.fileExists(atPath: sourcePath.path, isDirectory: &isDirectory),
                  isDirectory.boolValue else {
                print("⏭️ 跳过非目录文件: \(themePackName)")
                continue
            }

            let destPath = builtInDir.appendingPathComponent(themePackName)

            // 检查是否需要复制或更新
            if try await shouldCopyThemePack(from: sourcePath, to: destPath) {
                try copyThemePack(from: sourcePath, to: destPath)
                print("✅ 复制主题包: \(themePackName)")
            }
        }
    }

    private func shouldCopyThemePack(from source: URL, to destination: URL) async throws -> Bool {
        // 如果目标不存在，需要复制
        guard fileManager.fileExists(atPath: destination.path) else {
            return true
        }

        // 比较版本号
        let sourceMetadata = try loadMetadata(from: source)
        let destMetadata = try loadMetadata(from: destination)

        return sourceMetadata.version.compare(destMetadata.version, options: .numeric) == .orderedDescending
    }

    private func copyThemePack(from source: URL, to destination: URL) throws {
        // 如果目标存在，先删除
        if fileManager.fileExists(atPath: destination.path) {
            try fileManager.removeItem(at: destination)
        }

        // 创建目标目录
        try fileManager.createDirectory(at: destination, withIntermediateDirectories: true)

        // 获取源目录内容
        let contents = try fileManager.contentsOfDirectory(atPath: source.path)

        for item in contents {
            let sourcePath = source.appendingPathComponent(item)
            let destPath = destination.appendingPathComponent(item)

            // 跳过 theme.json 文件，因为我们会生成新的
            if item == "theme.json" {
                print("  ⏭️ 跳过Bundle中的theme.json，使用生成的版本")
                continue
            }

            // 复制其他文件和目录
            try fileManager.copyItem(at: sourcePath, to: destPath)
            print("  ✅ 复制: \(item)")
        }
    }

    private func loadAllThemePacks() async throws {
        var themePacks: [ThemePackMetadata] = []

        // 加载内置主题包
        let builtInDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(builtInSubPath)

        if let builtInPacks = try? fileManager.contentsOfDirectory(atPath: builtInDir.path) {
            for packName in builtInPacks {
                // 跳过非主题包文件
                if packName.hasPrefix(".") || packName.hasSuffix(".md") || packName.hasSuffix(".txt") {
                    continue
                }

                let packPath = builtInDir.appendingPathComponent(packName)

                // 确保是目录
                var isDirectory: ObjCBool = false
                guard fileManager.fileExists(atPath: packPath.path, isDirectory: &isDirectory),
                      isDirectory.boolValue else {
                    continue
                }

                if let metadata = try? loadMetadata(from: packPath) {
                    themePacks.append(metadata)
                }
            }
        }

        // 加载自定义主题包
        let customDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(customSubPath)

        if let customPacks = try? fileManager.contentsOfDirectory(atPath: customDir.path) {
            for packName in customPacks {
                // 跳过非主题包文件
                if packName.hasPrefix(".") || packName.hasSuffix(".md") || packName.hasSuffix(".txt") {
                    continue
                }

                let packPath = customDir.appendingPathComponent(packName)

                // 确保是目录
                var isDirectory: ObjCBool = false
                guard fileManager.fileExists(atPath: packPath.path, isDirectory: &isDirectory),
                      isDirectory.boolValue else {
                    continue
                }

                if let metadata = try? loadMetadata(from: packPath) {
                    themePacks.append(metadata)
                }
            }
        }

        // 按类别和名称排序
        themePacks.sort { first, second in
            if first.category != second.category {
                return first.category < second.category
            }
            return first.name < second.name
        }

        availableThemePacks = themePacks
    }

    private func loadMetadata(from themePackPath: URL) throws -> ThemePackMetadata {
        let metadataPath = themePackPath.appendingPathComponent(metadataFileName)

        do {
            let data = try Data(contentsOf: metadataPath)

            // 先尝试解析为基本JSON来调试
            if let jsonObject = try? JSONSerialization.jsonObject(with: data, options: []) {
                print("📄 JSON内容预览: \(jsonObject)")
            }

            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601

            let metadata = try decoder.decode(ThemePackMetadata.self, from: data)
            print("✅ 成功加载元数据: \(metadata.name)")
            return metadata
        } catch let decodingError as DecodingError {
            print("❌ 解析 metadata.json 失败 (\(themePackPath.lastPathComponent)):")
            switch decodingError {
            case .keyNotFound(let key, let context):
                print("  缺少键: \(key.stringValue)")
                print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                print("  描述: \(context.debugDescription)")
            case .typeMismatch(let type, let context):
                print("  类型不匹配: 期望 \(type)")
                print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                print("  描述: \(context.debugDescription)")
            case .valueNotFound(let type, let context):
                print("  值不存在: 期望 \(type)")
                print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                print("  描述: \(context.debugDescription)")
            case .dataCorrupted(let context):
                print("  数据损坏:")
                print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                print("  描述: \(context.debugDescription)")
            @unknown default:
                print("  未知解码错误: \(decodingError)")
            }
            throw decodingError
        } catch {
            print("❌ 读取 metadata.json 失败: \(error)")
            throw error
        }
    }

    private func updateInstalledPacksRecord() throws {
        let installedPacks = InstalledPacksRecord(
            packs: availableThemePacks.map { metadata in
                InstalledPackInfo(
                    id: metadata.id,
                    version: metadata.version,
                    installedAt: Date(),
                    isBuiltIn: metadata.isBuiltIn
                )
            },
            lastUpdated: Date()
        )

        let metadataDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(metadataSubPath)

        let recordPath = metadataDir.appendingPathComponent(installedPacksFileName)
        let data = try JSONEncoder().encode(installedPacks)
        try data.write(to: recordPath)
    }

    private func getSharedDirectory() -> URL {
        return dataManager.appGroup.containerURL
    }

    private func getThemePackPath(id: String, isBuiltIn: Bool) -> URL {
        let subPath = isBuiltIn ? builtInSubPath : customSubPath
        return getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(subPath)
            .appendingPathComponent(id)
    }

    /// 获取主题包资源的源路径（用于复制操作）
    private func getThemePackSourcePath(id: String, isBuiltIn: Bool) -> URL? {
        if isBuiltIn {
            // 内置主题包从 app bundle 中获取
            guard let bundlePath = Bundle.main.path(forResource: bundleThemePacksPath, ofType: nil) else {
                print("❌ 找不到 bundle 中的主题包路径: \(bundleThemePacksPath)")
                return nil
            }
            return URL(fileURLWithPath: bundlePath).appendingPathComponent(id)
        } else {
            // 自定义主题包从 App Groups 共享容器中获取
            return getSharedDirectory()
                .appendingPathComponent(sharedThemePacksPath)
                .appendingPathComponent(customSubPath)
                .appendingPathComponent(id)
        }
    }

    /// 验证键盘扩展图片同步状态
    public func validateKeyboardImageSync() -> [String: Any] {
        let syncResults = imageBridge.validateKeyboardImages()
        let statistics = imageBridge.getImageStatistics()

        print("\n🔍 键盘图片同步验证:")
        print("同步结果: \(syncResults)")
        print("统计信息: \(statistics)")

        return [
            "syncResults": syncResults,
            "statistics": statistics
        ]
    }

    /// 强制同步图片到键盘扩展
    public func forceSyncImagesToKeyboard() async {
        print("🔄 强制同步图片到键盘扩展...")
        await imageBridge.syncThemePackImagesToKeyboard()
        print("✅ 图片同步完成")
    }

    /// 测试主题包资源路径
    public func testThemePackResourcePaths() {
        print("\n🧪 测试主题包资源路径:")

        let testThemes = ["classic-light", "nature-wood", "neon-cyber"]

        for themeId in testThemes {
            print("\n📦 测试主题包: \(themeId)")

            // 测试内置主题包路径
            if let bundleSourcePath = getThemePackSourcePath(id: themeId, isBuiltIn: true) {
                print("  📁 Bundle源路径: \(bundleSourcePath.path)")
                print("  📁 资源目录: \(bundleSourcePath.appendingPathComponent("resources").path)")

                let resourcesPath = bundleSourcePath.appendingPathComponent("resources")
                let backgroundsPath = resourcesPath.appendingPathComponent("backgrounds")
                let keysPath = resourcesPath.appendingPathComponent("keys")

                print("  📁 背景图片目录: \(backgroundsPath.path)")
                print("  📁 按键图片目录: \(keysPath.path)")

                // 检查目录是否存在
                print("  🔍 资源目录存在: \(fileManager.fileExists(atPath: resourcesPath.path))")
                print("  🔍 背景目录存在: \(fileManager.fileExists(atPath: backgroundsPath.path))")
                print("  🔍 按键目录存在: \(fileManager.fileExists(atPath: keysPath.path))")

                // 检查Bundle中的通用文件名
                let bundleBgFile = backgroundsPath.appendingPathComponent("keyboard-bg.png")
                let bundleLetterKeyFile = keysPath.appendingPathComponent("letter-key.png")
                let bundlePressedKeyFile = keysPath.appendingPathComponent("letter-key-pressed.png")

                print("  🔍 Bundle中背景图片存在: \(fileManager.fileExists(atPath: bundleBgFile.path))")
                print("      Bundle文件名: keyboard-bg.png")
                print("  🔍 Bundle中字母按键图片存在: \(fileManager.fileExists(atPath: bundleLetterKeyFile.path))")
                print("      Bundle文件名: letter-key.png")
                print("  🔍 Bundle中按下状态图片存在: \(fileManager.fileExists(atPath: bundlePressedKeyFile.path))")
                print("      Bundle文件名: letter-key-pressed.png")

                print("  📄 复制后的文件名格式:")
                print("      背景图片: \(themeId)_background.png")
                print("      按键图片: \(themeId)_letter_key.png, \(themeId)_letter_key_pressed.png")

            } else {
                print("  ❌ 无法获取Bundle源路径")
            }

            // 测试App Groups目标路径（键盘扩展实际读取的路径）
            let targetPath = getSharedDirectory()
                .appendingPathComponent("keyboard_themes")
                .appendingPathComponent("built-in")

            print("  📁 键盘扩展目标路径: \(targetPath.path)")
            print("  🔍 目标目录存在: \(fileManager.fileExists(atPath: targetPath.path))")

            // 检查目标目录中的关键文件（键盘扩展实际读取的文件）
            let targetBgPath = targetPath.appendingPathComponent("\(themeId)_background.png")
            let targetKeyPath = targetPath.appendingPathComponent("\(themeId)_letter_key.png")

            print("  🔍 键盘扩展读取的背景图片存在: \(fileManager.fileExists(atPath: targetBgPath.path))")
            print("  🔍 键盘扩展读取的按键图片存在: \(fileManager.fileExists(atPath: targetKeyPath.path))")
            print("  📄 期望的背景文件名: \(themeId)_background.png")
            print("  📄 期望的按键文件名: \(themeId)_letter_key.png")

            // 列出键盘扩展目录的内容
            if let contents = try? fileManager.contentsOfDirectory(atPath: targetPath.path) {
                print("  📂 键盘扩展目录内容:")
                for item in contents {
                    print("    - \(item)")
                }
            }
        }

        print("\n🏁 主题包资源路径测试完成")
    }

    /// 列出Bundle中实际存在的主题包文件
    public func listBundleThemePackFiles(themeId: String = "nature-wood") {
        print("\n📦 列出Bundle中的主题包文件: \(themeId)")

        guard let bundleSourcePath = getThemePackSourcePath(id: themeId, isBuiltIn: true) else {
            print("❌ 无法获取Bundle源路径")
            return
        }

        print("📁 Bundle路径: \(bundleSourcePath.path)")

        let resourcesPath = bundleSourcePath.appendingPathComponent("resources")

        // 列出backgrounds目录
        let backgroundsPath = resourcesPath.appendingPathComponent("backgrounds")
        if fileManager.fileExists(atPath: backgroundsPath.path) {
            print("\n📂 backgrounds目录内容:")
            if let contents = try? fileManager.contentsOfDirectory(atPath: backgroundsPath.path) {
                for file in contents.sorted() {
                    print("  - \(file)")
                }
            }
        } else {
            print("\n❌ backgrounds目录不存在: \(backgroundsPath.path)")
        }

        // 列出keys目录
        let keysPath = resourcesPath.appendingPathComponent("keys")
        if fileManager.fileExists(atPath: keysPath.path) {
            print("\n📂 keys目录内容:")
            if let contents = try? fileManager.contentsOfDirectory(atPath: keysPath.path) {
                for file in contents.sorted() {
                    print("  - \(file)")
                }
            }
        } else {
            print("\n❌ keys目录不存在: \(keysPath.path)")
        }

        print("\n💡 Bundle中期望的通用文件名:")
        print("  背景图片: keyboard-bg.png")
        print("  按键图片: letter-key.png, letter-key-pressed.png, number-key.png, 等")

        print("\n📄 复制后的主题特定文件名:")
        print("  背景图片: \(themeId)_background.png")
        print("  按键图片: \(themeId)_letter_key.png, \(themeId)_letter_key_pressed.png, 等")

        print("\n🏁 Bundle文件列表完成")
    }

    /// 测试生成的JSON文件结构
    public func testGeneratedJSONStructure(themeId: String = "nature-wood") async {
        print("\n🧪 测试生成的JSON文件结构: \(themeId)")

        let themePackDir = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(builtInSubPath)
            .appendingPathComponent(themeId)

        let configPath = themePackDir.appendingPathComponent("theme.json")

        guard fileManager.fileExists(atPath: configPath.path) else {
            print("❌ theme.json 文件不存在: \(configPath.path)")
            return
        }

        do {
            let data = try Data(contentsOf: configPath)

            // 解析为通用JSON对象来检查结构
            if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                print("✅ JSON文件解析成功")

                // 检查advancedConfig
                if let advancedConfig = jsonObject["advancedConfig"] as? [String: Any] {
                    print("✅ advancedConfig 存在")

                    // 检查keyTypeConfigs
                    if let keyTypeConfigs = advancedConfig["keyTypeConfigs"] as? [String: Any] {
                        print("✅ keyTypeConfigs 存在，包含 \(keyTypeConfigs.count) 个配置")

                        for (key, value) in keyTypeConfigs {
                            if let config = value as? [String: Any] {
                                let hasKeyType = config["key_type"] != nil || config["keyType"] != nil
                                print("  \(hasKeyType ? "✅" : "❌") \(key): keyType字段\(hasKeyType ? "存在" : "缺失")")

                                if let keyType = config["key_type"] as? String ?? config["keyType"] as? String {
                                    print("    keyType值: \(keyType)")
                                }

                                // 列出所有字段
                                print("    字段: \(config.keys.sorted().joined(separator: ", "))")
                            }
                        }
                    } else {
                        print("❌ keyTypeConfigs 不存在")
                    }
                } else {
                    print("❌ advancedConfig 不存在")
                }
            }

            // 尝试解码为ThemePackConfig
            print("\n🔍 尝试解码为ThemePackConfig...")
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601

            do {
                let themeConfig = try decoder.decode(ThemePackConfig.self, from: data)
                print("✅ ThemePackConfig 解码成功")
                print("  keyTypeConfigs数量: \(themeConfig.advancedConfig.keyTypeConfigs.count)")

                for (key, config) in themeConfig.advancedConfig.keyTypeConfigs {
                    print("  ✅ \(key): keyType=\(config.keyType.rawValue)")
                }
            } catch {
                print("❌ ThemePackConfig 解码失败: \(error)")
                if let decodingError = error as? DecodingError {
                    switch decodingError {
                    case .keyNotFound(let key, let context):
                        print("  缺少键: \(key.stringValue)")
                        print("  路径: \(context.codingPath.map { $0.stringValue }.joined(separator: " -> "))")
                    default:
                        print("  其他解码错误: \(decodingError)")
                    }
                }
            }

        } catch {
            print("❌ 读取JSON文件失败: \(error)")
        }

        print("\n🏁 JSON结构测试完成")
    }

    /// 验证Bundle复制问题修复
    public func verifyBundleCopyFix() async {
        print("\n🔍 验证Bundle复制问题修复...")

        let themeId = "nature-wood"

        // 1. 检查Bundle中的原始文件
        print("📦 检查Bundle中的资源:")
        let bundleResourcesExist = checkBundleResourcesExist(for: themeId)
        print("  Bundle资源存在: \(bundleResourcesExist)")

        if let bundleSourcePath = getThemePackSourcePath(id: themeId, isBuiltIn: true) {
            let bundleResourcesPath = bundleSourcePath.appendingPathComponent("resources")
            let bundleBgPath = bundleResourcesPath.appendingPathComponent("backgrounds/keyboard-bg.png")
            let bundleKeyPath = bundleResourcesPath.appendingPathComponent("keys/letter-key.png")

            print("  Bundle背景图片: \(fileManager.fileExists(atPath: bundleBgPath.path))")
            print("  Bundle按键图片: \(fileManager.fileExists(atPath: bundleKeyPath.path))")
            print("  Bundle路径: \(bundleSourcePath.path)")
        }

        // 2. 检查App Groups中复制的文件
        print("\n📁 检查App Groups中的资源:")
        let appGroupThemePath = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(builtInSubPath)
            .appendingPathComponent(themeId)

        let appGroupResourcesPath = appGroupThemePath.appendingPathComponent("resources")
        let appGroupBgPath = appGroupResourcesPath.appendingPathComponent("backgrounds/keyboard-bg.png")
        let appGroupKeyPath = appGroupResourcesPath.appendingPathComponent("keys/letter-key.png")

        print("  App Groups主题目录存在: \(fileManager.fileExists(atPath: appGroupThemePath.path))")
        print("  App Groups资源目录存在: \(fileManager.fileExists(atPath: appGroupResourcesPath.path))")
        print("  App Groups背景图片存在: \(fileManager.fileExists(atPath: appGroupBgPath.path))")
        print("  App Groups按键图片存在: \(fileManager.fileExists(atPath: appGroupKeyPath.path))")

        // 3. 比较文件大小（验证是否是同一份文件）
        if bundleResourcesExist && fileManager.fileExists(atPath: appGroupBgPath.path) {
            if let bundleSourcePath = getThemePackSourcePath(id: themeId, isBuiltIn: true) {
                let bundleBgPath = bundleSourcePath.appendingPathComponent("resources/backgrounds/keyboard-bg.png")

                do {
                    let bundleAttrs = try fileManager.attributesOfItem(atPath: bundleBgPath.path)
                    let appGroupAttrs = try fileManager.attributesOfItem(atPath: appGroupBgPath.path)

                    let bundleSize = bundleAttrs[.size] as? Int64 ?? 0
                    let appGroupSize = appGroupAttrs[.size] as? Int64 ?? 0

                    print("\n📊 文件大小比较:")
                    print("  Bundle背景图片大小: \(bundleSize) 字节")
                    print("  App Groups背景图片大小: \(appGroupSize) 字节")
                    print("  文件大小匹配: \(bundleSize == appGroupSize)")

                    if bundleSize == appGroupSize {
                        print("  ✅ 文件大小一致，可能是同一份文件")
                    } else {
                        print("  ⚠️ 文件大小不一致，可能被生成的图片覆盖了")
                    }
                } catch {
                    print("  ❌ 无法获取文件属性: \(error)")
                }
            }
        }

        // 4. 检查键盘扩展目录
        print("\n🎹 检查键盘扩展目录:")
        let keyboardThemesDir = getSharedDirectory().appendingPathComponent("keyboard_themes/built-in")
        let keyboardBgPath = keyboardThemesDir.appendingPathComponent("\(themeId)_background.png")
        let keyboardKeyPath = keyboardThemesDir.appendingPathComponent("\(themeId)_letter_key.png")

        print("  键盘扩展目录存在: \(fileManager.fileExists(atPath: keyboardThemesDir.path))")
        print("  键盘扩展背景图片存在: \(fileManager.fileExists(atPath: keyboardBgPath.path))")
        print("  键盘扩展按键图片存在: \(fileManager.fileExists(atPath: keyboardKeyPath.path))")

        if let bundlePath = Bundle.main.path(forResource: bundleThemePacksPath, ofType: nil) {
            let bundleThemePath = URL(fileURLWithPath: bundlePath).appendingPathComponent(themeId)
            let bundleConfigPath = bundleThemePath.appendingPathComponent("theme.json")

            print("\n📦 Bundle中的theme.json:")
            print("  路径: \(bundleConfigPath.path)")
            print("  存在: \(fileManager.fileExists(atPath: bundleConfigPath.path))")

            if fileManager.fileExists(atPath: bundleConfigPath.path) {
                do {
                    let data = try Data(contentsOf: bundleConfigPath)
                    if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                       let advancedConfig = jsonObject["advanced_config"] as? [String: Any] {

                        if let keyTypeConfigs = advancedConfig["key_type_configs"] as? [String: Any] {
                            print("  ✅ Bundle使用旧格式: key_type_configs (应该被跳过)")
                            print("  包含配置: \(keyTypeConfigs.keys.joined(separator: ", "))")
                        }
                    }
                } catch {
                    print("  ❌ 读取Bundle配置失败: \(error)")
                }
            }
        }

        // 2. 检查App Groups中的生成文件
        let appGroupsThemePath = getSharedDirectory()
            .appendingPathComponent(sharedThemePacksPath)
            .appendingPathComponent(builtInSubPath)
            .appendingPathComponent(themeId)
        let appGroupsConfigPath = appGroupsThemePath.appendingPathComponent("theme.json")

        print("\n📁 App Groups中的theme.json:")
        print("  路径: \(appGroupsConfigPath.path)")
        print("  存在: \(fileManager.fileExists(atPath: appGroupsConfigPath.path))")

        if fileManager.fileExists(atPath: appGroupsConfigPath.path) {
            do {
                let data = try Data(contentsOf: appGroupsConfigPath)
                if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                   let advancedConfig = jsonObject["advancedConfig"] as? [String: Any] {

                    if let keyTypeConfigs = advancedConfig["keyTypeConfigs"] as? [String: Any] {
                        print("  ✅ App Groups使用新格式: keyTypeConfigs")
                        print("  包含配置: \(keyTypeConfigs.keys.joined(separator: ", "))")

                        // 检查每个配置是否包含keyType字段
                        for (key, value) in keyTypeConfigs {
                            if let config = value as? [String: Any] {
                                let hasKeyType = config["keyType"] != nil
                                print("    \(hasKeyType ? "✅" : "❌") \(key): keyType字段\(hasKeyType ? "存在" : "缺失")")
                            }
                        }
                    } else if let keyTypeConfigs = advancedConfig["key_type_configs"] as? [String: Any] {
                        print("  ❌ App Groups仍使用旧格式: key_type_configs (修复未生效)")
                    } else {
                        print("  ❌ 未找到keyTypeConfigs字段")
                    }
                }

                // 尝试解码验证
                print("\n🧪 解码验证:")
                let decoder = JSONDecoder()
                decoder.dateDecodingStrategy = .iso8601

                do {
                    let themeConfig = try decoder.decode(ThemePackConfig.self, from: data)
                    print("  ✅ 解码成功，keyTypeConfigs数量: \(themeConfig.advancedConfig.keyTypeConfigs.count)")
                } catch {
                    print("  ❌ 解码失败: \(error)")
                }

            } catch {
                print("  ❌ 读取App Groups配置失败: \(error)")
            }
        }

        print("\n🏁 Bundle复制问题验证完成")
    }

    /// 测试AdvancedKeyboardTheme编码解码
    public func testAdvancedThemeEncoding() async {
        print("\n🧪 测试AdvancedKeyboardTheme编码解码...")

        do {
            // 创建一个测试主题
            let baseTheme = KeyboardTheme(
                id: "test-theme",
                name: "测试主题",
                type: .custom,
                keyStyle: .rounded,
                backgroundColor: WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
                keyBackgroundColor: WidgetColor(red: 0.9, green: 0.9, blue: 0.9, alpha: 1),
                keyPressedColor: WidgetColor(red: 0.8, green: 0.8, blue: 0.8, alpha: 1),
                textColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
                specialKeyColor: WidgetColor(red: 0.7, green: 0.7, blue: 0.7, alpha: 1),
                borderColor: WidgetColor(red: 0.6, green: 0.6, blue: 0.6, alpha: 1),
                hasBackgroundImage: false,
                hasKeyImage: false,
                backgroundImagePath: "",
                keyImagePath: "",
                isBuiltInImageTheme: false,
                imageOpacity: 1.0,
                imageBlendMode: "normal",
                fontName: "SF Pro",
                fontSize: 16,
                fontWeight: .medium,
                keySpacing: 6,
                keyHeight: 44,
                showBorder: true,
                borderWidth: 1,
                enableShadow: true,
                shadowColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 0.2),
                shadowRadius: 2,
                enableHaptic: true,
                enableSound: true
            )

            let advancedTheme = AdvancedKeyboardTheme(
                name: "测试高级主题",
                description: "用于测试编码解码的主题",
                baseTheme: baseTheme
            )

            print("✅ 创建测试主题成功")
            print("  keyTypeConfigs数量: \(advancedTheme.keyTypeConfigs.count)")

            // 编码为JSON
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .secondsSince1970
            encoder.outputFormatting = [.prettyPrinted]

            let jsonData = try encoder.encode(advancedTheme)
            let jsonString = String(data: jsonData, encoding: .utf8) ?? "无法转换为字符串"

            print("✅ JSON编码成功")
            print("📄 编码后的JSON结构:")

            // 检查JSON结构
            if let jsonObject = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                if let keyTypeConfigs = jsonObject["keyTypeConfigs"] as? [String: Any] {
                    print("  ✅ keyTypeConfigs是字典格式，包含: \(keyTypeConfigs.keys.joined(separator: ", "))")

                    // 检查每个配置
                    for (key, value) in keyTypeConfigs {
                        if let config = value as? [String: Any] {
                            let hasKeyType = config["keyType"] != nil
                            print("    \(hasKeyType ? "✅" : "❌") \(key): keyType字段\(hasKeyType ? "存在" : "缺失")")
                        }
                    }
                } else {
                    print("  ❌ keyTypeConfigs不是字典格式")
                }
            }

            // 解码测试
            print("\n🔍 解码测试:")
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .secondsSince1970

            let decodedTheme = try decoder.decode(AdvancedKeyboardTheme.self, from: jsonData)
            print("  ✅ 解码成功")
            print("  keyTypeConfigs数量: \(decodedTheme.keyTypeConfigs.count)")

            for (keyType, config) in decodedTheme.keyTypeConfigs {
                print("    ✅ \(keyType.rawValue): \(config.name)")
            }

            print("🎉 AdvancedKeyboardTheme编码解码测试成功")

        } catch {
            print("❌ 测试失败: \(error)")
        }

        print("\n🏁 编码解码测试完成")
    }

    /// 图片资源诊断工具
    public func diagnoseImageResources() async {
        print("\n🔍 开始图片资源诊断...")

        let themeIds = ["classic-light", "nature-wood", "neon-cyber"]

        for themeId in themeIds {
            print("\n📦 诊断主题: \(themeId)")

            // 1. 检查App Groups中的主题包目录
            let themePackDir = getSharedDirectory()
                .appendingPathComponent(sharedThemePacksPath)
                .appendingPathComponent(builtInSubPath)
                .appendingPathComponent(themeId)

            print("  📁 主题包目录: \(themePackDir.path)")
            print("  🔍 目录存在: \(fileManager.fileExists(atPath: themePackDir.path))")

            if fileManager.fileExists(atPath: themePackDir.path) {
                // 检查resources目录
                let resourcesDir = themePackDir.appendingPathComponent("resources")
                print("  📁 资源目录: \(resourcesDir.path)")
                print("  🔍 资源目录存在: \(fileManager.fileExists(atPath: resourcesDir.path))")

                if fileManager.fileExists(atPath: resourcesDir.path) {
                    // 检查backgrounds目录
                    let backgroundsDir = resourcesDir.appendingPathComponent("backgrounds")
                    print("    📁 背景目录: \(backgroundsDir.path)")
                    print("    🔍 背景目录存在: \(fileManager.fileExists(atPath: backgroundsDir.path))")

                    if fileManager.fileExists(atPath: backgroundsDir.path) {
                        let bgFile = backgroundsDir.appendingPathComponent("keyboard-bg.png")
                        print("      📄 背景文件: \(bgFile.lastPathComponent)")
                        print("      🔍 文件存在: \(fileManager.fileExists(atPath: bgFile.path))")

                        if fileManager.fileExists(atPath: bgFile.path) {
                            do {
                                let attributes = try fileManager.attributesOfItem(atPath: bgFile.path)
                                let fileSize = attributes[.size] as? Int64 ?? 0
                                print("      📊 文件大小: \(fileSize) bytes")

                                let data = try Data(contentsOf: bgFile)
                                if let image = UIImage(data: data) {
                                    print("      🖼️ 图片尺寸: \(image.size)")
                                } else {
                                    print("      ❌ 无法解析图片数据")
                                }
                            } catch {
                                print("      ❌ 读取文件失败: \(error.localizedDescription)")
                            }
                        }
                    }

                    // 检查keys目录
                    let keysDir = resourcesDir.appendingPathComponent("keys")
                    print("    📁 按键目录: \(keysDir.path)")
                    print("    🔍 按键目录存在: \(fileManager.fileExists(atPath: keysDir.path))")

                    if fileManager.fileExists(atPath: keysDir.path) {
                        do {
                            let keyFiles = try fileManager.contentsOfDirectory(atPath: keysDir.path)
                            print("    📊 按键文件数量: \(keyFiles.count)")
                            print("    📄 按键文件: \(keyFiles.prefix(5).joined(separator: ", "))\(keyFiles.count > 5 ? "..." : "")")
                        } catch {
                            print("    ❌ 读取按键目录失败: \(error.localizedDescription)")
                        }
                    }
                }
            }

            // 2. 检查键盘扩展目录
            let keyboardDir = getSharedDirectory()
                .appendingPathComponent("keyboard_themes")
                .appendingPathComponent("built-in")

            print("  📁 键盘扩展目录: \(keyboardDir.path)")
            print("  🔍 目录存在: \(fileManager.fileExists(atPath: keyboardDir.path))")

            if fileManager.fileExists(atPath: keyboardDir.path) {
                do {
                    let keyboardFiles = try fileManager.contentsOfDirectory(atPath: keyboardDir.path)
                    let themeFiles = keyboardFiles.filter { $0.hasPrefix(themeId) }
                    print("  📊 主题相关文件数量: \(themeFiles.count)")
                    print("  📄 主题文件: \(themeFiles.joined(separator: ", "))")

                    // 检查关键文件
                    let bgFile = keyboardDir.appendingPathComponent("\(themeId)_background.png")
                    let keyFile = keyboardDir.appendingPathComponent("\(themeId)_key.png")

                    print("  🔍 背景文件存在: \(fileManager.fileExists(atPath: bgFile.path))")
                    print("  🔍 按键文件存在: \(fileManager.fileExists(atPath: keyFile.path))")

                } catch {
                    print("  ❌ 读取键盘目录失败: \(error.localizedDescription)")
                }
            }
        }

        print("\n🏁 图片资源诊断完成")
    }

    /// 测试键盘扩展JSON解码兼容性
    public func testKeyboardExtensionCompatibility() async {
        print("\n🔧 测试键盘扩展JSON解码兼容性...")

        do {
            // 创建一个测试主题
            let baseTheme = KeyboardTheme(
                id: "test-compatibility",
                name: "兼容性测试主题",
                type: .custom
            )

            let advancedTheme = AdvancedKeyboardTheme(
                name: "兼容性测试高级主题",
                description: "用于测试键盘扩展解码兼容性",
                baseTheme: baseTheme
            )

            print("✅ 创建测试主题成功")

            // 使用主应用的编码器编码
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .secondsSince1970
            encoder.outputFormatting = [.prettyPrinted]

            let jsonData = try encoder.encode(advancedTheme)
            let jsonString = String(data: jsonData, encoding: .utf8) ?? "无法转换为字符串"

            print("✅ 主应用编码成功")
            print("📄 JSON结构预览:")

            // 检查JSON结构
            if let jsonObject = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any] {
                if let keyTypeConfigs = jsonObject["keyTypeConfigs"] as? [String: Any] {
                    print("  ✅ keyTypeConfigs是字典格式，包含: \(keyTypeConfigs.keys.joined(separator: ", "))")
                } else {
                    print("  ❌ keyTypeConfigs不是字典格式")
                }
            }

            // 模拟键盘扩展解码（使用相同的解码逻辑）
            print("\n🔍 模拟键盘扩展解码:")
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .secondsSince1970

            // 这里我们使用相同的AdvancedKeyboardTheme类型，因为它们现在有相同的Codable实现
            let decodedTheme = try decoder.decode(AdvancedKeyboardTheme.self, from: jsonData)
            print("  ✅ 键盘扩展解码成功")
            print("  📊 keyTypeConfigs数量: \(decodedTheme.keyTypeConfigs.count)")

            for (keyType, config) in decodedTheme.keyTypeConfigs {
                print("    ✅ \(keyType.rawValue): \(config.name)")
            }

            // 验证数据完整性
            print("\n🔍 数据完整性验证:")
            print("  主题名称: \(decodedTheme.name)")
            print("  描述: \(decodedTheme.description)")
            print("  全局按键间距: \(decodedTheme.globalKeySpacing)")
            print("  全局按键高度: \(decodedTheme.globalKeyHeight)")
            print("  启用触觉反馈: \(decodedTheme.enableHapticFeedback)")
            print("  启用声音反馈: \(decodedTheme.enableSoundFeedback)")

            print("🎉 键盘扩展JSON解码兼容性测试成功")

        } catch {
            print("❌ 兼容性测试失败: \(error)")
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .typeMismatch(let type, let context):
                    print("   类型不匹配: 期望 \(type), 路径: \(context.codingPath)")
                    print("   描述: \(context.debugDescription)")
                case .keyNotFound(let key, let context):
                    print("   缺少键: \(key), 路径: \(context.codingPath)")
                    print("   描述: \(context.debugDescription)")
                case .valueNotFound(let type, let context):
                    print("   值不存在: \(type), 路径: \(context.codingPath)")
                    print("   描述: \(context.debugDescription)")
                case .dataCorrupted(let context):
                    print("   数据损坏: 路径: \(context.codingPath)")
                    print("   描述: \(context.debugDescription)")
                @unknown default:
                    print("   未知解码错误: \(decodingError)")
                }
            }
        }

        print("\n🏁 兼容性测试完成")
    }

    /// 测试字段命名兼容性
    public func testFieldNamingCompatibility() async {
        print("\n🔧 测试字段命名兼容性...")

        do {
            // 创建一个测试主题
            let baseTheme = KeyboardTheme(
                id: "test-naming",
                name: "命名兼容性测试主题",
                type: .custom
            )

            let advancedTheme = AdvancedKeyboardTheme(
                name: "命名兼容性测试高级主题",
                description: "用于测试字段命名兼容性",
                baseTheme: baseTheme
            )

            print("✅ 创建测试主题成功")

            // 使用主应用的编码器编码
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .secondsSince1970
            encoder.outputFormatting = [.prettyPrinted]

            let jsonData = try encoder.encode(advancedTheme)

            print("✅ 主应用编码成功")

            // 检查JSON中的字段命名
            if let jsonObject = try JSONSerialization.jsonObject(with: jsonData) as? [String: Any],
               let keyTypeConfigs = jsonObject["keyTypeConfigs"] as? [String: Any] {

                print("📄 检查字段命名格式:")

                for (keyTypeName, configData) in keyTypeConfigs {
                    if let config = configData as? [String: Any] {
                        print("  🔍 \(keyTypeName) 配置字段:")

                        // 检查关键字段是否存在
                        let expectedFields = [
                            "key_type", "default_background_color", "default_pressed_color",
                            "default_text_color", "default_border_color", "default_font_size",
                            "default_font_weight", "default_corner_radius", "default_border_width",
                            "affected_keys", "updated_at"
                        ]

                        for field in expectedFields {
                            let exists = config[field] != nil
                            print("    \(exists ? "✅" : "❌") \(field): \(exists ? "存在" : "缺失")")
                        }

                        // 只检查第一个配置
                        break
                    }
                }
            }

            print("🎉 字段命名兼容性测试完成")

        } catch {
            print("❌ 命名兼容性测试失败: \(error)")
        }

        print("\n🏁 命名兼容性测试完成")
    }

    /// 验证图片加载路径修复
    public func verifyImageLoadingPathFix() async {
        print("\n🔧 验证图片加载路径修复...")

        let themeIds = ["classic-light", "nature-wood", "neon-cyber"]

        for themeId in themeIds {
            print("\n📦 验证主题: \(themeId)")

            // 1. 检查主题包源目录结构
            let themePackDir = getSharedDirectory()
                .appendingPathComponent("theme-packs")
                .appendingPathComponent("built-in")
                .appendingPathComponent(themeId)

            print("  📁 主题包目录: \(themePackDir.path)")
            print("  🔍 目录存在: \(fileManager.fileExists(atPath: themePackDir.path))")

            if fileManager.fileExists(atPath: themePackDir.path) {
                // 检查resources目录结构
                let resourcesDir = themePackDir.appendingPathComponent("resources")
                print("    📁 资源目录: \(resourcesDir.path)")
                print("    🔍 资源目录存在: \(fileManager.fileExists(atPath: resourcesDir.path))")

                if fileManager.fileExists(atPath: resourcesDir.path) {
                    // 检查背景图片
                    let bgPath = resourcesDir.appendingPathComponent("backgrounds/keyboard-bg.png")
                    print("      🖼️ 背景图片: \(bgPath.path)")
                    print("      🔍 背景图片存在: \(fileManager.fileExists(atPath: bgPath.path))")

                    if fileManager.fileExists(atPath: bgPath.path) {
                        do {
                            let data = try Data(contentsOf: bgPath)
                            if let image = UIImage(data: data) {
                                print("        ✅ 背景图片可加载，尺寸: \(image.size)")
                            } else {
                                print("        ❌ 背景图片无法解析")
                            }
                        } catch {
                            print("        ❌ 背景图片读取失败: \(error.localizedDescription)")
                        }
                    }

                    // 检查按键图片
                    let keyPath = resourcesDir.appendingPathComponent("keys/letter-key.png")
                    print("      🔑 按键图片: \(keyPath.path)")
                    print("      🔍 按键图片存在: \(fileManager.fileExists(atPath: keyPath.path))")

                    if fileManager.fileExists(atPath: keyPath.path) {
                        do {
                            let data = try Data(contentsOf: keyPath)
                            if let image = UIImage(data: data) {
                                print("        ✅ 按键图片可加载，尺寸: \(image.size)")
                            } else {
                                print("        ❌ 按键图片无法解析")
                            }
                        } catch {
                            print("        ❌ 按键图片读取失败: \(error.localizedDescription)")
                        }
                    }

                    // 列出keys目录内容
                    let keysDir = resourcesDir.appendingPathComponent("keys")
                    if fileManager.fileExists(atPath: keysDir.path) {
                        do {
                            let keyFiles = try fileManager.contentsOfDirectory(atPath: keysDir.path)
                            print("      📄 按键文件列表: \(keyFiles.joined(separator: ", "))")
                        } catch {
                            print("      ❌ 无法读取按键目录: \(error.localizedDescription)")
                        }
                    }
                }
            }

            // 2. 检查旧的缓存目录（应该被废弃）
            let cacheDir = getSharedDirectory()
                .appendingPathComponent("keyboard_themes")
                .appendingPathComponent("built-in")

            print("  📁 旧缓存目录: \(cacheDir.path)")
            print("  🔍 缓存目录存在: \(fileManager.fileExists(atPath: cacheDir.path))")

            if fileManager.fileExists(atPath: cacheDir.path) {
                do {
                    let cacheFiles = try fileManager.contentsOfDirectory(atPath: cacheDir.path)
                    let themeFiles = cacheFiles.filter { $0.hasPrefix(themeId) }
                    print("    📄 主题缓存文件: \(themeFiles.joined(separator: ", "))")

                    if !themeFiles.isEmpty {
                        print("    ⚠️ 发现旧缓存文件，新逻辑应该优先使用主题包源目录")
                    }
                } catch {
                    print("    ❌ 无法读取缓存目录: \(error.localizedDescription)")
                }
            }
        }

        print("\n📋 图片加载路径修复验证总结:")
        print("  ✅ 新逻辑：优先从 theme-packs/built-in/{theme-id}/resources/ 加载")
        print("  🔄 回退逻辑：如果源目录不存在，回退到 keyboard_themes/built-in/ 缓存")
        print("  🎯 目标：直接从主题包源目录加载，避免缓存复制的复杂性")

        print("\n🏁 图片加载路径修复验证完成")
    }



    /// 测试完整的主题包复制流程
    public func testThemePackCopyFlow(themeId: String = "nature-wood") async {
        print("\n🧪 测试主题包复制流程: \(themeId)")

        do {
            // 1. 获取主题包
            guard let themePack = try await getThemePack(id: themeId) else {
                print("❌ 无法获取主题包: \(themeId)")
                return
            }

            print("✅ 成功获取主题包: \(themePack.metadata.name)")

            // 2. 测试复制流程
            print("🔄 开始测试复制流程...")
            try await copyThemePackImagesToKeyboard(themePack: themePack)

            // 3. 验证复制结果（键盘扩展实际读取的路径）
            let targetPath = getSharedDirectory()
                .appendingPathComponent("keyboard_themes")
                .appendingPathComponent("built-in")

            print("📁 验证目标路径（键盘扩展读取路径）: \(targetPath.path)")

            if fileManager.fileExists(atPath: targetPath.path) {
                print("✅ 目标目录存在")

                // 检查关键文件（键盘扩展期望的文件名）
                let bgPath = targetPath.appendingPathComponent("\(themeId)_background.png")
                let keyPath = targetPath.appendingPathComponent("\(themeId)_letter_key.png")
                let pressedKeyPath = targetPath.appendingPathComponent("\(themeId)_letter_key_pressed.png")

                print("🔍 背景图片: \(fileManager.fileExists(atPath: bgPath.path) ? "✅" : "❌") (\(themeId)_background.png)")
                print("🔍 按键图片: \(fileManager.fileExists(atPath: keyPath.path) ? "✅" : "❌") (\(themeId)_letter_key.png)")
                print("🔍 按下状态图片: \(fileManager.fileExists(atPath: pressedKeyPath.path) ? "✅" : "❌") (\(themeId)_letter_key_pressed.png)")

                // 列出所有复制的文件
                if let contents = try? fileManager.contentsOfDirectory(atPath: targetPath.path) {
                    print("📂 目标目录内容:")
                    for item in contents {
                        print("  - \(item)")

                        let itemPath = targetPath.appendingPathComponent(item)
                        var isDirectory: ObjCBool = false
                        if fileManager.fileExists(atPath: itemPath.path, isDirectory: &isDirectory),
                           isDirectory.boolValue {
                            if let subContents = try? fileManager.contentsOfDirectory(atPath: itemPath.path) {
                                for subItem in subContents {
                                    print("    - \(subItem)")
                                }
                            }
                        }
                    }
                }

            } else {
                print("❌ 目标目录不存在")
            }

            print("🎉 主题包复制流程测试完成")

        } catch {
            print("❌ 测试失败: \(error.localizedDescription)")
        }
    }

    // MARK: - 主题转换方法

    private func convertToAdvancedTheme(from themePack: ThemePackBundle) throws -> AdvancedKeyboardTheme {
        let config = themePack.config.advancedConfig

        // 转换按键类型配置
        var keyTypeConfigs: [KeyType: KeyTypeConfig] = [:]
        for (keyTypeString, typeConfig) in config.keyTypeConfigs {
            if let keyType = KeyType(rawValue: keyTypeString) {
                keyTypeConfigs[keyType] = typeConfig
            }
        }

        // 转换个性化按键配置
        let individualKeyConfigs = config.individualKeyConfigs

        print("🔧 转换AdvancedKeyboardTheme:")
        print("  - 按键类型配置数量: \(keyTypeConfigs.count)")
        print("  - 个性化按键配置数量: \(individualKeyConfigs.count)")

        // 打印个性化配置详情
        for (key, keyConfig) in individualKeyConfigs {
            print("  - 个性化按键「\(key)」: hasCustomImage=\(keyConfig.hasCustomImage), imagePath=\(keyConfig.normalImagePath ?? "nil")")
        }

        return AdvancedKeyboardTheme(
            id: themePack.metadata.id,
            name: themePack.metadata.name,
            description: themePack.metadata.description,
            baseTheme: convertToKeyboardTheme(from: themePack.config.baseTheme),
            keyTypeConfigs: keyTypeConfigs,
            individualKeyConfigs: individualKeyConfigs,
            globalKeySpacing: config.globalSettings.keySpacing,
            globalKeyHeight: config.globalSettings.keyHeight,
            enableHapticFeedback: config.globalSettings.enableHapticFeedback,
            enableSoundFeedback: config.globalSettings.enableSoundFeedback,
            enableKeyAnimations: config.globalSettings.enableKeyAnimations,
            animationDuration: config.globalSettings.animationDuration,
            enableGradientEffects: config.globalSettings.enableGradientEffects,
            enableParallaxEffect: config.globalSettings.enableParallaxEffect
        )
    }

    public func convertToKeyboardTheme(from baseTheme: BaseThemeConfig) -> KeyboardTheme {
        // 转换主题类型
        let themeType: KeyboardThemeType = {
            switch baseTheme.type.lowercased() {
            case "light", "浅色": return .light
            case "dark", "深色": return .dark
            case "colorful", "彩色": return .colorful
            case "gradient", "渐变": return .gradient
            case "custom", "自定义": return .custom
            default: return .custom
            }
        }()

        // 转换按键样式
        let keyStyle: KeyboardKeyStyle = {
            switch baseTheme.keyStyle.lowercased() {
            case "standard", "标准": return .standard
            case "rounded", "圆角": return .rounded
            case "circular", "圆形": return .circular
            case "flat", "扁平": return .flat
            default: return .rounded
            }
        }()

        // 转换字体权重
        let fontWeight: FontWeight = {
            switch baseTheme.typography.fontWeight.lowercased() {
            case "ultralight": return .ultraLight
            case "thin": return .thin
            case "light": return .light
            case "regular": return .regular
            case "medium": return .medium
            case "semibold": return .semibold
            case "bold": return .bold
            case "heavy": return .heavy
            case "black": return .black
            default: return .medium
            }
        }()

        return KeyboardTheme(
            id: baseTheme.id,
            name: baseTheme.name,
            type: themeType,
            keyStyle: keyStyle,
            backgroundColor: baseTheme.colors.background,
            keyBackgroundColor: baseTheme.colors.keyBackground,
            keyPressedColor: baseTheme.colors.keyPressed,
            textColor: baseTheme.colors.text,
            specialKeyColor: baseTheme.colors.specialKey,
            borderColor: baseTheme.colors.border,
            hasBackgroundImage: baseTheme.images.hasBackgroundImage,
            hasKeyImage: baseTheme.images.hasKeyImage,
            backgroundImagePath: baseTheme.id, // 使用主题ID而不是完整路径
            keyImagePath: baseTheme.images.hasKeyImage ? baseTheme.id : nil, // 使用主题ID，键盘扩展会自动查找letter-key.png
            isBuiltInImageTheme: baseTheme.images.isBuiltInImageTheme,
            imageOpacity: baseTheme.images.imageOpacity,
            imageBlendMode: baseTheme.images.imageBlendMode,
            fontName: baseTheme.typography.fontName,
            fontSize: baseTheme.typography.fontSize,
            fontWeight: fontWeight,
            keySpacing: baseTheme.layout.keySpacing,
            keyHeight: baseTheme.layout.keyHeight,
            showBorder: baseTheme.layout.showBorder,
            borderWidth: baseTheme.layout.borderWidth,
            enableShadow: baseTheme.effects.enableShadow,
            shadowColor: baseTheme.effects.shadowColor,
            shadowRadius: baseTheme.effects.shadowRadius,
            enableHaptic: baseTheme.effects.enableHaptic,
            enableSound: baseTheme.effects.enableSound
        )
    }
}

// MARK: - 错误定义

public enum ThemePackError: LocalizedError {
    case configFileNotFound(String)
    case themePackNotFound(String)
    case invalidConfiguration(String)
    case copyFailed(String)
    case validationFailed(String)
    case directoryCreationFailed(String)
    case imageGenerationFailed(String)
    case resourceNotFound(String)
    case permissionDenied(String)
    case insufficientSpace(String)

    public var errorDescription: String? {
        switch self {
        case .configFileNotFound(let id):
            return "主题包「\(id)」的配置文件未找到"
        case .themePackNotFound(let id):
            return "主题包「\(id)」不存在"
        case .invalidConfiguration(let reason):
            return "主题包配置无效: \(reason)"
        case .copyFailed(let reason):
            return "复制主题包失败: \(reason)"
        case .validationFailed(let reason):
            return "主题包验证失败: \(reason)"
        case .directoryCreationFailed(let reason):
            return "目录创建失败: \(reason)"
        case .imageGenerationFailed(let reason):
            return "图片生成失败: \(reason)"
        case .resourceNotFound(let resource):
            return "资源文件未找到: \(resource)"
        case .permissionDenied(let reason):
            return "权限被拒绝: \(reason)"
        case .insufficientSpace(let reason):
            return "存储空间不足: \(reason)"
        }
    }
}
