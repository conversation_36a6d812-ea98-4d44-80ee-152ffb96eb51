//
//  ThemePackImageBridge.swift
//  MyWidgetKit
//
//  主题包图片桥接器 - 统一主应用和键盘扩展的图片访问
//

import Foundation

#if canImport(UIKit)
import UIKit
#endif

/// 主题包图片桥接器
public final class ThemePackImageBridge: @unchecked Sendable {

    // MARK: - 单例
    public static let shared = ThemePackImageBridge()

    // MARK: - 私有属性
    private let fileManager = FileManager.default
    private let dataManager = AppGroupDataManager.shared

    // 路径配置
    private let themePacksPath = "theme-packs"
    private let keyboardThemesPath = "keyboard_themes"
    private let builtInSubPath = "built-in"
    private let customSubPath = "custom"

    // MARK: - 初始化
    private init() {}

    // MARK: - 公共方法

    /// 同步主题包图片到键盘扩展格式
    public func syncThemePackImagesToKeyboard() async {
        print("🔄 开始同步主题包图片到键盘扩展...")

        let themeIds = ["classic-light", "nature-wood", "neon-cyber", "colorful-vibrant"]

        for themeId in themeIds {
            await syncThemePackImages(themeId: themeId)
        }

        print("✅ 主题包图片同步完成")
    }

    /// 同步单个主题包的图片
    private func syncThemePackImages(themeId: String) async {
        print("📦 同步主题包: \(themeId)")

        // 源路径（主题包格式）
        let sourceThemeDir = dataManager.appGroup.containerURL
            .appendingPathComponent(themePacksPath)
            .appendingPathComponent(builtInSubPath)
            .appendingPathComponent(themeId)

        // 目标路径（键盘扩展格式）
        let targetKeyboardDir = dataManager.appGroup.containerURL
            .appendingPathComponent(keyboardThemesPath)
            .appendingPathComponent(builtInSubPath)

        // 创建目标目录
        try? fileManager.createDirectory(at: targetKeyboardDir, withIntermediateDirectories: true)

        // 同步背景图片
        await syncBackgroundImage(themeId: themeId, sourceDir: sourceThemeDir, targetDir: targetKeyboardDir)

        // 同步按键图片
        await syncKeyImages(themeId: themeId, sourceDir: sourceThemeDir, targetDir: targetKeyboardDir)
    }

    /// 同步背景图片
    private func syncBackgroundImage(themeId: String, sourceDir: URL, targetDir: URL) async {
        let sourcePath = sourceDir.appendingPathComponent("resources/backgrounds/keyboard-bg.png")
        let targetPath = targetDir.appendingPathComponent("\(themeId)_background.png")

        await copyImageFile(from: sourcePath, to: targetPath, type: "背景图片")
    }

    /// 同步按键图片
    private func syncKeyImages(themeId: String, sourceDir: URL, targetDir: URL) async {
        // 使用第一个按键图片作为通用按键图片
        let sourcePath = sourceDir.appendingPathComponent("resources/keys/letter-key.png")
        let targetPath = targetDir.appendingPathComponent("\(themeId)_key.png")

        await copyImageFile(from: sourcePath, to: targetPath, type: "按键图片")
    }

    /// 复制图片文件
    private func copyImageFile(from source: URL, to target: URL, type: String) async {
        // 检查源文件是否存在
        guard fileManager.fileExists(atPath: source.path) else {
            print("⚠️ 源文件不存在: \(source.lastPathComponent)")

            // 如果源文件不存在，生成占位符图片
            await generatePlaceholderImage(at: target, type: type)
            return
        }

        do {
            // 如果目标文件已存在，先删除
            if fileManager.fileExists(atPath: target.path) {
                try fileManager.removeItem(at: target)
            }

            // 复制文件
            try fileManager.copyItem(at: source, to: target)
            print("  ✅ 复制\(type): \(target.lastPathComponent)")
        } catch {
            print("  ❌ 复制\(type)失败: \(error)")

            // 复制失败时生成占位符图片
            await generatePlaceholderImage(at: target, type: type)
        }
    }

    /// 生成占位符图片
    private func generatePlaceholderImage(at url: URL, type: String) async {
        #if canImport(UIKit)
        let size: CGSize
        let color: UIColor

        if type.contains("背景") {
            size = CGSize(width: 1024, height: 512)
            color = UIColor.systemGray6
        } else {
            size = CGSize(width: 64, height: 64)
            color = UIColor.systemGray5
        }

        let image = createSolidColorImage(size: size, color: color)

        guard let data = image.pngData() else {
            print("  ❌ 无法生成\(type)数据")
            return
        }

        do {
            try data.write(to: url)
            print("  ✅ 生成\(type)占位符: \(url.lastPathComponent)")
        } catch {
            print("  ❌ 保存\(type)占位符失败: \(error)")
        }
        #else
        print("  ⚠️ UIKit不可用，跳过图片生成: \(type)")
        #endif
    }

    /// 创建纯色图片
    private func createSolidColorImage(size: CGSize, color: UIColor) -> UIImage {
        #if canImport(UIKit)
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            color.setFill()
            context.fill(CGRect(origin: .zero, size: size))
        }
        #else
        // 如果UIKit不可用，返回空图片（这种情况不应该发生）
        return UIImage()
        #endif
    }

    /// 验证键盘扩展图片资源
    public func validateKeyboardImages() -> [String: Bool] {
        var results: [String: Bool] = [:]

        let themeIds = ["classic-light", "nature-wood", "neon-cyber", "colorful-vibrant"]
        let keyboardDir = dataManager.appGroup.containerURL
            .appendingPathComponent(keyboardThemesPath)
            .appendingPathComponent(builtInSubPath)

        for themeId in themeIds {
            let backgroundPath = keyboardDir.appendingPathComponent("\(themeId)_background.png")
            let keyPath = keyboardDir.appendingPathComponent("\(themeId)_key.png")

            let backgroundExists = fileManager.fileExists(atPath: backgroundPath.path)
            let keyExists = fileManager.fileExists(atPath: keyPath.path)

            results["\(themeId)_background"] = backgroundExists
            results["\(themeId)_key"] = keyExists

            print("📊 \(themeId): 背景图片=\(backgroundExists ? "✅" : "❌"), 按键图片=\(keyExists ? "✅" : "❌")")
        }

        return results
    }

    /// 清理键盘扩展图片缓存
    public func cleanupKeyboardImageCache() {
        let keyboardDir = dataManager.appGroup.containerURL
            .appendingPathComponent(keyboardThemesPath)

        do {
            if fileManager.fileExists(atPath: keyboardDir.path) {
                try fileManager.removeItem(at: keyboardDir)
                print("🧹 已清理键盘图片缓存")
            }
        } catch {
            print("❌ 清理键盘图片缓存失败: \(error)")
        }
    }

    /// 获取主题包图片统计信息
    public func getImageStatistics() -> [String: Any] {
        var stats: [String: Any] = [:]

        // 主题包图片统计
        let themePackDir = dataManager.appGroup.containerURL
            .appendingPathComponent(themePacksPath)
            .appendingPathComponent(builtInSubPath)

        var themePackCount = 0
        var themePackImageCount = 0

        if let contents = try? fileManager.contentsOfDirectory(atPath: themePackDir.path) {
            for item in contents {
                let itemPath = themePackDir.appendingPathComponent(item)
                var isDirectory: ObjCBool = false

                if fileManager.fileExists(atPath: itemPath.path, isDirectory: &isDirectory),
                   isDirectory.boolValue {
                    themePackCount += 1

                    // 统计图片文件
                    let resourcesPath = itemPath.appendingPathComponent("resources")
                    if let resourceContents = try? fileManager.subpathsOfDirectory(atPath: resourcesPath.path) {
                        themePackImageCount += resourceContents.filter { $0.hasSuffix(".png") }.count
                    }
                }
            }
        }

        // 键盘扩展图片统计
        let keyboardDir = dataManager.appGroup.containerURL
            .appendingPathComponent(keyboardThemesPath)
            .appendingPathComponent(builtInSubPath)

        var keyboardImageCount = 0
        if let contents = try? fileManager.contentsOfDirectory(atPath: keyboardDir.path) {
            keyboardImageCount = contents.filter { $0.hasSuffix(".png") }.count
        }

        stats["themePackCount"] = themePackCount
        stats["themePackImageCount"] = themePackImageCount
        stats["keyboardImageCount"] = keyboardImageCount
        stats["syncRatio"] = themePackImageCount > 0 ? Double(keyboardImageCount) / Double(themePackImageCount) : 0.0

        return stats
    }
}
