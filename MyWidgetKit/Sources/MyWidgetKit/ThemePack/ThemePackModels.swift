//
//  ThemePackModels.swift
//  MyWidgetKit
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import Foundation
import UIKit

// MARK: - 动态编码键

/// 动态编码键，用于解码字典中的动态键名
public struct DynamicCodingKey: CodingKey {
    public var stringValue: String
    public var intValue: Int?
    
    public init?(stringValue: String) {
        self.stringValue = stringValue
        self.intValue = nil
    }
    
    public init?(intValue: Int) {
        self.stringValue = String(intValue)
        self.intValue = intValue
    }
}



// MARK: - 主题包元数据

/// 主题包元数据
public struct ThemePackMetadata: Codable, Identifiable, Equatable, Sendable {
    public let id: String
    public let name: String
    public let description: String
    public let version: String
    public let category: String
    public let style: String
    public let author: String
    public let createdAt: Date
    public let updatedAt: Date

    // 自定义编码键以支持snake_case和camelCase
    private enum CodingKeys: String, CodingKey {
        case id, name, description, version, category, style, author
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case compatibility, resources, features, tags, rating
        case downloadCount = "download_count"
        case isPremium = "is_premium"
        case isBuiltIn = "is_built_in"
    }

    // 自定义解码器以支持多种日期格式
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)
        version = try container.decode(String.self, forKey: .version)
        category = try container.decode(String.self, forKey: .category)
        style = try container.decode(String.self, forKey: .style)
        author = try container.decode(String.self, forKey: .author)

        // 尝试解码日期，支持多种格式
        let dateFormatter = ISO8601DateFormatter()

        if let createdAtString = try? container.decode(String.self, forKey: .createdAt),
           let date = dateFormatter.date(from: createdAtString) {
            createdAt = date
        } else {
            // 如果解码失败，使用当前时间
            createdAt = Date()
        }

        if let updatedAtString = try? container.decode(String.self, forKey: .updatedAt),
           let date = dateFormatter.date(from: updatedAtString) {
            updatedAt = date
        } else {
            // 如果解码失败，使用当前时间
            updatedAt = Date()
        }

        compatibility = try container.decode(CompatibilityInfo.self, forKey: .compatibility)
        resources = try container.decode(ResourceInfo.self, forKey: .resources)
        features = try container.decode(FeatureInfo.self, forKey: .features)
        tags = try container.decode([String].self, forKey: .tags)
        rating = try container.decode(Double.self, forKey: .rating)
        downloadCount = try container.decode(Int.self, forKey: .downloadCount)
        isPremium = try container.decode(Bool.self, forKey: .isPremium)
        isBuiltIn = try container.decode(Bool.self, forKey: .isBuiltIn)
    }

    // 兼容性信息
    public let compatibility: CompatibilityInfo

    // 资源信息
    public let resources: ResourceInfo

    // 功能特性
    public let features: FeatureInfo

    // 其他信息
    public let tags: [String]
    public let rating: Double
    public let downloadCount: Int
    public let isPremium: Bool
    public let isBuiltIn: Bool

    public init(
        id: String,
        name: String,
        description: String,
        version: String,
        category: String,
        style: String,
        author: String,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        compatibility: CompatibilityInfo,
        resources: ResourceInfo,
        features: FeatureInfo,
        tags: [String] = [],
        rating: Double = 0.0,
        downloadCount: Int = 0,
        isPremium: Bool = false,
        isBuiltIn: Bool = true
    ) {
        self.id = id
        self.name = name
        self.description = description
        self.version = version
        self.category = category
        self.style = style
        self.author = author
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.compatibility = compatibility
        self.resources = resources
        self.features = features
        self.tags = tags
        self.rating = rating
        self.downloadCount = downloadCount
        self.isPremium = isPremium
        self.isBuiltIn = isBuiltIn
    }
}

/// 兼容性信息
public struct CompatibilityInfo: Codable, Equatable, Sendable {
    public let minIOSVersion: String
    public let minAppVersion: String
    public let supportedDevices: [String]

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case minIOSVersion = "min_ios_version"
        case minAppVersion = "min_app_version"
        case supportedDevices = "supported_devices"
    }

    public init(minIOSVersion: String, minAppVersion: String, supportedDevices: [String]) {
        self.minIOSVersion = minIOSVersion
        self.minAppVersion = minAppVersion
        self.supportedDevices = supportedDevices
    }
}

/// 资源信息
public struct ResourceInfo: Codable, Equatable, Sendable {
    public let previewImage: String
    public let backgroundImages: [String]
    public let keyImages: [String]
    public let totalSize: Int64
    public let compressedSize: Int64

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case previewImage = "preview_image"
        case backgroundImages = "background_images"
        case keyImages = "key_images"
        case totalSize = "total_size"
        case compressedSize = "compressed_size"
    }

    public init(
        previewImage: String,
        backgroundImages: [String],
        keyImages: [String],
        totalSize: Int64,
        compressedSize: Int64
    ) {
        self.previewImage = previewImage
        self.backgroundImages = backgroundImages
        self.keyImages = keyImages
        self.totalSize = totalSize
        self.compressedSize = compressedSize
    }
}

/// 功能特性信息
public struct FeatureInfo: Codable, Equatable, Sendable {
    public let supportsDarkMode: Bool
    public let hasAnimations: Bool
    public let hasSounds: Bool
    public let hasHaptics: Bool
    public let customFonts: Bool

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case supportsDarkMode = "supports_dark_mode"
        case hasAnimations = "has_animations"
        case hasSounds = "has_sounds"
        case hasHaptics = "has_haptics"
        case customFonts = "custom_fonts"
    }

    public init(
        supportsDarkMode: Bool,
        hasAnimations: Bool,
        hasSounds: Bool,
        hasHaptics: Bool,
        customFonts: Bool
    ) {
        self.supportsDarkMode = supportsDarkMode
        self.hasAnimations = hasAnimations
        self.hasSounds = hasSounds
        self.hasHaptics = hasHaptics
        self.customFonts = customFonts
    }
}

// MARK: - 主题包配置

/// 主题包完整配置
public struct ThemePackConfig: Codable, Sendable {
    public let schemaVersion: String
    public let themeInfo: ThemeInfo
    public let baseTheme: BaseThemeConfig
    public let advancedConfig: AdvancedThemeConfig
    public let validation: ValidationInfo

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case schemaVersion = "schema_version"
        case themeInfo = "theme_info"
        case baseTheme = "base_theme"
        case advancedConfig = "advanced_config"
        case validation
    }

    // 自定义解码器以支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 必需属性
        schemaVersion = try container.decodeIfPresent(String.self, forKey: .schemaVersion) ?? "1.0"
        themeInfo = try container.decode(ThemeInfo.self, forKey: .themeInfo)

        // 可选属性，提供默认值
        baseTheme = try container.decodeIfPresent(BaseThemeConfig.self, forKey: .baseTheme) ??
            BaseThemeConfig.defaultConfig()
        advancedConfig = try container.decodeIfPresent(AdvancedThemeConfig.self, forKey: .advancedConfig) ??
            AdvancedThemeConfig.defaultConfig()
        validation = try container.decodeIfPresent(ValidationInfo.self, forKey: .validation) ??
            ValidationInfo.defaultConfig()
    }

    public init(
        schemaVersion: String,
        themeInfo: ThemeInfo,
        baseTheme: BaseThemeConfig,
        advancedConfig: AdvancedThemeConfig,
        validation: ValidationInfo
    ) {
        self.schemaVersion = schemaVersion
        self.themeInfo = themeInfo
        self.baseTheme = baseTheme
        self.advancedConfig = advancedConfig
        self.validation = validation
    }
}

/// 主题基本信息
public struct ThemeInfo: Codable, Sendable {
    public let id: String
    public let name: String
    public let description: String

    public init(id: String, name: String, description: String) {
        self.id = id
        self.name = name
        self.description = description
    }
}

/// 基础主题配置
public struct BaseThemeConfig: Codable, Sendable {
    public let id: String
    public let name: String
    public let type: String
    public let keyStyle: String
    public let colors: ColorConfig
    public let images: ImageConfig
    public let typography: TypographyConfig
    public let layout: LayoutConfig
    public let effects: EffectConfig

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case id, name, type
        case keyStyle = "key_style"
        case colors, images, typography, layout, effects
    }

    public init(
        id: String,
        name: String,
        type: String,
        keyStyle: String,
        colors: ColorConfig,
        images: ImageConfig,
        typography: TypographyConfig,
        layout: LayoutConfig,
        effects: EffectConfig
    ) {
        self.id = id
        self.name = name
        self.type = type
        self.keyStyle = keyStyle
        self.colors = colors
        self.images = images
        self.typography = typography
        self.layout = layout
        self.effects = effects
    }

    // 自定义解码方法，支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 必需属性
        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)

        // 可选属性，提供默认值
        type = try container.decodeIfPresent(String.self, forKey: .type) ?? "light"
        keyStyle = try container.decodeIfPresent(String.self, forKey: .keyStyle) ?? "rounded"
        colors = try container.decodeIfPresent(ColorConfig.self, forKey: .colors) ?? ColorConfig.defaultConfig()
        images = try container.decodeIfPresent(ImageConfig.self, forKey: .images) ?? ImageConfig.defaultConfig()
        typography = try container.decodeIfPresent(TypographyConfig.self, forKey: .typography) ?? TypographyConfig.defaultConfig()
        layout = try container.decodeIfPresent(LayoutConfig.self, forKey: .layout) ?? LayoutConfig.defaultConfig()
        effects = try container.decodeIfPresent(EffectConfig.self, forKey: .effects) ?? EffectConfig.defaultConfig()
    }

    // 提供默认配置
    public static func defaultConfig() -> BaseThemeConfig {
        return BaseThemeConfig(
            id: "default",
            name: "默认主题",
            type: "light",
            keyStyle: "rounded",
            colors: ColorConfig.defaultConfig(),
            images: ImageConfig.defaultConfig(),
            typography: TypographyConfig.defaultConfig(),
            layout: LayoutConfig.defaultConfig(),
            effects: EffectConfig.defaultConfig()
        )
    }
}

/// 颜色配置
public struct ColorConfig: Codable, Sendable {
    public let background: WidgetColor
    public let keyBackground: WidgetColor
    public let keyPressed: WidgetColor
    public let text: WidgetColor
    public let specialKey: WidgetColor
    public let border: WidgetColor

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case background, text, border
        case keyBackground = "key_background"
        case keyPressed = "key_pressed"
        case specialKey = "special_key"
    }

    public init(
        background: WidgetColor,
        keyBackground: WidgetColor,
        keyPressed: WidgetColor,
        text: WidgetColor,
        specialKey: WidgetColor,
        border: WidgetColor
    ) {
        self.background = background
        self.keyBackground = keyBackground
        self.keyPressed = keyPressed
        self.text = text
        self.specialKey = specialKey
        self.border = border
    }

    // 自定义解码方法，支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 所有属性都可选，提供默认值
        background = try container.decodeIfPresent(WidgetColor.self, forKey: .background) ?? WidgetColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1.0)
        keyBackground = try container.decodeIfPresent(WidgetColor.self, forKey: .keyBackground) ?? WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
        keyPressed = try container.decodeIfPresent(WidgetColor.self, forKey: .keyPressed) ?? WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0)
        text = try container.decodeIfPresent(WidgetColor.self, forKey: .text) ?? WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0)
        specialKey = try container.decodeIfPresent(WidgetColor.self, forKey: .specialKey) ?? WidgetColor(red: 0.68, green: 0.68, blue: 0.7, alpha: 1.0)
        border = try container.decodeIfPresent(WidgetColor.self, forKey: .border) ?? WidgetColor(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0)
    }

    // 提供默认配置
    public static func defaultConfig() -> ColorConfig {
        return ColorConfig(
            background: WidgetColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1.0),
            keyBackground: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
            keyPressed: WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
            text: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
            specialKey: WidgetColor(red: 0.68, green: 0.68, blue: 0.7, alpha: 1.0),
            border: WidgetColor(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0)
        )
    }
}

/// 按键状态图片配置
public struct KeyStateImages: Codable, Sendable {
    public let normal: String?
    public let pressed: String?
    public let hover: String?

    public init(normal: String? = nil, pressed: String? = nil, hover: String? = nil) {
        self.normal = normal
        self.pressed = pressed
        self.hover = hover
    }
}

/// 按键类型图片配置
public struct KeyTypeImages: Codable, Sendable {
    public let letter: KeyStateImages?
    public let number: KeyStateImages?
    public let function: KeyStateImages?
    public let space: KeyStateImages?
    public let shift: KeyStateImages?
    public let symbol: KeyStateImages?
    public let punctuation: KeyStateImages?

    public init(
        letter: KeyStateImages? = nil,
        number: KeyStateImages? = nil,
        function: KeyStateImages? = nil,
        space: KeyStateImages? = nil,
        shift: KeyStateImages? = nil,
        symbol: KeyStateImages? = nil,
        punctuation: KeyStateImages? = nil
    ) {
        self.letter = letter
        self.number = number
        self.function = function
        self.space = space
        self.shift = shift
        self.symbol = symbol
        self.punctuation = punctuation
    }
}

/// 图片配置
public struct ImageConfig: Codable, Sendable {
    public let hasBackgroundImage: Bool
    public let hasKeyImage: Bool
    public let backgroundImagePath: String?
    public let keyImagePath: String? // 保持向后兼容
    public let keyImages: KeyTypeImages? // 新的按键类型图片配置
    public let isBuiltInImageTheme: Bool
    public let imageOpacity: Double
    public let imageBlendMode: String

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case hasBackgroundImage = "has_background_image"
        case hasKeyImage = "has_key_image"
        case backgroundImagePath = "background_image_path"
        case keyImagePath = "key_image_path"
        case keyImages = "key_images"
        case isBuiltInImageTheme = "is_built_in_image_theme"
        case imageOpacity = "image_opacity"
        case imageBlendMode = "image_blend_mode"
    }

    public init(
        hasBackgroundImage: Bool,
        hasKeyImage: Bool,
        backgroundImagePath: String?,
        keyImagePath: String? = nil,
        keyImages: KeyTypeImages? = nil,
        isBuiltInImageTheme: Bool,
        imageOpacity: Double,
        imageBlendMode: String
    ) {
        self.hasBackgroundImage = hasBackgroundImage
        self.hasKeyImage = hasKeyImage
        self.backgroundImagePath = backgroundImagePath
        self.keyImagePath = keyImagePath
        self.keyImages = keyImages
        self.isBuiltInImageTheme = isBuiltInImageTheme
        self.imageOpacity = imageOpacity
        self.imageBlendMode = imageBlendMode
    }

    // 自定义解码方法，支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 所有属性都可选，提供默认值
        hasBackgroundImage = try container.decodeIfPresent(Bool.self, forKey: .hasBackgroundImage) ?? false
        hasKeyImage = try container.decodeIfPresent(Bool.self, forKey: .hasKeyImage) ?? false
        backgroundImagePath = try container.decodeIfPresent(String.self, forKey: .backgroundImagePath)
        keyImagePath = try container.decodeIfPresent(String.self, forKey: .keyImagePath)
        keyImages = try container.decodeIfPresent(KeyTypeImages.self, forKey: .keyImages)
        isBuiltInImageTheme = try container.decodeIfPresent(Bool.self, forKey: .isBuiltInImageTheme) ?? false
        imageOpacity = try container.decodeIfPresent(Double.self, forKey: .imageOpacity) ?? 1.0
        imageBlendMode = try container.decodeIfPresent(String.self, forKey: .imageBlendMode) ?? "normal"
    }

    // 提供默认配置
    public static func defaultConfig() -> ImageConfig {
        return ImageConfig(
            hasBackgroundImage: false,
            hasKeyImage: false,
            backgroundImagePath: nil,
            keyImagePath: nil,
            keyImages: nil,
            isBuiltInImageTheme: false,
            imageOpacity: 1.0,
            imageBlendMode: "normal"
        )
    }
}

/// 字体配置
public struct TypographyConfig: Codable, Sendable {
    public let fontName: String
    public let fontSize: Double
    public let fontWeight: String

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case fontName = "font_name"
        case fontSize = "font_size"
        case fontWeight = "font_weight"
    }

    public init(fontName: String, fontSize: Double, fontWeight: String) {
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontWeight = fontWeight
    }

    // 自定义解码方法，支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 所有属性都可选，提供默认值
        fontName = try container.decodeIfPresent(String.self, forKey: .fontName) ?? "SF Pro"
        fontSize = try container.decodeIfPresent(Double.self, forKey: .fontSize) ?? 16.0
        fontWeight = try container.decodeIfPresent(String.self, forKey: .fontWeight) ?? "medium"
    }

    // 提供默认配置
    public static func defaultConfig() -> TypographyConfig {
        return TypographyConfig(
            fontName: "SF Pro",
            fontSize: 16,
            fontWeight: "medium"
        )
    }
}

/// 布局配置
public struct LayoutConfig: Codable, Sendable {
    public let keySpacing: Double
    public let keyHeight: Double
    public let showBorder: Bool
    public let borderWidth: Double

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case keySpacing = "key_spacing"
        case keyHeight = "key_height"
        case showBorder = "show_border"
        case borderWidth = "border_width"
    }

    public init(keySpacing: Double, keyHeight: Double, showBorder: Bool, borderWidth: Double) {
        self.keySpacing = keySpacing
        self.keyHeight = keyHeight
        self.showBorder = showBorder
        self.borderWidth = borderWidth
    }

    // 自定义解码方法，支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 所有属性都可选，提供默认值
        keySpacing = try container.decodeIfPresent(Double.self, forKey: .keySpacing) ?? 4.0
        keyHeight = try container.decodeIfPresent(Double.self, forKey: .keyHeight) ?? 44.0
        showBorder = try container.decodeIfPresent(Bool.self, forKey: .showBorder) ?? true
        borderWidth = try container.decodeIfPresent(Double.self, forKey: .borderWidth) ?? 1.0
    }

    // 提供默认配置
    public static func defaultConfig() -> LayoutConfig {
        return LayoutConfig(
            keySpacing: 4.0,
            keyHeight: 44.0,
            showBorder: true,
            borderWidth: 1.0
        )
    }
}

/// 效果配置
public struct EffectConfig: Codable, Sendable {
    public let enableShadow: Bool
    public let shadowColor: WidgetColor
    public let shadowRadius: Double
    public let enableHaptic: Bool
    public let enableSound: Bool

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case enableShadow = "enable_shadow"
        case shadowColor = "shadow_color"
        case shadowRadius = "shadow_radius"
        case enableHaptic = "enable_haptic"
        case enableSound = "enable_sound"
    }

    public init(
        enableShadow: Bool,
        shadowColor: WidgetColor,
        shadowRadius: Double,
        enableHaptic: Bool,
        enableSound: Bool
    ) {
        self.enableShadow = enableShadow
        self.shadowColor = shadowColor
        self.shadowRadius = shadowRadius
        self.enableHaptic = enableHaptic
        self.enableSound = enableSound
    }

    // 自定义解码方法，支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 所有属性都可选，提供默认值
        enableShadow = try container.decodeIfPresent(Bool.self, forKey: .enableShadow) ?? true
        shadowColor = try container.decodeIfPresent(WidgetColor.self, forKey: .shadowColor) ?? WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.1)
        shadowRadius = try container.decodeIfPresent(Double.self, forKey: .shadowRadius) ?? 2.0
        enableHaptic = try container.decodeIfPresent(Bool.self, forKey: .enableHaptic) ?? true
        enableSound = try container.decodeIfPresent(Bool.self, forKey: .enableSound) ?? false
    }

    // 提供默认配置
    public static func defaultConfig() -> EffectConfig {
        return EffectConfig(
            enableShadow: true,
            shadowColor: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.1),
            shadowRadius: 2.0,
            enableHaptic: true,
            enableSound: false
        )
    }
}

/// 高级主题配置
public struct AdvancedThemeConfig: Codable, Sendable {
    public let globalSettings: GlobalSettings
    public let keyTypeConfigs: [String: KeyTypeConfig]
    public let individualKeyConfigs: [String: KeyConfig]
    public let createdAt: Date
    public let updatedAt: Date

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case globalSettings = "global_settings"
        case keyTypeConfigs = "key_type_configs"
        case individualKeyConfigs = "individual_key_configs"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
 

    public init(
        globalSettings: GlobalSettings,
        keyTypeConfigs: [String: KeyTypeConfig],
        individualKeyConfigs: [String: KeyConfig],
        createdAt: Date,
        updatedAt: Date
    ) {
        self.globalSettings = globalSettings
        self.keyTypeConfigs = keyTypeConfigs
        self.individualKeyConfigs = individualKeyConfigs
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    // 自定义解码方法，支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 可选属性，提供默认值
        globalSettings = try container.decodeIfPresent(GlobalSettings.self, forKey: .globalSettings) ?? GlobalSettings.defaultConfig()
        keyTypeConfigs = try container.decodeIfPresent([String: KeyTypeConfig].self, forKey: .keyTypeConfigs) ?? [:]

        // 特殊处理 individualKeyConfigs，确保即使某些 KeyConfig 属性缺失也能正常解码
        if container.contains(.individualKeyConfigs) {
//            do {
//                individualKeyConfigs = try container.decode([String: KeyConfig].self, forKey: .individualKeyConfigs)
//            } catch {
                // 如果直接解码失败，尝试逐个解码每个 KeyConfig
                var decodedConfigs: [String: KeyConfig] = [:]
                if let rawContainer = try? container.nestedContainer(keyedBy: DynamicCodingKey.self, forKey: .individualKeyConfigs) {
                    for key in rawContainer.allKeys {
                        do {
                            let keyConfig = try rawContainer.decode(KeyConfig.self, forKey: key)
                            decodedConfigs[key.stringValue] = keyConfig
                        } catch {
                            // 如果某个 KeyConfig 解码失败，跳过它但继续处理其他的
                            print("Warning: Failed to decode KeyConfig for key '\(key.stringValue)': \(error)")
                        }
                    }
                }
                individualKeyConfigs = decodedConfigs
//            }
        } else {
            individualKeyConfigs = [:]
        }

        createdAt = try container.decodeIfPresent(Date.self, forKey: .createdAt) ?? Date()
        updatedAt = try container.decodeIfPresent(Date.self, forKey: .updatedAt) ?? Date()
    }

    // 提供默认配置
    public static func defaultConfig() -> AdvancedThemeConfig {
        return AdvancedThemeConfig(
            globalSettings: GlobalSettings.defaultConfig(),
            keyTypeConfigs: [:],
            individualKeyConfigs: [:],
            createdAt: Date(),
            updatedAt: Date()
        )
    }
}

/// 全局设置
public struct GlobalSettings: Codable, Sendable {
    public let keySpacing: Double
    public let keyHeight: Double
    public let enableHapticFeedback: Bool
    public let enableSoundFeedback: Bool
    public let enableKeyAnimations: Bool
    public let animationDuration: Double
    public let enableGradientEffects: Bool
    public let enableParallaxEffect: Bool

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case keySpacing = "key_spacing"
        case keyHeight = "key_height"
        case enableHapticFeedback = "enable_haptic_feedback"
        case enableSoundFeedback = "enable_sound_feedback"
        case enableKeyAnimations = "enable_key_animations"
        case animationDuration = "animation_duration"
        case enableGradientEffects = "enable_gradient_effects"
        case enableParallaxEffect = "enable_parallax_effect"
    }

    public init(
        keySpacing: Double,
        keyHeight: Double,
        enableHapticFeedback: Bool,
        enableSoundFeedback: Bool,
        enableKeyAnimations: Bool,
        animationDuration: Double,
        enableGradientEffects: Bool,
        enableParallaxEffect: Bool
    ) {
        self.keySpacing = keySpacing
        self.keyHeight = keyHeight
        self.enableHapticFeedback = enableHapticFeedback
        self.enableSoundFeedback = enableSoundFeedback
        self.enableKeyAnimations = enableKeyAnimations
        self.animationDuration = animationDuration
        self.enableGradientEffects = enableGradientEffects
        self.enableParallaxEffect = enableParallaxEffect
    }

    // 自定义解码方法，支持默认值
    public init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        // 所有属性都可选，提供默认值
        keySpacing = try container.decodeIfPresent(Double.self, forKey: .keySpacing) ?? 4.0
        keyHeight = try container.decodeIfPresent(Double.self, forKey: .keyHeight) ?? 44.0
        enableHapticFeedback = try container.decodeIfPresent(Bool.self, forKey: .enableHapticFeedback) ?? true
        enableSoundFeedback = try container.decodeIfPresent(Bool.self, forKey: .enableSoundFeedback) ?? false
        enableKeyAnimations = try container.decodeIfPresent(Bool.self, forKey: .enableKeyAnimations) ?? true
        animationDuration = try container.decodeIfPresent(Double.self, forKey: .animationDuration) ?? 0.2
        enableGradientEffects = try container.decodeIfPresent(Bool.self, forKey: .enableGradientEffects) ?? false
        enableParallaxEffect = try container.decodeIfPresent(Bool.self, forKey: .enableParallaxEffect) ?? false
    }

    // 提供默认配置
    public static func defaultConfig() -> GlobalSettings {
        return GlobalSettings(
            keySpacing: 4.0,
            keyHeight: 44.0,
            enableHapticFeedback: true,
            enableSoundFeedback: false,
            enableKeyAnimations: true,
            animationDuration: 0.2,
            enableGradientEffects: false,
            enableParallaxEffect: false
        )
    }
}

/// 验证信息
public struct ValidationInfo: Codable, Sendable {
    public let checksum: String
    public let fileCount: Int
    public let totalSize: Int64

    // 自定义编码键
    private enum CodingKeys: String, CodingKey {
        case checksum
        case fileCount = "file_count"
        case totalSize = "total_size"
    }

    public init(checksum: String, fileCount: Int, totalSize: Int64) {
        self.checksum = checksum
        self.fileCount = fileCount
        self.totalSize = totalSize
    }

    // 提供默认配置
    public static func defaultConfig() -> ValidationInfo {
        return ValidationInfo(
            checksum: "default",
            fileCount: 0,
            totalSize: 0
        )
    }
}

// MARK: - 主题包捆绑

/// 主题包捆绑（元数据 + 配置）
public struct ThemePackBundle: Sendable {
    public let metadata: ThemePackMetadata
    public let config: ThemePackConfig

    public init(metadata: ThemePackMetadata, config: ThemePackConfig) {
        self.metadata = metadata
        self.config = config
    }
}

// MARK: - 安装记录

/// 已安装主题包记录
public struct InstalledPacksRecord: Codable {
    public let packs: [InstalledPackInfo]
    public let lastUpdated: Date

    public init(packs: [InstalledPackInfo], lastUpdated: Date) {
        self.packs = packs
        self.lastUpdated = lastUpdated
    }
}

/// 已安装主题包信息
public struct InstalledPackInfo: Codable {
    public let id: String
    public let version: String
    public let installedAt: Date
    public let isBuiltIn: Bool

    public init(id: String, version: String, installedAt: Date, isBuiltIn: Bool) {
        self.id = id
        self.version = version
        self.installedAt = installedAt
        self.isBuiltIn = isBuiltIn
    }
}
