import SwiftUI
import WidgetKit

/// 密码生成器小组件视图
@available(iOS 16.0, *)
public struct PasswordGeneratorWidgetView: View {
    // 配置数据
    public var config: PasswordGeneratorConfig

    // 小组件尺寸
    public var family: WidgetFamily

    // 初始化方法
    public init(config: PasswordGeneratorConfig, family: WidgetFamily = .systemMedium) {
        self.config = config
        self.family = family
    }

    // 主视图
    public var body: some View {
        ZStack {

            // 内容
            contentView
                .adaptiveBackground(content: {
                    // 背景
                    config.background.backgroundView()
                    
                })
                .padding()
        }
    }

    // 内容视图
    @ViewBuilder
    private var contentView: some View {
        switch family {
        case .systemSmall:
            smallWidgetView
        case .systemMedium:
            mediumWidgetView
        case .systemLarge:
            largeWidgetView
        default:
            mediumWidgetView
        }
    }

    // 小尺寸小组件视图
    private var smallWidgetView: some View {
        VStack(spacing: 8) {
            // 标题
            Text("密码生成器")
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(config.fontColor.toColor())

            // 密码显示
            passwordDisplayView
                .frame(height: 40)

            // 生成按钮
            if #available(iOS 17.0, *) {
                Button(intent: GeneratePasswordAppIntent(
                    length: config.passwordLength,
                    complexity: config.complexity.rawValue,
                    autoCopy: config.autoCopyToClipboard
                )) {
                    Text("生成密码")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.white)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 6)
                        .background(config.accentColor.toColor())
                        .cornerRadius(12)
                }
                .buttonStyle(.plain)
            } else {
                // Fallback on earlier versions
            }
        }
    }

    // 中尺寸小组件视图
    private var mediumWidgetView: some View {
        VStack(spacing: 10) {
            // 标题和复杂度
            HStack {
                Text("密码生成器")
                    .font(.system(size: 16, weight: .bold))
                    .foregroundColor(config.fontColor.toColor())

                Spacer()

                Text(config.complexity.rawValue)
                    .font(.system(size: 12))
                    .foregroundColor(.white)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(config.complexity.color)
                    .cornerRadius(8)
            }

            // 密码显示
            passwordDisplayView
                .frame(height: 50)

            // 按钮区域
            HStack(spacing: 12) {
                // 复制按钮
                if #available(iOS 17.0, *) {
                    Button(intent: CopyPasswordAppIntent(password: config.currentPassword)) {
                        Label("复制", systemImage: "doc.on.doc")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(config.accentColor.toColor().opacity(0.8))
                            .cornerRadius(12)
                    }
                    .buttonStyle(.plain)
                } else {
                    // Fallback on earlier versions
                }

                Spacer()

                // 生成按钮
                if #available(iOS 17.0, *) {
                    Button(intent: GeneratePasswordAppIntent(
                        length: config.passwordLength,
                        complexity: config.complexity.rawValue,
                        autoCopy: config.autoCopyToClipboard
                    )) {
                        Label("生成新密码", systemImage: "arrow.triangle.2.circlepath")
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(config.accentColor.toColor())
                            .cornerRadius(12)
                    }
                    .buttonStyle(.plain)
                } else {
                    // Fallback on earlier versions
                }
            }
        }
    }

    // 大尺寸小组件视图
    private var largeWidgetView: some View {
        VStack(spacing: 14) {
            // 标题和复杂度
            HStack {
                Text("密码生成器")
                    .font(.system(size: 18, weight: .bold))
                    .foregroundColor(config.fontColor.toColor())

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    HStack(spacing: 6) {
                        // 复杂度图标
                        Image(systemName: config.complexity.icon)
                            .font(.system(size: 12))
                            .foregroundColor(.white)

                        Text(config.complexity.rawValue)
                            .font(.system(size: 14))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 10)
                    .padding(.vertical, 5)
                    .background(config.complexity.color)
                    .cornerRadius(10)

                    HStack(spacing: 6) {
                        Image(systemName: "ruler")
                            .font(.system(size: 10))
                            .foregroundColor(config.fontColor.toColor().opacity(0.7))

                        Text("长度: \(config.passwordLength)")
                            .font(.system(size: 12))
                            .foregroundColor(config.fontColor.toColor().opacity(0.7))
                    }
                }
            }

            // 密码显示
            passwordDisplayView
                .frame(height: 60)

            // 密码强度指示器
            passwordStrengthIndicator
                .padding(.top, 2)

            // 按钮区域
            HStack(spacing: 16) {
                // 复制按钮
                if #available(iOS 17.0, *) {
                    Button(intent: CopyPasswordAppIntent(password: config.currentPassword)) {
                        Label("复制密码", systemImage: "doc.on.doc")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(config.accentColor.toColor().opacity(0.8))
                            .cornerRadius(14)
                    }
                    .buttonStyle(.plain)
                } else {
                    // Fallback on earlier versions
                }

                Spacer()

                // 生成按钮
                if #available(iOS 17.0, *) {
                    Button(intent: GeneratePasswordAppIntent(
                        length: config.passwordLength,
                        complexity: config.complexity.rawValue,
                        autoCopy: config.autoCopyToClipboard
                    )) {
                        Label("生成新密码", systemImage: "arrow.triangle.2.circlepath")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(.white)
                            .padding(.horizontal, 16)
                            .padding(.vertical, 8)
                            .background(config.accentColor.toColor())
                            .cornerRadius(14)
                    }
                    .buttonStyle(.plain)
                } else {
                    // Fallback on earlier versions
                }
            }

            // 上次更新时间和自动复制状态
            HStack {
                // 自动复制状态
                HStack(spacing: 4) {
                    Image(systemName: config.autoCopyToClipboard ? "doc.on.clipboard.fill" : "doc.on.clipboard")
                        .font(.system(size: 10))
                        .foregroundColor(config.autoCopyToClipboard ? config.accentColor.toColor() : config.fontColor.toColor().opacity(0.5))

                    Text(config.autoCopyToClipboard ? "自动复制已开启" : "自动复制已关闭")
                        .font(.system(size: 10))
                        .foregroundColor(config.autoCopyToClipboard ? config.accentColor.toColor() : config.fontColor.toColor().opacity(0.5))
                }

                Spacer()

                // 上次更新时间
                Text("上次更新: \(formattedDate)")
                    .font(.system(size: 10))
                    .foregroundColor(config.fontColor.toColor().opacity(0.5))
            }
            .padding(.top, 6)
        }
    }

    // 密码显示视图
    private var passwordDisplayView: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 12)
                .fill(Color.white.opacity(0.1))
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(config.accentColor.toColor().opacity(0.3), lineWidth: 1)
                )

            // 密码文本
            if config.showPassword {
                Text(config.currentPassword)
                    .font(.system(size: getFontSize(), weight: .medium, design: .monospaced))
                    .foregroundColor(config.fontColor.toColor())
                    .lineLimit(1)
                    .minimumScaleFactor(0.5)
                    .padding(.horizontal, 8)
            } else {
                Text(String(repeating: "•", count: config.currentPassword.count))
                    .font(.system(size: getFontSize(), weight: .medium))
                    .foregroundColor(config.fontColor.toColor())
                    .lineLimit(1)
                    .minimumScaleFactor(0.5)
                    .padding(.horizontal, 8)
            }
        }
    }

    // 密码强度指示器
    private var passwordStrengthIndicator: some View {
        let strength = PasswordGenerator.evaluatePasswordStrength(config.currentPassword)

        return VStack(alignment: .leading, spacing: 8) {
            // 强度条
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    // 背景
                    Rectangle()
                        .fill(Color.gray.opacity(0.2))
                        .cornerRadius(4)

                    // 进度
                    Rectangle()
                        .fill(strengthColor(for: strength))
                        .frame(width: geometry.size.width * CGFloat(strength / 100.0))
                        .cornerRadius(4)
                }
            }
            .frame(height: 8)

            // 强度文本
            HStack {
                Text("密码强度: \(strengthDescription(for: strength))")
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(config.fontColor.toColor().opacity(0.8))

                Spacer()

                // 显示百分比
                Text("\(Int(strength))%")
                    .font(.system(size: 12, weight: .bold))
                    .foregroundColor(strengthColor(for: strength))
            }

            // 在大尺寸小组件中显示详细的复杂度信息
            if family == .systemLarge {
                complexityDetailView
            }
        }
    }

    // 复杂度详细信息视图
    private var complexityDetailView: some View {
        VStack(alignment: .leading, spacing: 6) {
            // 复杂度标题
            Text("密码复杂度详情")
                .font(.system(size: 12, weight: .medium))
                .foregroundColor(config.fontColor.toColor().opacity(0.8))
                .padding(.top, 4)

            // 复杂度详情
            HStack(spacing: 12) {
                // 包含数字
                complexityItemView(
                    title: "数字",
                    isIncluded: config.currentPassword.rangeOfCharacter(from: .decimalDigits) != nil,
                    icon: "number"
                )

                // 包含小写字母
                complexityItemView(
                    title: "小写字母",
                    isIncluded: config.currentPassword.rangeOfCharacter(from: .lowercaseLetters) != nil,
                    icon: "textformat.abc"
                )

                // 包含大写字母
                complexityItemView(
                    title: "大写字母",
                    isIncluded: config.currentPassword.rangeOfCharacter(from: .uppercaseLetters) != nil,
                    icon: "textformat.abc.dottedunderline"
                )

                // 包含特殊字符
                complexityItemView(
                    title: "特殊字符",
                    isIncluded: config.currentPassword.rangeOfCharacter(from: .punctuationCharacters) != nil,
                    icon: "number.square"
                )
            }
            .padding(.top, 2)
        }
    }

    // 复杂度项目视图
    private func complexityItemView(title: String, isIncluded: Bool, icon: String) -> some View {
        VStack(spacing: 4) {
            // 图标
            Image(systemName: icon)
                .font(.system(size: 14))
                .foregroundColor(isIncluded ? config.accentColor.toColor() : config.fontColor.toColor().opacity(0.3))

            // 标题
            Text(title)
                .font(.system(size: 10))
                .foregroundColor(isIncluded ? config.fontColor.toColor() : config.fontColor.toColor().opacity(0.3))

            // 状态指示器
            Image(systemName: isIncluded ? "checkmark.circle.fill" : "xmark.circle")
                .font(.system(size: 12))
                .foregroundColor(isIncluded ? .green : .red.opacity(0.5))
        }
        .frame(maxWidth: .infinity)
    }

    // 获取字体大小
    private func getFontSize() -> CGFloat {
        switch family {
        case .systemSmall:
            return 14
        case .systemMedium:
            return 16
        case .systemLarge:
            return 18
        default:
            return 16
        }
    }

    // 获取强度颜色
    private func strengthColor(for strength: Double) -> Color {
        if strength < 30 {
            return .red
        } else if strength < 60 {
            return .orange
        } else if strength < 80 {
            return .blue
        } else {
            return .green
        }
    }

    // 获取强度描述
    private func strengthDescription(for strength: Double) -> String {
        if strength < 30 {
            return "弱"
        } else if strength < 60 {
            return "中等"
        } else if strength < 80 {
            return "强"
        } else {
            return "非常强"
        }
    }

    // 格式化日期
    private var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: config.lastUpdated)
    }
}
