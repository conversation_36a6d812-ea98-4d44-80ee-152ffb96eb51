import AppIntents
import WidgetKit
import SwiftUI
import UIKit

// MARK: - 生成密码的AppIntent
@available(iOS 16.0, *)
struct GeneratePasswordAppIntent: AppIntent {
    static var title: LocalizedStringResource { "生成新密码" }
    static var description: IntentDescription { IntentDescription("生成一个新的随机密码") }

    @Parameter(title: "密码长度")
    var length: Int
    
    @Parameter(title: "密码复杂度")
    var complexity: String
    
    @Parameter(title: "自动复制")
    var autoCopy: Bool

    init() {
        self.length = 12
        self.complexity = PasswordComplexity.strong.rawValue
        self.autoCopy = true
    }

    init(length: Int, complexity: String, autoCopy: Bool) {
        self.length = length
        self.complexity = complexity
        self.autoCopy = autoCopy
    }

    func perform() async throws -> some IntentResult {
        // 1. 获取密码复杂度
        let passwordComplexity = PasswordComplexity.allCases.first { $0.rawValue == complexity } ?? .strong
        
        // 2. 生成新密码
        let newPassword = PasswordGenerator.generatePassword(length: length, complexity: passwordComplexity)
        
        // 3. 更新保存的数据
        if var config = AppGroupDataManager.shared.read(PasswordGeneratorConfig.self, for: .passwordGenerator, property: .config) {
            config.currentPassword = newPassword
            config.lastUpdated = Date()
            AppGroupDataManager.shared.save(config, for: .passwordGenerator, property: .config)
            
            // 4. 如果设置了自动复制，则复制到剪贴板
            if autoCopy {
                UIPasteboard.general.string = newPassword
            }
            
            // 5. 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
            
            return .result()
        } else {
            // 如果没有配置，创建默认配置
            let defaultConfig = PasswordGeneratorConfig.createDefault()
            AppGroupDataManager.shared.save(defaultConfig, for: .passwordGenerator, property: .config)
            
            // 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
            
            return .result()
        }
    }
}

// MARK: - 复制密码的AppIntent
@available(iOS 16.0, *)
struct CopyPasswordAppIntent: AppIntent {
    static var title: LocalizedStringResource { "复制密码" }
    static var description: IntentDescription { IntentDescription("将当前密码复制到剪贴板") }

    @Parameter(title: "密码")
    var password: String

    init() {
        self.password = ""
    }

    init(password: String) {
        self.password = password
    }

    func perform() async throws -> some IntentResult {
        // 复制密码到剪贴板
        UIPasteboard.general.string = password
        
        return .result()
    }
}
