//
//  File.swift
//  MyWidgetKit
//
//  Created by yjzheng on 2025/5/10.
//

import Foundation

// 定义 AppGroup 枚举, 用于获取 AppGroup 的容器URL
public enum AppGroup: String {
    case defaultGroup = "group.com.ort.JZJJWidgetAPP.group" // 替换为实际的App Group名称

    // 获取 AppGroup 的容器URL
    public var containerURL: URL {
        return FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: self.rawValue)!
    }
}

// 扩展 AppGroup 枚举, 用于获取 AppGroup 的名称
extension AppGroup {
    public var name: String {
        return self.rawValue
    }
}

