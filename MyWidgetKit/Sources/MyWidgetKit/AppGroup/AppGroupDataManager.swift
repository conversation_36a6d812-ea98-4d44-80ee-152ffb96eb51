import Foundation

public class AppGroupDataManager: @unchecked Sendable {
    private init() {}

    public let appGroup = AppGroup.defaultGroup



    // 移除 @MainActor 标记，使其可以在任何线程上下文中访问
    public static let shared: AppGroupDataManager = {
        let instance = AppGroupDataManager()
        return instance
    }()

    public let userDefaults = UserDefaults(suiteName: AppGroup.defaultGroup.rawValue)

    /// 生成唯一文件名
    public func fileName(for widget: WidgetType, property: WidgetPropertyKey) -> String {
        return "\(widget.rawValue)_\(property.rawValue).\(property.fileExtension)"
    }

    // MARK: - 自动分流保存

    /// 自动分流保存
    /// - Parameters:
    ///   - value: 支持 String, Int, Double, Bool, [String: Any], [Any], Data, Codable
    ///   - widget: Widget类型
    ///   - property: 属性key
    public func saveAuto(_ value: Any?, for widget: WidgetType, property: WidgetPropertyKey) {
        let key = "\(widget.rawValue)_\(property.rawValue)"
        switch value {
        case let v as String:
            userDefaults?.set(v, forKey: key)
            userDefaults?.synchronize()
        case let v as Int:
            userDefaults?.set(v, forKey: key)
            userDefaults?.synchronize()
        case let v as Double:
            userDefaults?.set(v, forKey: key)
            userDefaults?.synchronize()
        case let v as Bool:
            userDefaults?.set(v, forKey: key)
            userDefaults?.synchronize()
        case let v as [String: Any]:
            userDefaults?.set(v, forKey: key)
            userDefaults?.synchronize()
        case let v as [Any]:
            userDefaults?.set(v, forKey: key)
            userDefaults?.synchronize()
        case let v as Data:
            let fileURL = appGroup.containerURL.appendingPathComponent(fileName(for: widget, property: property))
            do {
                try v.write(to: fileURL)
            } catch {
                print("保存Data失败: \(error)")
            }
        default:
            print("不支持的类型，请使用 save<T: Codable>() 方法保存 Codable 类型")
        }
    }

    /// 自动分流读取
    public func readAuto<T>(_ type: T.Type, for widget: WidgetType, property: WidgetPropertyKey) -> T? {
        let key = "\(widget.rawValue)_\(property.rawValue)"
        switch type {
        case is String.Type:
            return userDefaults?.string(forKey: key) as? T
        case is Int.Type:
            return userDefaults?.integer(forKey: key) as? T
        case is Double.Type:
            return userDefaults?.double(forKey: key) as? T
        case is Bool.Type:
            return userDefaults?.bool(forKey: key) as? T
        case is [String: Any].Type:
            return userDefaults?.dictionary(forKey: key) as? T
        case is [Any].Type:
            return userDefaults?.array(forKey: key) as? T
        case is Data.Type:
            let fileURL = appGroup.containerURL.appendingPathComponent(fileName(for: widget, property: property))
            return (try? Data(contentsOf: fileURL)) as? T
        default:
            print("请使用 read<T: Codable>() 方法读取 Codable 类型")
            return nil
        }
    }

    /// 保存数据到 AppGroup 中
    /// - Parameters:
    ///   - data: 数据
    ///   - group: 组
    ///   - fileName: 文件名
    public func saveData(data: Data, to group: AppGroup? = nil, fileName: String) {
        let fileURL = group?.containerURL.appendingPathComponent(fileName) ?? appGroup.containerURL.appendingPathComponent(fileName)
        do {
            try data.write(to: fileURL)
        } catch {
            print("Failed to save data to app group: \(error)")
        }
    }

    /// 从 AppGroup 中读取数据
    /// - Parameters:
    ///   - group: 组
    ///   - fileName: 文件名
    /// - Returns: 数据
    public func readData(from group: AppGroup? = nil, fileName: String) -> Data? {
        let fileURL = group?.containerURL.appendingPathComponent(fileName) ?? appGroup.containerURL.appendingPathComponent(fileName)
        return try? Data(contentsOf: fileURL)
    }

    /// 从 AppGroup 中读取数据
    /// - Parameters:
    ///   - group: 组
    ///   - fileName: 文件名
    /// - Returns: 文件路径
    public func getFileURL(from group: AppGroup? = nil, fileName: String) -> URL {
        return group?.containerURL.appendingPathComponent(fileName) ?? appGroup.containerURL.appendingPathComponent(fileName)
    }

    /// 保存 Codable 数据到 AppGroup
    /// - Parameters:
    ///   - value: 要保存的数据（必须遵循 Codable）
    ///   - widget: Widget类型
    ///   - property: 属性key
    public func save<T: Codable>(_ value: T, for widget: WidgetType, property: WidgetPropertyKey) {
        let encoder = JSONEncoder()
        do {
            let data = try encoder.encode(value)
            let fileURL = appGroup.containerURL.appendingPathComponent(fileName(for: widget, property: property))
            try data.write(to: fileURL)
        } catch {
            print("保存数据失败: \(error)")
        }
    }

    /// 保存键盘主题配置
    /// - Parameter theme: 键盘主题
    public func saveKeyboardTheme(_ theme: KeyboardTheme) {
        save(theme, for: .keyboard, property: .theme)

        // 同时保存简化版本供键盘扩展使用
        let simplifiedTheme = convertToSimplifiedTheme(theme)
        saveSimplifiedKeyboardTheme(simplifiedTheme)
    }

    /// 读取键盘主题配置
    /// - Returns: 键盘主题
    public func loadKeyboardTheme() -> KeyboardTheme? {
        return read(KeyboardTheme.self, for: .keyboard, property: .theme)
    }

    /// 保存简化的键盘主题（供键盘扩展使用）
    private func saveSimplifiedKeyboardTheme(_ theme: SimplifiedKeyboardTheme) {
        let key = "keyboard_simplified_theme"
        do {
            let data = try JSONEncoder().encode(theme)
            userDefaults?.set(data, forKey: key)
            userDefaults?.synchronize()
        } catch {
            print("保存简化键盘主题失败: \(error)")
        }
    }

    /// 转换为简化主题
    private func convertToSimplifiedTheme(_ theme: KeyboardTheme) -> SimplifiedKeyboardTheme {
        return SimplifiedKeyboardTheme(
            id: theme.id,
            name: theme.name,
            type: theme.type.rawValue,
            keyStyle: theme.keyStyle.rawValue,
            backgroundColor: ColorData(
                red: theme.backgroundColor.red,
                green: theme.backgroundColor.green,
                blue: theme.backgroundColor.blue,
                alpha: theme.backgroundColor.alpha
            ),
            keyBackgroundColor: ColorData(
                red: theme.keyBackgroundColor.red,
                green: theme.keyBackgroundColor.green,
                blue: theme.keyBackgroundColor.blue,
                alpha: theme.keyBackgroundColor.alpha
            ),
            keyPressedColor: ColorData(
                red: theme.keyPressedColor.red,
                green: theme.keyPressedColor.green,
                blue: theme.keyPressedColor.blue,
                alpha: theme.keyPressedColor.alpha
            ),
            textColor: ColorData(
                red: theme.textColor.red,
                green: theme.textColor.green,
                blue: theme.textColor.blue,
                alpha: theme.textColor.alpha
            ),
            specialKeyColor: ColorData(
                red: theme.specialKeyColor.red,
                green: theme.specialKeyColor.green,
                blue: theme.specialKeyColor.blue,
                alpha: theme.specialKeyColor.alpha
            ),
            borderColor: ColorData(
                red: theme.borderColor.red,
                green: theme.borderColor.green,
                blue: theme.borderColor.blue,
                alpha: theme.borderColor.alpha
            ),
            hasBackgroundImage: theme.hasBackgroundImage,
            hasKeyImage: theme.hasKeyImage,
            backgroundImagePath: theme.backgroundImagePath,
            keyImagePath: theme.keyImagePath,
            isBuiltInImageTheme: theme.isBuiltInImageTheme,
            imageOpacity: theme.imageOpacity,
            imageBlendMode: theme.imageBlendMode,
            fontName: theme.fontName,
            fontSize: theme.fontSize,
            fontWeight: theme.fontWeight.rawValue,
            keySpacing: theme.keySpacing,
            keyHeight: theme.keyHeight,
            showBorder: theme.showBorder,
            borderWidth: theme.borderWidth,
            enableShadow: theme.enableShadow,
            shadowColor: ColorData(
                red: theme.shadowColor.red,
                green: theme.shadowColor.green,
                blue: theme.shadowColor.blue,
                alpha: theme.shadowColor.alpha
            ),
            shadowRadius: theme.shadowRadius,
            enableHaptic: theme.enableHaptic,
            enableSound: theme.enableSound,
            createdAt: theme.createdAt,
            updatedAt: theme.updatedAt
        )
    }

    /// 读取 Codable 数据
    /// - Parameters:
    ///   - type: 数据类型
    ///   - widget: Widget类型
    ///   - property: 属性key
    /// - Returns: 解码后的数据
    public func read<T: Codable>(_ type: T.Type, for widget: WidgetType, property: WidgetPropertyKey) -> T? {
        let fileURL = appGroup.containerURL.appendingPathComponent(fileName(for: widget, property: property))
        guard let data = try? Data(contentsOf: fileURL) else { return nil }
        let decoder = JSONDecoder()
        return try? decoder.decode(type, from: data)
    }

    /// 保存简单数据到 AppGroup UserDefaults
    public func saveValue(_ value: Any?, for widget: WidgetType, property: WidgetPropertyKey) {
        let key = "\(widget.rawValue)_\(property.rawValue)"
        userDefaults?.set(value, forKey: key)
        userDefaults?.synchronize()
    }

    /// 读取简单数据
    public func value(for widget: WidgetType, property: WidgetPropertyKey) -> Any? {
        let key = "\(widget.rawValue)_\(property.rawValue)"
        return userDefaults?.object(forKey: key)
    }

    // 保存到 UserDefaults
    public func saveCodableToUserDefaults<T: Codable>(_ value: T, for widget: WidgetType, property: WidgetPropertyKey) {
        let key = "\(widget.rawValue)_\(property.rawValue)"
        let encoder = JSONEncoder()
        do {
            let data = try encoder.encode(value)
            userDefaults?.set(data, forKey: key)
            userDefaults?.synchronize()
        } catch {
            print("保存Codable到UserDefaults失败: \(error)")
        }
    }

    // 从 UserDefaults 读取
    public func readCodableFromUserDefaults<T: Codable>(_ type: T.Type, for widget: WidgetType, property: WidgetPropertyKey) -> T? {
        let key = "\(widget.rawValue)_\(property.rawValue)"
        guard let data = userDefaults?.data(forKey: key) else { return nil }
        let decoder = JSONDecoder()
        return try? decoder.decode(type, from: data)
    }
}

// MARK: - Codable 辅助扩展

private extension Encodable {
    func encodeToData() throws -> Data {
        let encoder = JSONEncoder()
        return try encoder.encode(self)
    }
}
