import SwiftUI
import WidgetKit

@available(iOS 16.0, *)
public extension View {
    /// 为小组件设置背景，同时兼容 iOS 16 和 iOS 17+
    /// - Parameters:
    ///   - backgroundView: 背景视图
    /// - Returns: 添加了背景的视图
    func widgetBackground<Background: View>(@ViewBuilder _ backgroundView: @escaping () -> Background) -> some View {
        // 自动设置环境值为小组件环境
        self.inWidgetEnvironment(true)
            .modifier(WidgetBackgroundModifier(content: backgroundView))
    }
}

/// 小组件背景修饰符
@available(iOS 16.0, *)
private struct WidgetBackgroundModifier<Background: View>: ViewModifier {
    let content: () -> Background

    func body(content: Content) -> some View {
        if #available(iOS 17.0, *) {
            content.containerBackground(for: .widget) {
                self.content()
            }
        } else {
            ZStack {
                self.content()
                content
            }
        }
    }
}
