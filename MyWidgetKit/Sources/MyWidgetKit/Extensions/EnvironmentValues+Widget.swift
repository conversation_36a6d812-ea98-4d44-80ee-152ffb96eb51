import SwiftUI

// 定义环境键
private struct IsWidgetEnvironmentKey: EnvironmentKey {
    static let defaultValue: Bool = false
}

// 扩展环境值
public extension EnvironmentValues {
    /// 标识当前是否在小组件环境中
    var isWidgetEnvironment: Bool {
        get { self[IsWidgetEnvironmentKey.self] }
        set { self[IsWidgetEnvironmentKey.self] = newValue }
    }
}

// 扩展视图，提供便捷方法
public extension View {
    /// 设置当前视图是否在小组件环境中
    /// - Parameter isWidget: 是否在小组件环境中
    /// - Returns: 修改后的视图
    func inWidgetEnvironment(_ isWidget: Bool = true) -> some View {
        environment(\.isWidgetEnvironment, isWidget)
    }
    
    /// 根据当前环境选择合适的背景
    /// - Parameter content: 背景内容
    /// - Returns: 添加了背景的视图
    @ViewBuilder
    func adaptiveBackground<Content: View>(@ViewBuilder content: @escaping () -> Content) -> some View {
        if #available(iOS 17.0, *) {
            self.modifier(AdaptiveBackgroundModifier(content: content))
        } else {
            self.background(content())
        }
    }
}

// 自适应背景修饰符
@available(iOS 17.0, *)
private struct AdaptiveBackgroundModifier<Background: View>: ViewModifier {
    @Environment(\.isWidgetEnvironment) private var isWidgetEnvironment
    let content: () -> Background
    
    func body(content: Content) -> some View {
        if isWidgetEnvironment {
            content.containerBackground(for: .widget) {
                self.content()
            }
        } else {
            content.background(self.content())
        }
    }
}
