import SwiftUI

@available(iOS 16.0, *)
public extension Bundle {
    /// 从包资源加载 UIImage
    /// - Parameters:
    ///   - name: 图片名称（不含扩展名）
    ///   - extension: 图片扩展名
    ///   - subdirectory: 子目录路径
    /// - Returns: 加载的 UIImage，如果加载失败则返回 nil
    func loadImage(named name: String, extension: String = "png", subdirectory: String? = nil) -> UIImage? {
        if let url = self.url(forResource: name, withExtension: `extension`, subdirectory: subdirectory) {
            return UIImage(contentsOfFile: url.path)
        }
        return nil
    }
    
    /// 从包资源加载 Data
    /// - Parameters:
    ///   - name: 资源名称（不含扩展名）
    ///   - extension: 资源扩展名
    ///   - subdirectory: 子目录路径
    /// - Returns: 加载的 Data，如果加载失败则返回 nil
    func loadData(named name: String, extension: String, subdirectory: String? = nil) -> Data? {
        if let url = self.url(forResource: name, withExtension: `extension`, subdirectory: subdirectory) {
            return try? Data(contentsOf: url)
        }
        return nil
    }
}

@available(iOS 16.0, *)
public extension Image {
    /// 从包资源创建图片
    /// - Parameters:
    ///   - name: 图片名称（不含扩展名）
    ///   - extension: 图片扩展名
    ///   - subdirectory: 子目录路径
    /// - Returns: 图片视图，如果加载失败则返回占位图
    static func fromPackage(named name: String, extension: String = "png", subdirectory: String? = nil) -> Image {
        if let uiImage = Bundle.module.loadImage(named: name, extension: `extension`, subdirectory: subdirectory) {
            return Image(uiImage: uiImage)
        }
        return Image(systemName: "photo")
    }
}
