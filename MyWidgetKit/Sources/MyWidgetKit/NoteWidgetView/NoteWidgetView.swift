import SwiftUI
import WidgetKit

/// 笔记小组件视图
@available(iOS 16.0, *)
public struct NoteWidgetView: View {
    /// 配置数据
    private let data: NoteWidgetData
    
    /// 小组件尺寸
    private let family: WidgetFamily
    
    /// 初始化方法
    public init(data: NoteWidgetData, family: WidgetFamily) {
        self.data = data
        self.family = family
    }
    
    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 根据不同尺寸显示不同布局
                switch family {
                case .systemSmall:
                    smallWidgetLayout
                case .systemMedium:
                    mediumWidgetLayout
                case .systemLarge:
                    largeWidgetLayout
                default:
                    // 默认使用中尺寸布局
                    mediumWidgetLayout
                }
            }
            .adaptiveBackground {
                // 背景
                switch data.background {
                case let .color(widgetColor):
                    widgetColor.toColor()
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageData(data):
                    Image(uiImage: UIImage(data: data) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageFile(path):
                    Image(uiImage: UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageURL(url):
                    if #available(iOS 15.0, *) {
                        AsyncImage(url: URL(string: url)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .clipped()
                        } placeholder: {
                            Color.gray
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .clipped()
                        }
                    } else {
                        Color.gray
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .clipped()
                    }
                case let .packageImage(name):
                    Image(name, bundle: .module)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                }
            }
        }
    }
    
    // MARK: - 小尺寸布局
    
    /// 小尺寸小组件布局
    private var smallWidgetLayout: some View {
        VStack(spacing: 8) {
            // 标题
            HStack {
                Text("快速笔记")
                    .font(customFont(size: 14, weight: .bold))
                    .foregroundColor(data.fontColor.toColor())
                
                Spacer()
                
                // 笔记数量
                Text("\(data.notes.count)")
                    .font(customFont(size: 12, weight: .medium))
                    .foregroundColor(data.fontColor.toColor().opacity(0.7))
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(data.fontColor.toColor().opacity(0.1))
                    .cornerRadius(8)
            }
            
            // 添加按钮
            HStack(spacing: 12) {
                // 添加文本笔记
                if #available(iOS 17.0, *) {
                    Button(intent: AddTextNoteAppIntent()) {
                        VStack(spacing: 4) {
                            Image(systemName: "square.and.pencil")
                                .font(.system(size: 20))
                            
                            Text("文本")
                                .font(customFont(size: 12))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(10)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    VStack(spacing: 4) {
                        Image(systemName: "square.and.pencil")
                            .font(.system(size: 20))
                        
                        Text("文本")
                            .font(customFont(size: 12))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(10)
                }
                
                // 添加语音笔记
                if #available(iOS 17.0, *) {
                    Button(intent: AddVoiceNoteAppIntent()) {
                        VStack(spacing: 4) {
                            Image(systemName: "mic")
                                .font(.system(size: 20))
                            
                            Text("语音")
                                .font(customFont(size: 12))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 8)
                        .background(Color.orange.opacity(0.1))
                        .foregroundColor(.orange)
                        .cornerRadius(10)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    VStack(spacing: 4) {
                        Image(systemName: "mic")
                            .font(.system(size: 20))
                        
                        Text("语音")
                            .font(customFont(size: 12))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color.orange.opacity(0.1))
                    .foregroundColor(.orange)
                    .cornerRadius(10)
                }
            }
            
            // 最新笔记
            if let latestNote = data.notesToDisplay().first {
                if #available(iOS 17.0, *) {
                    Button(intent: ViewNoteAppIntent(noteId: latestNote.id)) {
                        NoteItemView(
                            note: latestNote,
                            fontName: data.fontName,
                            fontColor: data.fontColor.toColor(),
                            fontSize: data.fontSize,
                            showCreationTime: data.showCreationTime,
                            showTypeIcon: data.showTypeIcon,
                            isCompact: true
                        )
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    NoteItemView(
                        note: latestNote,
                        fontName: data.fontName,
                        fontColor: data.fontColor.toColor(),
                        fontSize: data.fontSize,
                        showCreationTime: data.showCreationTime,
                        showTypeIcon: data.showTypeIcon,
                        isCompact: true
                    )
                }
            } else {
                // 空状态
                emptyStateView
            }
        }
        .padding(12)
    }
    
    // MARK: - 中尺寸布局
    
    /// 中尺寸小组件布局
    private var mediumWidgetLayout: some View {
        VStack(spacing: 8) {
            // 标题和添加按钮
            HStack {
                // 标题
                Text("快速笔记")
                    .font(customFont(size: 16, weight: .bold))
                    .foregroundColor(data.fontColor.toColor())
                
                Spacer()
                
                // 笔记数量
                Text("\(data.notes.count)")
                    .font(customFont(size: 12, weight: .medium))
                    .foregroundColor(data.fontColor.toColor().opacity(0.7))
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(data.fontColor.toColor().opacity(0.1))
                    .cornerRadius(8)
                
                // 添加文本笔记
                if #available(iOS 17.0, *) {
                    Button(intent: AddTextNoteAppIntent()) {
                        HStack(spacing: 4) {
                            Image(systemName: "square.and.pencil")
                            Text("文本")
                        }
                        .font(customFont(size: 12))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(8)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    HStack(spacing: 4) {
                        Image(systemName: "square.and.pencil")
                        Text("文本")
                    }
                    .font(customFont(size: 12))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(8)
                }
                
                // 添加语音笔记
                if #available(iOS 17.0, *) {
                    Button(intent: AddVoiceNoteAppIntent()) {
                        HStack(spacing: 4) {
                            Image(systemName: "mic")
                            Text("语音")
                        }
                        .font(customFont(size: 12))
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(Color.orange.opacity(0.1))
                        .foregroundColor(.orange)
                        .cornerRadius(8)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    HStack(spacing: 4) {
                        Image(systemName: "mic")
                        Text("语音")
                    }
                    .font(customFont(size: 12))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.orange.opacity(0.1))
                    .foregroundColor(.orange)
                    .cornerRadius(8)
                }
            }
            
            // 笔记列表
            let notesToShow = data.notesToDisplay()
            if !notesToShow.isEmpty {
                VStack(spacing: 8) {
                    ForEach(notesToShow.prefix(2)) { note in
                        if #available(iOS 17.0, *) {
                            Button(intent: ViewNoteAppIntent(noteId: note.id)) {
                                NoteItemView(
                                    note: note,
                                    fontName: data.fontName,
                                    fontColor: data.fontColor.toColor(),
                                    fontSize: data.fontSize,
                                    showCreationTime: data.showCreationTime,
                                    showTypeIcon: data.showTypeIcon
                                )
                            }
                            .buttonStyle(.plain)
                        } else {
                            // iOS 16 fallback
                            NoteItemView(
                                note: note,
                                fontName: data.fontName,
                                fontColor: data.fontColor.toColor(),
                                fontSize: data.fontSize,
                                showCreationTime: data.showCreationTime,
                                showTypeIcon: data.showTypeIcon
                            )
                        }
                    }
                }
            } else {
                // 空状态
                emptyStateView
            }
        }
        .padding(12)
    }
    
    // MARK: - 大尺寸布局
    
    /// 大尺寸小组件布局
    private var largeWidgetLayout: some View {
        VStack(spacing: 12) {
            // 标题和添加按钮
            HStack {
                // 标题
                Text("快速笔记")
                    .font(customFont(size: 18, weight: .bold))
                    .foregroundColor(data.fontColor.toColor())
                
                Spacer()
                
                // 笔记数量
                Text("\(data.notes.count)")
                    .font(customFont(size: 14, weight: .medium))
                    .foregroundColor(data.fontColor.toColor().opacity(0.7))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 3)
                    .background(data.fontColor.toColor().opacity(0.1))
                    .cornerRadius(10)
            }
            
            // 添加按钮
            HStack(spacing: 16) {
                // 添加文本笔记
                if #available(iOS 17.0, *) {
                    Button(intent: AddTextNoteAppIntent()) {
                        HStack(spacing: 8) {
                            Image(systemName: "square.and.pencil")
                                .font(.system(size: 16))
                            
                            Text("添加文本笔记")
                                .font(customFont(size: 14))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 10)
                        .background(Color.blue.opacity(0.1))
                        .foregroundColor(.blue)
                        .cornerRadius(12)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    HStack(spacing: 8) {
                        Image(systemName: "square.and.pencil")
                            .font(.system(size: 16))
                        
                        Text("添加文本笔记")
                            .font(customFont(size: 14))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(Color.blue.opacity(0.1))
                    .foregroundColor(.blue)
                    .cornerRadius(12)
                }
                
                // 添加语音笔记
                if #available(iOS 17.0, *) {
                    Button(intent: AddVoiceNoteAppIntent()) {
                        HStack(spacing: 8) {
                            Image(systemName: "mic")
                                .font(.system(size: 16))
                            
                            Text("添加语音笔记")
                                .font(customFont(size: 14))
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, 10)
                        .background(Color.orange.opacity(0.1))
                        .foregroundColor(.orange)
                        .cornerRadius(12)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    HStack(spacing: 8) {
                        Image(systemName: "mic")
                            .font(.system(size: 16))
                        
                        Text("添加语音笔记")
                            .font(customFont(size: 14))
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 10)
                    .background(Color.orange.opacity(0.1))
                    .foregroundColor(.orange)
                    .cornerRadius(12)
                }
            }
            
            // 笔记列表
            let notesToShow = data.notesToDisplay()
            if !notesToShow.isEmpty {
                VStack(spacing: 12) {
                    ForEach(notesToShow) { note in
                        if #available(iOS 17.0, *) {
                            Button(intent: ViewNoteAppIntent(noteId: note.id)) {
                                InteractiveNoteItemView(
                                    note: note,
                                    fontName: data.fontName,
                                    fontColor: data.fontColor.toColor(),
                                    fontSize: data.fontSize,
                                    showCreationTime: data.showCreationTime,
                                    showTypeIcon: data.showTypeIcon
                                )
                            }
                            .buttonStyle(.plain)
                        } else {
                            // iOS 16 fallback
                            InteractiveNoteItemView(
                                note: note,
                                fontName: data.fontName,
                                fontColor: data.fontColor.toColor(),
                                fontSize: data.fontSize,
                                showCreationTime: data.showCreationTime,
                                showTypeIcon: data.showTypeIcon
                            )
                        }
                    }
                }
            } else {
                // 空状态
                emptyStateView
                    .frame(maxHeight: .infinity)
            }
        }
        .padding(16)
    }
    
    // MARK: - 辅助视图
    
    /// 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: 8) {
            Image(systemName: "note.text")
                .font(.system(size: 24))
                .foregroundColor(data.fontColor.toColor().opacity(0.5))
            
            Text("暂无笔记")
                .font(customFont(size: 14))
                .foregroundColor(data.fontColor.toColor().opacity(0.7))
            
            Text("点击添加按钮创建新笔记")
                .font(customFont(size: 12))
                .foregroundColor(data.fontColor.toColor().opacity(0.5))
                .multilineTextAlignment(.center)
        }
        .padding()
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 辅助方法
    
    /// 自定义字体
    private func customFont(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        if data.fontName.isEmpty {
            return Font.system(size: size, weight: weight)
        } else {
            return Font.custom(data.fontName, size: size).weight(weight)
        }
    }
}

// MARK: - 预览

@available(iOS 16.0, *)
struct NoteWidgetView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 小尺寸预览
            NoteWidgetView(
                data: NoteWidgetData(
                    notes: NoteItem.createSampleNotes(),
                    background: .color(WidgetColor.fromColor(.white)),
                    fontColor: WidgetColor.fromColor(.black)
                ),
                family: .systemSmall
            )
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            
            // 中尺寸预览
            NoteWidgetView(
                data: NoteWidgetData(
                    notes: NoteItem.createSampleNotes(),
                    background: .color(WidgetColor.fromColor(.white)),
                    fontColor: WidgetColor.fromColor(.black)
                ),
                family: .systemMedium
            )
            .previewContext(WidgetPreviewContext(family: .systemMedium))
            
            // 大尺寸预览
            NoteWidgetView(
                data: NoteWidgetData(
                    notes: NoteItem.createSampleNotes(),
                    background: .color(WidgetColor.fromColor(.white)),
                    fontColor: WidgetColor.fromColor(.black)
                ),
                family: .systemLarge
            )
            .previewContext(WidgetPreviewContext(family: .systemLarge))
        }
    }
}
