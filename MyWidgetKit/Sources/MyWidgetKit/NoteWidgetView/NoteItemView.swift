import SwiftUI

/// 笔记项目视图
@available(iOS 16.0, *)
public struct NoteItemView: View {
    /// 笔记数据
    private let note: NoteItem
    
    /// 字体名称
    private let fontName: String
    
    /// 字体颜色
    private let fontColor: Color
    
    /// 字体大小
    private let fontSize: CGFloat
    
    /// 是否显示创建时间
    private let showCreationTime: Bool
    
    /// 是否显示类型图标
    private let showTypeIcon: Bool
    
    /// 是否为紧凑模式
    private let isCompact: Bool
    
    /// 初始化方法
    public init(
        note: NoteItem,
        fontName: String,
        fontColor: Color,
        fontSize: CGFloat,
        showCreationTime: Bool,
        showTypeIcon: Bool,
        isCompact: Bool = false
    ) {
        self.note = note
        self.fontName = fontName
        self.fontColor = fontColor
        self.fontSize = fontSize
        self.showCreationTime = showCreationTime
        self.showTypeIcon = showTypeIcon
        self.isCompact = isCompact
    }
    
    public var body: some View {
        VStack(alignment: .leading, spacing: isCompact ? 2 : 4) {
            // 标题行
            HStack(spacing: 4) {
                // 类型图标
                if showTypeIcon {
                    Image(systemName: note.type.icon)
                        .font(.system(size: isCompact ? fontSize - 2 : fontSize, weight: .medium))
                        .foregroundColor(note.type.color)
                }
                
                // 标题
                Text(note.title)
                    .font(customFont(size: isCompact ? fontSize : fontSize + 2, weight: .bold))
                    .foregroundColor(fontColor)
                    .lineLimit(1)
                
                Spacer()
                
                // 收藏图标
                if note.isFavorite {
                    Image(systemName: "star.fill")
                        .font(.system(size: isCompact ? fontSize - 2 : fontSize - 1))
                        .foregroundColor(.yellow)
                }
            }
            
            // 内容
            if !isCompact {
                Text(note.summary)
                    .font(customFont(size: fontSize - 1))
                    .foregroundColor(fontColor.opacity(0.8))
                    .lineLimit(2)
                    .padding(.top, 1)
            }
            
            // 创建时间
            if showCreationTime {
                HStack {
                    Text(formatDate(note.createdAt))
                        .font(customFont(size: isCompact ? fontSize - 3 : fontSize - 2))
                        .foregroundColor(fontColor.opacity(0.6))
                    
                    if note.type == .voice, let duration = note.audioDuration {
                        Spacer()
                        
                        HStack(spacing: 2) {
                            Image(systemName: "clock")
                                .font(.system(size: isCompact ? fontSize - 4 : fontSize - 3))
                            
                            Text(formatDuration(duration))
                                .font(customFont(size: isCompact ? fontSize - 4 : fontSize - 3))
                        }
                        .foregroundColor(fontColor.opacity(0.6))
                    }
                }
                .padding(.top, isCompact ? 1 : 2)
            }
        }
        .padding(isCompact ? 8 : 12)
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    /// 自定义字体
    private func customFont(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        if fontName.isEmpty {
            return Font.system(size: size, weight: weight)
        } else {
            return Font.custom(fontName, size: size).weight(weight)
        }
    }
    
    /// 格式化日期
    private func formatDate(_ date: Date) -> String {
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            // 今天
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm"
            return "今天 \(formatter.string(from: date))"
        } else if calendar.isDateInYesterday(date) {
            // 昨天
            let formatter = DateFormatter()
            formatter.dateFormat = "HH:mm"
            return "昨天 \(formatter.string(from: date))"
        } else {
            // 其他日期
            let formatter = DateFormatter()
            formatter.dateFormat = "MM-dd HH:mm"
            return formatter.string(from: date)
        }
    }
    
    /// 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60
        
        if minutes > 0 {
            return String(format: "%d:%02d", minutes, seconds)
        } else {
            return String(format: "0:%02d", seconds)
        }
    }
}

/// 笔记项目视图（带交互）
@available(iOS 16.0, *)
public struct InteractiveNoteItemView: View {
    /// 笔记数据
    private let note: NoteItem
    
    /// 字体名称
    private let fontName: String
    
    /// 字体颜色
    private let fontColor: Color
    
    /// 字体大小
    private let fontSize: CGFloat
    
    /// 是否显示创建时间
    private let showCreationTime: Bool
    
    /// 是否显示类型图标
    private let showTypeIcon: Bool
    
    /// 是否为紧凑模式
    private let isCompact: Bool
    
    /// 初始化方法
    public init(
        note: NoteItem,
        fontName: String,
        fontColor: Color,
        fontSize: CGFloat,
        showCreationTime: Bool,
        showTypeIcon: Bool,
        isCompact: Bool = false
    ) {
        self.note = note
        self.fontName = fontName
        self.fontColor = fontColor
        self.fontSize = fontSize
        self.showCreationTime = showCreationTime
        self.showTypeIcon = showTypeIcon
        self.isCompact = isCompact
    }
    
    public var body: some View {
        VStack {
            // 基本笔记视图
            NoteItemView(
                note: note,
                fontName: fontName,
                fontColor: fontColor,
                fontSize: fontSize,
                showCreationTime: showCreationTime,
                showTypeIcon: showTypeIcon,
                isCompact: isCompact
            )
            
            // 操作按钮
            if !isCompact {
                HStack(spacing: 12) {
                    // 查看按钮
                    if #available(iOS 17.0, *) {
                        Button(intent: ViewNoteAppIntent(noteId: note.id)) {
                            HStack {
                                Image(systemName: "eye")
                                Text("查看")
                            }
                            .font(customFont(size: fontSize - 2))
                            .foregroundColor(.blue)
                        }
                        .buttonStyle(.plain)
                    } else {
                        // iOS 16 fallback
                        HStack {
                            Image(systemName: "eye")
                            Text("查看")
                        }
                        .font(customFont(size: fontSize - 2))
                        .foregroundColor(.blue)
                    }
                    
                    Spacer()
                    
                    // 语音笔记播放按钮
                    if note.type == .voice {
                        if #available(iOS 17.0, *) {
                            Button(intent: PlayVoiceNoteAppIntent(noteId: note.id)) {
                                HStack {
                                    Image(systemName: "play.circle")
                                    Text("播放")
                                }
                                .font(customFont(size: fontSize - 2))
                                .foregroundColor(.orange)
                            }
                            .buttonStyle(.plain)
                        } else {
                            // iOS 16 fallback
                            HStack {
                                Image(systemName: "play.circle")
                                Text("播放")
                            }
                            .font(customFont(size: fontSize - 2))
                            .foregroundColor(.orange)
                        }
                    }
                    
                    // 收藏按钮
                    if #available(iOS 17.0, *) {
                        Button(intent: ToggleNoteFavoriteAppIntent(noteId: note.id)) {
                            HStack {
                                Image(systemName: note.isFavorite ? "star.fill" : "star")
                                Text(note.isFavorite ? "取消收藏" : "收藏")
                            }
                            .font(customFont(size: fontSize - 2))
                            .foregroundColor(note.isFavorite ? .yellow : .gray)
                        }
                        .buttonStyle(.plain)
                    } else {
                        // iOS 16 fallback
                        HStack {
                            Image(systemName: note.isFavorite ? "star.fill" : "star")
                            Text(note.isFavorite ? "取消收藏" : "收藏")
                        }
                        .font(customFont(size: fontSize - 2))
                        .foregroundColor(note.isFavorite ? .yellow : .gray)
                    }
                }
                .padding(.horizontal, 12)
                .padding(.top, 4)
                .padding(.bottom, 8)
            }
        }
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
    
    /// 自定义字体
    private func customFont(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        if fontName.isEmpty {
            return Font.system(size: size, weight: weight)
        } else {
            return Font.custom(fontName, size: size).weight(weight)
        }
    }
}

/// 预览
@available(iOS 16.0, *)
struct NoteItemView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 16) {
            // 文本笔记
            NoteItemView(
                note: NoteItem(
                    title: "购物清单",
                    content: "1. 牛奶\n2. 面包\n3. 鸡蛋\n4. 水果",
                    type: .text,
                    isFavorite: true
                ),
                fontName: "",
                fontColor: .black,
                fontSize: 14,
                showCreationTime: true,
                showTypeIcon: true
            )
            
            // 语音笔记
            NoteItemView(
                note: NoteItem(
                    title: "会议记录",
                    content: "关于项目进度的语音记录",
                    type: .voice,
                    audioFilePath: "audio.m4a",
                    audioDuration: 125,
                    isFavorite: false
                ),
                fontName: "",
                fontColor: .black,
                fontSize: 14,
                showCreationTime: true,
                showTypeIcon: true
            )
            
            // 交互式笔记项目
            InteractiveNoteItemView(
                note: NoteItem(
                    title: "购物清单",
                    content: "1. 牛奶\n2. 面包\n3. 鸡蛋\n4. 水果",
                    type: .text,
                    isFavorite: true
                ),
                fontName: "",
                fontColor: .black,
                fontSize: 14,
                showCreationTime: true,
                showTypeIcon: true
            )
        }
        .padding()
    }
}
