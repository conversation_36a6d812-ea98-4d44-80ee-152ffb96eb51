import AppIntents
import WidgetKit
import SwiftUI
import AVFoundation

// MARK: - 添加文本笔记的AppIntent
@available(iOS 16.0, *)
struct AddTextNoteAppIntent: AppIntent {
    static var title: LocalizedStringResource { "添加文本笔记" }
    static var description: IntentDescription { IntentDescription("快速添加一条文本笔记") }
    
    func perform() async throws -> some IntentResult {
        // 创建一个URL Scheme，打开应用并显示添加文本笔记界面
        if let url = URL(string: "jzjjwidget://addTextNote") {
            await openURL(url)
        }
        
        return .result()
    }
    
    @MainActor
    private func openURL(_ url: URL) async {
        await UIApplication.shared.open(url)
    }
}

// MARK: - 添加语音笔记的AppIntent
@available(iOS 16.0, *)
struct AddVoiceNoteAppIntent: AppIntent {
    static var title: LocalizedStringResource { "添加语音笔记" }
    static var description: IntentDescription { IntentDescription("快速添加一条语音笔记") }
    
    func perform() async throws -> some IntentResult {
        // 创建一个URL Scheme，打开应用并显示添加语音笔记界面
        if let url = URL(string: "jzjjwidget://addVoiceNote") {
            await openURL(url)
        }
        
        return .result()
    }
    
    @MainActor
    private func openURL(_ url: URL) async {
        await UIApplication.shared.open(url)
    }
}

// MARK: - 查看笔记的AppIntent
@available(iOS 16.0, *)
struct ViewNoteAppIntent: AppIntent {
    static var title: LocalizedStringResource { "查看笔记" }
    static var description: IntentDescription { IntentDescription("查看笔记详情") }
    
    @Parameter(title: "笔记ID")
    var noteId: String
    
    init() {
        self.noteId = ""
    }
    
    init(noteId: String) {
        self.noteId = noteId
    }
    
    func perform() async throws -> some IntentResult {
        // 创建一个URL Scheme，打开应用并显示特定笔记
        if let url = URL(string: "jzjjwidget://viewNote?id=\(noteId)") {
            await openURL(url)
        }
        
        return .result()
    }
    
    @MainActor
    private func openURL(_ url: URL) async {
        await UIApplication.shared.open(url)
    }
}

// MARK: - 切换笔记收藏状态的AppIntent
@available(iOS 16.0, *)
struct ToggleNoteFavoriteAppIntent: AppIntent {
    static var title: LocalizedStringResource { "收藏/取消收藏" }
    static var description: IntentDescription { IntentDescription("切换笔记的收藏状态") }
    
    @Parameter(title: "笔记ID")
    var noteId: String
    
    init() {
        self.noteId = ""
    }
    
    init(noteId: String) {
        self.noteId = noteId
    }
    
    func perform() async throws -> some IntentResult {
        // 1. 读取当前笔记数据
        if var config = AppGroupDataManager.shared.read(NoteWidgetData.self, for: .note, property: .config) {
            // 2. 查找并更新笔记
            if let index = config.notes.firstIndex(where: { $0.id == noteId }) {
                // 切换收藏状态
                config.notes[index].isFavorite.toggle()
                
                // 更新时间
                config.notes[index].updatedAt = Date()
                config.lastUpdated = Date()
                
                // 3. 保存更新后的数据
                AppGroupDataManager.shared.save(config, for: .note, property: .config)
                
                // 4. 刷新小组件
                WidgetCenter.shared.reloadAllTimelines()
                
                return .result()
            }
        }
        
        return .result()
    }
}

// MARK: - 播放语音笔记的AppIntent
@available(iOS 16.0, *)
struct PlayVoiceNoteAppIntent: AppIntent {
    static var title: LocalizedStringResource { "播放语音笔记" }
    static var description: IntentDescription { IntentDescription("播放语音笔记") }
    
    @Parameter(title: "笔记ID")
    var noteId: String
    
    init() {
        self.noteId = ""
    }
    
    init(noteId: String) {
        self.noteId = noteId
    }
    
    func perform() async throws -> some IntentResult {
        // 1. 读取当前笔记数据
        if let config = AppGroupDataManager.shared.read(NoteWidgetData.self, for: .note, property: .config) {
            // 2. 查找笔记
            if let note = config.notes.first(where: { $0.id == noteId }),
               note.type == .voice,
               let audioFilePath = note.audioFilePath {
                
                // 3. 创建一个URL Scheme，打开应用并播放特定语音笔记
                if let url = URL(string: "jzjjwidget://playVoiceNote?id=\(noteId)") {
                    await openURL(url)
                }
            }
        }
        
        return .result()
    }
    
    @MainActor
    private func openURL(_ url: URL) async {
        await UIApplication.shared.open(url)
    }
}

// MARK: - 删除笔记的AppIntent
@available(iOS 16.0, *)
struct DeleteNoteAppIntent: AppIntent {
    static var title: LocalizedStringResource { "删除笔记" }
    static var description: IntentDescription { IntentDescription("删除笔记") }
    
    @Parameter(title: "笔记ID")
    var noteId: String
    
    init() {
        self.noteId = ""
    }
    
    init(noteId: String) {
        self.noteId = noteId
    }
    
    func perform() async throws -> some IntentResult {
        // 1. 读取当前笔记数据
        if var config = AppGroupDataManager.shared.read(NoteWidgetData.self, for: .note, property: .config) {
            // 2. 查找并删除笔记
            if let index = config.notes.firstIndex(where: { $0.id == noteId }) {
                // 如果是语音笔记，删除音频文件
                if config.notes[index].type == .voice,
                   let audioFilePath = config.notes[index].audioFilePath {
                    
                    // 获取文件URL
                    let fileURL = AppGroup.defaultGroup.containerURL.appendingPathComponent(audioFilePath)
                    
                    // 删除文件
                    do {
                        if FileManager.default.fileExists(atPath: fileURL.path) {
                            try FileManager.default.removeItem(at: fileURL)
                        }
                    } catch {
                        print("删除音频文件失败: \(error.localizedDescription)")
                    }
                }
                
                // 删除笔记
                config.notes.remove(at: index)
                
                // 更新时间
                config.lastUpdated = Date()
                
                // 3. 保存更新后的数据
                AppGroupDataManager.shared.save(config, for: .note, property: .config)
                
                // 4. 刷新小组件
                WidgetCenter.shared.reloadAllTimelines()
            }
        }
        
        return .result()
    }
}
