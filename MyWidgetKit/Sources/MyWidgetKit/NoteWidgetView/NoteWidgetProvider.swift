import SwiftUI
import WidgetKit

/// 笔记小组件时间线提供者
@available(iOS 16.0, *)
public struct NoteWidgetProvider: TimelineProvider {
    public typealias Entry = NoteWidgetEntry
    
    public init() {}
    
    /// 提供占位符条目
    public func placeholder(in context: Context) -> Entry {
        // 创建默认配置
        let defaultConfig = NoteWidgetData(
            notes: NoteItem.createSampleNotes()
        )
        return Entry(date: Date(), configuration: defaultConfig)
    }
    
    /// 提供快照条目
    public func getSnapshot(in context: Context, completion: @escaping (Entry) -> Void) {
        // 读取保存的配置
        if let config = AppGroupDataManager.shared.read(NoteWidgetData.self, for: .note, property: .config) {
            let entry = Entry(date: Date(), configuration: config)
            completion(entry)
        } else {
            // 如果没有配置，使用默认配置
            let defaultConfig = NoteWidgetData(
                notes: NoteItem.createSampleNotes()
            )
            let entry = Entry(date: Date(), configuration: defaultConfig)
            completion(entry)
        }
    }
    
    /// 提供时间线条目
    public func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> Void) {
        // 读取保存的配置
        let config = AppGroupDataManager.shared.read(NoteWidgetData.self, for: .note, property: .config) ?? NoteWidgetData(
            notes: NoteItem.createSampleNotes()
        )
        
        // 创建时间线条目
        var entries: [Entry] = []
        
        // 当前日期
        let currentDate = Date()
        
        // 创建一个条目
        let entry = Entry(date: currentDate, configuration: config)
        entries.append(entry)
        
        // 设置刷新策略 - 每小时刷新一次
        let refreshDate = Calendar.current.date(byAdding: .hour, value: 1, to: currentDate) ?? currentDate
        let timeline = Timeline(entries: entries, policy: .after(refreshDate))
        completion(timeline)
    }
}
