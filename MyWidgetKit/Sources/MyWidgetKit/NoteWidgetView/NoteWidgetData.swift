import SwiftUI
import WidgetKit

/// 笔记类型
public enum NoteType: String, Codable, Hashable, CaseIterable {
    case text = "text"      // 文本笔记
    case voice = "voice"    // 语音笔记

    /// 获取类型图标
    public var icon: String {
        switch self {
        case .text:
            return "text.bubble"
        case .voice:
            return "waveform"
        }
    }

    /// 获取类型名称
    public var displayName: String {
        switch self {
        case .text:
            return "文本"
        case .voice:
            return "语音"
        }
    }

    /// 获取类型颜色
    public var color: Color {
        switch self {
        case .text:
            return .blue
        case .voice:
            return .orange
        }
    }
}

/// 笔记项目
public struct NoteItem: Identifiable, Codable, Hashable {
    /// 唯一标识符
    public var id: String

    /// 笔记标题
    public var title: String

    /// 笔记内容
    public var content: String

    /// 笔记类型
    public var type: NoteType

    /// 创建时间
    public var createdAt: Date

    /// 修改时间
    public var updatedAt: Date

    /// 语音文件路径（仅语音笔记）
    public var audioFilePath: String?

    /// 语音时长（秒）（仅语音笔记）
    public var audioDuration: TimeInterval?

    /// 是否已收藏
    public var isFavorite: Bool

    /// 初始化方法
    public init(
        id: String = UUID().uuidString,
        title: String,
        content: String,
        type: NoteType = .text,
        createdAt: Date = Date(),
        updatedAt: Date = Date(),
        audioFilePath: String? = nil,
        audioDuration: TimeInterval? = nil,
        isFavorite: Bool = false
    ) {
        self.id = id
        self.title = title
        self.content = content
        self.type = type
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.audioFilePath = audioFilePath
        self.audioDuration = audioDuration
        self.isFavorite = isFavorite
    }

    // MARK: - Hashable

    /// 实现Hashable协议的hash方法
    public func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(title)
        hasher.combine(content)
        hasher.combine(type)
        hasher.combine(createdAt)
        hasher.combine(updatedAt)
        hasher.combine(audioFilePath)
        hasher.combine(audioDuration)
        hasher.combine(isFavorite)
    }

    /// 实现Equatable协议的==方法
    public static func == (lhs: NoteItem, rhs: NoteItem) -> Bool {
        return lhs.id == rhs.id &&
               lhs.title == rhs.title &&
               lhs.content == rhs.content &&
               lhs.type == rhs.type &&
               lhs.createdAt == rhs.createdAt &&
               lhs.updatedAt == rhs.updatedAt &&
               lhs.audioFilePath == rhs.audioFilePath &&
               lhs.audioDuration == rhs.audioDuration &&
               lhs.isFavorite == rhs.isFavorite
    }

    /// 获取笔记摘要
    public var summary: String {
        if type == .text {
            if content.count > 50 {
                return String(content.prefix(50)) + "..."
            }
            return content
        } else {
            return "语音笔记 \(formatDuration())"
        }
    }

    /// 格式化时长
    private func formatDuration() -> String {
        guard let duration = audioDuration else { return "" }

        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60

        if minutes > 0 {
            return String(format: "%d分%d秒", minutes, seconds)
        } else {
            return String(format: "%d秒", seconds)
        }
    }

    /// 创建示例笔记
    public static func createSampleNotes() -> [NoteItem] {
        return [
            NoteItem(
                title: "购物清单",
                content: "1. 牛奶\n2. 面包\n3. 鸡蛋\n4. 水果",
                type: .text,
                createdAt: Date().addingTimeInterval(-86400), // 昨天
                isFavorite: true
            ),
            NoteItem(
                title: "会议记录",
                content: "讨论了新项目的进度和下一步计划，需要在周五前完成初步设计。",
                type: .text,
                createdAt: Date().addingTimeInterval(-3600) // 1小时前
            ),
            NoteItem(
                title: "语音备忘",
                content: "关于周末活动的语音备忘",
                type: .voice,
                createdAt: Date().addingTimeInterval(-7200), // 2小时前
                audioFilePath: "sample_audio.m4a",
                audioDuration: 45
            )
        ]
    }
}

/// 笔记小组件数据模型
public struct NoteWidgetData: WidgetPreviewableData, Codable, Hashable {
    /// 笔记列表
    public var notes: [NoteItem]

    /// 背景设置
    public var background: WidgetBackground

    /// 字体颜色
    public var fontColor: WidgetColor

    /// 字体名称
    public var fontName: String

    /// 字体大小
    public var fontSize: CGFloat

    /// 显示模式
    public var displayMode: NoteDisplayMode

    /// 最大显示笔记数量
    public var maxNoteCount: Int

    /// 是否显示创建时间
    public var showCreationTime: Bool

    /// 是否显示笔记类型图标
    public var showTypeIcon: Bool

    /// 最后更新时间
    public var lastUpdated: Date

    /// 初始化方法
    public init(
        notes: [NoteItem] = [],
        background: WidgetBackground = .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
        fontColor: WidgetColor = WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
        fontName: String = "",
        fontSize: CGFloat = 14,
        displayMode: NoteDisplayMode = .all,
        maxNoteCount: Int = 3,
        showCreationTime: Bool = true,
        showTypeIcon: Bool = true,
        lastUpdated: Date = Date()
    ) {
        self.notes = notes
        self.background = background
        self.fontColor = fontColor
        self.fontName = fontName
        self.fontSize = fontSize
        self.displayMode = displayMode
        self.maxNoteCount = maxNoteCount
        self.showCreationTime = showCreationTime
        self.showTypeIcon = showTypeIcon
        self.lastUpdated = lastUpdated
    }

    // MARK: - Hashable

    /// 实现Hashable协议的hash方法
    public func hash(into hasher: inout Hasher) {
        hasher.combine(notes)
        // 不直接使用background和fontColor，而是使用它们的描述或标识符
        switch background {
        case .color(let color):
            hasher.combine("color")
            hasher.combine(color.red)
            hasher.combine(color.green)
            hasher.combine(color.blue)
            hasher.combine(color.alpha)
        case .imageData(let data):
            hasher.combine("imageData")
            hasher.combine(data.count)
        case .imageURL(let url):
            hasher.combine("imageURL")
            hasher.combine(url)
        case .imageFile(let path):
            hasher.combine("imageFile")
            hasher.combine(path)
        case .packageImage(let name):
            hasher.combine("packageImage")
            hasher.combine(name)
        }

        // 处理fontColor
        hasher.combine(fontColor.red)
        hasher.combine(fontColor.green)
        hasher.combine(fontColor.blue)
        hasher.combine(fontColor.alpha)

        hasher.combine(fontName)
        hasher.combine(fontSize)
        hasher.combine(displayMode)
        hasher.combine(maxNoteCount)
        hasher.combine(showCreationTime)
        hasher.combine(showTypeIcon)
        hasher.combine(lastUpdated)
    }

    /// 实现Equatable协议的==方法
    public static func == (lhs: NoteWidgetData, rhs: NoteWidgetData) -> Bool {
        // 比较notes
        guard lhs.notes == rhs.notes else { return false }

        // 比较background
        switch (lhs.background, rhs.background) {
        case (.color(let lhsColor), .color(let rhsColor)):
            guard lhsColor.red == rhsColor.red &&
                  lhsColor.green == rhsColor.green &&
                  lhsColor.blue == rhsColor.blue &&
                  lhsColor.alpha == rhsColor.alpha else { return false }
        case (.imageData(let lhsData), .imageData(let rhsData)):
            guard lhsData == rhsData else { return false }
        case (.imageURL(let lhsURL), .imageURL(let rhsURL)):
            guard lhsURL == rhsURL else { return false }
        case (.imageFile(let lhsPath), .imageFile(let rhsPath)):
            guard lhsPath == rhsPath else { return false }
        case (.packageImage(let lhsName), .packageImage(let rhsName)):
            guard lhsName == rhsName else { return false }
        default:
            // 不同类型的background被视为不相等
            return false
        }

        // 比较fontColor
        guard lhs.fontColor.red == rhs.fontColor.red &&
              lhs.fontColor.green == rhs.fontColor.green &&
              lhs.fontColor.blue == rhs.fontColor.blue &&
              lhs.fontColor.alpha == rhs.fontColor.alpha else { return false }

        // 比较其他属性
        return lhs.fontName == rhs.fontName &&
               lhs.fontSize == rhs.fontSize &&
               lhs.displayMode == rhs.displayMode &&
               lhs.maxNoteCount == rhs.maxNoteCount &&
               lhs.showCreationTime == rhs.showCreationTime &&
               lhs.showTypeIcon == rhs.showTypeIcon &&
               lhs.lastUpdated == rhs.lastUpdated
    }

    /// 获取要显示的笔记
    public func notesToDisplay() -> [NoteItem] {
        var filteredNotes = notes

        // 根据显示模式筛选
        switch displayMode {
        case .all:
            // 不需要筛选
            break
        case .textOnly:
            filteredNotes = notes.filter { $0.type == .text }
        case .voiceOnly:
            filteredNotes = notes.filter { $0.type == .voice }
        case .favoritesOnly:
            filteredNotes = notes.filter { $0.isFavorite }
        }

        // 按创建时间排序（最新的在前面）
        filteredNotes.sort { $0.createdAt > $1.createdAt }

        // 限制数量
        if filteredNotes.count > maxNoteCount {
            return Array(filteredNotes.prefix(maxNoteCount))
        }

        return filteredNotes
    }
}

/// 笔记显示模式
public enum NoteDisplayMode: String, Codable, CaseIterable {
    case all = "all"                // 所有笔记
    case textOnly = "textOnly"      // 仅文本笔记
    case voiceOnly = "voiceOnly"    // 仅语音笔记
    case favoritesOnly = "favoritesOnly"  // 仅收藏笔记

    /// 获取显示名称
    public var displayName: String {
        switch self {
        case .all:
            return "所有笔记"
        case .textOnly:
            return "仅文本笔记"
        case .voiceOnly:
            return "仅语音笔记"
        case .favoritesOnly:
            return "仅收藏笔记"
        }
    }
}

/// 笔记小组件入口
public struct NoteWidgetEntry: TimelineEntry {
    /// 日期
    public let date: Date

    /// 配置数据
    public let configuration: NoteWidgetData

    /// 初始化方法
    public init(date: Date, configuration: NoteWidgetData) {
        self.date = date
        self.configuration = configuration
    }
}
