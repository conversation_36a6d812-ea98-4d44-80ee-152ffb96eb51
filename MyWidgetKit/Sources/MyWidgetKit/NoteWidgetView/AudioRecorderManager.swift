import Foundation
import AVFoundation
import Combine

/// 音频录制状态
public enum AudioRecordingState {
    case ready      // 准备就绪
    case recording  // 正在录制
    case paused     // 已暂停
    case stopped    // 已停止
    case playing    // 正在播放
}

/// 音频录制管理器
public class AudioRecorderManager: NSObject, @unchecked Sendable, ObservableObject {
    // MARK: - 属性
    
    /// 单例实例
    public static let shared = AudioRecorderManager()
    
    /// 音频录制器
    private var audioRecorder: AVAudioRecorder?
    
    /// 音频播放器
    private var audioPlayer: AVAudioPlayer?
    
    /// 录制状态
    @Published public var recordingState: AudioRecordingState = .ready
    
    /// 当前录制时长（秒）
    @Published public var recordingDuration: TimeInterval = 0
    
    /// 当前播放时长（秒）
    @Published public var playbackProgress: TimeInterval = 0
    
    /// 总时长（秒）
    @Published public var totalDuration: TimeInterval = 0
    
    /// 音频电平
    @Published public var audioLevels: [Float] = []
    
    /// 计时器
    private var timer: Timer?
    
    /// 当前录制的文件URL
    private var currentRecordingURL: URL?
    
    // MARK: - 初始化方法
    
    private override init() {
        super.init()
        setupAudioSession()
    }
    
    // MARK: - 公共方法
    
    /// 设置音频会话
    public func setupAudioSession() {
        let audioSession = AVAudioSession.sharedInstance()
        
        do {
            try audioSession.setCategory(.playAndRecord, mode: .default, options: [.defaultToSpeaker, .allowBluetooth])
            try audioSession.setActive(true)
        } catch {
            print("设置音频会话失败: \(error.localizedDescription)")
        }
    }
    
    /// 开始录制
    /// - Returns: 录制文件的URL
    public func startRecording() -> URL? {
        // 停止任何正在进行的播放
        stopPlayback()
        
        // 创建录音文件URL
        let documentsDirectory = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)[0]
        let audioFilename = documentsDirectory.appendingPathComponent("\(UUID().uuidString).m4a")
        currentRecordingURL = audioFilename
        
        // 设置录音参数
        let settings = [
            AVFormatIDKey: Int(kAudioFormatMPEG4AAC),
            AVSampleRateKey: 44100,
            AVNumberOfChannelsKey: 2,
            AVEncoderAudioQualityKey: AVAudioQuality.high.rawValue
        ]
        
        // 创建录音器
        do {
            audioRecorder = try AVAudioRecorder(url: audioFilename, settings: settings)
            audioRecorder?.delegate = self
            audioRecorder?.isMeteringEnabled = true
            
            if audioRecorder?.record() == true {
                recordingState = .recording
                recordingDuration = 0
                
                // 启动计时器
                startTimer()
                
                return audioFilename
            }
        } catch {
            print("录音失败: \(error.localizedDescription)")
        }
        
        return nil
    }
    
    /// 暂停录制
    public func pauseRecording() {
        guard let recorder = audioRecorder, recordingState == .recording else { return }
        
        recorder.pause()
        recordingState = .paused
        stopTimer()
    }
    
    /// 继续录制
    public func resumeRecording() {
        guard let recorder = audioRecorder, recordingState == .paused else { return }
        
        recorder.record()
        recordingState = .recording
        startTimer()
    }
    
    /// 停止录制
    /// - Returns: 录制文件的URL和时长
    public func stopRecording() -> (URL, TimeInterval)? {
        guard let recorder = audioRecorder, let url = currentRecordingURL else { return nil }
        
        recorder.stop()
        recordingState = .stopped
        stopTimer()
        
        let duration = recordingDuration
        recordingDuration = 0
        
        // 将录音文件复制到App Group共享目录
        if let sharedURL = copyToSharedContainer(url: url) {
            return (sharedURL, duration)
        }
        
        return (url, duration)
    }
    
    /// 开始播放
    /// - Parameter url: 音频文件URL
    public func startPlayback(url: URL) {
        // 停止任何正在进行的录制
        if recordingState == .recording || recordingState == .paused {
            _ = stopRecording()
        }
        
        do {
            audioPlayer = try AVAudioPlayer(contentsOf: url)
            audioPlayer?.delegate = self
            
            if audioPlayer?.play() == true {
                recordingState = .playing
                totalDuration = audioPlayer?.duration ?? 0
                playbackProgress = 0
                
                // 启动计时器
                startTimer()
            }
        } catch {
            print("播放失败: \(error.localizedDescription)")
        }
    }
    
    /// 暂停播放
    public func pausePlayback() {
        guard let player = audioPlayer, recordingState == .playing else { return }
        
        player.pause()
        recordingState = .paused
        stopTimer()
    }
    
    /// 继续播放
    public func resumePlayback() {
        guard let player = audioPlayer, recordingState == .paused else { return }
        
        player.play()
        recordingState = .playing
        startTimer()
    }
    
    /// 停止播放
    public func stopPlayback() {
        guard let player = audioPlayer else { return }
        
        player.stop()
        recordingState = .stopped
        stopTimer()
        playbackProgress = 0
    }
    
    /// 获取音频文件时长
    /// - Parameter url: 音频文件URL
    /// - Returns: 时长（秒）
    public func getAudioDuration(url: URL) -> TimeInterval {
        do {
            let player = try AVAudioPlayer(contentsOf: url)
            return player.duration
        } catch {
            print("获取音频时长失败: \(error.localizedDescription)")
            return 0
        }
    }
    
    // MARK: - 私有方法
    
    /// 启动计时器
    private func startTimer() {
        stopTimer()
        
        timer = Timer.scheduledTimer(withTimeInterval: 0.1, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            if self.recordingState == .recording {
                // 更新录制时长
                self.recordingDuration = self.audioRecorder?.currentTime ?? 0
                
                // 更新音频电平
                self.updateAudioLevels()
            } else if self.recordingState == .playing {
                // 更新播放进度
                self.playbackProgress = self.audioPlayer?.currentTime ?? 0
            }
        }
    }
    
    /// 停止计时器
    private func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
    
    /// 更新音频电平
    private func updateAudioLevels() {
        guard let recorder = audioRecorder, recorder.isRecording else { return }
        
        recorder.updateMeters()
        
        // 获取平均电平
        let level = recorder.averagePower(forChannel: 0)
        
        // 将分贝值转换为0-1范围
        let normalizedLevel = pow(10, level / 20)
        
        // 添加到电平数组
        audioLevels.append(Float(normalizedLevel))
        
        // 限制数组大小
        if audioLevels.count > 50 {
            audioLevels.removeFirst()
        }
    }
    
    /// 将文件复制到共享容器
    /// - Parameter url: 原始文件URL
    /// - Returns: 共享容器中的文件URL
    private func copyToSharedContainer(url: URL) -> URL? {
        let fileName = url.lastPathComponent
        let sharedURL = AppGroup.defaultGroup.containerURL.appendingPathComponent(fileName)
        
        do {
            if FileManager.default.fileExists(atPath: sharedURL.path) {
                try FileManager.default.removeItem(at: sharedURL)
            }
            
            try FileManager.default.copyItem(at: url, to: sharedURL)
            return sharedURL
        } catch {
            print("复制到共享容器失败: \(error.localizedDescription)")
            return nil
        }
    }
}

// MARK: - AVAudioRecorderDelegate

extension AudioRecorderManager: AVAudioRecorderDelegate {
    public func audioRecorderDidFinishRecording(_ recorder: AVAudioRecorder, successfully flag: Bool) {
        if !flag {
            print("录音完成但不成功")
        }
        
        recordingState = .stopped
        stopTimer()
    }
    
    public func audioRecorderEncodeErrorDidOccur(_ recorder: AVAudioRecorder, error: Error?) {
        if let error = error {
            print("录音编码错误: \(error.localizedDescription)")
        }
        
        recordingState = .stopped
        stopTimer()
    }
}

// MARK: - AVAudioPlayerDelegate

extension AudioRecorderManager: AVAudioPlayerDelegate {
    public func audioPlayerDidFinishPlaying(_ player: AVAudioPlayer, successfully flag: Bool) {
        recordingState = .stopped
        stopTimer()
        playbackProgress = 0
    }
    
    public func audioPlayerDecodeErrorDidOccur(_ player: AVAudioPlayer, error: Error?) {
        if let error = error {
            print("播放解码错误: \(error.localizedDescription)")
        }
        
        recordingState = .stopped
        stopTimer()
    }
}
