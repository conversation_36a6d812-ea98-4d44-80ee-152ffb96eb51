import SwiftUI
import WidgetKit

/// 时间小组件数据模型
public struct TimeWidgetData:WidgetPreviewableData, Codable, Hashable  {
    /// 背景设置
    public var background: WidgetBackground

    /// 字体颜色
    public var fontColor: WidgetColor

    /// 字体名称
    public var fontName: String

    /// 是否使用12小时制
    public var use12HourFormat: Bool

    /// 是否显示日期
    public var showDate: Bool

    /// 是否显示秒
    public var showSeconds: Bool

    /// 初始化方法
    public init(
        background: WidgetBackground = .color(WidgetColor(red: 0, green: 0, blue: 0, alpha: 1)),
        fontColor: WidgetColor = WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
        fontName: String = "",
        use12HourFormat: Bool = false,
        showDate: Bool = true,
        showSeconds: Bool = true
    ) {
        self.background = background
        self.fontColor = fontColor
        self.fontName = fontName
        self.use12HourFormat = use12HourFormat
        self.showDate = showDate
        self.showSeconds = showSeconds
    }

    // MARK: - Hashable

    /// 实现Hashable协议的hash方法
    public func hash(into hasher: inout Hasher) {
        // 由于WidgetBackground没有实现Hashable，我们使用自定义的哈希逻辑
        switch background {
        case let .color(color):
            hasher.combine("color")
            hasher.combine(color.red)
            hasher.combine(color.green)
            hasher.combine(color.blue)
            hasher.combine(color.alpha)
        case let .imageData(data):
            hasher.combine("imageData")
            hasher.combine(data.count) // 使用数据长度作为哈希值的一部分
        case let .imageURL(url):
            hasher.combine("imageURL")
            hasher.combine(url)
        case let .imageFile(path):
            hasher.combine("imageFile")
            hasher.combine(path)
        case let .packageImage(name):
            hasher.combine("packageImage")
            hasher.combine(name)
        }

        // 其他属性都是Hashable的，直接组合
        hasher.combine(fontColor.red)
        hasher.combine(fontColor.green)
        hasher.combine(fontColor.blue)
        hasher.combine(fontColor.alpha)
        hasher.combine(fontName)
        hasher.combine(use12HourFormat)
        hasher.combine(showDate)
        hasher.combine(showSeconds)
    }
}

/// 时间小组件入口
public struct TimeWidgetEntry: TimelineEntry {
    /// 日期
    public let date: Date

    /// 配置数据
    public let configuration: TimeWidgetData

    /// 初始化方法
    public init(date: Date, configuration: TimeWidgetData) {
        self.date = date
        self.configuration = configuration
    }
}
