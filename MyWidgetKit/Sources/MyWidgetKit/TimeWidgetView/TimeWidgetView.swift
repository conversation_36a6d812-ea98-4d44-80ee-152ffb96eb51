import SwiftUI
import WidgetKit

/// 时间小组件视图
public struct TimeWidgetView: View {
    /// 配置数据
    private let data: TimeWidgetData

    /// 当前日期
    @State private var currentDate: Date

    /// 日期格式化器
    private let dateFormatter = DateFormatter()

    /// 时间格式化器
    private let timeFormatter = DateFormatter()

    /// 定时器
    @State private var timer: Timer?

    /// 是否在小组件环境中
    @Environment(\.isWidgetEnvironment) private var isWidgetEnvironment

    /// 初始化方法
    public init(data: TimeWidgetData, date: Date = Date()) {
        self.data = data
        self._currentDate = State(initialValue: date)

        // 配置日期格式化器
        dateFormatter.dateFormat = "yyyy-MM-dd EEEE"
        dateFormatter.locale = Locale(identifier: "zh_CN")

        // 配置时间格式化器
        updateTimeFormatter()
    }

    /// 更新时间格式化器
    private func updateTimeFormatter() {
        // 使用系统时间样式而不是自定义格式，以确保正确显示
        if data.use12HourFormat {
            timeFormatter.timeStyle = data.showSeconds ? .medium : .short
            timeFormatter.dateStyle = .none
        } else {
            // 对于24小时制，我们需要使用自定义格式
            timeFormatter.dateFormat = data.showSeconds ? "HH:mm:ss" : "HH:mm"
        }
        timeFormatter.locale = Locale(identifier: "zh_CN")
    }

    public var body: some View {
        GeometryReader { geometry in
            ZStack {

                // 内容
                VStack(spacing: 8) {
                    // 时间
                    Group {
                        if data.showSeconds {
                            // 显示秒时
                            if isWidgetEnvironment {
                                // 在小组件环境中，使用Text的date风格来显示时间
                                // 这样可以利用系统的自动更新机制

                                // 对于显示秒的情况，使用timer风格
                                // timer风格会显示为"00:00:00"格式，适合显示秒
                                Text(currentDate.getCurrentDayStartHour(true), style: .timer)
                                    .font(customFont(size: timeTextSize(for: geometry.size)))
                                    .foregroundColor(data.fontColor.toColor())
                                    .minimumScaleFactor(0.5)
                                    .lineLimit(1)
                                    .environment(\.locale, Locale(identifier: "zh_CN"))
                                    .environment(\.calendar, Calendar(identifier: .gregorian))
                                    .environment(\.timeZone, TimeZone.current)
                            } else {
                                // 在应用内，使用自定义格式化器
                                Text(timeFormatter.string(from: currentDate))
                                    .font(customFont(size: timeTextSize(for: geometry.size)))
                                    .foregroundColor(data.fontColor.toColor())
                                    .minimumScaleFactor(0.5)
                                    .lineLimit(1)
                            }
                        } else {
                            // 不显示秒时
                            if isWidgetEnvironment {
                                // 在小组件环境中，使用Text的date风格来显示时间
                                // 对于不显示秒的情况，使用time风格
                                // time风格会根据系统设置显示时间，可以处理12/24小时制
                                Text(currentDate.getCurrentDayStartHour(true), style: .timer)
                                    .font(customFont(size: timeTextSize(for: geometry.size)))
                                    .foregroundColor(data.fontColor.toColor())
                                    .minimumScaleFactor(0.5)
                                    .lineLimit(1)
                                    .environment(\.locale, Locale(identifier: "zh_CN"))
                                    // 根据12/24小时制设置不同的日历
                                    .environment(\.calendar, data.use12HourFormat ? Calendar(identifier: .gregorian) : Calendar(identifier: .iso8601))
                                    .environment(\.timeZone, TimeZone.current)
                            } else {
                                // 在应用内，使用自定义格式化器
                                Text(timeFormatter.string(from: currentDate))
                                    .font(customFont(size: timeTextSize(for: geometry.size)))
                                    .foregroundColor(data.fontColor.toColor())
                                    .minimumScaleFactor(0.5)
                                    .lineLimit(1)
                            }
                        }
                    }


                    // 日期
                    if data.showDate {
                        Text(dateFormatter.string(from: currentDate))
                            .font(customFont(size: dateTextSize(for: geometry.size)))
                            .foregroundColor(data.fontColor.toColor())
                            .minimumScaleFactor(0.5)
                            .lineLimit(1)
                    }
                }
                .padding()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .adaptiveBackground {
                    // 背景
                    switch data.background {
                    case let .color(widgetColor):
                        widgetColor.toColor()
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .clipped()
                    case let .imageData(data):
                        Image(uiImage: UIImage(data: data) ?? UIImage())
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .clipped()
                    case let .imageFile(path):
                        Image(uiImage: UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) ?? UIImage())
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .clipped()
                    case let .imageURL(url):
                        if #available(iOS 15.0, *) {
                            AsyncImage(url: URL(string: url)!) { image in
                                image.resizable()
                                    .aspectRatio(contentMode: .fill)
                                    .frame(width: geometry.size.width, height: geometry.size.height)
                                    .clipped()
                            } placeholder: {
                                Color.gray
                            }
                        } else {
                            Color.gray
                        }
                    case let .packageImage(name):
                        // 使用包内图片资源
                        Image(name, bundle: .module)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .clipped()
                    }
                }
            }
        }
    }

    /// 获取自定义字体
    private func customFont(size: CGFloat) -> Font {
        if !data.fontName.isEmpty {
            return Font.custom(data.fontName, size: size)
        } else {
            return Font.system(size: size, weight: .semibold, design: .rounded)
        }
    }

    /// 根据容器大小计算时间文本大小
    private func timeTextSize(for size: CGSize) -> CGFloat {
        let minDimension = min(size.width, size.height)

        // 根据容器大小调整字体大小
        if minDimension < 120 { // 小尺寸
            return 28
        } else if minDimension < 200 { // 中尺寸
            return 42
        } else { // 大尺寸
            return 60
        }
    }

    /// 根据容器大小计算日期文本大小
    private func dateTextSize(for size: CGSize) -> CGFloat {
        let minDimension = min(size.width, size.height)

        // 根据容器大小调整字体大小
        if minDimension < 120 { // 小尺寸
            return 12
        } else if minDimension < 200 { // 中尺寸
            return 18
        } else { // 大尺寸
            return 24
        }
    }
 
}
