import SwiftUI
import UIKit
import WidgetKit

/// 每日一言数据模型对象
public struct DailyQuoteWidgetViewData: WidgetPreviewableData {
    // 每日一言
    public let quote: String
    // 每日一言背景图片
    public let background: WidgetBackground
    // 每日一言日期
    public let date: Date

    public let config: WidgetCommonConfig?

    public init(quote: String, background: WidgetBackground, date: Date, config: WidgetCommonConfig?) {
        self.quote = quote
        self.background = background
        self.date = date
        self.config = config
    }
}

public struct DailyQuoteWidgetView: View {
    public let data: DailyQuoteWidgetViewData
    // 字体设置参数
    public let fontName: String
    public let fontSize: CGFloat
    public let fontColor: Color
    // 是否处于预览模式，预览模式下优先使用传入的参数而不是保存的配置
    public let isPreviewMode: Bool

    public init(data: DailyQuoteWidgetViewData, fontName: String = "PingFangSC", fontSize: CGFloat = 14, fontColor: Color = .white, isPreviewMode: Bool = false) {
        self.data = data
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontColor = fontColor
        self.isPreviewMode = isPreviewMode
    }

    // 从 AppGroupDataManager 获取保存的配置
    private var savedWidgetConfig: DailyQuoteWidgetConfig? {
        return AppGroupDataManager.shared.read(DailyQuoteWidgetConfig.self, for: .dailyQuote, property: .config)
    }

    // 使用保存的配置或传入的参数
    private var effectiveFontName: String {
        // 如果处于预览模式，优先使用传入的参数
        if isPreviewMode {
            return fontName
        }
        // 如果有自定义字体名称，优先使用
        if let config = savedWidgetConfig {
            return config.fontName
        }
        return fontName
    }

    private var effectiveFontSize: CGFloat {
        // 如果处于预览模式，优先使用传入的参数
        if isPreviewMode {
            return fontSize
        }
        // 如果有保存的字体大小，优先使用
        if let config = savedWidgetConfig {
            return config.fontSize
        }
        return fontSize
    }

    private var effectiveFontColor: Color {
        // 如果处于预览模式，优先使用传入的参数
        if isPreviewMode {
            return fontColor
        }
        // 如果有保存的字体颜色，优先使用
        if let config = savedWidgetConfig {
            return config.fontColor
        }
        return fontColor
    }

    // 获取有效的引用文本
    private func getEffectiveQuoteText() -> String {
        // 如果处于预览模式，优先使用传入的参数
        if isPreviewMode {
            return data.quote
        }
        // 如果有保存的内容，优先使用
        if let config = savedWidgetConfig, !config.content.isEmpty {
            return config.content
        }
        return data.quote
    }

    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                VStack(alignment: .leading) {
                    VStack(alignment: .leading, spacing: 0) {
                        Text("\(data.date.day)")
                            .font(.system(size: 36, weight: .bold))
                            .foregroundColor(effectiveFontColor)
                            + Text(" / \(data.date.month)")
                            .font(.system(size: 18, weight: .bold))
                            .foregroundColor(effectiveFontColor)
                        Text("\(data.date.year), 星期 \(data.date.week)")
                            .font(.system(size: 11))
                            .foregroundColor(effectiveFontColor)
                    }
                    .frame(maxWidth: .infinity, alignment: .leading)

                    Spacer()

                    // 使用有效的字体设置
                    Text(getEffectiveQuoteText())
                        .font(.system(size: effectiveFontSize))
                        .foregroundColor(effectiveFontColor)
                        .lineSpacing(4)
                        .multilineTextAlignment(.leading)
                }
                .padding(.horizontal, 14)
                .padding(.top, 13)
                .padding(.bottom, 18)
                .frame(width: geometry.size.width, height: geometry.size.height)
            }
            .adaptiveBackground {
                // 背景
                switch data.background {
                case let .color(widgetColor):
                    widgetColor.toColor()
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageData(data):
                    Image(uiImage: UIImage(data: data) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageFile(path):
                    Image(uiImage: UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageURL(url):
                    if #available(iOS 15.0, *) {
                        AsyncImage(url: URL(string: url)!) { image in
                            image.resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .clipped()
                        } placeholder: {
                            Color.gray
                        }
                    } else {
                        Color.gray
                    }
                case let .packageImage(name):
                    // 使用包内图片资源
                    Image(name, bundle: .module)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                }
            }
        }
    }
}
