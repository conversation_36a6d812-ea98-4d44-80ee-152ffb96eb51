import SwiftUI

/// 环形计时器视图
public struct CircularTimerView: View {
    /// 进度值 (0-1)
    private let progress: Double
    
    /// 线宽
    private let lineWidth: CGFloat
    
    /// 背景颜色
    private let backgroundColor: Color
    
    /// 前景颜色
    private let foregroundColor: Color
    
    /// 内容视图
    private let content: AnyView
    
    /// 初始化方法
    public init<Content: View>(
        progress: Double,
        lineWidth: CGFloat = 10,
        backgroundColor: Color = Color.gray.opacity(0.2),
        foregroundColor: Color,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.progress = min(max(progress, 0), 1)
        self.lineWidth = lineWidth
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.content = AnyView(content())
    }
    
    public var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(backgroundColor, lineWidth: lineWidth)
            
            // 进度圆环
            Circle()
                .trim(from: 0, to: CGFloat(progress))
                .stroke(
                    foregroundColor,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.2), value: progress)
            
            // 内容
            content
        }
    }
}

/// 带有波纹效果的环形计时器视图
public struct PulsatingCircularTimerView: View {
    /// 进度值 (0-1)
    private let progress: Double
    
    /// 线宽
    private let lineWidth: CGFloat
    
    /// 背景颜色
    private let backgroundColor: Color
    
    /// 前景颜色
    private let foregroundColor: Color
    
    /// 是否脉动
    @State private var isPulsating = false
    
    /// 内容视图
    private let content: AnyView
    
    /// 初始化方法
    public init<Content: View>(
        progress: Double,
        lineWidth: CGFloat = 10,
        backgroundColor: Color = Color.gray.opacity(0.2),
        foregroundColor: Color,
        @ViewBuilder content: @escaping () -> Content
    ) {
        self.progress = min(max(progress, 0), 1)
        self.lineWidth = lineWidth
        self.backgroundColor = backgroundColor
        self.foregroundColor = foregroundColor
        self.content = AnyView(content())
    }
    
    public var body: some View {
        ZStack {
            // 背景圆环
            Circle()
                .stroke(backgroundColor, lineWidth: lineWidth)
            
            // 脉动效果
            Circle()
                .stroke(foregroundColor.opacity(0.3), lineWidth: lineWidth)
                .scaleEffect(isPulsating ? 1.1 : 1.0)
                .opacity(isPulsating ? 0 : 0.3)
                .animation(
                    Animation.easeInOut(duration: 1.5)
                        .repeatForever(autoreverses: false),
                    value: isPulsating
                )
            
            // 进度圆环
            Circle()
                .trim(from: 0, to: CGFloat(progress))
                .stroke(
                    foregroundColor,
                    style: StrokeStyle(
                        lineWidth: lineWidth,
                        lineCap: .round
                    )
                )
                .rotationEffect(.degrees(-90))
                .animation(.easeInOut(duration: 0.2), value: progress)
            
            // 内容
            content
        }
        .onAppear {
            isPulsating = true
        }
    }
}

/// 预览
struct CircularTimerView_Previews: PreviewProvider {
    static var previews: some View {
        VStack(spacing: 30) {
            CircularTimerView(
                progress: 0.75,
                lineWidth: 15,
                foregroundColor: .red
            ) {
                Text("12:30")
                    .font(.system(size: 24, weight: .bold))
                    .foregroundColor(.red)
            }
            .frame(width: 200, height: 200)
            
            PulsatingCircularTimerView(
                progress: 0.4,
                lineWidth: 15,
                foregroundColor: .blue
            ) {
                VStack {
                    Text("08:00")
                        .font(.system(size: 24, weight: .bold))
                    Text("工作中")
                        .font(.system(size: 14))
                }
                .foregroundColor(.blue)
            }
            .frame(width: 200, height: 200)
        }
        .padding()
    }
}
