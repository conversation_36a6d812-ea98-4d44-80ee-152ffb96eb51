import SwiftUI
import WidgetKit

/// 番茄时钟小组件时间线提供者
@available(iOS 16.0, *)
public struct PomodoroWidgetProvider: TimelineProvider {
    public typealias Entry = PomodoroWidgetEntry
    
    public init() {}
    
    /// 提供占位符条目
    public func placeholder(in context: Context) -> Entry {
        // 创建默认配置
        let defaultConfig = PomodoroWidgetData()
        return Entry(date: Date(), configuration: defaultConfig)
    }
    
    /// 提供快照条目
    public func getSnapshot(in context: Context, completion: @escaping (Entry) -> Void) {
        // 读取保存的配置
        if let config = AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) {
            let entry = Entry(date: Date(), configuration: config)
            completion(entry)
        } else {
            // 如果没有配置，使用默认配置
            let defaultConfig = PomodoroWidgetData()
            let entry = Entry(date: Date(), configuration: defaultConfig)
            completion(entry)
        }
    }
    
    /// 提供时间线条目
    public func getTimeline(in context: Context, completion: @escaping (Timeline<Entry>) -> Void) {
        // 读取保存的配置
        let config = AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) ?? PomodoroWidgetData()
        
        // 创建时间线条目
        var entries: [Entry] = []
        
        // 当前日期
        let currentDate = Date()
        
        // 根据当前状态生成时间线
        switch config.currentState {
        case .idle, .paused:
            // 空闲或暂停状态，只需要一个条目
            let entry = Entry(date: currentDate, configuration: config)
            entries.append(entry)
            
            // 设置刷新策略
            let refreshDate = Calendar.current.date(byAdding: .hour, value: 1, to: currentDate) ?? currentDate
            let timeline = Timeline(entries: entries, policy: .after(refreshDate))
            completion(timeline)
            
        case .working, .shortBreak, .longBreak:
            // 活动状态，需要多个条目以实现倒计时效果
            
            // 如果有开始时间，计算剩余时间
            if let startTime = config.startTime {
                let elapsedSeconds = Int(currentDate.timeIntervalSince(startTime))
                let remainingSeconds = max(0, config.remainingSeconds - elapsedSeconds)
                
                // 创建每秒更新的条目
                for second in 0...min(remainingSeconds, 300) { // 最多生成5分钟的条目
                    let entryDate = currentDate.addingTimeInterval(TimeInterval(second))
                    let entry = Entry(date: entryDate, configuration: config)
                    entries.append(entry)
                }
                
                // 如果剩余时间为0，添加一个额外的条目用于自动切换到下一个阶段
                if remainingSeconds <= 0 && config.autoStartNextPhase {
                    // 创建新的配置
                    var nextConfig = config
                    
                    // 更新状态
                    switch config.currentState {
                    case .working:
                        // 工作结束，进入休息阶段
                        nextConfig.completedPomodoros += 1
                        
                        // 判断是进入短休息还是长休息
                        if nextConfig.completedPomodoros % nextConfig.longBreakInterval == 0 {
                            nextConfig.currentState = .longBreak
                            nextConfig.remainingSeconds = nextConfig.longBreakDuration * 60
                        } else {
                            nextConfig.currentState = .shortBreak
                            nextConfig.remainingSeconds = nextConfig.shortBreakDuration * 60
                        }
                        
                    case .shortBreak, .longBreak:
                        // 休息结束，进入工作阶段
                        nextConfig.currentState = .working
                        nextConfig.remainingSeconds = nextConfig.workDuration * 60
                        
                    default:
                        break
                    }
                    
                    // 更新开始时间
                    nextConfig.startTime = currentDate
                    nextConfig.lastUpdated = currentDate
                    
                    // 保存新配置
                    AppGroupDataManager.shared.save(nextConfig, for: .pomodoro, property: .config)
                    
                    // 添加下一阶段的条目
                    let nextEntry = Entry(date: currentDate.addingTimeInterval(1), configuration: nextConfig)
                    entries.append(nextEntry)
                }
                
                // 设置刷新策略
                let refreshDate = entries.last?.date ?? currentDate.addingTimeInterval(60)
                let timeline = Timeline(entries: entries, policy: .after(refreshDate))
                completion(timeline)
            } else {
                // 没有开始时间，使用单个条目
                let entry = Entry(date: currentDate, configuration: config)
                entries.append(entry)
                
                // 设置刷新策略
                let refreshDate = Calendar.current.date(byAdding: .minute, value: 15, to: currentDate) ?? currentDate
                let timeline = Timeline(entries: entries, policy: .after(refreshDate))
                completion(timeline)
            }
        }
    }
}
