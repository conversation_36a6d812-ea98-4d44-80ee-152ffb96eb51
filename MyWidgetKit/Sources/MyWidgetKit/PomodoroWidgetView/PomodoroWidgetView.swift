import SwiftUI
import WidgetKit

/// 番茄时钟小组件视图
@available(iOS 16.0, *)
public struct PomodoroWidgetView: View {
    /// 配置数据
    private let data: PomodoroWidgetData
    
    /// 小组件尺寸
    private let family: WidgetFamily
    
    /// 当前日期
    private let date: Date
    
    /// 初始化方法
    public init(data: PomodoroWidgetData, family: WidgetFamily, date: Date = Date()) {
        self.data = data
        self.family = family
        self.date = date
    }
    
    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 根据不同尺寸显示不同布局
                switch family {
                case .systemSmall:
                    smallWidgetLayout
                case .systemMedium:
                    mediumWidgetLayout
                case .systemLarge:
                    largeWidgetLayout
                default:
                    // 默认使用中尺寸布局
                    mediumWidgetLayout
                }
            }
            .adaptiveBackground {
                // 背景
                switch data.background {
                case let .color(widgetColor):
                    widgetColor.toColor()
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageData(data):
                    Image(uiImage: UIImage(data: data) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageFile(path):
                    Image(uiImage: UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageURL(url):
                    if #available(iOS 15.0, *) {
                        AsyncImage(url: URL(string: url)) { image in
                            image
                                .resizable()
                                .aspectRatio(contentMode: .fill)
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .clipped()
                        } placeholder: {
                            Color.gray
                                .frame(width: geometry.size.width, height: geometry.size.height)
                                .clipped()
                        }
                    } else {
                        Color.gray
                            .frame(width: geometry.size.width, height: geometry.size.height)
                            .clipped()
                    }
                case let .packageImage(name):
                    Image(name, bundle: .module)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                }
            }
        }
    }
    
    // MARK: - 小尺寸布局
    
    /// 小尺寸小组件布局
    private var smallWidgetLayout: some View {
        VStack(spacing: 8) {
            // 标题
            Text(data.currentState.displayName)
                .font(customFont(size: 14, weight: .bold))
                .foregroundColor(data.fontColor.toColor())
                .lineLimit(1)
            
            // 计时器
            CircularTimerView(
                progress: calculateProgress(),
                lineWidth: 8,
                backgroundColor: data.fontColor.toColor().opacity(0.2),
                foregroundColor: stateColor()
            ) {
                VStack(spacing: 4) {
                    Text(formattedTime())
                        .font(customFont(size: 20, weight: .bold))
                        .foregroundColor(data.fontColor.toColor())
                    
                    if data.currentState != .idle {
                        Text(data.currentState == .working ? "工作中" : "休息中")
                            .font(customFont(size: 12))
                            .foregroundColor(data.fontColor.toColor().opacity(0.8))
                    }
                }
            }
            .frame(height: 80)
            
            // 控制按钮
            controlButtons(compact: true)
        }
        .padding(12)
    }
    
    // MARK: - 中尺寸布局
    
    /// 中尺寸小组件布局
    private var mediumWidgetLayout: some View {
        HStack(spacing: 16) {
            // 左侧：计时器
            CircularTimerView(
                progress: calculateProgress(),
                lineWidth: 10,
                backgroundColor: data.fontColor.toColor().opacity(0.2),
                foregroundColor: stateColor()
            ) {
                VStack(spacing: 4) {
                    Text(formattedTime())
                        .font(customFont(size: 24, weight: .bold))
                        .foregroundColor(data.fontColor.toColor())
                    
                    Text(data.currentState.displayName)
                        .font(customFont(size: 12))
                        .foregroundColor(data.fontColor.toColor().opacity(0.8))
                }
            }
            .frame(width: 120, height: 120)
            
            // 右侧：信息和控制
            VStack(alignment: .leading, spacing: 12) {
                // 标题和状态
                VStack(alignment: .leading, spacing: 4) {
                    Text("番茄时钟")
                        .font(customFont(size: 16, weight: .bold))
                        .foregroundColor(data.fontColor.toColor())
                    
                    HStack {
                        Image(systemName: data.currentState.iconName)
                            .foregroundColor(stateColor())
                        
                        Text(data.currentState.displayName)
                            .font(customFont(size: 14))
                            .foregroundColor(data.fontColor.toColor().opacity(0.8))
                    }
                }
                
                // 完成的番茄数
                HStack {
                    Text("已完成:")
                        .font(customFont(size: 12))
                        .foregroundColor(data.fontColor.toColor().opacity(0.8))
                    
                    Text("\(data.completedPomodoros) 个番茄")
                        .font(customFont(size: 12, weight: .medium))
                        .foregroundColor(data.fontColor.toColor())
                }
                
                Spacer()
                
                // 控制按钮
                controlButtons(compact: false)
            }
            .padding(.vertical, 12)
        }
        .padding(12)
    }
    
    // MARK: - 大尺寸布局
    
    /// 大尺寸小组件布局
    private var largeWidgetLayout: some View {
        VStack(spacing: 16) {
            // 标题和状态
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("番茄时钟")
                        .font(customFont(size: 20, weight: .bold))
                        .foregroundColor(data.fontColor.toColor())
                    
                    HStack {
                        Image(systemName: data.currentState.iconName)
                            .foregroundColor(stateColor())
                        
                        Text(data.currentState.displayName)
                            .font(customFont(size: 16))
                            .foregroundColor(data.fontColor.toColor().opacity(0.8))
                    }
                }
                
                Spacer()
                
                // 完成的番茄数
                HStack {
                    Text("已完成:")
                        .font(customFont(size: 14))
                        .foregroundColor(data.fontColor.toColor().opacity(0.8))
                    
                    Text("\(data.completedPomodoros) 个番茄")
                        .font(customFont(size: 14, weight: .medium))
                        .foregroundColor(data.fontColor.toColor())
                }
            }
            
            // 计时器
            PulsatingCircularTimerView(
                progress: calculateProgress(),
                lineWidth: 12,
                backgroundColor: data.fontColor.toColor().opacity(0.2),
                foregroundColor: stateColor()
            ) {
                VStack(spacing: 8) {
                    Text(formattedTime())
                        .font(customFont(size: 32, weight: .bold))
                        .foregroundColor(data.fontColor.toColor())
                    
                    Text(data.currentState.displayName)
                        .font(customFont(size: 16))
                        .foregroundColor(data.fontColor.toColor().opacity(0.8))
                }
            }
            .frame(height: 180)
            
            // 控制按钮
            controlButtons(compact: false)
            
            // 设置信息
            HStack(spacing: 16) {
                settingItem(title: "工作时间", value: "\(data.workDuration)分钟")
                settingItem(title: "短休息", value: "\(data.shortBreakDuration)分钟")
                settingItem(title: "长休息", value: "\(data.longBreakDuration)分钟")
                settingItem(title: "长休息间隔", value: "\(data.longBreakInterval)个")
            }
            .padding(.top, 8)
        }
        .padding(16)
    }
    
    // MARK: - 辅助视图
    
    /// 控制按钮
    private func controlButtons(compact: Bool) -> some View {
        HStack(spacing: compact ? 8 : 12) {
            // 根据当前状态显示不同的按钮
            if data.currentState == .idle {
                // 开始按钮
                if #available(iOS 17.0, *) {
                    Button(intent: StartPomodoroAppIntent(state: PomodoroState.working.rawValue)) {
                        buttonLabel(icon: "play.fill", text: compact ? "" : "开始", color: .green)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    buttonLabel(icon: "play.fill", text: compact ? "" : "开始", color: .green)
                }
            } else if data.currentState == .paused {
                // 继续按钮
                if #available(iOS 17.0, *) {
                    Button(intent: StartPomodoroAppIntent(state: data.currentState == .paused ? PomodoroState.working.rawValue : data.currentState.rawValue)) {
                        buttonLabel(icon: "play.fill", text: compact ? "" : "继续", color: .green)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    buttonLabel(icon: "play.fill", text: compact ? "" : "继续", color: .green)
                }
            } else {
                // 暂停按钮
                if #available(iOS 17.0, *) {
                    Button(intent: PausePomodoroAppIntent()) {
                        buttonLabel(icon: "pause.fill", text: compact ? "" : "暂停", color: .orange)
                    }
                    .buttonStyle(.plain)
                } else {
                    // iOS 16 fallback
                    buttonLabel(icon: "pause.fill", text: compact ? "" : "暂停", color: .orange)
                }
            }
            
            // 重置按钮
            if #available(iOS 17.0, *) {
                Button(intent: ResetPomodoroAppIntent()) {
                    buttonLabel(icon: "arrow.counterclockwise", text: compact ? "" : "重置", color: .red)
                }
                .buttonStyle(.plain)
            } else {
                // iOS 16 fallback
                buttonLabel(icon: "arrow.counterclockwise", text: compact ? "" : "重置", color: .red)
            }
        }
    }
    
    /// 按钮标签
    private func buttonLabel(icon: String, text: String, color: Color) -> some View {
        HStack(spacing: 4) {
            Image(systemName: icon)
                .font(.system(size: 12, weight: .bold))
            
            if !text.isEmpty {
                Text(text)
                    .font(customFont(size: 12, weight: .medium))
            }
        }
        .foregroundColor(.white)
        .padding(.horizontal, text.isEmpty ? 8 : 12)
        .padding(.vertical, 6)
        .background(color)
        .cornerRadius(12)
    }
    
    /// 设置项
    private func settingItem(title: String, value: String) -> some View {
        VStack(spacing: 4) {
            Text(title)
                .font(customFont(size: 12))
                .foregroundColor(data.fontColor.toColor().opacity(0.7))
            
            Text(value)
                .font(customFont(size: 14, weight: .medium))
                .foregroundColor(data.fontColor.toColor())
        }
        .frame(maxWidth: .infinity)
    }
    
    // MARK: - 辅助方法
    
    /// 获取自定义字体
    private func customFont(size: CGFloat, weight: Font.Weight = .regular) -> Font {
        if data.fontName.isEmpty {
            return Font.system(size: size, weight: weight)
        } else {
            return Font.custom(data.fontName, size: size).weight(weight)
        }
    }
    
    /// 计算当前进度
    private func calculateProgress() -> Double {
        // 如果是空闲状态，返回0
        if data.currentState == .idle {
            return 0
        }
        
        // 如果有开始时间，计算实时进度
        if let startTime = data.startTime, data.currentState != .paused {
            let elapsedSeconds = Int(date.timeIntervalSince(startTime))
            let adjustedRemainingSeconds = max(0, data.remainingSeconds - elapsedSeconds)
            return 1.0 - Double(adjustedRemainingSeconds) / Double(data.totalSeconds)
        }
        
        // 否则使用保存的进度
        return data.progress
    }
    
    /// 格式化时间
    private func formattedTime() -> String {
        // 如果是空闲状态，显示工作时间
        if data.currentState == .idle {
            let minutes = data.workDuration
            return String(format: "%02d:00", minutes)
        }
        
        // 如果有开始时间，计算实时剩余时间
        if let startTime = data.startTime, data.currentState != .paused {
            let elapsedSeconds = Int(date.timeIntervalSince(startTime))
            let adjustedRemainingSeconds = max(0, data.remainingSeconds - elapsedSeconds)
            let minutes = adjustedRemainingSeconds / 60
            let seconds = adjustedRemainingSeconds % 60
            return String(format: "%02d:%02d", minutes, seconds)
        }
        
        // 否则使用保存的剩余时间
        let minutes = data.remainingSeconds / 60
        let seconds = data.remainingSeconds % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    /// 获取状态颜色
    private func stateColor() -> Color {
        switch data.currentState {
        case .idle:
            return .blue
        case .working:
            return .red
        case .shortBreak:
            return .green
        case .longBreak:
            return .purple
        case .paused:
            return .orange
        }
    }
}

// MARK: - 预览

@available(iOS 16.0, *)
struct PomodoroWidgetView_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            // 小尺寸预览
            PomodoroWidgetView(
                data: PomodoroWidgetData(
                    background: .color(WidgetColor.fromColor(.black)),
                    fontColor: WidgetColor.fromColor(.white),
                    currentState: .working,
                    remainingSeconds: 15 * 60,
                    startTime: Date().addingTimeInterval(-10 * 60),
                    completedPomodoros: 2
                ),
                family: .systemSmall
            )
            .previewContext(WidgetPreviewContext(family: .systemSmall))
            
            // 中尺寸预览
            PomodoroWidgetView(
                data: PomodoroWidgetData(
                    background: .color(WidgetColor.fromColor(.black)),
                    fontColor: WidgetColor.fromColor(.white),
                    currentState: .shortBreak,
                    remainingSeconds: 3 * 60,
                    startTime: Date().addingTimeInterval(-2 * 60),
                    completedPomodoros: 3
                ),
                family: .systemMedium
            )
            .previewContext(WidgetPreviewContext(family: .systemMedium))
            
            // 大尺寸预览
            PomodoroWidgetView(
                data: PomodoroWidgetData(
                    background: .color(WidgetColor.fromColor(.black)),
                    fontColor: WidgetColor.fromColor(.white),
                    currentState: .longBreak,
                    remainingSeconds: 10 * 60,
                    startTime: Date().addingTimeInterval(-5 * 60),
                    completedPomodoros: 4
                ),
                family: .systemLarge
            )
            .previewContext(WidgetPreviewContext(family: .systemLarge))
        }
    }
}
