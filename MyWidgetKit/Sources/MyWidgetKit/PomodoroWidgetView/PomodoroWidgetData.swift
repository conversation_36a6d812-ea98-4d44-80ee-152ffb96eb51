import SwiftUI
import WidgetKit

/// 番茄时钟状态
public enum PomodoroState: String, Codable, Sendable {
    case idle        // 空闲状态
    case working     // 工作阶段
    case shortBreak  // 短休息阶段
    case longBreak   // 长休息阶段
    case paused      // 暂停状态
    
    /// 获取状态显示名称
    public var displayName: String {
        switch self {
        case .idle:
            return "准备开始"
        case .working:
            return "专注工作"
        case .shortBreak:
            return "短休息"
        case .longBreak:
            return "长休息"
        case .paused:
            return "已暂停"
        }
    }
    
    /// 获取状态图标
    public var iconName: String {
        switch self {
        case .idle:
            return "play.circle"
        case .working:
            return "timer"
        case .shortBreak, .longBreak:
            return "cup.and.saucer"
        case .paused:
            return "pause.circle"
        }
    }
    
    /// 获取状态颜色
    public var color: Color {
        switch self {
        case .idle:
            return .blue
        case .working:
            return .red
        case .shortBreak:
            return .green
        case .longBreak:
            return .purple
        case .paused:
            return .orange
        }
    }
}

/// 番茄时钟小组件数据模型
public struct PomodoroWidgetData: WidgetPreviewableData, Codable, Hashable {
    /// 背景设置
    public var background: WidgetBackground
    
    /// 字体颜色
    public var fontColor: WidgetColor
    
    /// 字体名称
    public var fontName: String
    
    /// 工作时间（分钟）
    public var workDuration: Int
    
    /// 短休息时间（分钟）
    public var shortBreakDuration: Int
    
    /// 长休息时间（分钟）
    public var longBreakDuration: Int
    
    /// 长休息间隔（完成几个番茄周期后进入长休息）
    public var longBreakInterval: Int
    
    /// 当前状态
    public var currentState: PomodoroState
    
    /// 当前剩余时间（秒）
    public var remainingSeconds: Int
    
    /// 开始时间
    public var startTime: Date?
    
    /// 完成的番茄数量
    public var completedPomodoros: Int
    
    /// 是否自动开始下一个阶段
    public var autoStartNextPhase: Bool
    
    /// 最后更新时间
    public var lastUpdated: Date
    
    /// 初始化方法
    public init(
        background: WidgetBackground = .color(WidgetColor(red: 0, green: 0, blue: 0, alpha: 1)),
        fontColor: WidgetColor = WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
        fontName: String = "",
        workDuration: Int = 25,
        shortBreakDuration: Int = 5,
        longBreakDuration: Int = 15,
        longBreakInterval: Int = 4,
        currentState: PomodoroState = .idle,
        remainingSeconds: Int = 25 * 60,
        startTime: Date? = nil,
        completedPomodoros: Int = 0,
        autoStartNextPhase: Bool = false,
        lastUpdated: Date = Date()
    ) {
        self.background = background
        self.fontColor = fontColor
        self.fontName = fontName
        self.workDuration = workDuration
        self.shortBreakDuration = shortBreakDuration
        self.longBreakDuration = longBreakDuration
        self.longBreakInterval = longBreakInterval
        self.currentState = currentState
        self.remainingSeconds = remainingSeconds
        self.startTime = startTime
        self.completedPomodoros = completedPomodoros
        self.autoStartNextPhase = autoStartNextPhase
        self.lastUpdated = lastUpdated
    }
    
    /// 获取当前阶段总时间（秒）
    public var totalSeconds: Int {
        switch currentState {
        case .working:
            return workDuration * 60
        case .shortBreak:
            return shortBreakDuration * 60
        case .longBreak:
            return longBreakDuration * 60
        case .idle:
            return workDuration * 60
        case .paused:
            return workDuration * 60
        }
    }
    
    /// 获取当前进度（0-1）
    public var progress: Double {
        if totalSeconds == 0 {
            return 0
        }
        return Double(totalSeconds - remainingSeconds) / Double(totalSeconds)
    }
    
    /// 格式化剩余时间
    public func formattedTime() -> String {
        let minutes = remainingSeconds / 60
        let seconds = remainingSeconds % 60
        return String(format: "%02d:%02d", minutes, seconds)
    }
    
    // MARK: - Hashable
    
    /// 实现Hashable协议的hash方法
    public func hash(into hasher: inout Hasher) {
        hasher.combine(workDuration)
        hasher.combine(shortBreakDuration)
        hasher.combine(longBreakDuration)
        hasher.combine(longBreakInterval)
        hasher.combine(currentState)
        hasher.combine(remainingSeconds)
        hasher.combine(startTime)
        hasher.combine(completedPomodoros)
        hasher.combine(autoStartNextPhase)
        hasher.combine(lastUpdated)
    }
}

/// 番茄时钟小组件入口
public struct PomodoroWidgetEntry: TimelineEntry {
    /// 日期
    public let date: Date
    
    /// 配置数据
    public let configuration: PomodoroWidgetData
    
    /// 初始化方法
    public init(date: Date, configuration: PomodoroWidgetData) {
        self.date = date
        self.configuration = configuration
    }
}
