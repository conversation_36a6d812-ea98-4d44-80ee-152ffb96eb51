import AppIntents
import WidgetKit
import SwiftUI
import UserNotifications

// MARK: - 启动番茄时钟的AppIntent
@available(iOS 16.0, *)
struct StartPomodoroAppIntent: AppIntent {
    static var title: LocalizedStringResource { "启动番茄时钟" }
    static var description: IntentDescription { IntentDescription("开始番茄工作法计时") }

    @Parameter(title: "状态")
    var state: String
    
    init() {
        self.state = PomodoroState.working.rawValue
    }
    
    init(state: String) {
        self.state = state
    }
    
    func perform() async throws -> some IntentResult {
        // 1. 读取当前配置
        if var config = AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) {
            // 2. 根据状态设置计时器
            let pomodoroState = PomodoroState(rawValue: state) ?? .working
            
            // 设置状态和剩余时间
            config.currentState = pomodoroState
            
            switch pomodoroState {
            case .working:
                config.remainingSeconds = config.workDuration * 60
            case .shortBreak:
                config.remainingSeconds = config.shortBreakDuration * 60
            case .longBreak:
                config.remainingSeconds = config.longBreakDuration * 60
            case .idle, .paused:
                // 不改变剩余时间
                break
            }
            
            // 设置开始时间
            config.startTime = Date()
            config.lastUpdated = Date()
            
            // 3. 保存配置
            AppGroupDataManager.shared.save(config, for: .pomodoro, property: .config)
            
            // 4. 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
            
            // 5. 发送通知
            await scheduleNotification(for: config)
            
            return .result()
        } else {
            // 如果没有配置，创建默认配置
            let defaultConfig = PomodoroWidgetData()
            AppGroupDataManager.shared.save(defaultConfig, for: .pomodoro, property: .config)
            
            // 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
            
            return .result()
        }
    }
    
    // 发送通知
    private func scheduleNotification(for config: PomodoroWidgetData) async {
        // 请求通知权限
        let center = UNUserNotificationCenter.current()
        let settings = await center.notificationSettings()
        
        guard settings.authorizationStatus == .authorized else {
            return
        }
        
        // 移除之前的通知
        center.removeAllPendingNotificationRequests()
        
        // 创建通知内容
        let content = UNMutableNotificationContent()
        
        switch config.currentState {
        case .working:
            content.title = "工作时间结束"
            content.body = "恭喜完成一个番茄工作周期！现在可以休息一下了。"
        case .shortBreak:
            content.title = "短休息结束"
            content.body = "休息时间结束，准备开始新的工作周期吧！"
        case .longBreak:
            content.title = "长休息结束"
            content.body = "长休息时间结束，准备开始新的工作周期吧！"
        default:
            return
        }
        
        content.sound = UNNotificationSound.default
        
        // 创建触发器
        let trigger = UNTimeIntervalNotificationTrigger(
            timeInterval: TimeInterval(config.remainingSeconds),
            repeats: false
        )
        
        // 创建请求
        let request = UNNotificationRequest(
            identifier: "com.abc.JZJJWidgetAPP.pomodoro",
            content: content,
            trigger: trigger
        )
        
        // 添加通知请求
        try? await center.add(request)
    }
}

// MARK: - 暂停番茄时钟的AppIntent
@available(iOS 16.0, *)
struct PausePomodoroAppIntent: AppIntent {
    static var title: LocalizedStringResource { "暂停番茄时钟" }
    static var description: IntentDescription { IntentDescription("暂停当前番茄工作法计时") }
    
    func perform() async throws -> some IntentResult {
        // 1. 读取当前配置
        if var config = AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) {
            // 2. 计算已经过去的时间
            if let startTime = config.startTime, config.currentState != .paused && config.currentState != .idle {
                let elapsedTime = Int(Date().timeIntervalSince(startTime))
                config.remainingSeconds = max(0, config.remainingSeconds - elapsedTime)
            }
            
            // 3. 设置为暂停状态
            config.currentState = .paused
            config.startTime = nil
            config.lastUpdated = Date()
            
            // 4. 保存配置
            AppGroupDataManager.shared.save(config, for: .pomodoro, property: .config)
            
            // 5. 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
            
            // 6. 取消通知
            UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
            
            return .result()
        } else {
            return .result()
        }
    }
}

// MARK: - 重置番茄时钟的AppIntent
@available(iOS 16.0, *)
struct ResetPomodoroAppIntent: AppIntent {
    static var title: LocalizedStringResource { "重置番茄时钟" }
    static var description: IntentDescription { IntentDescription("重置番茄工作法计时器") }
    
    func perform() async throws -> some IntentResult {
        // 1. 读取当前配置
        if var config = AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) {
            // 2. 重置状态
            config.currentState = .idle
            config.remainingSeconds = config.workDuration * 60
            config.startTime = nil
            config.lastUpdated = Date()
            
            // 3. 保存配置
            AppGroupDataManager.shared.save(config, for: .pomodoro, property: .config)
            
            // 4. 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
            
            // 5. 取消通知
            UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
            
            return .result()
        } else {
            return .result()
        }
    }
}
