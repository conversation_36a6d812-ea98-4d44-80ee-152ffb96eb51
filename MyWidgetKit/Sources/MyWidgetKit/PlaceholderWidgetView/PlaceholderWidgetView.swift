//
//  PlaceholderWidgetView.swift
//  MyWidgetKit
//
//  Created by yjzheng on 2025/5/13.
//

import SwiftUI

/// 占位小组件视图，提示用户长按编辑小组件进行选择
public struct PlaceholderWidgetView: View {
    public init() {}

    public var body: some View {
        VStack(spacing: 14) {
            // 系统设置/编辑相关的 SF Symbol 图标
            Image(systemName: "slider.horizontal.3")
                .resizable()
                .scaledToFit()
                .frame(width: 38, height: 38)
                .foregroundColor(.accentColor)
                .opacity(0.85)

            Text("请长按小组件进行编辑")
                .font(.headline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
                .padding(.horizontal, 8)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .adaptiveBackground(content: {
            Color(.systemGray6)

        })
    }
}

#Preview {
    PlaceholderWidgetView()
}
