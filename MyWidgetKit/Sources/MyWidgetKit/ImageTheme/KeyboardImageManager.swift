//
//  KeyboardImageManager.swift
//  MyWidgetKit
//
//  Created by y<PERSON><PERSON><PERSON> on 2025/5/23.
//

import UIKit
import SwiftUI
import Foundation

/// 线程安全的图片缓存
private class ImageCache: @unchecked Sendable {
    private var cache: [String: UIImage] = [:]
    private let queue = DispatchQueue(label: "image.cache.queue", attributes: .concurrent)

    func get(_ key: String) -> UIImage? {
        return queue.sync {
            return cache[key]
        }
    }

    func set(_ key: String, _ image: UIImage) {
        queue.async(flags: .barrier) {
            self.cache[key] = image
        }
    }

    func remove(_ key: String) {
        queue.async(flags: .barrier) {
            self.cache.removeValue(forKey: key)
        }
    }

    func removeAll() {
        queue.async(flags: .barrier) {
            self.cache.removeAll()
        }
    }

    var count: Int {
        return queue.sync {
            return cache.count
        }
    }
}

/// 键盘图片主题管理器
@MainActor
public class KeyboardImageManager: ObservableObject {

    // MARK: - 单例
    public static let shared = KeyboardImageManager()

    // MARK: - 属性
    private let appGroupDataManager = AppGroupDataManager.shared
    private let fileManager = FileManager.default

    // 目录结构
    private var baseDirectory: URL {
        return appGroupDataManager.appGroup.containerURL.appendingPathComponent("keyboard_themes")
    }

    private var builtInDirectory: URL {
        return baseDirectory.appendingPathComponent("built-in")
    }

    private var customDirectory: URL {
        return baseDirectory.appendingPathComponent("custom")
    }

    // 线程安全的图片缓存
    private let imageCache = ImageCache()
    private let cacheQueue = DispatchQueue(label: "keyboard.image.cache", qos: .userInitiated)

    // MARK: - 初始化
    private init() {
        setupDirectories()
        setupBuiltInThemes()
    }

    // MARK: - 目录设置
    private func setupDirectories() {
        do {
            try fileManager.createDirectory(at: baseDirectory, withIntermediateDirectories: true)
            try fileManager.createDirectory(at: builtInDirectory, withIntermediateDirectories: true)
            try fileManager.createDirectory(at: customDirectory, withIntermediateDirectories: true)
        } catch {
            print("❌ 创建键盘主题目录失败: \(error)")
        }
    }

    // MARK: - 内置主题设置
    private func setupBuiltInThemes() {
        // 检查是否已经安装内置主题
        let versionKey = "keyboard_builtin_themes_version"
        let currentVersion = "1.0"
        let installedVersion = UserDefaults.standard.string(forKey: versionKey)

        if installedVersion != currentVersion {
            installBuiltInThemes()
            UserDefaults.standard.set(currentVersion, forKey: versionKey)
        }
    }

    private func installBuiltInThemes() {
        // 创建内置主题的占位图片
        createBuiltInThemeImages()
    }

    private func createBuiltInThemeImages() {
        let themes = [
            ("tech", "科技风", UIColor.systemBlue),
            ("nature", "自然风", UIColor.systemGreen),
            ("minimal", "简约风", UIColor.systemGray),
            ("gradient", "渐变风", UIColor.systemPurple),
            ("dark", "深色风", UIColor.black)
        ]

        for (id, name, color) in themes {
            // 创建背景图片
            let backgroundImage = createGradientImage(
                size: CGSize(width: 1024, height: 512),
                colors: [color, color.withAlphaComponent(0.7)],
                direction: .diagonal
            )
            saveImage(backgroundImage, for: id, type: .background, isBuiltIn: true)

            // 创建按键图片
            let keyImage = createGradientImage(
                size: CGSize(width: 64, height: 64),
                colors: [color.withAlphaComponent(0.8), color.withAlphaComponent(0.6)],
                direction: .vertical
            )
            saveImage(keyImage, for: id, type: .key, isBuiltIn: true)
        }
    }

    // MARK: - 图片创建工具
    private func createGradientImage(size: CGSize, colors: [UIColor], direction: GradientDirection) -> UIImage {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            let cgContext = context.cgContext
            let colorSpace = CGColorSpaceCreateDeviceRGB()
            let cgColors = colors.map { $0.cgColor }

            guard let gradient = CGGradient(colorsSpace: colorSpace, colors: cgColors as CFArray, locations: nil) else {
                return
            }

            let startPoint: CGPoint
            let endPoint: CGPoint

            switch direction {
            case .horizontal:
                startPoint = CGPoint(x: 0, y: size.height / 2)
                endPoint = CGPoint(x: size.width, y: size.height / 2)
            case .vertical:
                startPoint = CGPoint(x: size.width / 2, y: 0)
                endPoint = CGPoint(x: size.width / 2, y: size.height)
            case .diagonal:
                startPoint = CGPoint(x: 0, y: 0)
                endPoint = CGPoint(x: size.width, y: size.height)
            }

            cgContext.drawLinearGradient(gradient, start: startPoint, end: endPoint, options: [])
        }
    }

    // MARK: - 图片保存和加载
    public func saveImage(_ image: UIImage, for themeId: String, type: ImageType, isBuiltIn: Bool = false) {
        let directory = isBuiltIn ? builtInDirectory : customDirectory
        let filename = "\(themeId)_\(type.rawValue).png"
        let url = directory.appendingPathComponent(filename)

        guard let data = image.pngData() else {
            print("❌ 图片转换为PNG数据失败")
            return
        }

        do {
            try data.write(to: url)
            print("✅ 图片保存成功: \(filename)")

            // 更新缓存
            let cacheKey = "\(themeId)_\(type.rawValue)_\(isBuiltIn)"
            imageCache.set(cacheKey, image)
        } catch {
            print("❌ 图片保存失败: \(error)")
        }
    }

    public func loadImage(for themeId: String, type: ImageType, isBuiltIn: Bool = false) async -> UIImage? {
        let cacheKey = "\(themeId)_\(type.rawValue)_\(isBuiltIn)"

        // 先检查缓存
        if let cachedImage = imageCache.get(cacheKey) {
            return cachedImage
        }

        // 从文件加载
        let directory = isBuiltIn ? builtInDirectory : customDirectory
        let filename = "\(themeId)_\(type.rawValue).png"
        let url = directory.appendingPathComponent(filename)

        guard let data = try? Data(contentsOf: url),
              let image = UIImage(data: data) else {
            return nil
        }

        // 更新缓存
        imageCache.set(cacheKey, image)

        return image
    }

    // MARK: - 图片处理
    public func processAndSaveCustomImage(_ image: UIImage, for themeId: String, type: ImageType) async -> Bool {
        let targetSize: CGSize

        switch type {
        case .background:
            targetSize = CGSize(width: 1024, height: 512)
        case .key:
            targetSize = CGSize(width: 64, height: 64)
        }

        guard let processedImage = resizeImage(image, to: targetSize) else {
            return false
        }

        saveImage(processedImage, for: themeId, type: type, isBuiltIn: false)
        return true
    }

    private func resizeImage(_ image: UIImage, to size: CGSize) -> UIImage? {
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            image.draw(in: CGRect(origin: .zero, size: size))
        }
    }

    // MARK: - 主题管理
    public func deleteCustomTheme(_ themeId: String) {
        let backgroundFile = customDirectory.appendingPathComponent("\(themeId)_background.png")
        let keyFile = customDirectory.appendingPathComponent("\(themeId)_key.png")

        try? fileManager.removeItem(at: backgroundFile)
        try? fileManager.removeItem(at: keyFile)

        // 清除缓存
        imageCache.remove("\(themeId)_background_false")
        imageCache.remove("\(themeId)_key_false")
    }

    public func getBuiltInThemeIds() -> [String] {
        return ["tech", "nature", "minimal", "gradient", "dark"]
    }

    public func getCustomThemeIds() -> [String] {
        guard let files = try? fileManager.contentsOfDirectory(at: customDirectory, includingPropertiesForKeys: nil) else {
            return []
        }

        let themeIds = Set(files.compactMap { url -> String? in
            let filename = url.lastPathComponent
            if filename.hasSuffix("_background.png") {
                return String(filename.dropLast("_background.png".count))
            }
            return nil
        })

        return Array(themeIds)
    }

    // MARK: - 内存管理
    public func clearImageCache() {
        imageCache.removeAll()
    }

    public func handleMemoryWarning() {
        clearImageCache()
    }

    public func getCacheSize() -> Int {
        return imageCache.count
    }
}

// MARK: - 辅助枚举
public enum ImageType: String, CaseIterable {
    case background = "background"
    case key = "key"

    public var displayName: String {
        switch self {
        case .background: return "背景图片"
        case .key: return "按键图片"
        }
    }
}

public enum GradientDirection {
    case horizontal
    case vertical
    case diagonal
}
