//
//  File.swift
//  MyWidgetKit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/12.
//

import Foundation
import SwiftUI<PERSON>ore
import UIKit

import CoreImage.CIFilterBuiltins

/// 二维码小组件数据模型
public struct QRWidgetViewData: Codable, WidgetPreviewableData {
    public var content: String // 二维码内容
    public var foreground: WidgetBackground // 二维码前景色
    public var background: WidgetBackground

    public var foregroundColor: WidgetColor

    public init(content: String, foreground: WidgetBackground, background: WidgetBackground, foregroundColor: WidgetColor) {
        self.content = content
        self.foreground = foreground
        self.background = background
        self.foregroundColor = foregroundColor     }
}

/// 二维码小组件视图
public struct QRWidgetView: View {
    public let data: QRWidgetViewData

    public init(data: QRWidgetViewData) {
        self.data = data
    }

    public var body: some View {
        GeometryReader { geometry in
            ZStack {
                data.foreground.backgroundView(width: geometry.size.width - 20, height: geometry.size.height - 20)
                    .padding(10)
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
            .adaptiveBackground {
                // 背景
                switch data.background {
                case let .color(widgetColor):
                    widgetColor.toColor()
                case let .imageData(data):
                    Image(uiImage: UIImage(data: data) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageFile(path):
                    Image(uiImage: UIImage(contentsOfFile: AppGroupDataManager.shared.getFileURL(fileName: path).path()) ?? UIImage())
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                case let .imageURL(url):
                    Color.gray
//                    if #available(iOS 15.0, *) {
//                        AsyncImage(url: URL(string: url)!) { image in
//                            image.resizable()
//                                .aspectRatio(contentMode: .fill)
//                                .frame(width: geometry.size.width, height: geometry.size.height)
//                                .clipped()
//                        } placeholder: {
//                            Color.gray
//                        }
//                    } else {
//                        Color.gray
//                    }
                case let .packageImage(name):
                    // 使用包内图片资源
                    Image(name, bundle: .module)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(width: geometry.size.width, height: geometry.size.height)
                        .clipped()
                }
            }
        }
    }
}
//
///// 生成二维码图片的 SwiftUI 视图
// struct QRCodeImage: View {
//    let content: String
//    let foregroundColor: UIColor
//    let backgroundColor: UIColor
//
//    var body: some View {
//        if let image = generateQRCode(from: content, color: foregroundColor, backgroundColor: backgroundColor) {
//            Image(uiImage: image)
//                .renderingMode(.original)
//                .resizable()
//                .interpolation(.none) // 这行在 iOS 14+ 可用
//                .scaledToFit()
//        } else {
//            Color.clear
//        }
//    }
//
//
// }
