// swift-tools-version: 6.1
// The swift-tools-version declares the minimum version of Swift required to build this package.

import PackageDescription

let package = Package(
    name: "MyWidgetKit",
    platforms: [
        .iOS("16.0"),
        .macOS("14.0"),
        .watchOS("10.0"),
        .tvOS("17.0"),
        .visionOS("1.0")
    ],
    products: [
        .library(
            name: "MyWidgetKit",
            targets: ["MyWidgetKit"]
        )
    ],
    targets: [
        ///  Targets
        .target(name: "MyWidgetKit", resources: [
            .process("Resources/Assets.xcassets")
        ])
    ]
)
