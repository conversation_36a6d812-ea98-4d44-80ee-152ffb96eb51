//
//  StringExtensions.swift
//  JZJJWidget
//
//  通用字符串扩展
//

import Foundation

extension String {
    /// 字符串重复操作符
    static func *(lhs: String, rhs: Int) -> String {
        return String(repeating: lhs, count: rhs)
    }
    
    /// 安全的子字符串截取
    func safeSubstring(from index: Int, length: Int) -> String {
        let startIndex = self.index(self.startIndex, offsetBy: max(0, index))
        let endIndex = self.index(startIndex, offsetBy: min(length, self.count - index))
        return String(self[startIndex..<endIndex])
    }
    
    /// 移除首尾空白字符
    var trimmed: String {
        return self.trimmingCharacters(in: .whitespacesAndNewlines)
    }
    
    /// 检查字符串是否为空或只包含空白字符
    var isBlank: Bool {
        return self.trimmed.isEmpty
    }
}
