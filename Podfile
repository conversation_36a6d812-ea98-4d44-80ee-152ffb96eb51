# Uncomment the next line to define a global platform for your project
# platform :ios, '16.0'
source 'https://github.com/CocoaPods/Specs.git'

post_install do |installer|
  installer.pods_project.targets.each do |target|
    target.build_configurations.each do |config|
      config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '16.0'
    end
  end
end


target 'JZJJWidget' do
  # Comment the next line if you don't want to use dynamic frameworks
  use_frameworks!

  # Pods for JZJJWidget
#  pod 'GDTMobSDK'
#  
#  pod 'Ads-CN', '~> *******-CN'
  
end
