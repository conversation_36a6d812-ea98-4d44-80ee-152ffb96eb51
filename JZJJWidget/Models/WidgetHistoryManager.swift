import Foundation
import Combine

class WidgetHistoryManager: ObservableObject {
    static let shared = WidgetHistoryManager()
    private let historyKey = "widgetViewHistory"
    private let maxHistoryCount = 20 // 最多保存20条历史记录

    @Published var history: [WidgetHistoryItem] = []

    private init() {
        loadHistory()
    }

    func addWidgetToHistory(name: String, iconName: String) {
        // 避免重复添加最近查看的同一个组件
        if let firstItem = history.first, firstItem.widgetName == name {
            return
        }

        let newItem = WidgetHistoryItem(widgetName: name, widgetIconName: iconName)
        
        // 插入到最前面
        history.insert(newItem, at: 0)

        // 保持历史记录数量上限
        if history.count > maxHistoryCount {
            history = Array(history.prefix(maxHistoryCount))
        }
        saveHistory()
    }

    func loadHistory() {
        guard let data = UserDefaults.standard.data(forKey: historyKey) else { return }
        do {
            let decodedHistory = try JSONDecoder().decode([WidgetHistoryItem].self, from: data)
            // 按日期降序排序，确保最新的在前面
            self.history = decodedHistory.sorted(by: { $0.viewedDate > $1.viewedDate })
        } catch {
            print("Failed to load widget history: \(error)")
            self.history = [] // 如果解码失败，则清空历史记录
        }
    }

    private func saveHistory() {
        do {
            let data = try JSONEncoder().encode(history)
            UserDefaults.standard.set(data, forKey: historyKey)
        } catch {
            print("Failed to save widget history: \(error)")
        }
    }
    
    func clearHistory() {
        history.removeAll()
        saveHistory()
    }
}