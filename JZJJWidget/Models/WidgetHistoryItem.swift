import Foundation

struct WidgetHistoryItem: Identifiable, Codable {
    let id: UUID
    let widgetName: String
    let viewedDate: Date
    let widgetIconName: String // 用于在历史记录中显示图标

    init(id: UUID = UUID(), widgetName: String, viewedDate: Date = Date(), widgetIconName: String) {
        self.id = id
        self.widgetName = widgetName
        self.viewedDate = viewedDate
        self.widgetIconName = widgetIconName
    }
}