//
//  EnhancedViewController.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/15.
//

import MyWidgetKit
import SwiftUI
import UIKit
import WidgetKit

// MARK: - 通知名称常量

// extension Notification.Name {
//    static let saveAppLauncherConfig = Notification.Name("saveAppLauncherConfig")
// }

class EnhancedViewController: UIViewController, UIPopoverPresentationControllerDelegate {
    // MARK: - 属性

    private var selectedWidgetFamily: WidgetFamily = .systemSmall
    private var currentTabIndex: Int = 0
    private var widgetsVC: UIHostingController<AnyView>!
    private var mainTabBarController: UITabBarController!

    override func viewDidLoad() {
        super.viewDidLoad()
        setupNavigationController()

        // 初始设置尺寸选择器（因为默认选中第一个标签）
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
            self?.setupWidgetSizeSelector()
        }

        // 访问百度网站
        performBaiduRequest()
    }

    private func setupNavigationController() {
        // 创建 TabBarController
        mainTabBarController = UITabBarController()
        mainTabBarController.delegate = self // 设置代理

        // 创建 SwiftUI 视图的 Hosting Controllers
        let widgetsView = AnyView(
            EnhancedWidgetsView(
                selectedFamily: selectedWidgetFamily,
                onThemeButtonTapped: { [weak self] in
                    self?.showThemeSelector()
                }
            )
            .environmentObject(DropdownMenuManager.shared) // 添加下拉菜单管理器作为环境对象
        )

        widgetsVC = UIHostingController(rootView: widgetsView)
        widgetsVC.title = "组件"

        // 创建快捷启动组件视图
        let appLauncherView = AnyView(
            AppLauncherConfigView()
                .environmentObject(ThemeManager.shared)
        )
        let appLauncherVC = UIHostingController(rootView: appLauncherView)
        appLauncherVC.title = "快捷启动"

        // 创建键盘换肤视图
        let keyboardThemeView = AnyView(
            KeyboardThemeTabView()
                .environmentObject(ThemeManager.shared)
        )
        let keyboardThemeVC = UIHostingController(rootView: keyboardThemeView)
        keyboardThemeVC.title = "键盘主题"

        let profileVC = UIHostingController(rootView: EnhancedProfileView())
        profileVC.title = "我的"

        // 设置 TabBar 图标和标题
        widgetsVC.tabBarItem = UITabBarItem(title: "组件", image: UIImage(systemName: "square.grid.2x2"), selectedImage: UIImage(systemName: "square.grid.2x2.fill"))
        appLauncherVC.tabBarItem = UITabBarItem(title: "快捷启动", image: UIImage(systemName: "square.grid.3x2"), selectedImage: UIImage(systemName: "square.grid.3x2.fill"))
        keyboardThemeVC.tabBarItem = UITabBarItem(title: "键盘主题", image: UIImage(systemName: "keyboard"), selectedImage: UIImage(systemName: "keyboard.fill"))
        profileVC.tabBarItem = UITabBarItem(title: "我的", image: UIImage(systemName: "person"), selectedImage: UIImage(systemName: "person.fill"))

        // 设置 TabBarController 的视图控制器数组
        mainTabBarController.viewControllers = [widgetsVC, appLauncherVC, keyboardThemeVC, profileVC]

        // 获取当前主题
        let themeManager = ThemeManager.shared
        let currentTheme = themeManager.currentTheme

        // 设置 TabBar 的外观
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()

        // 根据当前主题设置 TabBar 外观
        if currentTheme == .iosDark {
            // 深色模式
            appearance.backgroundColor = UIColor(currentTheme.colors.background)
            appearance.stackedLayoutAppearance.selected.iconColor = UIColor(currentTheme.colors.accent)
            appearance.stackedLayoutAppearance.selected.titleTextAttributes = [.foregroundColor: UIColor(currentTheme.colors.accent)]
            appearance.stackedLayoutAppearance.normal.iconColor = UIColor(currentTheme.colors.subtext)
            appearance.stackedLayoutAppearance.normal.titleTextAttributes = [.foregroundColor: UIColor(currentTheme.colors.subtext)]
        } else {
            // 浅色模式
            appearance.backgroundColor = UIColor(currentTheme.colors.background)
            appearance.stackedLayoutAppearance.selected.iconColor = UIColor(currentTheme.colors.accent)
            appearance.stackedLayoutAppearance.selected.titleTextAttributes = [.foregroundColor: UIColor(currentTheme.colors.accent)]
            appearance.stackedLayoutAppearance.normal.iconColor = UIColor(currentTheme.colors.subtext)
            appearance.stackedLayoutAppearance.normal.titleTextAttributes = [.foregroundColor: UIColor(currentTheme.colors.subtext)]
        }

        if #available(iOS 15.0, *) {
            UITabBar.appearance().scrollEdgeAppearance = appearance
        }
        UITabBar.appearance().standardAppearance = appearance

        // 创建根导航控制器
        let navigationController = UINavigationController(rootViewController: mainTabBarController)
        navigationController.navigationBar.prefersLargeTitles = false

        // 设置导航栏外观
        let navBarAppearance = UINavigationBarAppearance()
        navBarAppearance.configureWithOpaqueBackground()
        navBarAppearance.backgroundColor = UIColor(currentTheme.colors.background)
        navBarAppearance.titleTextAttributes = [.foregroundColor: UIColor(currentTheme.colors.text)]
        navBarAppearance.largeTitleTextAttributes = [.foregroundColor: UIColor(currentTheme.colors.text)]

        navigationController.navigationBar.standardAppearance = navBarAppearance
        navigationController.navigationBar.scrollEdgeAppearance = navBarAppearance
        navigationController.navigationBar.compactAppearance = navBarAppearance

        // 将导航控制器添加为子视图控制器
        addChild(navigationController)
        view.addSubview(navigationController.view)
        navigationController.view.frame = view.bounds
        navigationController.didMove(toParent: self)

        // 设置初始标题
        mainTabBarController.navigationItem.title = widgetsVC.title

        // 监听主题变化
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(themeDidChange),
            name: NSNotification.Name("ThemeDidChangeNotification"),
            object: nil
        )
    }

    @objc private func themeDidChange() {
        // 保存当前选中的标签索引和小部件尺寸
        let savedTabIndex = currentTabIndex
        let savedWidgetFamily = selectedWidgetFamily

        // 先清除所有导航栏按钮和标题
        clearNavigationBarItems(animated: false)

        // 主题变化时重新设置界面
        setupNavigationController()

        // 恢复选中的标签和小部件尺寸
        if let tabBarController = mainTabBarController {
            if savedTabIndex < tabBarController.viewControllers?.count ?? 0 {
                tabBarController.selectedIndex = savedTabIndex
                currentTabIndex = savedTabIndex
            }
        }
        selectedWidgetFamily = savedWidgetFamily

        // 根据当前标签页设置导航栏
        if currentTabIndex == 0 {
            // 如果是组件标签，设置尺寸选择器
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                guard let self = self else { return }

                // 设置尺寸选择器
                self.setupWidgetSizeSelector()

                // 更新组件视图以应用新的主题和尺寸
                self.updateWidgetsView()
            }
        } else if currentTabIndex == 1 {
            // 如果是快捷启动标签，设置标题和保存按钮
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                guard let self = self, let viewController = self.mainTabBarController.selectedViewController else { return }

                // 设置标题
                self.mainTabBarController.navigationItem.title = viewController.title

                // 添加保存按钮
                self.setupAppLauncherSaveButton(for: self.mainTabBarController)
            }
        } else {
            // 如果是键盘主题或我的标签，只设置标题
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                guard let self = self else { return }

                // 设置标题
                if let title = self.mainTabBarController.selectedViewController?.title {
                    self.mainTabBarController.navigationItem.title = title
                }
            }
        }
    }

    override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        // 系统外观变化时重新设置界面
        if traitCollection.userInterfaceStyle != previousTraitCollection?.userInterfaceStyle {
            // 保存当前选中的标签索引和小部件尺寸
            let savedTabIndex = currentTabIndex
            let savedWidgetFamily = selectedWidgetFamily

            // 先清除所有导航栏按钮和标题
            clearNavigationBarItems(animated: false)

            // 重新设置界面
            setupNavigationController()

            // 恢复选中的标签和小部件尺寸
            if let tabBarController = mainTabBarController {
                if savedTabIndex < tabBarController.viewControllers?.count ?? 0 {
                    tabBarController.selectedIndex = savedTabIndex
                    currentTabIndex = savedTabIndex
                }
            }
            selectedWidgetFamily = savedWidgetFamily

            // 根据当前标签页设置导航栏
            if currentTabIndex == 0 {
                // 如果是组件标签，设置尺寸选择器
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                    guard let self = self else { return }

                    // 设置尺寸选择器
                    self.setupWidgetSizeSelector()

                    // 更新组件视图以应用新的主题和尺寸
                    self.updateWidgetsView()
                }
            } else if currentTabIndex == 1 {
                // 如果是快捷启动标签，设置标题和保存按钮
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                    guard let self = self, let viewController = self.mainTabBarController.selectedViewController else { return }

                    // 设置标题
                    self.mainTabBarController.navigationItem.title = viewController.title

                    // 添加保存按钮
                    self.setupAppLauncherSaveButton(for: self.mainTabBarController)
                }
            } else {
                // 如果是键盘主题或我的标签，只设置标题
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                    guard let self = self else { return }

                    // 设置标题
                    if let title = self.mainTabBarController.selectedViewController?.title {
                        self.mainTabBarController.navigationItem.title = title
                    }
                }
            }
        }
    }

    // MARK: - 组件尺寸选择器相关方法

    private func setupWidgetSizeSelector() {
        // 获取当前主题
        let themeManager = ThemeManager.shared
        let currentTheme = themeManager.currentTheme

        // 获取主题颜色
        let accentColor = UIColor(currentTheme.colors.accent)
        let surfaceColor = UIColor(currentTheme.colors.surface)
        let subtextColor = UIColor(currentTheme.colors.subtext)

        // 创建一个简单的标题标签作为 titleView
        let titleLabel = UILabel()
        titleLabel.text = sizeName(for: selectedWidgetFamily)
        titleLabel.textColor = UIColor.label
        titleLabel.font = UIFont.systemFont(ofSize: 17, weight: .semibold)
        titleLabel.textAlignment = .center
        titleLabel.sizeToFit()

        // 添加点击手势
        titleLabel.isUserInteractionEnabled = true
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(titleLabelTapped))
        titleLabel.addGestureRecognizer(tapGesture)

        // 添加下拉指示器
        let containerView = UIView(frame: CGRect(x: 0, y: 0, width: titleLabel.frame.width + 20, height: titleLabel.frame.height))
        containerView.addSubview(titleLabel)
        titleLabel.center = CGPoint(x: containerView.frame.width / 2, y: containerView.frame.height / 2)

        // 设置 tag 用于后续查找
        containerView.tag = 1001

        // 创建搜索按钮
        let searchButton = UIButton(type: .system)
        searchButton.setImage(UIImage(systemName: "magnifyingglass"), for: .normal)
        searchButton.tintColor = accentColor
        searchButton.addTarget(self, action: #selector(searchButtonTapped), for: .touchUpInside)

        // 设置搜索按钮的尺寸
        searchButton.frame = CGRect(x: 0, y: 0, width: 40, height: 40)

        // 创建主题选择按钮
        let themeButton = UIButton(type: .system)
        themeButton.setImage(UIImage(systemName: "paintpalette"), for: .normal)
        themeButton.tintColor = accentColor
        themeButton.addTarget(self, action: #selector(themeButtonTapped), for: .touchUpInside)

        // 设置主题按钮的尺寸
        themeButton.frame = CGRect(x: 0, y: 0, width: 40, height: 40)

        // 创建UIBarButtonItem来包装按钮
        let searchBarButtonItem = UIBarButtonItem(customView: searchButton)
        let themeBarButtonItem = UIBarButtonItem(customView: themeButton)

        // 设置导航栏的titleView和barButtonItems
        let navigationItem = mainTabBarController.navigationItem

        // 先清除所有现有的导航栏按钮和标题视图
        navigationItem.leftBarButtonItem = nil
        navigationItem.rightBarButtonItem = nil
        navigationItem.titleView = nil
        navigationItem.title = nil

        // 设置titleView为标题容器
        navigationItem.titleView = containerView

        // 设置leftBarButtonItem为搜索按钮
        navigationItem.leftBarButtonItem = searchBarButtonItem

        // 设置rightBarButtonItem为主题按钮
        navigationItem.rightBarButtonItem = themeBarButtonItem

        // 使用淡入动画显示控件
        containerView.alpha = 0
        searchButton.alpha = 0
        themeButton.alpha = 0

        UIView.animate(withDuration: 0.3) {
            containerView.alpha = 1
            searchButton.alpha = 1
            themeButton.alpha = 1
        }
    }

    private func removeWidgetSizeSelector() {
        // 使用clearNavigationBarItems方法清除导航栏上的控件
        clearNavigationBarItems(animated: true)
    }

    // 提供触觉反馈
    private func provideFeedback() {
        let generator = UIImpactFeedbackGenerator(style: .light)
        generator.impactOccurred()
    }

    private func updateWidgetsView() {
        // 创建新的 EnhancedWidgetsView 实例
        let updatedWidgetsView = AnyView(
            EnhancedWidgetsView(
                selectedFamily: selectedWidgetFamily,
                onThemeButtonTapped: { [weak self] in
                    self?.showThemeSelector()
                }
            )
        )

        // 更新 UIHostingController 的根视图
        widgetsVC.rootView = updatedWidgetsView
    }

    @objc private func searchButtonTapped() {
        showSearchView()
    }

    private func showSearchView() {
        // 创建并显示搜索视图
        let searchVC = SearchViewController()
        let navigationController = UINavigationController(rootViewController: searchVC)
        navigationController.modalPresentationStyle = .fullScreen
        present(navigationController, animated: true)
    }

    @objc private func themeButtonTapped() {
        showThemeSelector()
    }

    @objc private func titleLabelTapped() {
        showSizeSelector()
    }

    private func showSizeSelector() {
        // 创建自定义的尺寸选择器视图控制器
        let sizeSelectorVC = SizeSelectorViewController()
        sizeSelectorVC.modalPresentationStyle = .popover
        sizeSelectorVC.preferredContentSize = CGSize(width: 180, height: 150)

        // 设置当前选中的尺寸
        sizeSelectorVC.selectedSize = selectedWidgetFamily

        // 设置选择回调
        sizeSelectorVC.onSizeSelected = { [weak self] size in
            guard let self = self else { return }
            self.selectedWidgetFamily = size
            self.updateWidgetsView()
            self.setupWidgetSizeSelector() // 更新标题
        }

        // 配置弹出控制器
        if let popoverController = sizeSelectorVC.popoverPresentationController {
            if let titleView = mainTabBarController.navigationItem.titleView {
                popoverController.sourceView = titleView
                popoverController.sourceRect = titleView.bounds
                popoverController.permittedArrowDirections = [.up, .down]
                popoverController.delegate = self
            }
        }

        // 显示弹出菜单
        present(sizeSelectorVC, animated: true)
    }

    // 自定义尺寸选择器视图控制器
    class SizeSelectorViewController: UIViewController, UITableViewDelegate, UITableViewDataSource {
        // 尺寸选项
        let sizeOptions: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]

        // 当前选中的尺寸
        var selectedSize: WidgetFamily = .systemMedium

        // 选择回调
        var onSizeSelected: ((WidgetFamily) -> Void)?

        // 主题管理器
        private let themeManager = ThemeManager.shared

        // 获取当前主题
        private var theme: AppTheme {
            return themeManager.currentTheme
        }

        // 表格视图
        private lazy var tableView: UITableView = {
            let tableView = UITableView(frame: .zero, style: .plain)
            tableView.delegate = self
            tableView.dataSource = self
            tableView.register(UITableViewCell.self, forCellReuseIdentifier: "SizeCell")
            tableView.separatorInset = UIEdgeInsets(top: 0, left: 15, bottom: 0, right: 15)
            tableView.rowHeight = 44
            tableView.isScrollEnabled = false
            tableView.layer.cornerRadius = 10
            tableView.clipsToBounds = true

            // 应用主题颜色
            applyThemeToTableView(tableView)

            return tableView
        }()

        override func viewDidLoad() {
            super.viewDidLoad()
            setupUI()

            // 监听主题变化
            NotificationCenter.default.addObserver(
                self,
                selector: #selector(themeDidChange),
                name: NSNotification.Name("ThemeDidChangeNotification"),
                object: nil
            )
        }

        deinit {
            NotificationCenter.default.removeObserver(self)
        }

        @objc private func themeDidChange() {
            // 主题变化时更新 UI
            applyThemeToTableView(tableView)
            tableView.reloadData()
            view.backgroundColor = UIColor(theme.colors.surface)
        }

        private func applyThemeToTableView(_ tableView: UITableView) {
            // 设置表格视图背景色
            tableView.backgroundColor = UIColor(theme.colors.surface)

            // 设置分隔线颜色
            tableView.separatorColor = UIColor(theme.colors.divider)
        }

        private func setupUI() {
            // 设置视图背景色
            view.backgroundColor = UIColor(theme.colors.surface)

            // 添加表格视图
            view.addSubview(tableView)
            tableView.translatesAutoresizingMaskIntoConstraints = false
            NSLayoutConstraint.activate([
                tableView.topAnchor.constraint(equalTo: view.topAnchor),
                tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
                tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
                tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
            ])
        }

        // MARK: - UITableViewDataSource

        func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
            return sizeOptions.count
        }

        func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
            let cell = tableView.dequeueReusableCell(withIdentifier: "SizeCell", for: indexPath)
            let size = sizeOptions[indexPath.row]

            // 配置单元格
            var content = cell.defaultContentConfiguration()
            content.text = sizeName(for: size)

            // 应用主题颜色到文本
            content.textProperties.color = UIColor(theme.colors.text)

            // 设置图标
            let imageName = sizeIcon(for: size)
            content.image = UIImage(systemName: imageName)
            content.imageProperties.tintColor = UIColor(theme.colors.accent)

            cell.contentConfiguration = content

            // 设置选中状态
            cell.accessoryType = size == selectedSize ? .checkmark : .none

            // 设置单元格背景色
            cell.backgroundColor = UIColor(theme.colors.surface)

            // 设置选中背景色
            let selectedBackgroundView = UIView()
            selectedBackgroundView.backgroundColor = UIColor(theme.colors.accent.opacity(0.1))
            cell.selectedBackgroundView = selectedBackgroundView

            return cell
        }

        // MARK: - UITableViewDelegate

        func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
            tableView.deselectRow(at: indexPath, animated: true)

            let size = sizeOptions[indexPath.row]
            selectedSize = size
            onSizeSelected?(size)

            // 关闭弹出窗口
            dismiss(animated: true)
        }

        // MARK: - 辅助方法

        private func sizeName(for family: WidgetFamily) -> String {
            switch family {
            case .systemSmall:
                return "小尺寸"
            case .systemMedium:
                return "中尺寸"
            case .systemLarge:
                return "大尺寸"
            case .systemExtraLarge:
                return "超大尺寸"
            @unknown default:
                return "未知尺寸"
            }
        }

        private func sizeIcon(for family: WidgetFamily) -> String {
            switch family {
            case .systemSmall:
                return "square"
            case .systemMedium:
                return "rectangle"
            case .systemLarge:
                return "rectangle.portrait"
            case .systemExtraLarge:
                return "square.grid.2x2"
            @unknown default:
                return "square"
            }
        }
    }

    // 获取尺寸对应的名称
    private func sizeName(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }

    private func showThemeSelector() {
        // 创建并显示主题选择器
        let themeSelectorView = ThemeSelectorView(themeManager: ThemeManager.shared)
        let themeSelectorVC = UIHostingController(rootView: themeSelectorView)
        themeSelectorVC.modalPresentationStyle = .formSheet
        present(themeSelectorVC, animated: true)
    }

    // 查找视图层次结构中的 UIScrollView
    private func findScrollView(in view: UIView) -> UIScrollView? {
        if let scrollView = view as? UIScrollView {
            return scrollView
        }

        for subview in view.subviews {
            if let scrollView = findScrollView(in: subview) {
                return scrollView
            }
        }

        return nil
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UIPopoverPresentationControllerDelegate

extension EnhancedViewController {
    // 确保弹出窗口以正确的样式显示
    func adaptivePresentationStyle(for controller: UIPresentationController) -> UIModalPresentationStyle {
        return .none // 强制使用 popover 样式，不适应为其他样式
    }
}

// MARK: - UITabBarControllerDelegate

extension EnhancedViewController: UITabBarControllerDelegate {
    func tabBarController(_ tabBarController: UITabBarController, didSelect viewController: UIViewController) {
        // 保存当前选中的标签索引
        currentTabIndex = tabBarController.selectedIndex

        // 获取导航栏项
        let navigationItem = mainTabBarController.navigationItem

        // 如果选中的是组件标签，则显示尺寸选择器并隐藏标题
        if currentTabIndex == 0 {
            // 先清除所有导航栏按钮和标题
            clearNavigationBarItems(animated: true) { [weak self] in
                guard let self = self else { return }

                // 延迟一点时间再添加新的选择器，确保UI已经更新
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    self.setupWidgetSizeSelector()
                }
            }
        } else if currentTabIndex == 1 {
            // 如果选中的是快捷启动标签，则移除尺寸选择器并显示标题和保存按钮

            // 先清除所有导航栏按钮和标题
            clearNavigationBarItems(animated: true) { [weak self] in
                guard let self = self else { return }

                // 设置标题
                tabBarController.navigationItem.title = viewController.title

                // 添加保存按钮
                self.setupAppLauncherSaveButton(for: tabBarController)
            }
        } else {
            // 如果选中的不是组件标签，则移除尺寸选择器并显示标题

            // 先清除所有导航栏按钮和标题
            clearNavigationBarItems(animated: true) { [weak self] in
                // 设置标题
                tabBarController.navigationItem.title = viewController.title
            }
        }
    }

    // 清除导航栏上的所有项目
    private func clearNavigationBarItems(animated: Bool = true, completion: (() -> Void)? = nil) {
        // 获取导航栏项
        let navigationItem = mainTabBarController.navigationItem

        // 保存当前的控件引用
        let titleView = navigationItem.titleView
        let leftBarButtonItem = navigationItem.leftBarButtonItem
        let rightBarButtonItem = navigationItem.rightBarButtonItem

        if animated {
            // 添加淡出动画
            UIView.animate(withDuration: 0.2, animations: {
                titleView?.alpha = 0
                leftBarButtonItem?.customView?.alpha = 0
                rightBarButtonItem?.customView?.alpha = 0
            }, completion: { _ in
                // 动画完成后移除控件
                navigationItem.titleView = nil
                navigationItem.leftBarButtonItem = nil
                navigationItem.rightBarButtonItem = nil
                navigationItem.title = nil

                // 执行完成回调
                completion?()
            })
        } else {
            // 直接移除控件，不使用动画
            navigationItem.titleView = nil
            navigationItem.leftBarButtonItem = nil
            navigationItem.rightBarButtonItem = nil
            navigationItem.title = nil

            // 执行完成回调
            completion?()
        }
    }

    // 为快捷启动配置视图设置保存按钮
    private func setupAppLauncherSaveButton(for tabBarController: UITabBarController) {
        // 获取当前主题
        let themeManager = ThemeManager.shared
        let currentTheme = themeManager.currentTheme

        // 创建保存按钮
        let saveButton = UIButton(type: .system)
        saveButton.setTitle("保存", for: .normal)
        saveButton.titleLabel?.font = UIFont.systemFont(ofSize: 17, weight: .semibold)
        saveButton.tintColor = UIColor(currentTheme.colors.accent)
        saveButton.addTarget(self, action: #selector(saveAppLauncherConfig), for: .touchUpInside)

        // 设置按钮的尺寸
        saveButton.sizeToFit()
        let saveBarButtonItem = UIBarButtonItem(customView: saveButton)

        // 设置导航栏的rightBarButtonItem
        tabBarController.navigationItem.rightBarButtonItem = saveBarButtonItem
    }

    // 保存快捷启动配置
    @objc private func saveAppLauncherConfig() {
        // 发送通知，通知AppLauncherConfigView保存配置
        NotificationCenter.default.post(name: .saveAppLauncherConfig, object: nil)
    }

    // MARK: - 网络请求

    /// 访问百度网站
    private func performBaiduRequest() {
        guard let url = URL(string: "https://www.baidu.com") else {
//            print("❌ 无效的URL")
            return
        }

//        print("🌐 开始访问百度网站...")

        let task = URLSession.shared.dataTask(with: url) { data, response, error in
            DispatchQueue.main.async {
                if let error = error {
//                    print("❌ 访问百度失败: \(error.localizedDescription)")
                    return
                }

                if let httpResponse = response as? HTTPURLResponse {
//                    print("✅ 百度访问成功 - 状态码: \(httpResponse.statusCode)")
//
//                    if let data = data {
//                        let dataSize = ByteCountFormatter.string(fromByteCount: Int64(data.count), countStyle: .binary)
//                        print("📊 接收数据大小: \(dataSize)")
//                    }
                } else {
//                    print("⚠️ 无效的HTTP响应")
                }
            }
        }

        task.resume()
    }
}

// 扩展 UIColor 以支持从 SwiftUI Color 创建
extension UIColor {
    convenience init(_ color: Color) {
        let components = color.components()
        self.init(red: components.r, green: components.g, blue: components.b, alpha: components.a)
    }
}

// 扩展 Color 以获取 RGBA 组件
extension Color {
    func components() -> (r: CGFloat, g: CGFloat, b: CGFloat, a: CGFloat) {
        let scanner = Scanner(string: description.trimmingCharacters(in: CharacterSet.alphanumerics.inverted))
        var hexNumber: UInt64 = 0
        var r: CGFloat = 0.0, g: CGFloat = 0.0, b: CGFloat = 0.0, a: CGFloat = 1.0

        let result = scanner.scanHexInt64(&hexNumber)
        if result {
            r = CGFloat((hexNumber & 0xFF00_0000) >> 24) / 255
            g = CGFloat((hexNumber & 0x00FF_0000) >> 16) / 255
            b = CGFloat((hexNumber & 0x0000_FF00) >> 8) / 255
            a = CGFloat(hexNumber & 0x0000_00FF) / 255
        }
        return (r, g, b, a)
    }
}

// 扩展 UIImage 以创建纯色图像
extension UIImage {
    static func imageWithColor(_ color: UIColor) -> UIImage {
        let rect = CGRect(x: 0, y: 0, width: 1, height: 1)
        UIGraphicsBeginImageContext(rect.size)
        let context = UIGraphicsGetCurrentContext()

        context?.setFillColor(color.cgColor)
        context?.fill(rect)

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return image ?? UIImage()
    }
}
