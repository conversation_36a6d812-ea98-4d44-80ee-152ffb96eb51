<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>com.abc.JZJJWidgetAPP</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>jzjjwidget</string>
			</array>
		</dict>
	</array>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixin</string>
		<string>alipay</string>
		<string>taobao</string>
		<string>openapp.jdmobile</string>
		<string>mqq</string>
		<string>sinaweibo</string>
		<string>snssdk1128</string>
		<string>zhihu</string>
		<string>bilibili</string>
		<string>orpheus</string>
		<string>iosamap</string>
		<string>baidumap</string>
		<string>meituanwaimai</string>
		<string>eleme</string>
		<string>diditaxi</string>
		<string>ctrip</string>
		<string>taobaotravel</string>
		<string>snssdk141</string>
		<string>kwai</string>
		<string>xhsdiscover</string>
		<string>kwaiopenapi</string>
		<string>douyinliteopensdk</string>
		<string>snssdk1128</string>
		<string>kwai</string>
		<string>mailto</string>
		<string>sinaweibohd</string>
		<string>sinaweibo</string>
		<string>weibosdk</string>
		<string>weibosdk2.5</string>
		<string>weibosdk3.3</string>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>weixinURLParamsAPI</string>
		<string>mqqapi</string>
		<string>mqq</string>
		<string>tim</string>
		<string>mqqopensdknopasteboard</string>
		<string>mqqopensdknopasteboardios16</string>
		<string>mqqopensdkapiV2</string>
		<string>mqqbrowser</string>
		<string>mttbrowser</string>
		<string>pddopen</string>
		<string>openapp.jdmobile</string>
		<string>tbopen</string>
		<string>pinduoduo</string>
		<string>iMeituan</string>
		<string>taobaotravel</string>
		<string>taobao</string>
		<string>youku</string>
		<string>ctrip</string>
		<string>ctripOAuth</string>
		<string>dewuapp</string>
		<string>xhsdiscover</string>
		<string>meituanwaimai</string>
		<string>eleme</string>
		<string>magicwidget</string>
		<string>hanjulite</string>
		<string>tencentlaunch1104466820</string>
		<string>yykiwi</string>
		<string>itlogin-JSMahjong</string>
		<string>faceu</string>
		<string>lkxb9vv3sb4rv9jp6u</string>
		<string>videocut</string>
		<string>wx452924392e7fc5a7</string>
		<string>qiyi-iphone</string>
		<string>tenvideo</string>
		<string>wb1176522257</string>
		<string>baidumap</string>
		<string>baiduyun</string>
		<string>keep</string>
		<string>b612cn</string>
		<string>qqmail</string>
		<string>MojiWeather</string>
		<string>awddzldpfgxsqram</string>
		<string>bdboxiosqrcode</string>
		<string>cainiao</string>
		<string>myxj</string>
		<string>ulike</string>
		<string>mtxx</string>
		<string>fleamarket</string>
		<string>cn.12306</string>
		<string>iting</string>
		<string>dianping</string>
		<string>douban</string>
		<string>snssdk141</string>
		<string>zhihu</string>
		<string>wxwork</string>
		<string>diditaxi</string>
		<string>ucbrowser</string>
		<string>kugouURL</string>
		<string>qqmusic</string>
		<string>dingtalk</string>
		<string>Twitch</string>
		<string>Uber</string>
		<string>paypal</string>
		<string>googlegmail</string>
		<string>fb-messenger</string>
		<string>bilibili</string>
		<string>iosamap</string>
		<string>com.amazon.mobile.shopping</string>
		<string>orpheus</string>
		<string>youtube</string>
		<string>whatsapp</string>
		<string>twitter</string>
		<string>tumblr</string>
		<string>tinder</string>
		<string>musically</string>
		<string>tg</string>
		<string>spotify</string>
		<string>snapchat</string>
		<string>skype</string>
		<string>nflx</string>
		<string>line</string>
		<string>instagram</string>
		<string>fb</string>
		<string>googlechrome</string>
	</array>
	<key>NSUserActivityTypes</key>
	<array>
		<string>DynamicConfigIntent</string>
		<string>INSendMessageIntent</string>
		<string>StaticConfigIntent</string>
	</array>
	<key>UIApplicationSceneManifest</key>
	<dict>
		<key>UIApplicationSupportsMultipleScenes</key>
		<false/>
		<key>UISceneConfigurations</key>
		<dict>
			<key>UIWindowSceneSessionRoleApplication</key>
			<array>
				<dict>
					<key>UISceneConfigurationName</key>
					<string>Default Configuration</string>
					<key>UISceneDelegateClassName</key>
					<string>$(PRODUCT_MODULE_NAME).SceneDelegate</string>
				</dict>
			</array>
		</dict>
	</dict>
</dict>
</plist>
