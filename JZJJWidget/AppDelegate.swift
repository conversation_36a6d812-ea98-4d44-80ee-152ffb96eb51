//
//  AppDelegate.swift
//  JZJJWidget
//
//  Created by y<PERSON><PERSON><PERSON> on 2025/5/8.
//

import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {



    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.

        // 打印支持的URL Schemes，用于调试
        print("应用支持的URL Schemes:")
        if let urlTypes = Bundle.main.infoDictionary?["CFBundleURLTypes"] as? [[String: Any]] {
            for urlType in urlTypes {
                if let urlSchemes = urlType["CFBundleURLSchemes"] as? [String] {
                    print("URL Schemes: \(urlSchemes)")
                }
            }
        } else {
            print("未找到URL Schemes配置")
        }

        return true
    }

    // 处理通过URL打开应用的情况（iOS 9以下）
    func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bo<PERSON> {
        print("通过URL打开应用（iOS 9以下）: \(url)")
        return true
    }

    // 处理通过URL打开应用的情况（iOS 9及以上）
    func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> Bool {
        print("通过URL打开应用（iOS 9及以上）: \(url)")
        return true
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }


}

