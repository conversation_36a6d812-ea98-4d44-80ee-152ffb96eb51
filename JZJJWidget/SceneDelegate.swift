//
//  SceneDelegate.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/8.
//

import MyWidgetKit
import SwiftUI
import UIKit
import WidgetKit
import SafariServices

class SceneDelegate: UIResponder, UIWindowSceneDelegate {
    var window: UIWindow?

    func scene(_ scene: UIScene, willConnectTo _: UISceneSession, options _: UIScene.ConnectionOptions) {
        // Use this method to optionally configure and attach the UIWindow `window` to the provided UIWindowScene `scene`.
        // If using a storyboard, the `window` property will automatically be initialized and attached to the scene.
        // This delegate does not imply the connecting scene or session are new (see `application:configurationForConnectingSceneSession` instead).
        guard let windowScene = (scene as? UIWindowScene) else { return }

        // 创建窗口
        window = UIWindow(windowScene: windowScene)

        // 设置根视图控制器为增强版视图控制器
        window?.rootViewController = EnhancedViewController()

        // 显示窗口
        window?.makeKeyAndVisible()
    }

    func sceneDidDisconnect(_: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneDidBecomeActive(_: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
    }

    func sceneWillResignActive(_: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
    }

    func sceneWillEnterForeground(_: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
    }

    func sceneDidEnterBackground(_: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
    }

    // 处理URL打开事件
    func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
        guard let url = URLContexts.first?.url else {
            print("没有URL可以处理")
            return
        }

        print("收到URL打开请求: \(url)")
        print("URL详情 - scheme: \(url.scheme ?? "nil"), host: \(url.host ?? "nil"), path: \(url.path), query: \(url.query ?? "nil")")

        // 处理所有jzjjwidget开头的URL
        if url.scheme == "jzjjwidget" {
            print("处理jzjjwidget URL")

            // 处理水分摄入追踪器的URL
            if url.host == "addwater" {
                print("处理水分摄入追踪器URL")
                // 解析URL中的参数
                if let components = URLComponents(url: url, resolvingAgainstBaseURL: true),
                   let queryItems = components.queryItems,
                   let amountItem = queryItems.first(where: { $0.name == "amount" }),
                   let amountString = amountItem.value,
                   let amount = Double(amountString) {

                    print("添加水分摄入记录: \(amount)ml")
                    // 添加水分摄入记录
                    addWaterIntake(amount: amount)
                } else {
                    print("无法解析水分摄入参数")
                }
            }
            // 处理应用快捷启动URL
            else if url.host == "app" {
                print("处理应用快捷启动URL")

                // 解析URL中的参数
                if let components = URLComponents(url: url, resolvingAgainstBaseURL: true) {
                    let queryItems = components.queryItems ?? []
                    print("查询参数: \(queryItems)")

                    // 获取项目ID
                    let itemId = queryItems.first(where: { $0.name == "id" })?.value
                    print("项目ID: \(itemId ?? "nil")")

                    // 获取项目名称
                    let itemName = queryItems.first(where: { $0.name == "name" })?.value?.removingPercentEncoding
                    print("项目名称: \(itemName ?? "nil")")

                    // 获取小组件类型
                    let widgetType = queryItems.first(where: { $0.name == "type" })?.value
                    print("小组件类型: \(widgetType ?? "nil")")

                    // 获取URL Scheme
                    let urlScheme = queryItems.first(where: { $0.name == "urlScheme" })?.value?.removingPercentEncoding
                    print("URL Scheme: \(urlScheme ?? "nil")")

                    // 显示预览界面，传递所有参数
                    showPreviewController(itemId: itemId, itemName: itemName, widgetType: widgetType, urlScheme: urlScheme)
                } else {
                    print("无法解析应用快捷启动URL参数")
                }
            }
            // 处理小组件点击URL
            else if url.host == "widget" {
                print("处理小组件点击URL")

                // 解析URL中的参数
                if let components = URLComponents(url: url, resolvingAgainstBaseURL: true) {
                    let queryItems = components.queryItems ?? []
                    print("查询参数: \(queryItems)")

                    // 获取小组件类型
                    let widgetType = queryItems.first(where: { $0.name == "type" })?.value
                    print("小组件类型: \(widgetType ?? "nil")")

                    // 根据小组件类型显示相应界面
                    if widgetType == "appLauncher" {
                        // 显示应用快捷启动器配置界面
                        showAppLauncherConfigView()
                    } else {
                        // 显示默认预览界面
                        showPreviewController(itemId: nil, itemName: nil, widgetType: widgetType, urlScheme: nil)
                    }
                } else {
                    print("无法解析小组件点击URL参数")
                    // 显示默认预览界面
                    showPreviewController(itemId: nil, itemName: nil, widgetType: nil, urlScheme: nil)
                }
            }
            // 处理应用预览URL
            else if url.host == "preview" || url.host == nil || url.host == "" {
                print("处理应用预览URL")

                // 解析URL中的参数
                if let components = URLComponents(url: url, resolvingAgainstBaseURL: true) {
                    let queryItems = components.queryItems ?? []
                    print("查询参数: \(queryItems)")

                    // 获取项目ID
                    let itemId = queryItems.first(where: { $0.name == "id" })?.value
                    print("项目ID: \(itemId ?? "nil")")

                    // 获取项目名称
                    let itemName = queryItems.first(where: { $0.name == "name" })?.value?.removingPercentEncoding
                    print("项目名称: \(itemName ?? "nil")")

                    // 获取小组件类型
                    let widgetType = queryItems.first(where: { $0.name == "type" })?.value
                    print("小组件类型: \(widgetType ?? "nil")")

                    // 获取URL Scheme
                    let urlScheme = queryItems.first(where: { $0.name == "urlScheme" })?.value?.removingPercentEncoding
                    print("URL Scheme: \(urlScheme ?? "nil")")

                    // 显示预览界面，传递所有参数
                    showPreviewController(itemId: itemId, itemName: itemName, widgetType: widgetType, urlScheme: urlScheme)
                } else {
                    print("无法解析预览URL参数，使用默认预览")
                    // 即使没有参数，也显示默认预览界面
                    showPreviewController(itemId: nil, itemName: nil, widgetType: nil, urlScheme: nil)
                }
            }
            // 处理其他jzjjwidget URL
            else {
                print("处理其他jzjjwidget URL，host: \(url.host ?? "nil")")
                // 显示默认预览界面
                showPreviewController(itemId: nil, itemName: nil, widgetType: nil, urlScheme: nil)
            }
        } else {
            print("未知的URL格式: scheme=\(url.scheme ?? "nil"), host=\(url.host ?? "nil")")

            // 尝试直接打开URL
            if url.scheme?.contains("://") == true {
                print("尝试直接打开URL: \(url)")
                UIApplication.shared.open(url, options: [:]) { success in
                    print("直接打开URL结果: \(success ? "成功" : "失败")")
                }
            } else {
                print("无法识别的URL格式")
                // 显示默认预览界面
                showPreviewController(itemId: nil, itemName: nil, widgetType: nil, urlScheme: nil)
            }
        }
    }

    // 添加水分摄入记录
    private func addWaterIntake(amount: Double) {
        // 1. 更新保存的数据
        if var config = AppGroupDataManager.shared.read(WaterIntakeWidgetData.self, for: .waterIntake, property: .config) {
            config.currentIntake += amount
            config.lastUpdated = Date()
            AppGroupDataManager.shared.save(config, for: .waterIntake, property: .config)

            // 2. 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
 

            // 4. 显示成功提示
            if let rootVC = window?.rootViewController {
                let alertController = UIAlertController(
                    title: "添加成功",
                    message: "已添加 \(Int(amount))ml 水分摄入记录",
                    preferredStyle: .alert
                )
                alertController.addAction(UIAlertAction(title: "确定", style: .default))
                rootVC.present(alertController, animated: true)
            }
        }
    }

    // 显示应用快捷启动器配置界面
    private func showAppLauncherConfigView() {
        guard let rootVC = window?.rootViewController else {
            print("无法获取根视图控制器")
            return
        }

        print("显示应用快捷启动器配置界面")

        // 这里需要根据您的应用结构来实现
        // 例如，如果您使用UINavigationController作为根视图控制器
        if let navController = rootVC as? UINavigationController {
            // 创建应用快捷启动器配置视图控制器
            let appLauncherConfigVC = AppLauncherConfigViewController()
            navController.pushViewController(appLauncherConfigVC, animated: true)
        } else {
            // 如果根视图控制器不是UINavigationController，则使用模态方式显示
            let appLauncherConfigVC = AppLauncherConfigViewController()
            appLauncherConfigVC.modalPresentationStyle = .fullScreen
            rootVC.present(appLauncherConfigVC, animated: true)
        }
    }

    // 显示预览控制器
    private func showPreviewController(itemId: String?, itemName: String?, widgetType: String?, urlScheme: String? = nil) {
        guard let rootVC = window?.rootViewController else {
            print("无法获取根视图控制器")
            return
        }

        print("showPreviewController - itemId: \(itemId ?? "nil"), itemName: \(itemName ?? "nil"), widgetType: \(widgetType ?? "nil"), urlScheme: \(urlScheme ?? "nil")")

        // 首先检查是否有传入的urlScheme参数
        if let scheme = urlScheme, !scheme.isEmpty {
            print("检测到传入的URL Scheme: \(scheme)")
            if let url = URL(string: scheme) {
                print("尝试打开传入的URL: \(url)")

                // 检查是否可以打开URL
                if UIApplication.shared.canOpenURL(url) {
                    // 询问用户是否要打开外部应用
                    UIApplication.shared.open(url, options: [:]) { success in
                        print("打开URL结果: \(success ? "成功" : "失败")")
                    }

                    return
                } else {
                    print("无法打开URL: \(url)，应用可能未注册该URL Scheme")
                    // 显示提示
                    let alert = UIAlertController(
                        title: "无法打开应用",
                        message: "未安装 \(itemName ?? "所需应用")",
                        preferredStyle: .alert
                    )
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    rootVC.present(alert, animated: true)
                }
            }
        }

        // 如果itemId是URL Scheme，作为备用方案尝试打开
        if let scheme = itemId, scheme.contains("://") && scheme != urlScheme {
            print("检测到备用URL Scheme: \(scheme)")
            if let url = URL(string: scheme) {
                print("尝试打开备用URL: \(url)")

                // 检查是否可以打开URL
                if UIApplication.shared.canOpenURL(url) {
                    // 询问用户是否要打开外部应用
                    UIApplication.shared.open(url, options: [:]) { success in
                        print("打开URL结果: \(success ? "成功" : "失败")")
                    }
                    return
                }
            }
        }

        // 如果是网址，使用Safari视图控制器打开
        if let urlString = itemId, (urlString.hasPrefix("http://") || urlString.hasPrefix("https://")),
           let url = URL(string: urlString) {
            print("检测到网址: \(urlString)")
            let safariVC = SFSafariViewController(url: url)
            safariVC.preferredControlTintColor = .systemBlue
            rootVC.present(safariVC, animated: true)
            return
        }

        // 尝试打开特定应用
        if let appScheme = getAppScheme(for: itemName), urlScheme == nil {
            print("尝试打开应用: \(itemName ?? "未知"), scheme: \(appScheme)")
            if let url = URL(string: appScheme) {
                if UIApplication.shared.canOpenURL(url) {
                    // 询问用户是否要打开外部应用


                    UIApplication.shared.open(url, options: [:]) { success in
                        print("打开URL结果: \(success ? "成功" : "失败")")
                    }
                    return
                } else {
                    print("无法打开应用: \(appScheme)，应用可能未安装")
                    // 显示提示
                    let alert = UIAlertController(
                        title: "无法打开应用",
                        message: "未安装 \(itemName ?? "所需应用")",
                        preferredStyle: .alert
                    )
                    alert.addAction(UIAlertAction(title: "确定", style: .default))
                    rootVC.present(alert, animated: true)
                }
            }
        }

        // 否则，显示自定义预览控制器
        print("显示自定义预览控制器")
        let previewController = ItemPreviewController(itemId: itemId, itemName: itemName, widgetType: widgetType)
        previewController.modalPresentationStyle = .overFullScreen
        rootVC.present(previewController, animated: true)
    }

    // 根据应用名称获取URL Scheme
    private func getAppScheme(for appName: String?) -> String? {
        guard let appName = appName else { return nil }

        // 常见应用的URL Scheme映射
        let appSchemes: [String: String] = [
            "微信": "weixin://",
            "支付宝": "alipay://",
            "淘宝": "taobao://",
            "京东": "openapp.jdmobile://",
            "QQ": "mqq://",
            "微博": "sinaweibo://",
            "抖音": "snssdk1128://",
            "知乎": "zhihu://",
            "哔哩哔哩": "bilibili://",
            "网易云音乐": "orpheus://",
            "高德地图": "iosamap://",
            "百度地图": "baidumap://",
            "美团": "meituanwaimai://",
            "饿了么": "eleme://",
            "滴滴出行": "diditaxi://",
            "携程": "ctrip://",
            "飞猪": "taobaotravel://",
            "今日头条": "snssdk141://",
            "快手": "kwai://",
            "小红书": "xhsdiscover://"
        ]

        return appSchemes[appName]
    }
}
