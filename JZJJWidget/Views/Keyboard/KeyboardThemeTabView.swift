//
//  KeyboardThemeTabView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 键盘换肤独立TabBar页面
struct KeyboardThemeTabView: View {
    // MARK: - 环境对象
    @EnvironmentObject private var themeManager: ThemeManager

    // MARK: - 状态
    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared
    @State private var selectedTheme: KeyboardTheme
    @State private var showingCustomThemeEditor = false
    @State private var showingImageThemeConfig = false
    @State private var showingAdvancedThemeConfig = false
    @State private var showingThemePackSelection = false
    @State private var showingPreview = false
    @State private var showSuccessToast = false
    @State private var successMessage = ""

    // MARK: - 初始化
    init() {
        _selectedTheme = State(initialValue: KeyboardThemeManager.shared.currentTheme)
    }

    // MARK: - 视图主体
    var body: some View {

            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 当前主题预览
                    currentThemePreviewSection
/*
                    // 调试按钮 - 检查custom图片
                    Section("调试工具") {
                        Button("🔍 检查Custom图片复制状态") {
                            _Concurrency.Task {
                                await debugCustomImages()
                            }
                        }
                        .foregroundColor(.blue)

                        Button("🔄 强制重新复制Custom图片") {
                            _Concurrency.Task {
                                await forceRecopyCustomImages()
                            }
                        }
                        .foregroundColor(.orange)

                        Button("🎨 直接应用colorful-vibrant主题") {
                            _Concurrency.Task {
                                await applyColorfulVibrantTheme()
                            }
                        }
                        .foregroundColor(.green)

                        Button("🔧 检查AdvancedTheme配置") {
                            _Concurrency.Task {
                                await checkAdvancedThemeConfig()
                            }
                        }
                        .foregroundColor(.purple)

                        Button("💾 强制保存AdvancedTheme") {
                            _Concurrency.Task {
                                await forceSaveAdvancedTheme()
                            }
                        }
                        .foregroundColor(.red)

                        Button("🗑️ 清除并重新生成") {
                            _Concurrency.Task {
                                await clearAndRegenerateTheme()
                            }
                        }
                        .foregroundColor(.orange)

                        Button("🧪 测试编码解码") {
                            _Concurrency.Task {
                                await testKeyConfigEncoding()
                            }
                        }
                        .foregroundColor(.blue)

                        Button("🖼️ 修复预览图片") {
                            Task {
                                await fixPreviewImages()
                            }
                        }
                        .foregroundColor(.green)

                        Button("🔄 重新生成内置主题包") {
                            Task {
                                await regenerateBuiltInThemePacks()
                            }
                        }
                        .foregroundColor(.purple)

                        Button("🧡 测试默认主题") {
                            testDefaultTheme()
                        }
                        .foregroundColor(.orange)
                    }
*/
                    // 预设主题选择
                    presetThemesSection

                    // 自定义主题
                    customThemeSection

                    // 图片主题
                    imageThemeSection

                    // 高k级主题定制
                    advancedThemeSection

                    // 内置主题包
                    themePackSection

                    // 键盘设置指南
                    settingsGuideSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .frame(width: AppLayout.DeviceAdaptive.screenSize.width)
            .navigationTitle("键盘主题")
            .navigationBarTitleDisplayMode(.large)
            .background(themeManager.currentTheme.colors.background.ignoresSafeArea())

        .sheet(isPresented: $showingCustomThemeEditor) {
            customThemeEditorView
        }
        .sheet(isPresented: $showingImageThemeConfig) {
            imageThemeConfigView
        }
        .sheet(isPresented: $showingAdvancedThemeConfig) {
            advancedThemeConfigView
        }
        .sheet(isPresented: $showingThemePackSelection) {
            themePackSelectionView
        }
        .sheet(isPresented: $showingPreview) {
            previewView
        }
        .overlay(
            // 成功提示Toast
            VStack {
                Spacer()
                if showSuccessToast {
                    SuccessToast(message: successMessage, theme: themeManager.currentTheme)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                        .onAppear {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                                withAnimation {
                                    showSuccessToast = false
                                }
                            }
                        }
                }
            }
        )
        .onAppear {
            selectedTheme = keyboardThemeManager.currentTheme
        }
    }

    // MARK: - 当前主题预览区域
    private var currentThemePreviewSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("当前主题")
                    .font(AppTypography.accentSmall(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)
                Spacer()

                Button("预览") {
                    showingPreview = true
                }
                .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                .foregroundColor(themeManager.currentTheme.colors.accent)
            }

            // 主题预览卡片
            CompactKeyboardPreview(theme: selectedTheme)
                .frame(height: 120)
                .background(themeManager.currentTheme.colors.surface)
                .cornerRadius(AppLayout.CornerRadius.large)
                .shadow(color: themeManager.currentTheme.colors.shadow, radius: 4, x: 0, y: 2)

            // 主题信息
            VStack(spacing: 4) {
                Text(selectedTheme.name)
                    .font(AppTypography.bodyLarge(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)

                Text(selectedTheme.type.name)
                    .font(AppTypography.bodySmall(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.subtext)
            }
        }
        .padding(AppLayout.Spacing.medium)
        .background(themeManager.currentTheme.colors.surface)
        .cornerRadius(AppLayout.CornerRadius.large)
        .shadow(color: themeManager.currentTheme.colors.shadow, radius: 2, x: 0, y: 1)
    }

    // MARK: - 预设主题选择区域
    private var presetThemesSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("预设主题")
                    .font(AppTypography.accentSmall(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)
                Spacer()
            }

            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: AppLayout.Spacing.small), count: 2), spacing: AppLayout.Spacing.small) {
                ForEach(KeyboardTheme.defaultThemes, id: \.id) { theme in
                    PresetThemeCard(
                        theme: theme,
                        isSelected: selectedTheme.id == theme.id,
                        onSelect: {
                            selectedTheme = theme
                            saveTheme()
                        }
                    )
                }
            }
        }
        .padding(AppLayout.Spacing.medium)
        .background(themeManager.currentTheme.colors.surface)
        .cornerRadius(AppLayout.CornerRadius.large)
        .shadow(color: themeManager.currentTheme.colors.shadow, radius: 2, x: 0, y: 1)
    }

    // MARK: - 自定义主题区域
    private var customThemeSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("自定义主题")
                    .font(AppTypography.accentSmall(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)
                Spacer()
            }

            Button(action: {
                showingCustomThemeEditor = true
            }) {
                HStack {
                    Image(systemName: "paintbrush.pointed.fill")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                        .frame(width: 24, height: 24)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("创建自定义主题")
                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        Text("自由定制颜色、样式和效果")
                            .font(AppTypography.bodySmall(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.subtext)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(themeManager.currentTheme.colors.subtext)
                        .font(.system(size: 14))
                }
                .padding(AppLayout.Spacing.medium)
                .background(themeManager.currentTheme.colors.surface)
                .cornerRadius(AppLayout.CornerRadius.medium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                        .stroke(themeManager.currentTheme.colors.divider, lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(AppLayout.Spacing.medium)
        .background(themeManager.currentTheme.colors.surface)
        .cornerRadius(AppLayout.CornerRadius.large)
        .shadow(color: themeManager.currentTheme.colors.shadow, radius: 2, x: 0, y: 1)
    }

    // MARK: - 图片主题区域
    private var imageThemeSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("图片主题")
                    .font(AppTypography.accentSmall(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)
                Spacer()
            }

            Button(action: {
                showingImageThemeConfig = true
            }) {
                HStack {
                    Image(systemName: "photo.fill")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                        .frame(width: 24, height: 24)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("图片背景主题")
                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        Text("使用自定义图片作为键盘背景")
                            .font(AppTypography.bodySmall(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.subtext)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(themeManager.currentTheme.colors.subtext)
                        .font(.system(size: 14))
                }
                .padding(AppLayout.Spacing.medium)
                .background(themeManager.currentTheme.colors.surface)
                .cornerRadius(AppLayout.CornerRadius.medium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                        .stroke(themeManager.currentTheme.colors.divider, lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(AppLayout.Spacing.medium)
        .background(themeManager.currentTheme.colors.surface)
        .cornerRadius(AppLayout.CornerRadius.large)
        .shadow(color: themeManager.currentTheme.colors.shadow, radius: 2, x: 0, y: 1)
    }

    // MARK: - 高级主题定制区域
    private var advancedThemeSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("高级定制")
                    .font(AppTypography.accentSmall(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)
                Spacer()
            }

            Button(action: {
                showingAdvancedThemeConfig = true
            }) {
                HStack {
                    Image(systemName: "gearshape.2.fill")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                        .frame(width: 24, height: 24)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("高级主题定制")
                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        Text("按键级别的精细化定制")
                            .font(AppTypography.bodySmall(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.subtext)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(themeManager.currentTheme.colors.subtext)
                        .font(.system(size: 14))
                }
                .padding(AppLayout.Spacing.medium)
                .background(themeManager.currentTheme.colors.surface)
                .cornerRadius(AppLayout.CornerRadius.medium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                        .stroke(themeManager.currentTheme.colors.divider, lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(AppLayout.Spacing.medium)
        .background(themeManager.currentTheme.colors.surface)
        .cornerRadius(AppLayout.CornerRadius.large)
        .shadow(color: themeManager.currentTheme.colors.shadow, radius: 2, x: 0, y: 1)
    }

    // MARK: - 内置主题包区域
    private var themePackSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("内置主题包")
                    .font(AppTypography.accentSmall(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)
                Spacer()
            }

            Button(action: {
                showingThemePackSelection = true
            }) {
                HStack {
                    Image(systemName: "cube.box.fill")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                        .frame(width: 24, height: 24)

                    VStack(alignment: .leading, spacing: 2) {
                        Text("精美主题包")
                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        Text("专业设计的完整主题套装")
                            .font(AppTypography.bodySmall(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.subtext)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(themeManager.currentTheme.colors.subtext)
                        .font(.system(size: 14))
                }
                .padding(AppLayout.Spacing.medium)
                .background(themeManager.currentTheme.colors.surface)
                .cornerRadius(AppLayout.CornerRadius.medium)
                .overlay(
                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                        .stroke(themeManager.currentTheme.colors.divider, lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(AppLayout.Spacing.medium)
        .background(themeManager.currentTheme.colors.surface)
        .cornerRadius(AppLayout.CornerRadius.large)
        .shadow(color: themeManager.currentTheme.colors.shadow, radius: 2, x: 0, y: 1)
    }

    // MARK: - 键盘设置指南区域
    private var settingsGuideSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("使用指南")
                    .font(AppTypography.accentSmall(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)
                Spacer()

                // 一键跳转到设置按钮
                Button(action: openSettings) {
                    HStack(spacing: 4) {
                        Image(systemName: "gear")
                            .font(.system(size: 12, weight: .medium))
                        Text("前往设置")
                            .font(AppTypography.bodySmall(theme: themeManager.currentTheme))
                    }
                    .foregroundColor(themeManager.currentTheme.colors.accent)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(themeManager.currentTheme.colors.accent.opacity(0.1))
                    .cornerRadius(AppLayout.CornerRadius.small)
                }
                .buttonStyle(PlainButtonStyle())
            }

            VStack(spacing: AppLayout.Spacing.small) {
                GuideStepView(
                    step: "1",
                    title: "启用键盘",
                    description: "设置 > 通用 > 键盘 > 添加新键盘",
                    theme: themeManager.currentTheme
                )

                GuideStepView(
                    step: "2",
                    title: "允许完全访问",
                    description: "开启\"允许完全访问\"权限",
                    theme: themeManager.currentTheme
                )

                GuideStepView(
                    step: "3",
                    title: "切换键盘",
                    description: "长按地球图标选择\(Bundle.main.displayName ?? "JZJJWidget")键盘",
                    theme: themeManager.currentTheme
                )
            }
        }
        .padding(AppLayout.Spacing.medium)
        .background(themeManager.currentTheme.colors.surface)
        .cornerRadius(AppLayout.CornerRadius.large)
        .shadow(color: themeManager.currentTheme.colors.shadow, radius: 2, x: 0, y: 1)
    }

    // MARK: - Sheet视图
    private var customThemeEditorView: some View {
        CustomKeyboardThemeEditorView(
            themeName: .constant(""),
            themeType: .constant(.custom),
            keyStyle: .constant(.standard),
            backgroundColor: .constant(.gray),
            keyColor: .constant(.white),
            textColor: .constant(.black)
        ) { newTheme in
            selectedTheme = newTheme
            saveTheme()
            showingCustomThemeEditor = false
        }
    }

    private var imageThemeConfigView: some View {
        ImageThemeConfigView()
            .environmentObject(themeManager)
    }

    private var advancedThemeConfigView: some View {
        AdvancedThemeConfigView()
            .environmentObject(themeManager)
    }

    private var themePackSelectionView: some View {
        ThemePackSelectionView()
    }

    private var previewView: some View {
        KeyboardPreviewView(theme: selectedTheme)
            .navigationTitle("键盘预览")
            .navigationBarTitleDisplayMode(.inline)
    }

    // MARK: - 辅助方法
    private func saveTheme() {
        keyboardThemeManager.setTheme(selectedTheme)

        // 显示成功提示
        successMessage = "主题「\(selectedTheme.name)」已应用"
        showSuccessToast = true

        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
    }

    /// 打开系统设置
    private func openSettings() {
        // 尝试打开键盘设置页面
        if let settingsURL = URL(string: "App-Prefs:General&path=Keyboard") {
            if UIApplication.shared.canOpenURL(settingsURL) {
                UIApplication.shared.open(settingsURL)
                return
            }
        }

        // 如果无法打开特定页面，则打开通用设置
        if let generalSettingsURL = URL(string: "App-Prefs:General") {
            if UIApplication.shared.canOpenURL(generalSettingsURL) {
                UIApplication.shared.open(generalSettingsURL)
                return
            }
        }
        // 最后尝试打开系统设置主页
        if let appSettings = URL(string: UIApplication.openSettingsURLString) {
            if UIApplication.shared.canOpenURL(appSettings) {
                UIApplication.shared.open(appSettings)
            }
        } 
        // 触觉反馈
        let impactFeedback = UIImpactFeedbackGenerator(style: .light)
        impactFeedback.impactOccurred()
    }

    // MARK: - 调试方法

    /// 调试Custom图片复制状态
    private func debugCustomImages() async {
        print("\n🔍 开始检查Custom图片复制状态...")

        // 1. 检查App Groups目录
        let appGroupURL = FileManager.default.containerURL(
            forSecurityApplicationGroupIdentifier: "group.com.abc.JZJJWidgetAPP"
        )!

        print("📁 App Groups路径: \(appGroupURL.path)")

        // 2. 检查keyboard_themes目录
        let keyboardThemesDir = appGroupURL.appendingPathComponent("keyboard_themes/built-in")
        print("📁 键盘主题目录: \(keyboardThemesDir.path)")
        print("📁 目录存在: \(FileManager.default.fileExists(atPath: keyboardThemesDir.path))")

        // 3. 列出所有文件
        do {
            let allFiles = try FileManager.default.contentsOfDirectory(atPath: keyboardThemesDir.path)
            print("📄 目录中所有文件数量: \(allFiles.count)")

            // 4. 筛选custom文件
            let customFiles = allFiles.filter { $0.contains("_custom_") }
            print("🎨 Custom图片文件数量: \(customFiles.count)")

            if customFiles.isEmpty {
                print("❌ 没有找到任何custom图片文件")
            } else {
                print("✅ 找到的custom图片文件:")
                for file in customFiles.sorted() {
                    let filePath = keyboardThemesDir.appendingPathComponent(file)
                    let fileSize = try? FileManager.default.attributesOfItem(atPath: filePath.path)[.size] as? Int64 ?? 0
                    print("  - \(file) (大小: \(fileSize ?? 0) 字节)")
                }
            }

            // 5. 检查特定的colorful-vibrant custom文件
            let expectedCustomFiles = [
                "colorful-vibrant_custom_A-key.png",
                "colorful-vibrant_custom_A-key-pressed.png",
                "colorful-vibrant_custom_1-key.png",
                "colorful-vibrant_custom_1-key-pressed.png",
                "colorful-vibrant_custom_space-key.png",
                "colorful-vibrant_custom_space-key-pressed.png",
                "colorful-vibrant_custom_shift-key.png",
                "colorful-vibrant_custom_shift-key-pressed.png"
            ]

            print("\n🎯 检查预期的colorful-vibrant custom文件:")
            for expectedFile in expectedCustomFiles {
                let filePath = keyboardThemesDir.appendingPathComponent(expectedFile)
                let exists = FileManager.default.fileExists(atPath: filePath.path)
                print("  \(exists ? "✅" : "❌") \(expectedFile)")
            }

        } catch {
            print("❌ 读取目录失败: \(error)")
        }

        // 6. 运行ThemePackManager的调试方法
        print("\n🧪 运行ThemePackManager调试...")
        await ThemePackManager.shared.debugCustomKeyImagesCopy(themeId: "colorful-vibrant")

        print("\n🏁 Custom图片检查完成")
    }

    /// 强制重新复制Custom图片
    private func forceRecopyCustomImages() async {
        print("\n🔄 开始强制重新复制Custom图片...")

        do {
            // 1. 强制重新生成主题包
            print("📦 强制重新生成主题包...")
            try await ThemePackManager.shared.forceRegenerateBuiltInThemePacks()

            // 2. 重新应用colorful-vibrant主题
            print("🎨 重新应用colorful-vibrant主题...")
            try await ThemePackManager.shared.applyThemePack(id: "colorful-vibrant")

            // 3. 强制同步图片
            print("🔄 强制同步图片到键盘扩展...")
            await ThemePackManager.shared.forceSyncImagesToKeyboard()

            print("✅ 强制重新复制完成")

            // 4. 再次检查结果
            await debugCustomImages()

        } catch {
            print("❌ 强制重新复制失败: \(error)")
        }
    }

    /// 直接应用colorful-vibrant主题（跳过重新生成）
    private func applyColorfulVibrantTheme() async {
        print("\n🎨 直接应用colorful-vibrant主题...")

        do {
            // 直接尝试应用主题
            try await ThemePackManager.shared.applyThemePack(id: "colorful-vibrant")
            print("✅ colorful-vibrant主题应用成功")

            // 检查应用结果
            await debugCustomImages()

        } catch {
            print("❌ 应用colorful-vibrant主题失败: \(error)")

            // 如果失败，尝试重新生成
            print("🔄 尝试重新生成主题包...")
            do {
                try await ThemePackManager.shared.forceRegenerateBuiltInThemePacks()
                try await ThemePackManager.shared.applyThemePack(id: "colorful-vibrant")
                print("✅ 重新生成后应用成功")
            } catch {
                print("❌ 重新生成后仍然失败: \(error)")
            }
        }
    }

    /// 检查AdvancedTheme配置
    private func checkAdvancedThemeConfig() async {
        print("\n🔧 检查AdvancedTheme配置...")

        // 1. 检查当前AdvancedKeyboardTheme
        let advancedThemeManager = AdvancedKeyboardThemeManager.shared
        if let currentAdvancedTheme = advancedThemeManager.currentAdvancedTheme {
            print("✅ 当前AdvancedTheme: \(currentAdvancedTheme.name) (ID: \(currentAdvancedTheme.id))")
            print("  - 按键类型配置数量: \(currentAdvancedTheme.keyTypeConfigs.count)")
            print("  - 个性化按键配置数量: \(currentAdvancedTheme.individualKeyConfigs.count)")

            // 检查个性化配置详情
            for (key, config) in currentAdvancedTheme.individualKeyConfigs {
                print("  - 个性化按键「\(key)」:")
                print("    • hasCustomImage: \(config.hasCustomImage)")
                print("    • normalImagePath: \(config.normalImagePath ?? "nil")")
                print("    • pressedImagePath: \(config.pressedImagePath ?? "nil")")
                print("    • backgroundColor: \(config.backgroundColor)")
            }

            // 检查按键类型配置
            for (keyType, config) in currentAdvancedTheme.keyTypeConfigs {
                print("  - 按键类型「\(keyType.rawValue)」:")
                print("    • 影响按键数量: \(config.affectedKeys.count)")
                print("    • 默认背景色: \(config.defaultBackgroundColor)")
            }
        } else {
            print("❌ 当前没有AdvancedTheme")
        }

        // 2. 检查键盘扩展中的主题加载
        print("\n🔍 检查键盘扩展主题加载...")

        // 检查App Groups中的主题数据
        let appGroupURL = FileManager.default.containerURL(
            forSecurityApplicationGroupIdentifier: "group.com.abc.JZJJWidgetAPP"
        )!

        // 检查AdvancedTheme存储
        if let userDefaults = UserDefaults(suiteName: "group.com.abc.JZJJWidgetAPP") {
            if let advancedThemeData = userDefaults.data(forKey: "current_advanced_keyboard_theme") {
                print("✅ App Groups中存在AdvancedTheme数据，大小: \(advancedThemeData.count) 字节")

                // 尝试解析
                do {
                    let decodedTheme = try JSONDecoder().decode(AdvancedKeyboardTheme.self, from: advancedThemeData)
                    print("✅ AdvancedTheme解析成功: \(decodedTheme.name)")
                    print("  - 个性化配置数量: \(decodedTheme.individualKeyConfigs.count)")

                    // 检查特定按键配置
                    if let aConfig = decodedTheme.individualKeyConfigs["A"] {
                        print("  - A键配置: hasCustomImage=\(aConfig.hasCustomImage), path=\(aConfig.normalImagePath ?? "nil")")
                    } else {
                        print("  - A键配置: 不存在")
                    }

                    if let oneConfig = decodedTheme.individualKeyConfigs["1"] {
                        print("  - 1键配置: hasCustomImage=\(oneConfig.hasCustomImage), path=\(oneConfig.normalImagePath ?? "nil")")
                    } else {
                        print("  - 1键配置: 不存在")
                    }

                } catch {
                    print("❌ AdvancedTheme解析失败: \(error)")
                }
            } else {
                print("❌ App Groups中没有AdvancedTheme数据")
            }
        } else {
            print("❌ 无法访问App Groups UserDefaults")
        }

        print("\n🏁 AdvancedTheme配置检查完成")
    }

    /// 强制保存AdvancedTheme到App Groups
    private func forceSaveAdvancedTheme() async {
        print("\n💾 强制保存AdvancedTheme到App Groups...")

        let advancedThemeManager = AdvancedKeyboardThemeManager.shared

        if let currentTheme = advancedThemeManager.currentAdvancedTheme {
            print("✅ 找到当前AdvancedTheme: \(currentTheme.name)")

            // 强制重新应用主题（这会触发保存）
            await MainActor.run {
                advancedThemeManager.applyAdvancedTheme(currentTheme)
            }

            print("✅ 强制重新应用完成")

            // 验证保存结果
            if let userDefaults = UserDefaults(suiteName: "group.com.abc.JZJJWidgetAPP") {
                if let savedData = userDefaults.data(forKey: "current_advanced_keyboard_theme") {
                    print("✅ 验证成功：App Groups中已保存AdvancedTheme数据，大小: \(savedData.count) 字节")

                    // 尝试解析验证
                    do {
                        let decodedTheme = try JSONDecoder().decode(AdvancedKeyboardTheme.self, from: savedData)
                        print("✅ 解析验证成功: \(decodedTheme.name)")
                        print("  - 个性化配置数量: \(decodedTheme.individualKeyConfigs.count)")

                        // 检查关键配置
                        if let aConfig = decodedTheme.individualKeyConfigs["A"] {
                            print("  - A键配置: hasCustomImage=\(aConfig.hasCustomImage)")
                        }
                        if let oneConfig = decodedTheme.individualKeyConfigs["1"] {
                            print("  - 1键配置: hasCustomImage=\(oneConfig.hasCustomImage)")
                        }
                        if let spaceConfig = decodedTheme.individualKeyConfigs["空格"] {
                            print("  - 空格键配置: hasCustomImage=\(spaceConfig.hasCustomImage)")
                        }
                        if let shiftConfig = decodedTheme.individualKeyConfigs["⇧"] {
                            print("  - ⇧键配置: hasCustomImage=\(shiftConfig.hasCustomImage)")
                        }

                    } catch {
                        print("❌ 解析验证失败: \(error)")
                    }
                } else {
                    print("❌ 验证失败：App Groups中仍然没有AdvancedTheme数据")
                }
            } else {
                print("❌ 无法访问App Groups UserDefaults")
            }
        } else {
            print("❌ 当前没有AdvancedTheme")
        }

        print("\n🏁 强制保存完成")
    }

    /// 清除并重新生成主题
    private func clearAndRegenerateTheme() async {
        print("\n🗑️ 开始清除并重新生成主题...")

        do {
            // 1. 清除App Groups中的AdvancedTheme数据
            print("🗑️ 清除App Groups中的AdvancedTheme数据...")
            if let userDefaults = UserDefaults(suiteName: "group.com.abc.JZJJWidgetAPP") {
                userDefaults.removeObject(forKey: "current_advanced_keyboard_theme")
                userDefaults.removeObject(forKey: "advanced_keyboard_themes")
                userDefaults.synchronize()
                print("✅ 清除完成")
            }

            // 2. 重置AdvancedKeyboardThemeManager
            print("🔄 重置AdvancedKeyboardThemeManager...")
            await MainActor.run {
                let advancedThemeManager = AdvancedKeyboardThemeManager.shared
                advancedThemeManager.currentAdvancedTheme = nil
                advancedThemeManager.availableAdvancedThemes = []
            }

            // 3. 强制重新生成主题包
            print("📦 强制重新生成主题包...")
            try await ThemePackManager.shared.forceRegenerateBuiltInThemePacks()

            // 4. 重新应用colorful-vibrant主题
            print("🎨 重新应用colorful-vibrant主题...")
            try await ThemePackManager.shared.applyThemePack(id: "colorful-vibrant")

            // 5. 验证结果
            print("🔍 验证重新生成结果...")
            await checkAdvancedThemeConfig()

            print("✅ 清除并重新生成完成")

        } catch {
            print("❌ 清除并重新生成失败: \(error)")
        }
    }

    /// 测试KeyConfig编码解码
    private func testKeyConfigEncoding() async {
        print("\n🧪 测试KeyConfig编码解码...")

        do {
            // 1. 创建测试KeyConfig
            let testKeyConfig = KeyConfig(
                id: "test-key",
                keyType: .letter,
                keyValue: "A",
                backgroundColor: WidgetColor(red: 1.0, green: 0.6, blue: 0.8, alpha: 1.0),
                pressedColor: WidgetColor(red: 1.0, green: 0.3, blue: 0.6, alpha: 1.0),
                textColor: WidgetColor(red: 0.1, green: 0.1, blue: 0.2, alpha: 1.0),
                borderColor: WidgetColor(red: 0.9, green: 0.4, blue: 0.8, alpha: 0.8),
                fontName: "SF Pro",
                fontSize: 18,
                fontWeight: .bold,
                cornerRadius: 12,
                borderWidth: 2,
                shadowEnabled: true,
                shadowColor: WidgetColor(red: 1.0, green: 0.4, blue: 0.8, alpha: 0.3),
                shadowRadius: 4,
                shadowOffset: CGSize(width: 0, height: 2),
                widthMultiplier: 1.0,
                heightMultiplier: 1.0,
                hasCustomImage: true,
                normalImagePath: "resources/keys/custom/A-key.png",
                pressedImagePath: "resources/keys/custom/A-key-pressed.png",
                hoverImagePath: nil,
                imageOpacity: 0.9,
                imageBlendMode: "normal",
                hideTextWhenImageExists: true
            )

            print("✅ 创建测试KeyConfig成功")
            print("  - keyType: \(testKeyConfig.keyType)")
            print("  - hasCustomImage: \(testKeyConfig.hasCustomImage)")
            print("  - normalImagePath: \(testKeyConfig.normalImagePath ?? "nil")")
            print("  - hideTextWhenImageExists: \(testKeyConfig.hideTextWhenImageExists)")
            print("  - textColor: \(testKeyConfig.textColor)")

            // 2. 编码为JSON
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = [.prettyPrinted, .sortedKeys]

            let jsonData = try encoder.encode(testKeyConfig)
            let jsonString = String(data: jsonData, encoding: .utf8) ?? "无法转换为字符串"

            print("✅ JSON编码成功")
            print("📄 编码后的JSON:")
            print(jsonString)

            // 3. 解码测试
            print("\n🔍 解码测试:")
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601

            let decodedKeyConfig = try decoder.decode(KeyConfig.self, from: jsonData)
            print("✅ 解码成功")
            print("  - keyType: \(decodedKeyConfig.keyType)")
            print("  - hasCustomImage: \(decodedKeyConfig.hasCustomImage)")
            print("  - normalImagePath: \(decodedKeyConfig.normalImagePath ?? "nil")")
            print("  - hideTextWhenImageExists: \(decodedKeyConfig.hideTextWhenImageExists)")
            print("  - textColor: \(decodedKeyConfig.textColor)")

            // 4. 验证数据一致性
            print("\n🔍 数据一致性验证:")
            print("  - keyType一致: \(testKeyConfig.keyType == decodedKeyConfig.keyType)")
            print("  - hasCustomImage一致: \(testKeyConfig.hasCustomImage == decodedKeyConfig.hasCustomImage)")
            print("  - normalImagePath一致: \(testKeyConfig.normalImagePath == decodedKeyConfig.normalImagePath)")
            print("  - hideTextWhenImageExists一致: \(testKeyConfig.hideTextWhenImageExists == decodedKeyConfig.hideTextWhenImageExists)")
            print("  - textColor一致: \(testKeyConfig.textColor == decodedKeyConfig.textColor)")

            print("🎉 KeyConfig编码解码测试成功")

        } catch {
            print("❌ 测试失败: \(error)")
        }

        print("\n🏁 编码解码测试完成")
    }

    /// 修复预览图片
    private func fixPreviewImages() async {
        print("\n🖼️ 开始修复预览图片...")

        let themePackManager = ThemePackManager.shared
        let appGroupURL = FileManager.default.containerURL(
            forSecurityApplicationGroupIdentifier: "group.com.abc.JZJJWidgetAPP"
        )!

        let themeIds = ["classic-light", "nature-wood", "neon-cyber", "colorful-vibrant"]

        for themeId in themeIds {
            print("\n🎨 处理主题: \(themeId)")

            // 1. 检查App Groups中的预览图片
            let themePackDir = appGroupURL.appendingPathComponent("theme-packs/built-in/\(themeId)")
            let previewPath = themePackDir.appendingPathComponent("preview.png")

            print("📁 主题包目录: \(themePackDir.path)")
            print("📁 预览图片路径: \(previewPath.path)")
            print("📁 目录存在: \(FileManager.default.fileExists(atPath: themePackDir.path))")
            print("📁 预览图片存在: \(FileManager.default.fileExists(atPath: previewPath.path))")

            // 2. 检查Bundle中的预览图片
            if let bundlePath = Bundle.main.path(forResource: "BuiltInThemePacks/\(themeId)/preview", ofType: "png") {
                print("📦 Bundle预览图片存在: \(bundlePath)")

                // 检查Bundle文件是否是真正的图片
                do {
                    let bundleData = try Data(contentsOf: URL(fileURLWithPath: bundlePath))
                    if let bundleImage = UIImage(data: bundleData) {
                        print("✅ Bundle预览图片有效: \(bundleImage.size)")

                        // 复制到App Groups
                        do {
                            try FileManager.default.createDirectory(at: themePackDir, withIntermediateDirectories: true)
                            try bundleData.write(to: previewPath)
                            print("✅ 复制Bundle预览图片到App Groups成功")
                        } catch {
                            print("❌ 复制Bundle预览图片失败: \(error)")
                        }
                    } else {
                        print("❌ Bundle预览图片无效（可能是占位符文件）")
                        await generateDefaultPreviewImage(for: themeId, at: previewPath)
                    }
                } catch {
                    print("❌ 读取Bundle预览图片失败: \(error)")
                    await generateDefaultPreviewImage(for: themeId, at: previewPath)
                }
            } else {
                print("❌ Bundle中没有预览图片")
                await generateDefaultPreviewImage(for: themeId, at: previewPath)
            }

            // 3. 验证最终结果
            if FileManager.default.fileExists(atPath: previewPath.path) {
                do {
                    let finalData = try Data(contentsOf: previewPath)
                    if let finalImage = UIImage(data: finalData) {
                        print("✅ 最终预览图片有效: \(finalImage.size)")
                    } else {
                        print("❌ 最终预览图片无效")
                    }
                } catch {
                    print("❌ 验证最终预览图片失败: \(error)")
                }
            } else {
                print("❌ 最终预览图片不存在")
            }
        }

        // 4. 测试预览图片加载
        print("\n🔍 测试预览图片加载...")
        for themeId in themeIds {
            if let previewImage = await themePackManager.getPreviewImage(for: themeId) {
                print("✅ \(themeId): 预览图片加载成功 (\(Int(previewImage.size.width))x\(Int(previewImage.size.height)))")
            } else {
                print("❌ \(themeId): 预览图片加载失败")
            }
        }

        print("\n🏁 预览图片修复完成")
    }

    /// 生成默认预览图片
    private func generateDefaultPreviewImage(for themeId: String, at url: URL) async {
        print("🎨 为 \(themeId) 生成默认预览图片...")

        // 根据主题ID选择颜色
        let backgroundColor: UIColor
        let keyColor: UIColor

        switch themeId {
        case "classic-light":
            backgroundColor = UIColor.systemGray6
            keyColor = UIColor.white
        case "nature-wood":
            backgroundColor = UIColor(red: 0.6, green: 0.4, blue: 0.2, alpha: 1.0)
            keyColor = UIColor(red: 0.8, green: 0.6, blue: 0.4, alpha: 1.0)
        case "neon-cyber":
            backgroundColor = UIColor.black
            keyColor = UIColor(red: 0.0, green: 1.0, blue: 1.0, alpha: 1.0)
        case "colorful-vibrant":
            backgroundColor = UIColor(red: 0.9, green: 0.6, blue: 1.0, alpha: 1.0)
            keyColor = UIColor(red: 1.0, green: 0.8, blue: 0.9, alpha: 1.0)
        default:
            backgroundColor = UIColor.systemGray5
            keyColor = UIColor.systemGray3
        }

        // 生成预览图片
        let size = CGSize(width: 320, height: 200)
        let renderer = UIGraphicsImageRenderer(size: size)

        let previewImage = renderer.image { context in
            let rect = CGRect(origin: .zero, size: size)

            // 背景
            backgroundColor.setFill()
            context.fill(rect)

            // 绘制简化的键盘布局
            let keySize = CGSize(width: 24, height: 16)
            let spacing: CGFloat = 4
            let startX: CGFloat = 20
            let startY: CGFloat = 40

            keyColor.setFill()

            // 第一行 - 10个按键
            for i in 0..<10 {
                let x = startX + CGFloat(i) * (keySize.width + spacing)
                let keyRect = CGRect(x: x, y: startY, width: keySize.width, height: keySize.height)
                let path = UIBezierPath(roundedRect: keyRect, cornerRadius: 4)
                path.fill()
            }

            // 第二行 - 9个按键
            for i in 0..<9 {
                let x = startX + 12 + CGFloat(i) * (keySize.width + spacing)
                let y = startY + keySize.height + spacing
                let keyRect = CGRect(x: x, y: y, width: keySize.width, height: keySize.height)
                let path = UIBezierPath(roundedRect: keyRect, cornerRadius: 4)
                path.fill()
            }

            // 第三行 - 7个按键 + 特殊键
            let shiftWidth: CGFloat = 32
            let deleteWidth: CGFloat = 32

            // Shift键
            let shiftRect = CGRect(x: startX, y: startY + 2 * (keySize.height + spacing), width: shiftWidth, height: keySize.height)
            let shiftPath = UIBezierPath(roundedRect: shiftRect, cornerRadius: 4)
            shiftPath.fill()

            // 字母键
            for i in 0..<7 {
                let x = startX + shiftWidth + spacing + CGFloat(i) * (keySize.width + spacing)
                let y = startY + 2 * (keySize.height + spacing)
                let keyRect = CGRect(x: x, y: y, width: keySize.width, height: keySize.height)
                let path = UIBezierPath(roundedRect: keyRect, cornerRadius: 4)
                path.fill()
            }

            // Delete键
            let deleteX = startX + shiftWidth + spacing + 7 * (keySize.width + spacing)
            let deleteRect = CGRect(x: deleteX, y: startY + 2 * (keySize.height + spacing), width: deleteWidth, height: keySize.height)
            let deletePath = UIBezierPath(roundedRect: deleteRect, cornerRadius: 4)
            deletePath.fill()

            // 底部行 - 功能键和空格键
            let bottomY = startY + 3 * (keySize.height + spacing)

            // 123键（特殊键颜色）
            let numbersRect = CGRect(x: startX, y: bottomY, width: 36, height: keySize.height)
            let numbersPath = UIBezierPath(roundedRect: numbersRect, cornerRadius: 4)
            numbersPath.fill()

            // 空格键（普通键颜色）
            keyColor.setFill()
            let spaceWidth: CGFloat = 120
            let spaceX = startX + 50
            let spaceRect = CGRect(x: spaceX, y: bottomY, width: spaceWidth, height: keySize.height)
            let spacePath = UIBezierPath(roundedRect: spaceRect, cornerRadius: 4)
            spacePath.fill()

            // 回车键（特殊键颜色）
            let specialKeyColor = keyColor.withAlphaComponent(0.8)
            specialKeyColor.setFill()
            let returnRect = CGRect(x: spaceX + spaceWidth + spacing, y: bottomY, width: 36, height: keySize.height)
            let returnPath = UIBezierPath(roundedRect: returnRect, cornerRadius: 4)
            returnPath.fill()

            // 添加主题名称
            let themeName: String
            switch themeId {
            case "classic-light":
                themeName = "经典浅色"
            case "nature-wood":
                themeName = "自然木纹"
            case "neon-cyber":
                themeName = "霓虹科技"
            case "colorful-vibrant":
                themeName = "活力彩色"
            default:
                themeName = themeId.replacingOccurrences(of: "-", with: " ").capitalized
            }

            let titleAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 16, weight: .semibold),
                .foregroundColor: UIColor.label
            ]

            let titleSize = themeName.size(withAttributes: titleAttributes)
            let titleX = (size.width - titleSize.width) / 2
            let titleY: CGFloat = 150

            themeName.draw(at: CGPoint(x: titleX, y: titleY), withAttributes: titleAttributes)

            // 添加描述文字
            let descAttributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.systemFont(ofSize: 12, weight: .regular),
                .foregroundColor: UIColor.secondaryLabel
            ]

            let description = "键盘主题预览"
            let descSize = description.size(withAttributes: descAttributes)
            let descX = (size.width - descSize.width) / 2
            let descY: CGFloat = 170

            description.draw(at: CGPoint(x: descX, y: descY), withAttributes: descAttributes)
        }

        // 保存图片
        do {
            try FileManager.default.createDirectory(at: url.deletingLastPathComponent(), withIntermediateDirectories: true)
            if let data = previewImage.pngData() {
                try data.write(to: url)
                print("✅ 生成默认预览图片成功: \(url.lastPathComponent)")
            } else {
                print("❌ 生成预览图片数据失败")
            }
        } catch {
            print("❌ 保存预览图片失败: \(error)")
        }
    }

    /// 重新生成内置主题包
    private func regenerateBuiltInThemePacks() async {
        print("\n🔄 开始重新生成内置主题包...")

        let themePackManager = ThemePackManager.shared

        do {
            // 强制重新生成所有内置主题包
            try await themePackManager.forceRegenerateBuiltInThemePacks()

            // 重新初始化主题包列表
            await themePackManager.initializeThemePacks()

            print("✅ 内置主题包重新生成完成")

            // 验证预览图片
            print("\n🔍 验证预览图片...")
            let themeIds = ["classic-light", "nature-wood", "neon-cyber", "colorful-vibrant"]

            for themeId in themeIds {
                if let previewImage = await themePackManager.getPreviewImage(for: themeId) {
                    print("✅ \(themeId): 预览图片验证成功 (\(Int(previewImage.size.width))x\(Int(previewImage.size.height)))")
                } else {
                    print("❌ \(themeId): 预览图片验证失败")
                }
            }

        } catch {
            print("❌ 重新生成内置主题包失败: \(error)")
        }

        print("\n🏁 内置主题包重新生成完成")
    }

    /// 测试默认主题设置
    private func testDefaultTheme() {
        print("\n🧡 测试默认主题设置...")

        // 1. 检查应用主题管理器
        let appThemeManager = ThemeManager.shared
        print("📱 应用当前主题: \(appThemeManager.currentTheme.rawValue)")
        print("📱 应用主题颜色: primary=\(appThemeManager.colors.primary), accent=\(appThemeManager.colors.accent)")

        // 2. 检查键盘主题管理器
        let keyboardThemeManager = KeyboardThemeManager.shared
        print("⌨️ 键盘当前主题: \(keyboardThemeManager.currentTheme.name)")
        print("⌨️ 键盘主题类型: \(keyboardThemeManager.currentTheme.type)")
        print("⌨️ 键盘主题背景色: \(keyboardThemeManager.currentTheme.backgroundColor)")
        print("⌨️ 键盘主题按键色: \(keyboardThemeManager.currentTheme.keyBackgroundColor)")

        // 3. 检查默认主题列表
        let defaultThemes = KeyboardTheme.defaultThemes
        print("📋 默认主题列表:")
        for (index, theme) in defaultThemes.enumerated() {
            print("  \(index): \(theme.name) (\(theme.type))")
        }

        // 4. 测试重置为默认主题
        print("\n🔄 重置为默认主题...")

        // 清除保存的主题设置
        UserDefaults.standard.removeObject(forKey: "app_selected_theme")

        // 清除键盘主题设置
        if let appGroupDefaults = UserDefaults(suiteName: "group.com.abc.JZJJWidgetAPP") {
            appGroupDefaults.removeObject(forKey: "keyboard_theme")
            appGroupDefaults.removeObject(forKey: "keyboard_simplified_theme")
            appGroupDefaults.removeObject(forKey: "current_advanced_keyboard_theme")
        }

        // 5. 重新初始化主题管理器（模拟首次启动）
        print("🔄 模拟首次启动...")

        // 创建新的ThemeManager实例来测试默认值
        // 注意：由于ThemeManager是单例，我们只能检查当前的默认逻辑
        let newAppTheme = AppTheme.warmOrange // 这应该是默认主题
        let newKeyboardTheme = KeyboardTheme.defaultThemes[0] // 这应该是暖橙主题

        print("✅ 预期应用默认主题: \(newAppTheme.rawValue)")
        print("✅ 预期键盘默认主题: \(newKeyboardTheme.name)")

        // 6. 应用默认主题
        appThemeManager.switchTheme(to: newAppTheme)
        keyboardThemeManager.setTheme(newKeyboardTheme)

        print("🎉 默认主题测试完成")
        print("📱 应用主题已设置为: \(appThemeManager.currentTheme.rawValue)")
        print("⌨️ 键盘主题已设置为: \(keyboardThemeManager.currentTheme.name)")
    }
}

// MARK: - 预设主题卡片
struct PresetThemeCard: View {
    let theme: KeyboardTheme
    let isSelected: Bool
    let onSelect: () -> Void

    @EnvironmentObject private var themeManager: ThemeManager

    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: 8) {
                // 主题预览
                CompactKeyboardPreview(theme: theme)
                    .frame(height: 60)
                    .cornerRadius(8)
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(
                                isSelected ? themeManager.currentTheme.colors.accent : Color.clear,
                                lineWidth: 2
                            )
                    )

                // 主题信息
                VStack(spacing: 2) {
                    Text(theme.name)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(1)
                        .foregroundColor(themeManager.currentTheme.colors.text)

                    Text(theme.type.name)
                        .font(.caption2)
                        .foregroundColor(themeManager.currentTheme.colors.subtext)
                        .lineLimit(1)
                }
            }
            .padding(8)
            .background(themeManager.currentTheme.colors.surface)
            .cornerRadius(AppLayout.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                    .stroke(
                        isSelected ? themeManager.currentTheme.colors.accent : themeManager.currentTheme.colors.divider,
                        lineWidth: isSelected ? 2 : 1
                    )
            )
            .scaleEffect(isSelected ? 1.02 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 指南步骤视图
struct GuideStepView: View {
    let step: String
    let title: String
    let description: String
    let theme: AppTheme

    var body: some View {
        HStack(alignment: .top, spacing: AppLayout.Spacing.medium) {
            // 步骤编号
            Text(step)
                .font(.system(size: 14, weight: .bold))
                .foregroundColor(.white)
                .frame(width: 24, height: 24)
                .background(theme.colors.accent)
                .clipShape(Circle())

            // 内容
            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(AppTypography.bodyMedium(theme: theme))
                    .foregroundColor(theme.colors.text)
                    .fontWeight(.medium)
                    .lineLimit(nil)

                Text(description)
                    .font(AppTypography.bodySmall(theme: theme))
                    .foregroundColor(theme.colors.subtext)
                    .lineLimit(nil)
                    .multilineTextAlignment(.leading)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(AppLayout.Spacing.small)
        .frame(maxWidth: .infinity)
        .background(theme.colors.background)
        .cornerRadius(AppLayout.CornerRadius.medium)
        .overlay(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                .stroke(theme.colors.divider, lineWidth: 1)
        )
    }
}

// MARK: - 紧凑键盘预览
struct CompactKeyboardPreview: View {
    let theme: KeyboardTheme

    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // 背景颜色
                theme.backgroundColor.color

                // 简化的键盘布局
                VStack(spacing: 4) {
                    // 第一行 - 10个按键
                    HStack(spacing: 2) {
                        ForEach(0..<10, id: \.self) { _ in
                            keyButton
                        }
                    }

                    // 第二行 - 9个按键，两侧有间距
                    HStack(spacing: 2) {
                        Spacer().frame(width: keyWidth * 0.5)
                        ForEach(0..<9, id: \.self) { _ in
                            keyButton
                        }
                        Spacer().frame(width: keyWidth * 0.5)
                    }

                    // 第三行 - 特殊键 + 7个字母键 + 特殊键
                    HStack(spacing: 2) {
                        specialKeyButton
                        ForEach(0..<7, id: \.self) { _ in
                            keyButton
                        }
                        specialKeyButton
                    }

                    // 底部行 - 功能键
                    HStack(spacing: 2) {
                        specialKeyButton.frame(width: keyWidth * 1.2)
                        specialKeyButton.frame(width: keyWidth * 0.8)
                        keyButton // 空格键，自动填充剩余空间
                        specialKeyButton.frame(width: keyWidth * 1.2)
                    }
                }
                .padding(6)
            }
            .cornerRadius(8)
        }
    }

    private var keyWidth: CGFloat {
        // 基于可用宽度计算按键宽度
        return 24
    }

    private var keyButton: some View {
        RoundedRectangle(cornerRadius: 4)
            .fill(theme.keyBackgroundColor.color)
            .frame(width: keyWidth, height: 16)
            .overlay(
                RoundedRectangle(cornerRadius: 4)
                    .stroke(theme.showBorder ? theme.borderColor.color : Color.clear, lineWidth: 0.5)
            )
    }

    private var specialKeyButton: some View {
        RoundedRectangle(cornerRadius: 4)
            .fill(theme.specialKeyColor.color)
            .frame(width: keyWidth, height: 16)
            .overlay(
                RoundedRectangle(cornerRadius: 4)
                    .stroke(theme.showBorder ? theme.borderColor.color : Color.clear, lineWidth: 0.5)
            )
    }
}

// MARK: - 预览
struct KeyboardThemeTabView_Previews: PreviewProvider {
    static var previews: some View {
        KeyboardThemeTabView()
            .environmentObject(ThemeManager.shared)
    }
}
