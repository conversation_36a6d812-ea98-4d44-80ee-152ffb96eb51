//
//  AdvancedThemeCreatorView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 高级主题创建器
struct AdvancedThemeCreatorView: View {
    // MARK: - 环境对象
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理
    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared

    @State private var themeName = ""
    @State private var themeDescription = ""
    @State private var selectedBaseTheme: KeyboardTheme
    @State private var showingBaseThemeSelector = false
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 高级设置
    @State private var globalKeySpacing: Double = 6
    @State private var globalKeyHeight: Double = 44
    @State private var enableHapticFeedback = true
    @State private var enableSoundFeedback = true
    @State private var enableKeyAnimations = true
    @State private var animationDuration: Double = 0.1
    @State private var enableGradientEffects = false
    @State private var enableParallaxEffect = false

    // MARK: - 初始化
    init() {
        _selectedBaseTheme = State(initialValue: KeyboardTheme.defaultThemes[0])
    }

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 基本信息
                    basicInfoSection

                    // 基础主题选择
                    baseThemeSection

                    // 全局设置
                    globalSettingsSection

                    // 高级效果
                    advancedEffectsSection

                    // 创建按钮
                    createButtonSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .navigationTitle("创建高级主题")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingBaseThemeSelector) {
            BaseThemeSelectorView(selectedTheme: $selectedBaseTheme)
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }

    // MARK: - 基本信息
    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("基本信息")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                // 主题名称
                VStack(alignment: .leading, spacing: 8) {
                    Text("主题名称")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    TextField("输入主题名称", text: $themeName)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                }

                // 主题描述
                VStack(alignment: .leading, spacing: 8) {
                    Text("主题描述")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    TextField("输入主题描述（可选）", text: $themeDescription, axis: .vertical)
                        .textFieldStyle(RoundedBorderTextFieldStyle())
                        .lineLimit(3)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 基础主题选择
    private var baseThemeSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("基础主题")
                .font(.headline)
                .fontWeight(.semibold)

            Button(action: {
                showingBaseThemeSelector = true
            }) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text("当前选择")
                            .font(.caption)
                            .foregroundColor(.secondary)

                        Text(selectedBaseTheme.name)
                            .font(.subheadline)
                            .fontWeight(.medium)
                            .foregroundColor(.primary)
                    }

                    Spacer()

                    // 主题预览
                    HStack(spacing: 4) {
                        ForEach(0..<3) { _ in
                            RoundedRectangle(cornerRadius: 4)
                                .fill(selectedBaseTheme.keyBackgroundColor.color)
                                .frame(width: 20, height: 16)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 4)
                                        .stroke(selectedBaseTheme.borderColor.color, lineWidth: 0.5)
                                )
                        }
                    }

                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    // MARK: - 全局设置
    private var globalSettingsSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("全局设置")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                // 按键间距
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("按键间距")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(globalKeySpacing))pt")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }

                    Slider(value: $globalKeySpacing, in: 2...12, step: 1) {
                        Text("按键间距")
                    } minimumValueLabel: {
                        Text("2")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } maximumValueLabel: {
                        Text("12")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // 按键高度
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("按键高度")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(globalKeyHeight))pt")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }

                    Slider(value: $globalKeyHeight, in: 32...60, step: 2) {
                        Text("按键高度")
                    } minimumValueLabel: {
                        Text("32")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } maximumValueLabel: {
                        Text("60")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // 反馈设置
                VStack(spacing: 12) {
                    Toggle("触觉反馈", isOn: $enableHapticFeedback)
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Toggle("声音反馈", isOn: $enableSoundFeedback)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 高级效果
    private var advancedEffectsSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("高级效果")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                // 动画效果
                VStack(alignment: .leading, spacing: 12) {
                    Toggle("按键动画", isOn: $enableKeyAnimations)
                        .font(.subheadline)
                        .fontWeight(.medium)

                    if enableKeyAnimations {
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text("动画时长")
                                    .font(.caption)
                                    .foregroundColor(.secondary)

                                Spacer()

                                Text("\(Int(animationDuration * 1000))ms")
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                                    .frame(width: 50)
                            }

                            Slider(value: $animationDuration, in: 0.05...0.3, step: 0.01) {
                                Text("动画时长")
                            } minimumValueLabel: {
                                Text("50ms")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            } maximumValueLabel: {
                                Text("300ms")
                                    .font(.caption2)
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding(.leading, 16)
                    }
                }

                // 视觉效果
                VStack(spacing: 12) {
                    Toggle("渐变效果", isOn: $enableGradientEffects)
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Toggle("视差效果", isOn: $enableParallaxEffect)
                        .font(.subheadline)
                        .fontWeight(.medium)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 创建按钮
    private var createButtonSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            Button(action: createAdvancedTheme) {
                Text("创建高级主题")
                    .font(.headline)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(canCreateTheme ? Color.blue : Color.gray)
                    .cornerRadius(12)
            }
            .disabled(!canCreateTheme)

            Text("创建后您可以进一步配置每个按键的详细样式")
                .font(.caption)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    // MARK: - 辅助属性
    private var canCreateTheme: Bool {
        return !themeName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }

    // MARK: - 辅助方法
    private func createAdvancedTheme() {
        let newTheme = advancedThemeManager.createAdvancedTheme(
            name: themeName,
            description: themeDescription,
            baseTheme: selectedBaseTheme
        )

        // 应用全局设置
        var updatedTheme = newTheme
        updatedTheme.globalKeySpacing = globalKeySpacing
        updatedTheme.globalKeyHeight = globalKeyHeight
        updatedTheme.enableHapticFeedback = enableHapticFeedback
        updatedTheme.enableSoundFeedback = enableSoundFeedback
        updatedTheme.enableKeyAnimations = enableKeyAnimations
        updatedTheme.animationDuration = animationDuration
        updatedTheme.enableGradientEffects = enableGradientEffects
        updatedTheme.enableParallaxEffect = enableParallaxEffect

        // 更新主题
        if let index = advancedThemeManager.availableAdvancedThemes.firstIndex(where: { $0.id == newTheme.id }) {
            advancedThemeManager.availableAdvancedThemes[index] = updatedTheme
        }

        // 应用新主题
        advancedThemeManager.applyAdvancedTheme(updatedTheme)

        alertMessage = "高级主题「\(themeName)」创建成功并已应用！\n\n您可以继续配置按键类型和单个按键来进一步个性化您的键盘。"
        showingAlert = true

        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            dismiss()
        }
    }
}

// MARK: - 预览
struct AdvancedThemeCreatorView_Previews: PreviewProvider {
    static var previews: some View {
        AdvancedThemeCreatorView()
    }
}
