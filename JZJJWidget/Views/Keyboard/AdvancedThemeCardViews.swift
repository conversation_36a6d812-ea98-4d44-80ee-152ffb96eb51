//
//  AdvancedThemeCardViews.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

// MARK: - 高级主题卡片
struct AdvancedThemeCard: View {
    let theme: AdvancedKeyboardTheme
    let isSelected: Bool
    let onTap: () -> Void
    let onEdit: () -> Void
    let onDuplicate: () -> Void
    let onDelete: (() -> Void)?

    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @State private var showingActionSheet = false

    var body: some View {
        VStack(spacing: 12) {
            // 主题预览区域
            themePreviewArea

            // 主题信息
            themeInfoArea

            // 操作按钮
            actionButtonsArea
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(16)
        .overlay(
            RoundedRectangle(cornerRadius: 16)
                .stroke(isSelected ? Color.blue : Color(.systemGray4), lineWidth: isSelected ? 2 : 1)
        )
        .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        .confirmationDialog("主题操作", isPresented: $showingActionSheet) {
            Button("编辑主题") {
                onEdit()
            }

            Button("复制主题") {
                onDuplicate()
            }

            if let onDelete = onDelete {
                Button("删除主题", role: .destructive) {
                    onDelete()
                }
            }

            Button("取消", role: .cancel) { }
        }
    }

    // MARK: - 主题预览区域
    private var themePreviewArea: some View {
        ZStack {
            // 背景
            RoundedRectangle(cornerRadius: 12)
                .fill(theme.baseTheme.backgroundColor.color)
                .frame(height: 120)

            // 背景图片（如果有）
            if theme.baseTheme.hasBackgroundImage {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.blue.opacity(0.3))
                    .frame(height: 120)
                    .overlay(
                        Image(systemName: "photo.fill")
                            .foregroundColor(.white)
                            .font(.title2)
                    )
            }

            // 键盘布局预览
            VStack(spacing: 4) {
                // 第一行 - 数字键
                HStack(spacing: 3) {
                    ForEach(0..<5) { index in
                        keyPreviewButton(
                            keyType: .number,
                            keyValue: "\(index + 1)",
                            width: 18,
                            height: 14
                        )
                    }
                }

                // 第二行 - 字母键
                HStack(spacing: 3) {
                    ForEach(["Q", "W", "E", "R", "T"], id: \.self) { letter in
                        keyPreviewButton(
                            keyType: .letter,
                            keyValue: letter,
                            width: 18,
                            height: 14
                        )
                    }
                }

                // 第三行 - 字母键和功能键
                HStack(spacing: 3) {
                    keyPreviewButton(
                        keyType: .function,
                        keyValue: "⇧",
                        width: 24,
                        height: 14
                    )

                    ForEach(["A", "S", "D"], id: \.self) { letter in
                        keyPreviewButton(
                            keyType: .letter,
                            keyValue: letter,
                            width: 18,
                            height: 14
                        )
                    }

                    keyPreviewButton(
                        keyType: .function,
                        keyValue: "⌫",
                        width: 24,
                        height: 14
                    )
                }

                // 第四行 - 空格键
                HStack(spacing: 3) {
                    keyPreviewButton(
                        keyType: .function,
                        keyValue: "123",
                        width: 24,
                        height: 14
                    )

                    keyPreviewButton(
                        keyType: .space,
                        keyValue: "空格",
                        width: 60,
                        height: 14
                    )

                    keyPreviewButton(
                        keyType: .function,
                        keyValue: "↵",
                        width: 24,
                        height: 14
                    )
                }
            }
            .padding(8)
        }
    }

    // MARK: - 按键预览按钮
    private func keyPreviewButton(keyType: KeyType, keyValue: String, width: CGFloat, height: CGFloat) -> some View {
        let config = advancedThemeManager.getEffectiveKeyConfig(
            for: keyValue,
            keyType: keyType,
            in: theme
        )

        return ZStack {
            RoundedRectangle(cornerRadius: CGFloat(config.cornerRadius / 2))
                .fill(config.backgroundColor.color)
                .frame(width: width, height: height)
                .overlay(
                    RoundedRectangle(cornerRadius: CGFloat(config.cornerRadius / 2))
                        .stroke(config.borderColor.color, lineWidth: 0.5)
                )

            if keyValue != "空格" {
                Text(keyValue)
                    .font(.system(size: 6, weight: .medium))
                    .foregroundColor(config.textColor.color)
            }
        }
    }

    // MARK: - 主题信息区域
    private var themeInfoArea: some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(theme.name)
                    .font(.headline)
                    .fontWeight(.semibold)
                    .lineLimit(1)

                Spacer()

                if isSelected {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.blue)
                        .font(.title3)
                }
            }

            if !theme.description.isEmpty {
                Text(theme.description)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
            }

            // 主题统计信息
            HStack(spacing: 12) {
                Label("\(theme.keyTypeConfigs.count)", systemImage: "keyboard")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Label("\(theme.individualKeyConfigs.count)", systemImage: "key.fill")
                    .font(.caption2)
                    .foregroundColor(.secondary)

                Spacer()

                Text(formatDate(theme.updatedAt))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }

    // MARK: - 操作按钮区域
    private var actionButtonsArea: some View {
        HStack(spacing: 8) {
            // 应用按钮
            Button(action: onTap) {
                HStack(spacing: 4) {
                    if isSelected {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.caption)
                    }
                    Text(isSelected ? "当前主题" : "应用主题")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                }
                .foregroundColor(isSelected ? .white : .white)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.green : Color.blue)
                )
            }
            .disabled(isSelected)

            Spacer()

            // 更多操作按钮
            Button(action: {
                showingActionSheet = true
            }) {
                Image(systemName: "ellipsis")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .padding(8)
                    .background(Color(.systemGray5))
                    .clipShape(Circle())
            }
        }
    }

    // MARK: - 辅助方法
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateStyle = .short
        formatter.timeStyle = .none
        return formatter.string(from: date)
    }
}

// MARK: - 空主题卡片
struct EmptyThemeCard: View {
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 16) {
                Image(systemName: "plus.circle.dashed")
                    .font(.system(size: 40))
                    .foregroundColor(.blue)

                VStack(spacing: 4) {
                    Text("创建高级主题")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("开始个性化定制您的键盘")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                }
            }
            .frame(maxWidth: .infinity)
            .frame(height: 200)
            .background(Color(.systemGray6))
            .cornerRadius(16)
            .overlay(
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.blue, style: StrokeStyle(lineWidth: 2, dash: [8]))
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 空状态视图
struct EmptyStateView: View {
    let icon: String
    let title: String
    let description: String
    let actionTitle: String
    let action: () -> Void

    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: icon)
                .font(.system(size: 50))
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                Text(title)
                    .font(.headline)
                    .fontWeight(.semibold)

                Text(description)
                    .font(.subheadline)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }

            Button(action: action) {
                Text(actionTitle)
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.white)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(Color.blue)
                    .cornerRadius(20)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 40)
    }
}

// MARK: - 主题统计卡片
struct ThemeStatsCard: View {
    let theme: AdvancedKeyboardTheme

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("主题统计")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                StatItem(
                    title: "按键类型",
                    value: "\(theme.keyTypeConfigs.count)",
                    icon: "keyboard",
                    color: .blue
                )

                StatItem(
                    title: "自定义按键",
                    value: "\(theme.individualKeyConfigs.count)",
                    icon: "key.fill",
                    color: .green
                )

                StatItem(
                    title: "动画效果",
                    value: theme.enableKeyAnimations ? "开启" : "关闭",
                    icon: "sparkles",
                    color: .orange
                )

                StatItem(
                    title: "触觉反馈",
                    value: theme.enableHapticFeedback ? "开启" : "关闭",
                    icon: "hand.tap",
                    color: .purple
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 统计项目
struct StatItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color

    var body: some View {
        VStack(spacing: 8) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundColor(color)

            VStack(spacing: 2) {
                Text(value)
                    .font(.headline)
                    .fontWeight(.bold)

                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .frame(maxWidth: .infinity)
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
    }
}

// MARK: - 预览
struct AdvancedThemeCardViews_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            AdvancedThemeCard(
                theme: AdvancedKeyboardTheme(
                    name: "我的自定义主题",
                    description: "个性化键盘主题",
                    baseTheme: KeyboardTheme.defaultThemes[0]
                ),
                isSelected: true,
                onTap: { },
                onEdit: { },
                onDuplicate: { },
                onDelete: { }
            )

            EmptyThemeCard { }
        }
        .padding()
    }
}
