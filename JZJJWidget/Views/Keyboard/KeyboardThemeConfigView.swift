//
//  KeyboardThemeConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 键盘主题配置视图
struct KeyboardThemeConfigView: View {
    // MARK: - 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态
    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared
    @State private var selectedTheme: KeyboardTheme
    @State private var showingCustomThemeEditor = false
    @State private var showingImageThemeConfig = false
    @State private var showingAdvancedThemeConfig = false
    @State private var showingThemePackSelection = false
    @State private var showingPreview = false
    @State private var showSuccessToast = false

    // 自定义主题编辑状态
    @State private var customThemeName = ""
    @State private var customThemeType: KeyboardThemeType = .custom
    @State private var customKeyStyle: KeyboardKeyStyle = .rounded
    @State private var customBackgroundColor = Color.white
    @State private var customKeyColor = Color.gray.opacity(0.3)
    @State private var customTextColor = Color.black

    // 布局
    private let columns = [
        GridItem(.adaptive(minimum: 150, maximum: 200), spacing: AppLayout.Spacing.medium)
    ]

    // 初始化
    init() {
        self._selectedTheme = State(initialValue: KeyboardThemeManager.shared.currentTheme)
    }

    // MARK: - 视图主体
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 当前主题预览
                    currentThemePreviewSection

                    // 预设主题选择
                    presetThemesSection

                    // 自定义主题
                    customThemeSection

                    // 图片主题
                    imageThemeSection

                    // 高级主题定制
                    advancedThemeSection

                    // 内置主题包
                    themePackSection

                    // 键盘设置指南
                    settingsGuideSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .navigationTitle("键盘主题")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveTheme()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingCustomThemeEditor) {
            customThemeEditorView
        }
        .sheet(isPresented: $showingImageThemeConfig) {
            ImageThemeConfigView()
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showingAdvancedThemeConfig) {
            AdvancedThemeConfigView()
                .environmentObject(themeManager)
        }
        .sheet(isPresented: $showingThemePackSelection) {
            ThemePackSelectionView()
        }
        .sheet(isPresented: $showingPreview) {
            keyboardPreviewView
        }
        .overlay(
            // 成功提示
            Group {
                if showSuccessToast {
                    VStack {
                        Spacer()
                        HStack {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.green)
                            Text("主题已保存")
                                .fontWeight(.medium)
                        }
                        .padding()
                        .background(Color(.systemBackground))
                        .cornerRadius(12)
                        .shadow(radius: 8)
                        .padding(.bottom, 50)
                    }
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
        )
    }

    // MARK: - 当前主题预览
    private var currentThemePreviewSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("当前主题")
                .font(.headline)
                .fontWeight(.semibold)

            KeyboardPreviewCard(theme: selectedTheme) {
                showingPreview = true
            }
        }
    }

    // MARK: - 预设主题选择
    private var presetThemesSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("预设主题")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: columns, spacing: AppLayout.Spacing.medium) {
                ForEach(KeyboardTheme.defaultThemes, id: \.id) { theme in
                    ThemeSelectionCard(
                        theme: theme,
                        isSelected: selectedTheme.id == theme.id
                    ) {
                        selectedTheme = theme
                    }
                }
            }
        }
    }

    // MARK: - 自定义主题
    private var customThemeSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("自定义主题")
                .font(.headline)
                .fontWeight(.semibold)

            Button(action: {
                showingCustomThemeEditor = true
            }) {
                HStack {
                    Image(systemName: "plus.circle.fill")
                        .font(.title2)

                    VStack(alignment: .leading) {
                        Text("创建自定义主题")
                            .fontWeight(.medium)
                        Text("个性化你的键盘外观")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    // MARK: - 图片主题
    private var imageThemeSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("图片主题")
                .font(.headline)
                .fontWeight(.semibold)

            Button(action: {
                showingImageThemeConfig = true
            }) {
                HStack {
                    Image(systemName: "photo.fill")
                        .font(.title2)
                        .foregroundColor(.blue)

                    VStack(alignment: .leading) {
                        Text("图片主题配置")
                            .fontWeight(.medium)
                        Text("使用自定义图片作为键盘背景")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    // MARK: - 高级主题定制
    private var advancedThemeSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("高级主题定制")
                .font(.headline)
                .fontWeight(.semibold)

            Button(action: {
                showingAdvancedThemeConfig = true
            }) {
                HStack {
                    Image(systemName: "slider.horizontal.3")
                        .font(.title2)
                        .foregroundColor(.purple)

                    VStack(alignment: .leading) {
                        Text("按键级别定制")
                            .fontWeight(.medium)
                        Text("为每个按键设置独特的颜色和样式")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 2) {
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)

                        Text("高级")
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(.purple)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.purple.opacity(0.1))
                            .cornerRadius(4)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    // MARK: - 内置主题包
    private var themePackSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("内置主题包")
                .font(.headline)
                .fontWeight(.semibold)

            Button(action: {
                showingThemePackSelection = true
            }) {
                HStack {
                    Image(systemName: "paintbrush.pointed.fill")
                        .font(.title2)
                        .foregroundColor(.orange)

                    VStack(alignment: .leading) {
                        Text("精美主题包")
                            .fontWeight(.medium)
                        Text("经典、科技、自然等多种风格主题")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    VStack(alignment: .trailing, spacing: 2) {
                        Image(systemName: "chevron.right")
                            .foregroundColor(.secondary)

                        Text("推荐")
                            .font(.caption2)
                            .fontWeight(.medium)
                            .foregroundColor(.orange)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.orange.opacity(0.1))
                            .cornerRadius(4)
                    }
                }
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
        }
    }

    // MARK: - 设置指南
    private var settingsGuideSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("使用指南")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                GuideStepView(
                    step: "1",
                    title: "启用键盘",
                    description: "前往 设置 > 通用 > 键盘 > 键盘，添加新键盘",
                    theme: themeManager.currentTheme
                )

                GuideStepView(
                    step: "2",
                    title: "选择键盘",
                    description: "在键盘列表中选择 \"JZJJWidget键盘\"",
                    theme: themeManager.currentTheme
                )

                GuideStepView(
                    step: "3",
                    title: "允许完全访问",
                    description: "开启完全访问权限以使用自定义主题",
                    theme: themeManager.currentTheme
                )

                GuideStepView(
                    step: "4",
                    title: "切换键盘",
                    description: "在任何输入框中长按地球图标切换到自定义键盘",
                    theme: themeManager.currentTheme
                )
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 自定义主题编辑器
    private var customThemeEditorView: some View {
        NavigationView {
            CustomKeyboardThemeEditorView(
                themeName: $customThemeName,
                themeType: $customThemeType,
                keyStyle: $customKeyStyle,
                backgroundColor: $customBackgroundColor,
                keyColor: $customKeyColor,
                textColor: $customTextColor
            ) { customTheme in
                selectedTheme = customTheme
                keyboardThemeManager.addCustomTheme(customTheme)
                showingCustomThemeEditor = false
            }
        }
    }

    // MARK: - 键盘预览
    private var keyboardPreviewView: some View {
        NavigationView {
            KeyboardPreviewView(theme: selectedTheme)
                .navigationTitle("键盘预览")
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button("完成") {
                            showingPreview = false
                        }
                    }
                }
        }
    }

    // MARK: - 保存主题
    private func saveTheme() {
        keyboardThemeManager.setTheme(selectedTheme)

        // 显示成功提示
        withAnimation(.spring()) {
            showSuccessToast = true
        }

        // 2秒后隐藏提示并关闭页面
        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
            withAnimation(.spring()) {
                showSuccessToast = false
            }

            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                dismiss()
            }
        }
    }
}

// MARK: - 主题选择卡片
struct ThemeSelectionCard: View {
    let theme: KeyboardTheme
    let isSelected: Bool
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                // 主题预览
                RoundedRectangle(cornerRadius: 8)
                    .fill(theme.backgroundColor.color)
                    .frame(height: 80)
                    .overlay(
                        VStack(spacing: 4) {
                            HStack(spacing: 4) {
                                ForEach(0..<3) { _ in
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(theme.keyBackgroundColor.color)
                                        .frame(width: 20, height: 16)
                                }
                            }
                            HStack(spacing: 4) {
                                ForEach(0..<2) { _ in
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(theme.keyBackgroundColor.color)
                                        .frame(width: 20, height: 16)
                                }
                            }
                        }
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                    )

                // 主题信息
                VStack(spacing: 2) {
                    Text(theme.name)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(1)

                    Text(theme.type.name)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
struct KeyboardThemeConfigView_Previews: PreviewProvider {
    static var previews: some View {
        KeyboardThemeConfigView()
            .environmentObject(ThemeManager.shared)
    }
}
