//
//  IndividualKeyConfigEditorView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 单个按键配置编辑器
struct IndividualKeyConfigEditorView: View {
    // MARK: - 属性
    let theme: AdvancedKeyboardTheme
    @State var keyConfig: KeyConfig

    // MARK: - 环境对象
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理
    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @State private var hasChanges = false
    @State private var showingDiscardAlert = false

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 按键信息
                    keyInfoSection

                    // 实时预览
                    previewSection

                    // 颜色配置
                    colorConfigSection

                    // 字体配置
                    fontConfigSection

                    // 视觉效果配置
                    visualEffectsSection

                    // 高级设置
                    advancedSettingsSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .navigationTitle("按键配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        if hasChanges {
                            showingDiscardAlert = true
                        } else {
                            dismiss()
                        }
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveChanges()
                    }
                    .fontWeight(.semibold)
                    .disabled(!hasChanges)
                }
            }
        }
        .alert("放弃更改", isPresented: $showingDiscardAlert) {
            Button("取消", role: .cancel) { }
            Button("放弃", role: .destructive) {
                dismiss()
            }
        } message: {
            Text("您有未保存的更改，确定要放弃吗？")
        }
        .onChange(of: keyConfig) { _ in
            hasChanges = true
        }
    }

    // MARK: - 按键信息
    private var keyInfoSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Image(systemName: keyConfig.keyType.icon)
                    .font(.title2)
                    .foregroundColor(.blue)

                VStack(alignment: .leading, spacing: 4) {
                    Text("按键：\(keyConfig.keyValue)")
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("类型：\(keyConfig.keyType.displayName)")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 实时预览
    private var previewSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("实时预览")
                .font(.headline)
                .fontWeight(.semibold)

            HStack {
                Spacer()

                VStack(spacing: 8) {
                    // 正常状态
                    IndividualKeyPreviewButton(
                        text: keyConfig.keyValue == "空格" ? "空格" : keyConfig.keyValue,
                        backgroundColor: keyConfig.backgroundColor,
                        textColor: keyConfig.textColor,
                        borderColor: keyConfig.borderColor,
                        fontSize: keyConfig.fontSize,
                        fontWeight: keyConfig.fontWeight,
                        cornerRadius: keyConfig.cornerRadius,
                        borderWidth: keyConfig.borderWidth,
                        width: keyConfig.keyType == .space ? 120 : 60,
                        height: 44
                    )

                    Text("正常状态")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(spacing: 8) {
                    // 按下状态
                    IndividualKeyPreviewButton(
                        text: keyConfig.keyValue == "空格" ? "空格" : keyConfig.keyValue,
                        backgroundColor: keyConfig.pressedColor,
                        textColor: keyConfig.textColor,
                        borderColor: keyConfig.borderColor,
                        fontSize: keyConfig.fontSize,
                        fontWeight: keyConfig.fontWeight,
                        cornerRadius: keyConfig.cornerRadius,
                        borderWidth: keyConfig.borderWidth,
                        width: keyConfig.keyType == .space ? 120 : 60,
                        height: 44
                    )

                    Text("按下状态")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 颜色配置
    private var colorConfigSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("颜色配置")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                AdvancedColorPicker(
                    title: "背景颜色",
                    selectedColor: $keyConfig.backgroundColor
                )

                AdvancedColorPicker(
                    title: "按下颜色",
                    selectedColor: $keyConfig.pressedColor
                )

                AdvancedColorPicker(
                    title: "文字颜色",
                    selectedColor: $keyConfig.textColor
                )

                AdvancedColorPicker(
                    title: "边框颜色",
                    selectedColor: $keyConfig.borderColor
                )
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 字体配置
    private var fontConfigSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("字体配置")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                // 字体大小
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("字体大小")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(keyConfig.fontSize))pt")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }

                    Slider(value: $keyConfig.fontSize, in: 10...24, step: 1) {
                        Text("字体大小")
                    } minimumValueLabel: {
                        Text("10")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } maximumValueLabel: {
                        Text("24")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // 字体粗细
                VStack(alignment: .leading, spacing: 8) {
                    Text("字体粗细")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Picker("字体粗细", selection: $keyConfig.fontWeight) {
                        ForEach(FontWeight.allCases, id: \.self) { weight in
                            Text(weight.displayName).tag(weight)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 视觉效果配置
    private var visualEffectsSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("视觉效果")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                // 圆角半径
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("圆角半径")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(keyConfig.cornerRadius))pt")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }

                    Slider(value: $keyConfig.cornerRadius, in: 0...20, step: 1) {
                        Text("圆角半径")
                    } minimumValueLabel: {
                        Text("0")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } maximumValueLabel: {
                        Text("20")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // 边框宽度
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("边框宽度")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(keyConfig.borderWidth))pt")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }

                    Slider(value: $keyConfig.borderWidth, in: 0...5, step: 0.5) {
                        Text("边框宽度")
                    } minimumValueLabel: {
                        Text("0")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } maximumValueLabel: {
                        Text("5")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 高级设置
    private var advancedSettingsSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("高级设置")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.small) {
                AdvancedSettingButton(
                    title: "应用到同类型",
                    description: "将配置应用到所有\(keyConfig.keyType.displayName)按键",
                    icon: "square.grid.3x3.fill"
                ) {
                    applyToSameType()
                }

                AdvancedSettingButton(
                    title: "重置为类型配置",
                    description: "恢复到按键类型的默认配置",
                    icon: "arrow.clockwise"
                ) {
                    resetToTypeConfig()
                }

                AdvancedSettingButton(
                    title: "移除自定义配置",
                    description: "删除此按键的个性化配置",
                    icon: "trash"
                ) {
                    removeCustomConfig()
                }
            }
        }
    }

    // MARK: - 辅助方法
    private func saveChanges() {
        advancedThemeManager.updateIndividualKeyConfig(
            for: keyConfig.keyValue,
            in: theme,
            config: keyConfig
        )
        hasChanges = false
        dismiss()
    }

    private func applyToSameType() {
        // 将当前配置转换为类型配置
        let typeConfig = KeyTypeConfig(
            keyType: keyConfig.keyType,
            defaultBackgroundColor: keyConfig.backgroundColor,
            defaultPressedColor: keyConfig.pressedColor,
            defaultTextColor: keyConfig.textColor,
            defaultBorderColor: keyConfig.borderColor,
            defaultFontSize: keyConfig.fontSize,
            defaultFontWeight: keyConfig.fontWeight,
            defaultCornerRadius: keyConfig.cornerRadius,
            defaultBorderWidth: keyConfig.borderWidth
        )

        advancedThemeManager.applyConfigToKeyType(
            keyType: keyConfig.keyType,
            in: theme,
            config: typeConfig
        )
    }

    private func resetToTypeConfig() {
        if let typeConfig = theme.keyTypeConfigs[keyConfig.keyType] {
            keyConfig = KeyConfig(
                keyType: keyConfig.keyType,
                keyValue: keyConfig.keyValue,
                backgroundColor: typeConfig.defaultBackgroundColor,
                pressedColor: typeConfig.defaultPressedColor,
                textColor: typeConfig.defaultTextColor,
                borderColor: typeConfig.defaultBorderColor,
                fontSize: typeConfig.defaultFontSize,
                fontWeight: typeConfig.defaultFontWeight,
                cornerRadius: typeConfig.defaultCornerRadius,
                borderWidth: typeConfig.defaultBorderWidth
            )
        } else {
            keyConfig = KeyConfig(keyType: keyConfig.keyType, keyValue: keyConfig.keyValue)
        }
    }

    private func removeCustomConfig() {
        advancedThemeManager.removeIndividualKeyConfig(
            for: keyConfig.keyValue,
            in: theme
        )
        dismiss()
    }
}

// MARK: - 单个按键预览按钮
struct IndividualKeyPreviewButton: View {
    let text: String
    let backgroundColor: WidgetColor
    let textColor: WidgetColor
    let borderColor: WidgetColor
    let fontSize: Double
    let fontWeight: FontWeight
    let cornerRadius: Double
    let borderWidth: Double
    let width: CGFloat
    let height: CGFloat

    var body: some View {
        Text(text)
            .font(.system(size: CGFloat(fontSize), weight: fontWeight.fontWeight))
            .foregroundColor(textColor.color)
            .frame(width: width, height: height)
            .background(backgroundColor.color)
            .cornerRadius(CGFloat(cornerRadius))
            .overlay(
                RoundedRectangle(cornerRadius: CGFloat(cornerRadius))
                    .stroke(borderColor.color, lineWidth: CGFloat(borderWidth))
            )
    }
}

// MARK: - 预览
struct IndividualKeyConfigEditorView_Previews: PreviewProvider {
    static var previews: some View {
        IndividualKeyConfigEditorView(
            theme: AdvancedKeyboardTheme(
                name: "测试主题",
                baseTheme: KeyboardTheme.defaultThemes[0]
            ),
            keyConfig: KeyConfig(keyType: .letter, keyValue: "A")
        )
    }
}
