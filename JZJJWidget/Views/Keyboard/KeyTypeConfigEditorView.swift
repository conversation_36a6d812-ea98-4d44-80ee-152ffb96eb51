//
//  KeyTypeConfigEditorView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 按键类型配置编辑器
struct KeyTypeConfigEditorView: View {
    // MARK: - 属性
    let theme: AdvancedKeyboardTheme
    let keyType: KeyType
    @State var config: KeyTypeConfig

    // MARK: - 环境对象
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理
    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @State private var hasChanges = false
    @State private var showingDiscardAlert = false
    @State private var showingSuccessToast = false
    @State private var successMessage = ""

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 实时预览
                    previewSection

                    // 颜色配置
                    colorConfigSection

                    // 字体配置
                    fontConfigSection

                    // 视觉效果配置
                    visualEffectsSection

                    // 高级设置
                    advancedSettingsSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .navigationTitle("\(keyType.displayName)配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        if hasChanges {
                            showingDiscardAlert = true
                        } else {
                            dismiss()
                        }
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveChanges()
                    }
                    .fontWeight(.semibold)
                    .disabled(!hasChanges)
                }
            }
        }
        .alert("放弃更改", isPresented: $showingDiscardAlert) {
            Button("取消", role: .cancel) { }
            Button("放弃", role: .destructive) {
                dismiss()
            }
        } message: {
            Text("您有未保存的更改，确定要放弃吗？")
        }
        .onChange(of: config) { _ in
            hasChanges = true
        }
        .overlay(
            // 成功提示Toast
            VStack {
                Spacer()
                if showingSuccessToast {
                    SuccessToast(message: successMessage, theme: ThemeManager.shared.currentTheme)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                        .onAppear {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                                withAnimation {
                                    showingSuccessToast = false
                                }
                            }
                        }
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showingSuccessToast)
        )
    }

    // MARK: - 实时预览
    private var previewSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("实时预览")
                .font(.headline)
                .fontWeight(.semibold)

            // 单个按键预览
            HStack {
                Spacer()

                VStack(spacing: 8) {
                    // 正常状态
                    ConfigKeyPreviewButton(
                        text: keyType == .space ? "空格" : (config.affectedKeys.first ?? "A"),
                        backgroundColor: config.defaultBackgroundColor,
                        textColor: config.defaultTextColor,
                        borderColor: config.defaultBorderColor,
                        fontSize: config.defaultFontSize,
                        fontWeight: config.defaultFontWeight,
                        cornerRadius: config.defaultCornerRadius,
                        borderWidth: config.defaultBorderWidth,
                        width: keyType == .space ? 120 : 60,
                        height: 44
                    )

                    Text("正常状态")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                VStack(spacing: 8) {
                    // 按下状态
                    ConfigKeyPreviewButton(
                        text: keyType == .space ? "空格" : (config.affectedKeys.first ?? "A"),
                        backgroundColor: config.defaultPressedColor,
                        textColor: config.defaultTextColor,
                        borderColor: config.defaultBorderColor,
                        fontSize: config.defaultFontSize,
                        fontWeight: config.defaultFontWeight,
                        cornerRadius: config.defaultCornerRadius,
                        borderWidth: config.defaultBorderWidth,
                        width: keyType == .space ? 120 : 60,
                        height: 44
                    )

                    Text("按下状态")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 颜色配置
    private var colorConfigSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("颜色配置")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                AdvancedColorPicker(
                    title: "背景颜色",
                    selectedColor: $config.defaultBackgroundColor
                )

                AdvancedColorPicker(
                    title: "按下颜色",
                    selectedColor: $config.defaultPressedColor
                )

                AdvancedColorPicker(
                    title: "文字颜色",
                    selectedColor: $config.defaultTextColor
                )

                AdvancedColorPicker(
                    title: "边框颜色",
                    selectedColor: $config.defaultBorderColor
                )
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 字体配置
    private var fontConfigSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("字体配置")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                // 字体大小
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("字体大小")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(config.defaultFontSize))pt")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }

                    Slider(value: $config.defaultFontSize, in: 10...24, step: 1) {
                        Text("字体大小")
                    } minimumValueLabel: {
                        Text("10")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } maximumValueLabel: {
                        Text("24")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // 字体粗细
                VStack(alignment: .leading, spacing: 8) {
                    Text("字体粗细")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    Picker("字体粗细", selection: $config.defaultFontWeight) {
                        ForEach(FontWeight.allCases, id: \.self) { weight in
                            Text(weight.displayName).tag(weight)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 视觉效果配置
    private var visualEffectsSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("视觉效果")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                // 圆角半径
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("圆角半径")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(config.defaultCornerRadius))pt")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }

                    Slider(value: $config.defaultCornerRadius, in: 0...20, step: 1) {
                        Text("圆角半径")
                    } minimumValueLabel: {
                        Text("0")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } maximumValueLabel: {
                        Text("20")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }

                // 边框宽度
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("边框宽度")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Spacer()

                        Text("\(Int(config.defaultBorderWidth))pt")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }

                    Slider(value: $config.defaultBorderWidth, in: 0...5, step: 0.5) {
                        Text("边框宽度")
                    } minimumValueLabel: {
                        Text("0")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    } maximumValueLabel: {
                        Text("5")
                            .font(.caption2)
                            .foregroundColor(.secondary)
                    }
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 高级设置
    private var advancedSettingsSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("高级设置")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.small) {
                AdvancedSettingButton(
                    title: "应用到所有按键",
                    description: "将当前配置应用到该类型的所有按键",
                    icon: "square.grid.3x3.fill"
                ) {
                    applyToAllKeys()
                }

                AdvancedSettingButton(
                    title: "复制到其他类型",
                    description: "将配置复制到其他按键类型",
                    icon: "doc.on.doc"
                ) {
                    copyToOtherTypes()
                }

                AdvancedSettingButton(
                    title: "重置为默认",
                    description: "恢复到系统默认配置",
                    icon: "arrow.clockwise"
                ) {
                    resetToDefault()
                }
            }
        }
    }

    // MARK: - 辅助方法
    private func saveChanges() {
        advancedThemeManager.updateKeyTypeConfig(
            for: keyType,
            in: theme,
            config: config
        )
        hasChanges = false

        // 显示成功提示
        successMessage = "「\(keyType.displayName)」配置已保存"
        showingSuccessToast = true

        // 延迟关闭界面
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            dismiss()
        }
    }

    private func applyToAllKeys() {
        advancedThemeManager.applyConfigToKeyType(
            keyType: keyType,
            in: theme,
            config: config
        )
    }

    private func copyToOtherTypes() {
        // 实现复制到其他类型的逻辑
    }

    private func resetToDefault() {
        config = KeyTypeConfig(keyType: keyType)
    }
}

// MARK: - 按键预览按钮
struct ConfigKeyPreviewButton: View {
    let text: String
    let backgroundColor: WidgetColor
    let textColor: WidgetColor
    let borderColor: WidgetColor
    let fontSize: Double
    let fontWeight: FontWeight
    let cornerRadius: Double
    let borderWidth: Double
    let width: CGFloat
    let height: CGFloat

    var body: some View {
        Text(text)
            .font(.system(size: CGFloat(fontSize), weight: fontWeight.fontWeight))
            .foregroundColor(textColor.color)
            .frame(width: width, height: height)
            .background(backgroundColor.color)
            .cornerRadius(CGFloat(cornerRadius))
            .overlay(
                RoundedRectangle(cornerRadius: CGFloat(cornerRadius))
                    .stroke(borderColor.color, lineWidth: CGFloat(borderWidth))
            )
    }
}

// MARK: - 高级设置按钮
struct AdvancedSettingButton: View {
    let title: String
    let description: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.blue)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
struct KeyTypeConfigEditorView_Previews: PreviewProvider {
    static var previews: some View {
        KeyTypeConfigEditorView(
            theme: AdvancedKeyboardTheme(
                name: "测试主题",
                baseTheme: KeyboardTheme.defaultThemes[0]
            ),
            keyType: .letter,
            config: KeyTypeConfig(keyType: .letter)
        )
    }
}
