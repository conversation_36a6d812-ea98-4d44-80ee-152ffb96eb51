//
//  ImportExportView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import UniformTypeIdentifiers
import MyWidgetKit

/// 导入导出视图
struct ImportExportView: View {
    // MARK: - 环境对象
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理
    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @State private var showingDocumentPicker = false
    @State private var showingShareSheet = false
    @State private var showingAlert = false
    @State private var alertMessage = ""
    @State private var selectedThemeForExport: AdvancedKeyboardTheme?
    @State private var exportData: Data?

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 导出部分
                    exportSection

                    // 导入部分
                    importSection

                    // 使用说明
                    instructionsSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .navigationTitle("导入导出")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }
            }
        }
        .fileImporter(
            isPresented: $showingDocumentPicker,
            allowedContentTypes: [UTType.json],
            allowsMultipleSelection: false
        ) { result in
            handleImport(result: result)
        }
        .sheet(isPresented: $showingShareSheet) {
            if let data = exportData {
                ShareSheet(items: [data])
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
    }

    // MARK: - 导出部分
    private var exportSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Image(systemName: "square.and.arrow.up")
                    .font(.title2)
                    .foregroundColor(.blue)

                Text("导出主题")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            Text("将您的高级主题配置导出为文件，可以分享给其他人或作为备份")
                .font(.subheadline)
                .foregroundColor(.secondary)

            if advancedThemeManager.availableAdvancedThemes.isEmpty {
                EmptyExportCard()
            } else {
                LazyVGrid(columns: [
                    GridItem(.flexible()),
                    GridItem(.flexible())
                ], spacing: AppLayout.Spacing.medium) {
                    ForEach(advancedThemeManager.availableAdvancedThemes) { theme in
                        ExportThemeCard(theme: theme) {
                            exportTheme(theme)
                        }
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 导入部分
    private var importSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Image(systemName: "square.and.arrow.down")
                    .font(.title2)
                    .foregroundColor(.green)

                Text("导入主题")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            Text("从文件导入高级主题配置，支持JSON格式的主题文件")
                .font(.subheadline)
                .foregroundColor(.secondary)

            Button(action: {
                showingDocumentPicker = true
            }) {
                HStack {
                    Image(systemName: "doc.badge.plus")
                        .font(.title3)

                    VStack(alignment: .leading, spacing: 4) {
                        Text("选择主题文件")
                            .font(.subheadline)
                            .fontWeight(.medium)

                        Text("支持 .json 格式")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 使用说明
    private var instructionsSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Image(systemName: "info.circle")
                    .font(.title2)
                    .foregroundColor(.orange)

                Text("使用说明")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            VStack(alignment: .leading, spacing: 12) {
                InstructionItem(
                    icon: "1.circle.fill",
                    title: "导出主题",
                    description: "选择要导出的主题，生成JSON配置文件"
                )

                InstructionItem(
                    icon: "2.circle.fill",
                    title: "分享文件",
                    description: "通过AirDrop、邮件或其他方式分享主题文件"
                )

                InstructionItem(
                    icon: "3.circle.fill",
                    title: "导入主题",
                    description: "在目标设备上选择主题文件进行导入"
                )

                InstructionItem(
                    icon: "4.circle.fill",
                    title: "应用主题",
                    description: "导入成功后即可在主题列表中找到并应用"
                )
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 辅助方法
    private func exportTheme(_ theme: AdvancedKeyboardTheme) {
        guard let data = advancedThemeManager.exportTheme(theme) else {
            alertMessage = "导出失败，请重试"
            showingAlert = true
            return
        }

        exportData = data
        showingShareSheet = true
    }

    private func handleImport(result: Result<[URL], Error>) {
        switch result {
        case .success(let urls):
            guard let url = urls.first else { return }

            do {
                let data = try Data(contentsOf: url)
                if let theme = advancedThemeManager.importTheme(from: data) {
                    alertMessage = "主题\"\(theme.name)\"导入成功！"
                    showingAlert = true
                } else {
                    alertMessage = "导入失败，文件格式不正确"
                    showingAlert = true
                }
            } catch {
                alertMessage = "读取文件失败：\(error.localizedDescription)"
                showingAlert = true
            }

        case .failure(let error):
            alertMessage = "选择文件失败：\(error.localizedDescription)"
            showingAlert = true
        }
    }
}

// MARK: - 导出主题卡片
struct ExportThemeCard: View {
    let theme: AdvancedKeyboardTheme
    let onExport: () -> Void

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(theme.name)
                .font(.subheadline)
                .fontWeight(.medium)
                .lineLimit(1)

            Text("\(theme.keyTypeConfigs.count) 类型配置")
                .font(.caption)
                .foregroundColor(.secondary)

            Button(action: onExport) {
                Text("导出")
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 6)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(6)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
}

// MARK: - 空导出卡片
struct EmptyExportCard: View {
    var body: some View {
        VStack(spacing: 12) {
            Image(systemName: "tray")
                .font(.system(size: 30))
                .foregroundColor(.secondary)

            Text("暂无可导出的主题")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(.secondary)

            Text("创建高级主题后即可导出")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 30)
        .background(Color(.systemBackground))
        .cornerRadius(8)
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
}

// MARK: - 说明项目
struct InstructionItem: View {
    let icon: String
    let title: String
    let description: String

    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: icon)
                .font(.title3)
                .foregroundColor(.orange)
                .frame(width: 24)

            VStack(alignment: .leading, spacing: 2) {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text(description)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Spacer()
        }
    }
}

// MARK: - 分享表单
struct ShareSheet: UIViewControllerRepresentable {
    let items: [Any]

    func makeUIViewController(context: Context) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: items, applicationActivities: nil)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: Context) {}
}

// MARK: - 预览
struct ImportExportView_Previews: PreviewProvider {
    static var previews: some View {
        ImportExportView()
    }
}
