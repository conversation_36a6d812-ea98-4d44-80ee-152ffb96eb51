//
//  ThemePackSelectionView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 主题包选择视图
struct ThemePackSelectionView: View {

    // MARK: - 状态管理
    @StateObject private var themePackManager = ThemePackManager.shared
    @State private var selectedCategory: String = "all"
    @State private var searchText: String = ""
    @State private var showingApplyConfirmation = false
    @State private var selectedThemePackId: String?
    @State private var showingErrorAlert = false
    @State private var errorMessage: String = ""
    @State private var showingImagePreview = false
    @State private var selectedThemePackForPreview: ThemePackMetadata?

    // MARK: - 计算属性
    private var categories: [String] {
        let allCategories = themePackManager.availableThemePacks.map { $0.category }
        let uniqueCategories = Array(Set(allCategories)).sorted()
        return ["all"] + uniqueCategories
    }

    private var filteredThemePacks: [ThemePackMetadata] {
        var packs = themePackManager.availableThemePacks

        // 按类别过滤
        if selectedCategory != "all" {
            packs = packs.filter { $0.category == selectedCategory }
        }

        // 按搜索文本过滤
        if !searchText.isEmpty {
            packs = packs.filter { pack in
                pack.name.localizedCaseInsensitiveContains(searchText) ||
                pack.description.localizedCaseInsensitiveContains(searchText) ||
                pack.tags.contains { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }

        return packs
    }

    private var groupedThemePacks: [String: [ThemePackMetadata]] {
        Dictionary(grouping: filteredThemePacks) { $0.category }
    }

    // MARK: - 视图主体
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                searchBar

                // 类别选择器
                categorySelector

                // 主题包列表
                themePackList
            }
            .navigationTitle("内置主题包")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    refreshButton
                }
            }
        }
        .alert("应用主题包", isPresented: $showingApplyConfirmation) {
            Button("取消", role: .cancel) { }
            Button("应用") {
                applySelectedThemePack()
            }
        } message: {
            if let packId = selectedThemePackId,
               let pack = themePackManager.availableThemePacks.first(where: { $0.id == packId }) {
                Text("确定要应用主题包「\(pack.name)」吗？这将替换当前的键盘主题配置。")
            }
        }
        .alert("错误", isPresented: $showingErrorAlert) {
            Button("确定") { }
        } message: {
            Text(errorMessage)
        }
        .sheet(isPresented: $showingImagePreview) {
            if let themePack = selectedThemePackForPreview {
                NavigationView {
                    ThemePackImagePreviewView(
                        themePackId: themePack.id,
                        themePack: ThemePackBundle(
                            metadata: themePack,
                            config: ThemePackConfig(
                                schemaVersion: "1.0.0",
                                themeInfo: ThemeInfo(
                                    id: themePack.id,
                                    name: themePack.name,
                                    description: themePack.description
                                ),
                                baseTheme: BaseThemeConfig(
                                    id: "\(themePack.id)-base",
                                    name: "\(themePack.name)基础",
                                    type: themePack.style,
                                    keyStyle: "rounded",
                                    colors: ColorConfig(
                                        background: WidgetColor(red: 0.96, green: 0.96, blue: 0.97, alpha: 1.0),
                                        keyBackground: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
                                        keyPressed: WidgetColor(red: 0.86, green: 0.86, blue: 0.88, alpha: 1.0),
                                        text: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
                                        specialKey: WidgetColor(red: 0.68, green: 0.68, blue: 0.7, alpha: 1.0),
                                        border: WidgetColor(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0)
                                    ),
                                    images: ImageConfig(
                                        hasBackgroundImage: true,
                                        hasKeyImage: true,
                                        backgroundImagePath: "resources/backgrounds/keyboard-bg.png",
                                        isBuiltInImageTheme: true,
                                        imageOpacity: 0.8,
                                        imageBlendMode: "normal"
                                    ),
                                    typography: TypographyConfig(
                                        fontName: "SF Pro",
                                        fontSize: 16,
                                        fontWeight: "medium"
                                    ),
                                    layout: LayoutConfig(
                                        keySpacing: 6,
                                        keyHeight: 44,
                                        showBorder: true,
                                        borderWidth: 1
                                    ),
                                    effects: EffectConfig(
                                        enableShadow: true,
                                        shadowColor: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.2),
                                        shadowRadius: 2,
                                        enableHaptic: true,
                                        enableSound: true
                                    )
                                ),
                                advancedConfig: AdvancedThemeConfig(
                                    globalSettings: GlobalSettings(
                                        keySpacing: 6,
                                        keyHeight: 44,
                                        enableHapticFeedback: true,
                                        enableSoundFeedback: true,
                                        enableKeyAnimations: true,
                                        animationDuration: 0.1,
                                        enableGradientEffects: false,
                                        enableParallaxEffect: false
                                    ),
                                    keyTypeConfigs: [:],
                                    individualKeyConfigs: [:],
                                    createdAt: Date(),
                                    updatedAt: Date()
                                ),
                                validation: ValidationInfo(
                                    checksum: "temp",
                                    fileCount: 3,
                                    totalSize: 1024768
                                )
                            )
                        )
                    )
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Button("关闭") {
                                showingImagePreview = false
                            }
                        }
                    }
                }
            }
        }
        .task {
            if themePackManager.availableThemePacks.isEmpty {
                await themePackManager.initializeThemePacks()
            }
        }
    }

    // MARK: - 子视图

    private var searchBar: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)

            TextField("搜索主题包...", text: $searchText)
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
        .padding(.horizontal)
        .padding(.top, 8)
    }

    private var categorySelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(categories, id: \.self) { category in
                    CategoryChip(
                        title: categoryDisplayName(category),
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.vertical, 8)
    }

    private var themePackList: some View {
        Group {
            if themePackManager.isLoading {
                loadingView
            } else if filteredThemePacks.isEmpty {
                emptyView
            } else {
                ScrollView {
                    LazyVStack(spacing: 16) {
                        if selectedCategory == "all" {
                            // 按类别分组显示
                            ForEach(groupedThemePacks.keys.sorted(), id: \.self) { category in
                                categorySection(category: category, packs: groupedThemePacks[category] ?? [])
                            }
                        } else {
                            // 单类别显示
                            ForEach(filteredThemePacks, id: \.id) { pack in
                                ThemePackCard(
                                    themePack: pack,
                                    onTap: {
                                        selectThemePack(pack.id)
                                    },
                                    onPreview: {
                                        selectedThemePackForPreview = pack
                                        showingImagePreview = true
                                    }
                                )
                            }
                        }
                    }
                    .padding()
                }
            }
        }
    }

    private var loadingView: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)

            Text("正在加载主题包...")
                .font(.headline)
                .foregroundColor(.secondary)

            if themePackManager.loadingProgress > 0 {
                ProgressView(value: themePackManager.loadingProgress)
                    .frame(width: 200)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var emptyView: some View {
        VStack(spacing: 16) {
            Image(systemName: "paintbrush.fill")
                .font(.system(size: 48))
                .foregroundColor(.secondary)

            Text("未找到主题包")
                .font(.headline)
                .foregroundColor(.secondary)

            Text("请尝试调整搜索条件或检查网络连接")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }

    private var refreshButton: some View {
        Button {
            _Concurrency.Task {
                await themePackManager.refreshThemePacks()
            }
        } label: {
            Image(systemName: "arrow.clockwise")
        }
        .disabled(themePackManager.isLoading)
    }

    // MARK: - 辅助方法

    private func categorySection(category: String, packs: [ThemePackMetadata]) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text(categoryDisplayName(category))
                    .font(.title2)
                    .fontWeight(.semibold)

                Spacer()

                Text("\(packs.count)个主题")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            ForEach(packs, id: \.id) { pack in
                ThemePackCard(
                    themePack: pack,
                    onTap: {
                        selectThemePack(pack.id)
                    },
                    onPreview: {
                        selectedThemePackForPreview = pack
                        showingImagePreview = true
                    }
                )
            }
        }
    }

    private func categoryDisplayName(_ category: String) -> String {
        switch category {
        case "all": return "全部"
        case "classic": return "经典"
        case "colorful": return "彩色"
        case "tech": return "科技"
        case "nature": return "自然"
        case "minimal": return "极简"
        case "business": return "商务"
        default: return category.capitalized
        }
    }

    private func selectThemePack(_ id: String) {
        selectedThemePackId = id
        showingApplyConfirmation = true
    }

    private func applySelectedThemePack() {
        guard let packId = selectedThemePackId else { return }

        _Concurrency.Task {
            do {
                try await themePackManager.applyThemePack(id: packId)
                // 成功应用主题包
            } catch {
                errorMessage = error.localizedDescription
                showingErrorAlert = true
            }
        }
    }
}

// MARK: - 类别芯片视图

struct CategoryChip: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(isSelected ? Color.accentColor : Color.secondary.opacity(0.2))
                )
                .foregroundColor(isSelected ? .white : .primary)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 主题包卡片视图

struct ThemePackCard: View {
    let themePack: ThemePackMetadata
    let onTap: () -> Void
    let onPreview: (() -> Void)?

    @State private var previewImage: UIImage?
    @State private var isLoadingImage = false

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 16) {
                // 预览图
                Group {
                    if let image = previewImage {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                    } else if isLoadingImage {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.secondary.opacity(0.2))
                            .overlay(
                                ProgressView()
                                    .scaleEffect(0.8)
                            )
                    } else {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.secondary.opacity(0.3))
                            .overlay(
                                Image(systemName: "paintbrush.fill")
                                    .foregroundColor(.secondary)
                            )
                    }
                }
                .frame(width: 80, height: 60)
                .clipShape(RoundedRectangle(cornerRadius: 8))
                .task {
                    await loadPreviewImage()
                }

                // 主题信息
                VStack(alignment: .leading, spacing: 4) {
                    HStack {
                        Text(themePack.name)
                            .font(.headline)
                            .foregroundColor(.primary)

                        Spacer()

                        if themePack.isPremium {
                            Image(systemName: "crown.fill")
                                .foregroundColor(.orange)
                                .font(.caption)
                        }
                    }

                    Text(themePack.description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)

                    HStack {
                        // 标签
                        if !themePack.tags.isEmpty {
                            HStack(spacing: 4) {
                                ForEach(themePack.tags.prefix(2), id: \.self) { tag in
                                    Text(tag)
                                        .font(.caption2)
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(Color.accentColor.opacity(0.2))
                                        .foregroundColor(.accentColor)
                                        .clipShape(Capsule())
                                }
                            }
                        }

                        Spacer()

                        // 评分
                        if themePack.rating > 0 {
                            HStack(spacing: 2) {
                                Image(systemName: "star.fill")
                                    .foregroundColor(.yellow)
                                    .font(.caption)
                                Text(String(format: "%.1f", themePack.rating))
                                    .font(.caption)
                                    .foregroundColor(.secondary)
                            }
                        }
                    }
                }

                Spacer()

                // 操作按钮
                VStack(spacing: 8) {
                    if let onPreview = onPreview {
                        Button(action: onPreview) {
                            Image(systemName: "photo")
                                .foregroundColor(.blue)
                                .font(.caption)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }

                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                        .font(.caption)
                }
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color(.systemBackground))
                    .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }

    private func loadPreviewImage() async {
        guard previewImage == nil && !isLoadingImage else { return }

        await MainActor.run {
            isLoadingImage = true
        }

        let image = await ThemePackManager.shared.getPreviewImage(for: themePack.id)

        await MainActor.run {
            self.previewImage = image
            self.isLoadingImage = false
        }
    }
}

// MARK: - 预览

struct ThemePackSelectionView_Previews: PreviewProvider {
    static var previews: some View {
        ThemePackSelectionView()
    }
}
