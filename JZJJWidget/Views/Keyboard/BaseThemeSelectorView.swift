//
//  BaseThemeSelectorView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 基础主题选择器
struct BaseThemeSelectorView: View {
    @Binding var selectedTheme: KeyboardTheme
    @Environment(\.dismiss) private var dismiss
    
    // 布局配置
    private let columns = [
        GridItem(.adaptive(minimum: 150, maximum: 200), spacing: AppLayout.Spacing.medium)
    ]
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 说明文字
                    VStack(spacing: 8) {
                        Text("选择基础主题")
                            .font(.title2)
                            .fontWeight(.bold)
                        
                        Text("基础主题将作为高级定制的起点，您可以在此基础上进行详细的按键级别配置")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                            .multilineTextAlignment(.center)
                    }
                    .padding(.horizontal)
                    
                    // 默认主题
                    defaultThemesSection
                    
                    // 图片主题
                    imageThemesSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .navigationTitle("选择基础主题")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - 默认主题
    private var defaultThemesSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("默认主题")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: columns, spacing: AppLayout.Spacing.medium) {
                ForEach(KeyboardTheme.defaultThemes, id: \.id) { theme in
                    BaseThemeCard(
                        theme: theme,
                        isSelected: selectedTheme.id == theme.id
                    ) {
                        selectedTheme = theme
                    }
                }
            }
        }
    }
    
    // MARK: - 图片主题
    private var imageThemesSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("图片主题")
                .font(.headline)
                .fontWeight(.semibold)
            
            LazyVGrid(columns: columns, spacing: AppLayout.Spacing.medium) {
                ForEach(KeyboardTheme.builtInImageThemes, id: \.id) { theme in
                    BaseThemeCard(
                        theme: theme,
                        isSelected: selectedTheme.id == theme.id
                    ) {
                        selectedTheme = theme
                    }
                }
            }
        }
    }
}

// MARK: - 基础主题卡片
struct BaseThemeCard: View {
    let theme: KeyboardTheme
    let isSelected: Bool
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // 主题预览
                ZStack {
                    // 背景
                    RoundedRectangle(cornerRadius: 8)
                        .fill(theme.backgroundColor.color)
                        .frame(height: 80)
                    
                    // 图片主题标识
                    if theme.hasBackgroundImage {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color.blue.opacity(0.3))
                            .frame(height: 80)
                            .overlay(
                                Image(systemName: "photo.fill")
                                    .foregroundColor(.white)
                                    .font(.title3)
                            )
                    }
                    
                    // 键盘预览
                    VStack(spacing: 3) {
                        // 第一行
                        HStack(spacing: 3) {
                            ForEach(0..<4) { _ in
                                keyPreview
                            }
                        }
                        
                        // 第二行
                        HStack(spacing: 3) {
                            ForEach(0..<3) { _ in
                                keyPreview
                            }
                        }
                    }
                    .padding(6)
                }
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                )
                
                // 主题信息
                VStack(spacing: 4) {
                    Text(theme.name)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(1)
                    
                    Text(theme.type.description)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                    
                    // 选中标识
                    if isSelected {
                        HStack(spacing: 4) {
                            Image(systemName: "checkmark.circle.fill")
                                .foregroundColor(.blue)
                                .font(.caption)
                            
                            Text("已选择")
                                .font(.caption2)
                                .foregroundColor(.blue)
                        }
                    }
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private var keyPreview: some View {
        RoundedRectangle(cornerRadius: 3)
            .fill(theme.keyBackgroundColor.color)
            .frame(width: 16, height: 12)
            .overlay(
                RoundedRectangle(cornerRadius: 3)
                    .stroke(theme.borderColor.color, lineWidth: 0.5)
            )
    }
}

// MARK: - 主题类型描述扩展
extension KeyboardThemeType {
    var description: String {
        switch self {
        case .light: return "浅色"
        case .dark: return "深色"
        case .colorful: return "彩色"
        case .gradient: return "渐变"
        case .custom: return "自定义"
        }
    }
}

// MARK: - 预览
struct BaseThemeSelectorView_Previews: PreviewProvider {
    static var previews: some View {
        BaseThemeSelectorView(
            selectedTheme: .constant(KeyboardTheme.defaultThemes[0])
        )
    }
}
