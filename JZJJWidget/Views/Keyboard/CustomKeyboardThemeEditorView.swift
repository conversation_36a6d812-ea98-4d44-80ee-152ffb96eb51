//
//  CustomKeyboardThemeEditorView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 自定义键盘主题编辑器
struct CustomKeyboardThemeEditorView: View {
    // MARK: - 绑定属性
    @Binding var themeName: String
    @Binding var themeType: KeyboardThemeType
    @Binding var keyStyle: KeyboardKeyStyle
    @Binding var backgroundColor: Color
    @Binding var keyColor: Color
    @Binding var textColor: Color

    // MARK: - 回调
    let onSave: (KeyboardTheme) -> Void

    // MARK: - 环境
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态
    @State private var fontSize: Double = 16
    @State private var fontWeight: FontWeight = .medium
    @State private var keySpacing: Double = 6
    @State private var keyHeight: Double = 44
    @State private var showBorder: Bool = true
    @State private var borderWidth: Double = 1
    @State private var enableShadow: Bool = true
    @State private var enableHaptic: Bool = true
    @State private var enableSound: Bool = true

    // MARK: - 视图主体
    var body: some View {
        NavigationView {
            Form {
                // 基本信息
                basicInfoSection

                // 外观设置
                appearanceSection

                // 字体设置
                fontSection

                // 布局设置
                layoutSection

                // 特效设置
                effectsSection

                // 预览
                previewSection
            }
            .navigationTitle("自定义主题")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveCustomTheme()
                    }
                    .fontWeight(.semibold)
                    .disabled(themeName.isEmpty)
                }
            }
        }
    }

    // MARK: - 基本信息
    private var basicInfoSection: some View {
        Section("基本信息") {
            TextField("主题名称", text: $themeName)

            Picker("主题类型", selection: $themeType) {
                ForEach(KeyboardThemeType.allCases, id: \.self) { type in
                    Text(type.name).tag(type)
                }
            }

            Picker("按键样式", selection: $keyStyle) {
                ForEach(KeyboardKeyStyle.allCases, id: \.self) { style in
                    Text(style.name).tag(style)
                }
            }
        }
    }

    // MARK: - 外观设置
    private var appearanceSection: some View {
        Section("外观设置") {
            ColorPicker("背景颜色", selection: $backgroundColor)

            ColorPicker("按键颜色", selection: $keyColor)

            ColorPicker("文字颜色", selection: $textColor)

            Toggle("显示边框", isOn: $showBorder)

            if showBorder {
                HStack {
                    Text("边框宽度")
                    Spacer()
                    Slider(value: $borderWidth, in: 0.5...3, step: 0.5) {
                        Text("边框宽度")
                    }
                    Text("\(borderWidth, specifier: "%.1f")px")
                        .foregroundColor(.secondary)
                        .frame(width: 40)
                }
            }
        }
    }

    // MARK: - 字体设置
    private var fontSection: some View {
        Section("字体设置") {
            HStack {
                Text("字体大小")
                Spacer()
                Slider(value: $fontSize, in: 12...24, step: 1) {
                    Text("字体大小")
                }
                Text("\(Int(fontSize))pt")
                    .foregroundColor(.secondary)
                    .frame(width: 30)
            }

            Picker("字体粗细", selection: $fontWeight) {
                ForEach(FontWeight.allCases, id: \.self) { weight in
                    Text(weight.name).tag(weight)
                }
            }
        }
    }

    // MARK: - 布局设置
    private var layoutSection: some View {
        Section("布局设置") {
            HStack {
                Text("按键间距")
                Spacer()
                Slider(value: $keySpacing, in: 2...12, step: 1) {
                    Text("按键间距")
                }
                Text("\(Int(keySpacing))px")
                    .foregroundColor(.secondary)
                    .frame(width: 30)
            }

            HStack {
                Text("按键高度")
                Spacer()
                Slider(value: $keyHeight, in: 36...60, step: 2) {
                    Text("按键高度")
                }
                Text("\(Int(keyHeight))px")
                    .foregroundColor(.secondary)
                    .frame(width: 30)
            }
        }
    }

    // MARK: - 特效设置
    private var effectsSection: some View {
        Section("特效设置") {
            Toggle("启用阴影", isOn: $enableShadow)

            Toggle("启用触觉反馈", isOn: $enableHaptic)

            Toggle("启用按键音效", isOn: $enableSound)
        }
    }

    // MARK: - 预览
    private var previewSection: some View {
        Section("预览") {
            VStack(spacing: 8) {
                Text("预览效果")
                    .font(.caption)
                    .foregroundColor(.secondary)

                // 简化预览
                VStack(spacing: CGFloat(keySpacing)) {
                    HStack(spacing: CGFloat(keySpacing)) {
                        ForEach(["Q", "W", "E"], id: \.self) { letter in
                            previewKey(letter)
                        }
                    }

                    HStack(spacing: CGFloat(keySpacing)) {
                        ForEach(["A", "S"], id: \.self) { letter in
                            previewKey(letter)
                        }
                    }
                }
                .padding(8)
                .background(backgroundColor)
                .cornerRadius(8)
            }
        }
    }

    // MARK: - 预览按键
    private func previewKey(_ text: String) -> some View {
        Text(text)
            .font(.system(size: CGFloat(fontSize), weight: fontWeight.uiKitWeight.swiftUIWeight))
            .foregroundColor(textColor)
            .frame(width: 40, height: CGFloat(keyHeight))
            .background(keyColor)
            .cornerRadius(keyStyle.cornerRadius)
            .overlay(
                RoundedRectangle(cornerRadius: keyStyle.cornerRadius)
                    .stroke(Color.gray.opacity(0.3), lineWidth: showBorder ? CGFloat(borderWidth) : 0)
            )
            .shadow(
                color: enableShadow ? .black.opacity(0.1) : .clear,
                radius: 2,
                x: 0,
                y: 1
            )
    }

    // MARK: - 保存自定义主题
    private func saveCustomTheme() {
        let customTheme = KeyboardTheme(
            name: themeName,
            type: themeType,
            keyStyle: keyStyle,
            backgroundColor: WidgetColor.fromColor(backgroundColor),
            keyBackgroundColor: WidgetColor.fromColor(keyColor),
            keyPressedColor: WidgetColor.fromColor(.blue),
            textColor: WidgetColor.fromColor(textColor),
            specialKeyColor: WidgetColor.fromColor(keyColor.opacity(0.8)),
            borderColor: WidgetColor.fromColor(.gray),
            fontName: "SF Pro",
            fontSize: fontSize,
            fontWeight: fontWeight,
            keySpacing: keySpacing,
            keyHeight: keyHeight,
            showBorder: showBorder,
            borderWidth: borderWidth,
            enableShadow: enableShadow,
            enableHaptic: enableHaptic,
            enableSound: enableSound
        )

        onSave(customTheme)
    }
}



// MARK: - 扩展：UIFont.Weight 到 SwiftUI Font.Weight 的转换
extension UIFont.Weight {
    var swiftUIWeight: Font.Weight {
        switch self {
        case .ultraLight: return .ultraLight
        case .thin: return .thin
        case .light: return .light
        case .regular: return .regular
        case .medium: return .medium
        case .semibold: return .semibold
        case .bold: return .bold
        case .heavy: return .heavy
        case .black: return .black
        default: return .regular
        }
    }
}

// MARK: - 预览
struct CustomKeyboardThemeEditorView_Previews: PreviewProvider {
    static var previews: some View {
        CustomKeyboardThemeEditorView(
            themeName: .constant("我的主题"),
            themeType: .constant(.custom),
            keyStyle: .constant(.rounded),
            backgroundColor: .constant(.white),
            keyColor: .constant(.gray.opacity(0.3)),
            textColor: .constant(.black)
        ) { _ in }
    }
}
