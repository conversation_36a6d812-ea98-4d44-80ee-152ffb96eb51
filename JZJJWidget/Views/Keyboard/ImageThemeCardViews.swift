//
//  ImageThemeCardViews.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

// MARK: - 图片主题卡片
struct ImageThemeCard: View {
    let theme: KeyboardTheme
    let isSelected: Bool
    let onTap: () -> Void

    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared
    @State private var backgroundImage: UIImage?
    @State private var keyImage: UIImage?

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 8) {
                // 主题预览
                ZStack {
                    // 背景
                    RoundedRectangle(cornerRadius: 8)
                        .fill(theme.backgroundColor.color)
                        .frame(height: 100)

                    // 背景图片
                    if theme.hasBackgroundImage,
                       let backgroundImage = backgroundImage {
                        Image(uiImage: backgroundImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(height: 100)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                            .opacity(theme.imageOpacity)
                    }

                    // 键盘预览
                    VStack(spacing: 4) {
                        // 第一行按键
                        HStack(spacing: 4) {
                            ForEach(0..<4) { _ in
                                keyPreview
                            }
                        }

                        // 第二行按键
                        HStack(spacing: 4) {
                            ForEach(0..<3) { _ in
                                keyPreview
                            }
                        }
                    }
                    .padding(8)
                }
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
                )

                // 主题信息
                VStack(spacing: 2) {
                    Text(theme.name)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(1)

                    Text(theme.type.description)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .lineLimit(1)
                }
            }
        }
        .buttonStyle(PlainButtonStyle())
        .task {
            await loadImages()
        }
    }

    private func loadImages() async {
        if theme.hasBackgroundImage {
            backgroundImage = await keyboardThemeManager.getBackgroundImage(for: theme)
        }
        if theme.hasKeyImage {
            keyImage = await keyboardThemeManager.getKeyImage(for: theme)
        }
    }

    private var keyPreview: some View {
        ZStack {
            // 按键背景
            RoundedRectangle(cornerRadius: 4)
                .fill(theme.keyBackgroundColor.color)
                .frame(width: 20, height: 16)

            // 按键图片
            if theme.hasKeyImage,
               let keyImage = keyImage {
                Image(uiImage: keyImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 20, height: 16)
                    .clipShape(RoundedRectangle(cornerRadius: 4))
                    .opacity(theme.imageOpacity)
            }
        }
    }
}

// MARK: - 自定义图片主题卡片
struct CustomImageThemeCard: View {
    let theme: KeyboardTheme
    let isSelected: Bool
    let onApply: () -> Void
    let onDelete: () -> Void

    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared
    @State private var showingDeleteAlert = false
    @State private var backgroundImage: UIImage?
    @State private var keyImage: UIImage?

    var body: some View {
        VStack(spacing: 8) {
            // 主题预览
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 8)
                    .fill(theme.backgroundColor.color)
                    .frame(height: 100)

                // 背景图片
                if theme.hasBackgroundImage,
                   let backgroundImage = backgroundImage {
                    Image(uiImage: backgroundImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 100)
                        .clipShape(RoundedRectangle(cornerRadius: 8))
                        .opacity(theme.imageOpacity)
                }

                // 键盘预览
                VStack(spacing: 4) {
                    // 第一行按键
                    HStack(spacing: 4) {
                        ForEach(0..<4) { _ in
                            keyPreview
                        }
                    }

                    // 第二行按键
                    HStack(spacing: 4) {
                        ForEach(0..<3) { _ in
                            keyPreview
                        }
                    }
                }
                .padding(8)

                // 操作按钮
                VStack {
                    HStack {
                        Spacer()

                        Button(action: {
                            showingDeleteAlert = true
                        }) {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                                .padding(4)
                                .background(Color.white.opacity(0.8))
                                .clipShape(Circle())
                        }
                    }
                    Spacer()
                }
                .padding(8)
            }
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.blue : Color.clear, lineWidth: 2)
            )

            // 主题信息和应用按钮
            VStack(spacing: 8) {
                VStack(spacing: 2) {
                    Text(theme.name)
                        .font(.caption)
                        .fontWeight(.medium)
                        .lineLimit(1)

                    Text("自定义主题")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }

                Button(action: onApply) {
                    Text(isSelected ? "已应用" : "应用主题")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(isSelected ? .secondary : .blue)
                        .padding(.horizontal, 12)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(isSelected ? Color(.systemGray5) : Color.blue.opacity(0.1))
                        )
                }
                .disabled(isSelected)
            }
        }
        .alert("删除主题", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                onDelete()
            }
        } message: {
            Text("确定要删除主题\"\(theme.name)\"吗？此操作无法撤销。")
        }
        .task {
            await loadImages()
        }
    }

    private func loadImages() async {
        if theme.hasBackgroundImage {
            backgroundImage = await keyboardThemeManager.getBackgroundImage(for: theme)
        }
        if theme.hasKeyImage {
            keyImage = await keyboardThemeManager.getKeyImage(for: theme)
        }
    }

    private var keyPreview: some View {
        ZStack {
            // 按键背景
            RoundedRectangle(cornerRadius: 4)
                .fill(theme.keyBackgroundColor.color)
                .frame(width: 20, height: 16)

            // 按键图片
            if theme.hasKeyImage,
               let keyImage = keyImage {
                Image(uiImage: keyImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(width: 20, height: 16)
                    .clipShape(RoundedRectangle(cornerRadius: 4))
                    .opacity(theme.imageOpacity)
            }
        }
    }
}

// MARK: - 图片主题预览卡片
struct ImageThemePreviewCard: View {
    let theme: KeyboardTheme
    let backgroundImage: UIImage?
    let keyImage: UIImage?
    let imageOpacity: Double

    var body: some View {
        VStack(spacing: 12) {
            // 主题信息
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(theme.name)
                        .font(.headline)
                        .fontWeight(.semibold)

                    Text("图片主题预览")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "photo.fill")
                    .foregroundColor(.blue)
            }

            // 键盘预览
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 12)
                    .fill(theme.backgroundColor.color)
                    .frame(height: 120)

                // 背景图片
                if let backgroundImage = backgroundImage {
                    Image(uiImage: backgroundImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .frame(height: 120)
                        .clipShape(RoundedRectangle(cornerRadius: 12))
                        .opacity(imageOpacity)
                }

                // 键盘布局预览
                VStack(spacing: 6) {
                    // 第一行
                    HStack(spacing: 4) {
                        ForEach(0..<6) { _ in
                            keyPreviewButton
                        }
                    }

                    // 第二行
                    HStack(spacing: 4) {
                        Spacer().frame(width: 12)
                        ForEach(0..<5) { _ in
                            keyPreviewButton
                        }
                        Spacer().frame(width: 12)
                    }

                    // 第三行
                    HStack(spacing: 4) {
                        keyPreviewButton.frame(width: 32)
                        ForEach(0..<3) { _ in
                            keyPreviewButton
                        }
                        keyPreviewButton.frame(width: 32)
                    }

                    // 底部行
                    HStack(spacing: 4) {
                        keyPreviewButton.frame(width: 40)
                        keyPreviewButton.frame(width: 30)
                        keyPreviewButton // 空格键
                        keyPreviewButton.frame(width: 40)
                    }
                }
                .padding(12)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(16)
    }

    private var keyPreviewButton: some View {
        ZStack {
            // 按键背景
            RoundedRectangle(cornerRadius: 6)
                .fill(theme.keyBackgroundColor.color)
                .frame(height: 24)

            // 按键图片
            if let keyImage = keyImage {
                Image(uiImage: keyImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 24)
                    .clipShape(RoundedRectangle(cornerRadius: 6))
                    .opacity(imageOpacity)
            }
        }
    }
}

// MARK: - 预览
struct ImageThemeCardViews_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            ImageThemeCard(
                theme: KeyboardTheme.builtInImageThemes[0],
                isSelected: false
            ) { }

            CustomImageThemeCard(
                theme: KeyboardTheme.builtInImageThemes[0],
                isSelected: true,
                onApply: { },
                onDelete: { }
            )
        }
        .padding()
    }
}
