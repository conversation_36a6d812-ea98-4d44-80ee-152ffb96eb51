//
//  AdvancedKeyboardPreviewView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 高级键盘预览视图
struct AdvancedKeyboardPreviewView: View {
    let theme: AdvancedKeyboardTheme

    // MARK: - 环境对象
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理
    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @State private var backgroundImage: UIImage?
    @State private var keyImages: [String: UIImage] = [:]

    // 键盘布局配置
    private let letterRows: [[String]] = [
        ["Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P"],
        ["A", "S", "D", "F", "G", "H", "J", "K", "L"],
        ["Z", "X", "C", "V", "B", "N", "M"]
    ]

    private let numberRow: [String] = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 主题信息
                themeInfoSection

                Divider()

                // 键盘预览
                ScrollView {
                    VStack(spacing: AppLayout.Spacing.large) {
                        keyboardPreviewSection

                        // 主题统计
                        ThemeStatsCard(theme: theme)
                    }
                    .padding(AppLayout.Spacing.medium)
                }
            }
            .navigationTitle("主题预览")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("应用") {
                        advancedThemeManager.applyAdvancedTheme(theme)
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .task {
            await loadImages()
        }
    }

    // MARK: - 主题信息
    private var themeInfoSection: some View {
        VStack(spacing: 8) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(theme.name)
                        .font(.headline)
                        .fontWeight(.semibold)

                    if !theme.description.isEmpty {
                        Text(theme.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }

                Spacer()

                VStack(alignment: .trailing, spacing: 4) {
                    Text("高级主题")
                        .font(.caption)
                        .fontWeight(.medium)
                        .foregroundColor(.purple)
                        .padding(.horizontal, 8)
                        .padding(.vertical, 2)
                        .background(Color.purple.opacity(0.1))
                        .cornerRadius(8)

                    Text("基于 \(theme.baseTheme.name)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding()
        .background(Color(.systemGray6))
    }

    // MARK: - 键盘预览
    private var keyboardPreviewSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("键盘预览")
                .font(.headline)
                .fontWeight(.semibold)

            // 键盘区域
            ZStack {
                // 背景颜色
                theme.baseTheme.backgroundColor.color

                // 背景图片
                if theme.baseTheme.hasBackgroundImage,
                   let backgroundImage = backgroundImage {
                    Image(uiImage: backgroundImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .opacity(theme.baseTheme.imageOpacity)
                }

                // 键盘布局
                VStack(spacing: CGFloat(theme.globalKeySpacing)) {
                    // 数字行
                    numberRowView

                    // 字母行
                    ForEach(Array(letterRows.enumerated()), id: \.offset) { index, row in
                        letterRowView(row: row, rowIndex: index)
                    }

                    // 底部功能行
                    bottomRowView
                }
                .padding(CGFloat(theme.globalKeySpacing))
            }
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
        }
    }

    // MARK: - 数字行
    private var numberRowView: some View {
        HStack(spacing: CGFloat(theme.globalKeySpacing)) {
            ForEach(numberRow, id: \.self) { number in
                AdvancedKeyButton(
                    text: number,
                    keyType: .number,
                    theme: theme,
                    keyImage: keyImages[number]
                )
            }
        }
    }

    // MARK: - 字母行
    private func letterRowView(row: [String], rowIndex: Int) -> some View {
        HStack(spacing: CGFloat(theme.globalKeySpacing)) {
            // 第三行添加Shift键
            if rowIndex == 2 {
                AdvancedKeyButton(
                    text: "⇧",
                    keyType: .shift,
                    theme: theme,
                    width: 50,
                    keyImage: keyImages["⇧"]
                )
            }

            // 字母键
            ForEach(row, id: \.self) { letter in
                AdvancedKeyButton(
                    text: letter,
                    keyType: .letter,
                    theme: theme,
                    keyImage: keyImages[letter]
                )
            }

            // 第三行添加删除键
            if rowIndex == 2 {
                AdvancedKeyButton(
                    text: "⌫",
                    keyType: .function,
                    theme: theme,
                    width: 50,
                    keyImage: keyImages["⌫"]
                )
            }
        }
    }

    // MARK: - 底部功能行
    private var bottomRowView: some View {
        HStack(spacing: CGFloat(theme.globalKeySpacing)) {
            // 123键
            AdvancedKeyButton(
                text: "123",
                keyType: .function,
                theme: theme,
                width: 50,
                keyImage: keyImages["123"]
            )

            // 空格键
            AdvancedKeyButton(
                text: "空格",
                keyType: .space,
                theme: theme,
                isFlexible: true,
                keyImage: keyImages["空格"]
            )

            // 换行键
            AdvancedKeyButton(
                text: "↵",
                keyType: .function,
                theme: theme,
                width: 50,
                keyImage: keyImages["↵"]
            )
        }
    }

    // MARK: - 辅助方法
    private func loadImages() async {
        // 加载背景图片
        if theme.baseTheme.hasBackgroundImage {
            backgroundImage = await KeyboardThemeManager.shared.getBackgroundImage(for: theme.baseTheme)
        }

        // 加载按键图片
        if theme.baseTheme.hasKeyImage {
            let allKeys = numberRow + letterRows.flatMap { $0 } + ["⇧", "⌫", "123", "空格", "↵"]

            for key in allKeys {
                let keyType = getKeyType(for: key)
                if let keyImage = await KeyboardThemeManager.shared.getKeyImage(for: theme.baseTheme) {
                    keyImages[key] = keyImage
                }
            }
        }
    }

    private func getKeyType(for key: String) -> KeyType {
        if numberRow.contains(key) {
            return .number
        } else if letterRows.flatMap({ $0 }).contains(key) {
            return .letter
        } else if key == "空格" {
            return .space
        } else if key == "⇧" {
            return .shift
        } else {
            return .function
        }
    }
}

// MARK: - 高级按键预览按钮
struct AdvancedKeyButton: View {
    let text: String
    let keyType: KeyType
    let theme: AdvancedKeyboardTheme
    var width: CGFloat? = nil
    var isFlexible: Bool = false
    let keyImage: UIImage?

    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared

    @State private var individualKeyImage: UIImage?

    private var config: KeyConfig {
        advancedThemeManager.getEffectiveKeyConfig(
            for: text,
            keyType: keyType,
            in: theme
        )
    }

    var body: some View {

        Button(action: {}) {
            ZStack {
                // 背景颜色
                config.backgroundColor.color
                    .cornerRadius(CGFloat(config.cornerRadius))

                // 按键图片 - 优先显示个性化图片
                if let individualImage = individualKeyImage {
                    Image(uiImage: individualImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .opacity(config.imageOpacity)
                        .cornerRadius(CGFloat(config.cornerRadius))
                } else if let keyImage = keyImage {
                    Image(uiImage: keyImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .opacity(theme.baseTheme.imageOpacity)
                        .cornerRadius(CGFloat(config.cornerRadius))
                }

                // 文字
                if text != "空格" {
                    Text(text)
                        .font(.system(
                            size: CGFloat(config.fontSize),
                            weight: config.fontWeight.fontWeight
                        ))
                        .foregroundColor(config.textColor.color)
                }
            }
            .frame(
                maxWidth: isFlexible ? .infinity : width,
                minHeight: CGFloat(theme.globalKeyHeight)
            )
            .frame(width: width)
            .overlay(
                RoundedRectangle(cornerRadius: CGFloat(config.cornerRadius))
                    .stroke(config.borderColor.color, lineWidth: CGFloat(config.borderWidth))
            )
            .shadow(
                color: config.shadowEnabled ? config.shadowColor.color : .clear,
                radius: CGFloat(config.shadowRadius),
                x: config.shadowOffset.width,
                y: config.shadowOffset.height
            )
        }
        .disabled(true) // 预览模式下禁用交互
        .task {
            // 加载个性化按键图片
            if config.hasCustomImage {
                await loadIndividualKeyImage()
            }
        }
    }

    /// 加载个性化按键图片
    private func loadIndividualKeyImage() async {
        guard config.hasCustomImage,
              let normalImagePath = config.normalImagePath else {
            return
        }

        // 构建图片路径
        let appGroupURL = AppGroupDataManager.shared.appGroup.containerURL
        let themePackPath = appGroupURL
            .appendingPathComponent("theme-packs")
            .appendingPathComponent("built-in")
            .appendingPathComponent(theme.id)
            .appendingPathComponent(normalImagePath)

        // 尝试加载图片
        if FileManager.default.fileExists(atPath: themePackPath.path) {
            do {
                let imageData = try Data(contentsOf: themePackPath)
                if let image = UIImage(data: imageData) {
                    await MainActor.run {
                        self.individualKeyImage = image
                    }
                    print("✅ 预览加载个性化按键图片成功: \(text)")
                }
            } catch {
                print("❌ 预览加载个性化按键图片失败: \(error)")
            }
        } else {
            print("⚠️ 个性化按键图片文件不存在: \(themePackPath.path)")
        }
    }
}

// MARK: - 预览
struct AdvancedKeyboardPreviewView_Previews: PreviewProvider {
    static var previews: some View {
        AdvancedKeyboardPreviewView(
            theme: AdvancedKeyboardTheme(
                name: "测试高级主题",
                baseTheme: KeyboardTheme.defaultThemes[0]
            )
        )
    }
}
