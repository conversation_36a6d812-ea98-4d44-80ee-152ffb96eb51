//
//  ColorPickerComponents.swift
//  JZJJWidget
//
//  Created by y<PERSON><PERSON>g on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

// MARK: - 高级颜色选择器
struct AdvancedColorPicker: View {
    let title: String
    @Binding var selectedColor: WidgetColor
    @State private var showingColorPicker = false

    // 预设颜色
    private let presetColors: [WidgetColor] = [
        // 基础颜色
        WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0), // 白色
        WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0), // 黑色
        WidgetColor(red: 0.5, green: 0.5, blue: 0.5, alpha: 1.0), // 灰色

        // 系统颜色
        WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0), // 系统蓝
        WidgetColor(red: 0.2, green: 0.78, blue: 0.35, alpha: 1.0), // 系统绿
        WidgetColor(red: 1.0, green: 0.23, blue: 0.19, alpha: 1.0), // 系统红
        WidgetColor(red: 1.0, green: 0.58, blue: 0.0, alpha: 1.0), // 系统橙
        WidgetColor(red: 0.69, green: 0.32, blue: 0.87, alpha: 1.0), // 系统紫

        // 浅色系
        WidgetColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1.0), // 浅灰
        WidgetColor(red: 0.9, green: 0.95, blue: 1.0, alpha: 1.0), // 浅蓝
        WidgetColor(red: 0.9, green: 1.0, blue: 0.9, alpha: 1.0), // 浅绿
        WidgetColor(red: 1.0, green: 0.9, blue: 0.9, alpha: 1.0), // 浅红

        // 深色系
        WidgetColor(red: 0.1, green: 0.1, blue: 0.1, alpha: 1.0), // 深灰
        WidgetColor(red: 0.0, green: 0.2, blue: 0.4, alpha: 1.0), // 深蓝
        WidgetColor(red: 0.0, green: 0.3, blue: 0.1, alpha: 1.0), // 深绿
        WidgetColor(red: 0.4, green: 0.0, blue: 0.0, alpha: 1.0), // 深红
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题和当前颜色显示
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                Button(action: {
                    showingColorPicker = true
                }) {
                    RoundedRectangle(cornerRadius: 8)
                        .fill(selectedColor.color)
                        .frame(width: 40, height: 24)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(Color(.systemGray4), lineWidth: 1)
                        )
                }
            }

            // 预设颜色网格
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 8), spacing: 8) {
                ForEach(Array(presetColors.enumerated()), id: \.offset) { index, color in
                    Button(action: {
                        selectedColor = color
                    }) {
                        RoundedRectangle(cornerRadius: 6)
                            .fill(color.color)
                            .frame(height: 32)
                            .overlay(
                                RoundedRectangle(cornerRadius: 6)
                                    .stroke(
                                        selectedColor == color ? Color.blue : Color(.systemGray4),
                                        lineWidth: selectedColor == color ? 2 : 1
                                    )
                            )
                    }
                }
            }
        }
        .sheet(isPresented: $showingColorPicker) {
            ColorPickerSheet(selectedColor: $selectedColor)
        }
    }
}

// MARK: - 颜色选择器弹窗
struct ColorPickerSheet: View {
    @Binding var selectedColor: WidgetColor
    @Environment(\.dismiss) private var dismiss
    @State private var tempColor: Color

    init(selectedColor: Binding<WidgetColor>) {
        self._selectedColor = selectedColor
        self._tempColor = State(initialValue: selectedColor.wrappedValue.color)
    }

    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 颜色预览
                RoundedRectangle(cornerRadius: 12)
                    .fill(tempColor)
                    .frame(height: 100)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color(.systemGray4), lineWidth: 1)
                    )

                // 系统颜色选择器
                ColorPicker("选择颜色", selection: $tempColor, supportsOpacity: true)
                    .labelsHidden()

                // RGB值显示
                VStack(alignment: .leading, spacing: 8) {
                    Text("RGB 值")
                        .font(.headline)

                    let components = tempColor.cgColor?.components ?? [0, 0, 0, 1]
                    HStack {
                        VStack {
                            Text("R")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(Int(components[0] * 255))")
                                .font(.system(.body, design: .monospaced))
                        }

                        Spacer()

                        VStack {
                            Text("G")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(Int(components[1] * 255))")
                                .font(.system(.body, design: .monospaced))
                        }

                        Spacer()

                        VStack {
                            Text("B")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(Int(components[2] * 255))")
                                .font(.system(.body, design: .monospaced))
                        }

                        Spacer()

                        VStack {
                            Text("A")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            Text("\(Int(components[3] * 100))%")
                                .font(.system(.body, design: .monospaced))
                        }
                    }
                    .padding()
                    .background(Color(.systemGray6))
                    .cornerRadius(8)
                }

                Spacer()
            }
            .padding()
            .navigationTitle("选择颜色")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("确定") {
                        let components = tempColor.cgColor?.components ?? [0, 0, 0, 1]
                        selectedColor = WidgetColor(
                            red: Double(components[0]),
                            green: Double(components[1]),
                            blue: Double(components[2]),
                            alpha: Double(components[3])
                        )
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
}

// MARK: - 渐变颜色选择器
struct GradientColorPicker: View {
    let title: String
    @Binding var startColor: WidgetColor
    @Binding var endColor: WidgetColor
    @Binding var gradientDirection: GradientDirection

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)

            // 渐变预览
            RoundedRectangle(cornerRadius: 12)
                .fill(
                    LinearGradient(
                        colors: [startColor.color, endColor.color],
                        startPoint: gradientDirection.startPoint,
                        endPoint: gradientDirection.endPoint
                    )
                )
                .frame(height: 80)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )

            // 颜色选择
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("起始颜色")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    AdvancedColorPicker(title: "", selectedColor: $startColor)
                }

                VStack(alignment: .leading, spacing: 8) {
                    Text("结束颜色")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    AdvancedColorPicker(title: "", selectedColor: $endColor)
                }
            }

            // 方向选择
            VStack(alignment: .leading, spacing: 8) {
                Text("渐变方向")
                    .font(.subheadline)
                    .fontWeight(.medium)

                Picker("渐变方向", selection: $gradientDirection) {
                    ForEach(GradientDirection.allCases, id: \.self) { direction in
                        Text(direction.displayName).tag(direction)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
            }
        }
    }
}

// MARK: - 渐变方向枚举
enum GradientDirection: String, CaseIterable, Codable {
    case horizontal = "horizontal"
    case vertical = "vertical"
    case diagonal = "diagonal"
    case radial = "radial"

    var displayName: String {
        switch self {
        case .horizontal: return "水平"
        case .vertical: return "垂直"
        case .diagonal: return "对角"
        case .radial: return "径向"
        }
    }

    var startPoint: UnitPoint {
        switch self {
        case .horizontal: return .leading
        case .vertical: return .top
        case .diagonal: return .topLeading
        case .radial: return .center
        }
    }

    var endPoint: UnitPoint {
        switch self {
        case .horizontal: return .trailing
        case .vertical: return .bottom
        case .diagonal: return .bottomTrailing
        case .radial: return .center
        }
    }
}

// MARK: - 透明度滑块
struct OpacitySlider: View {
    let title: String
    @Binding var opacity: Double

    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)

                Spacer()

                Text("\(Int(opacity * 100))%")
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .frame(width: 40)
            }

            Slider(value: $opacity, in: 0...1, step: 0.01) {
                Text(title)
            } minimumValueLabel: {
                Text("0%")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            } maximumValueLabel: {
                Text("100%")
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - 预览
struct ColorPickerComponents_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            AdvancedColorPicker(
                title: "背景颜色",
                selectedColor: .constant(WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0))
            )

            OpacitySlider(
                title: "透明度",
                opacity: .constant(0.8)
            )
        }
        .padding()
    }
}
