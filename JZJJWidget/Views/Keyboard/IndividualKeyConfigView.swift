//
//  IndividualKeyConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 单个按键配置视图
struct IndividualKeyConfigView: View {
    // MARK: - 属性
    let theme: AdvancedKeyboardTheme

    // MARK: - 环境对象
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理
    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @State private var selectedKeyType: KeyType = .letter
    @State private var selectedKeyValue: String?
    @State private var showingKeyConfigEditor = false
    @State private var editingKeyConfig: KeyConfig?

    // 键盘布局
    private let keyboardLayout: [KeyType: [String]] = [
        .number: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"],
        .letter: ["Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P", "A", "S", "D", "F", "G", "H", "J", "K", "L", "Z", "X", "C", "V", "B", "N", "M"],
        .function: ["删除", "换行", "123", "ABC", "切换键盘"],
        .space: ["空格"],
        .shift: ["⇧"],
        .symbol: ["@", "#", "$", "%", "&", "*", "+", "="],
        .punctuation: [".", ",", "?", "!", ";", ":", "'", "\""]
    ]

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 按键类型选择器
                keyTypeSelector

                Divider()

                // 按键网格
                ScrollView {
                    VStack(spacing: AppLayout.Spacing.large) {
                        // 说明文字
                        instructionSection

                        // 按键网格
                        keyGridSection

                        // 已自定义的按键
                        customizedKeysSection
                    }
                    .padding(AppLayout.Spacing.medium)
                }
            }
            .navigationTitle("单个按键配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingKeyConfigEditor) {
            if let config = editingKeyConfig {
                IndividualKeyConfigEditorView(
                    theme: theme,
                    keyConfig: config
                )
            }
        }
    }

    // MARK: - 按键类型选择器
    private var keyTypeSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(KeyType.allCases, id: \.self) { keyType in
                    KeyTypeTab(
                        keyType: keyType,
                        isSelected: selectedKeyType == keyType
                    ) {
                        selectedKeyType = keyType
                        selectedKeyValue = nil
                    }
                }
            }
            .padding(.horizontal, AppLayout.Spacing.medium)
            .padding(.vertical, AppLayout.Spacing.small)
        }
        .background(Color(.systemGray6))
    }

    // MARK: - 说明文字
    private var instructionSection: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)

                Text("单个按键定制")
                    .font(.headline)
                    .fontWeight(.semibold)
            }

            Text("点击按键进行个性化定制，单个按键的配置会覆盖类型配置")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }

    // MARK: - 按键网格
    private var keyGridSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("\(selectedKeyType.displayName)按键")
                .font(.headline)
                .fontWeight(.semibold)

            if let keys = keyboardLayout[selectedKeyType] {
                LazyVGrid(columns: gridColumns(for: selectedKeyType), spacing: 8) {
                    ForEach(keys, id: \.self) { keyValue in
                        IndividualKeyButton(
                            keyValue: keyValue,
                            keyType: selectedKeyType,
                            theme: theme,
                            isSelected: selectedKeyValue == keyValue,
                            hasCustomConfig: theme.individualKeyConfigs[keyValue] != nil
                        ) {
                            selectedKeyValue = keyValue
                            editKey(keyValue: keyValue, keyType: selectedKeyType)
                        }
                    }
                }
            }
        }
    }

    // MARK: - 已自定义的按键
    private var customizedKeysSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("已自定义的按键")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("\(theme.individualKeyConfigs.count) 个")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            if theme.individualKeyConfigs.isEmpty {
                Text("暂无自定义按键")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 6), spacing: 8) {
                    ForEach(Array(theme.individualKeyConfigs.keys.sorted()), id: \.self) { keyValue in
                        if let config = theme.individualKeyConfigs[keyValue] {
                            CustomizedKeyButton(
                                keyConfig: config,
                                onEdit: {
                                    editingKeyConfig = config
                                    showingKeyConfigEditor = true
                                },
                                onRemove: {
                                    advancedThemeManager.removeIndividualKeyConfig(
                                        for: keyValue,
                                        in: theme
                                    )
                                }
                            )
                        }
                    }
                }
            }
        }
    }

    // MARK: - 辅助方法
    private func gridColumns(for keyType: KeyType) -> [GridItem] {
        let count: Int
        switch keyType {
        case .number:
            count = 5
        case .letter:
            count = 6
        case .space:
            count = 1
        default:
            count = 4
        }
        return Array(repeating: GridItem(.flexible(), spacing: 8), count: count)
    }

    private func editKey(keyValue: String, keyType: KeyType) {
        let effectiveConfig = advancedThemeManager.getEffectiveKeyConfig(
            for: keyValue,
            keyType: keyType,
            in: theme
        )

        editingKeyConfig = effectiveConfig
        showingKeyConfigEditor = true
    }
}

// MARK: - 单个按键按钮
struct IndividualKeyButton: View {
    let keyValue: String
    let keyType: KeyType
    let theme: AdvancedKeyboardTheme
    let isSelected: Bool
    let hasCustomConfig: Bool
    let onTap: () -> Void

    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared

    var body: some View {
        let config = advancedThemeManager.getEffectiveKeyConfig(
            for: keyValue,
            keyType: keyType,
            in: theme
        )

        Button(action: onTap) {
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: CGFloat(config.cornerRadius))
                    .fill(config.backgroundColor.color)
                    .overlay(
                        RoundedRectangle(cornerRadius: CGFloat(config.cornerRadius))
                            .stroke(
                                isSelected ? Color.blue : config.borderColor.color,
                                lineWidth: isSelected ? 2 : CGFloat(config.borderWidth)
                            )
                    )

                // 文字
                if keyValue != "空格" {
                    Text(keyValue)
                        .font(.system(
                            size: CGFloat(config.fontSize * 0.8),
                            weight: config.fontWeight.fontWeight
                        ))
                        .foregroundColor(config.textColor.color)
                }

                // 自定义标识
                if hasCustomConfig {
                    VStack {
                        HStack {
                            Spacer()
                            Circle()
                                .fill(Color.orange)
                                .frame(width: 6, height: 6)
                        }
                        Spacer()
                    }
                    .padding(4)
                }
            }
            .frame(height: 40)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 已自定义按键按钮
struct CustomizedKeyButton: View {
    let keyConfig: KeyConfig
    let onEdit: () -> Void
    let onRemove: () -> Void

    @State private var showingActionSheet = false

    var body: some View {
        Button(action: {
            showingActionSheet = true
        }) {
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: CGFloat(keyConfig.cornerRadius))
                    .fill(keyConfig.backgroundColor.color)
                    .overlay(
                        RoundedRectangle(cornerRadius: CGFloat(keyConfig.cornerRadius))
                            .stroke(keyConfig.borderColor.color, lineWidth: CGFloat(keyConfig.borderWidth))
                    )

                // 文字
                if keyConfig.keyValue != "空格" {
                    Text(keyConfig.keyValue)
                        .font(.system(
                            size: CGFloat(keyConfig.fontSize * 0.7),
                            weight: keyConfig.fontWeight.fontWeight
                        ))
                        .foregroundColor(keyConfig.textColor.color)
                }

                // 自定义标识
                VStack {
                    HStack {
                        Spacer()
                        Circle()
                            .fill(Color.orange)
                            .frame(width: 4, height: 4)
                    }
                    Spacer()
                }
                .padding(2)
            }
            .frame(height: 32)
        }
        .buttonStyle(PlainButtonStyle())
        .confirmationDialog("按键操作", isPresented: $showingActionSheet) {
            Button("编辑配置") {
                onEdit()
            }

            Button("移除自定义", role: .destructive) {
                onRemove()
            }

            Button("取消", role: .cancel) { }
        }
    }
}

// MARK: - 预览
struct IndividualKeyConfigView_Previews: PreviewProvider {
    static var previews: some View {
        IndividualKeyConfigView(
            theme: AdvancedKeyboardTheme(
                name: "测试主题",
                baseTheme: KeyboardTheme.defaultThemes[0]
            )
        )
    }
}
