//
//  KeyTypeConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 按键类型配置界面
struct KeyTypeConfigView: View {
    // MARK: - 属性
    let theme: AdvancedKeyboardTheme

    // MARK: - 环境对象
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理
    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @State private var selectedKeyType: KeyType = .letter
    @State private var editingConfig: KeyTypeConfig?
    @State private var showingConfigEditor = false
    @State private var showingPreview = false
    @State private var showingSuccessToast = false
    @State private var successMessage = ""
    @State private var refreshPreview = false

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 按键类型选择器
                keyTypeSelector

                Divider()

                // 配置内容
                ScrollView {
                    VStack(spacing: AppLayout.Spacing.large) {
                        // 实时预览区域
                        livePreviewSection

                        // 当前类型配置
                        currentTypeConfigSection

                        // 影响的按键列表
                        affectedKeysSection

                        // 快速配置选项
                        quickConfigSection
                    }
                    .padding(AppLayout.Spacing.medium)
                }
            }
            .navigationTitle("按键类型配置")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    HStack(spacing: 16) {
                        Button("保存主题") {
                            saveCurrentTheme()
                        }
                        .fontWeight(.semibold)

                        Button("预览") {
                            showingPreview = true
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingConfigEditor) {
            if let config = editingConfig {
                KeyTypeConfigEditorView(
                    theme: theme,
                    keyType: selectedKeyType,
                    config: config
                )
            }
        }
        .sheet(isPresented: $showingPreview) {
            AdvancedKeyboardPreviewView(theme: theme)
        }
        .overlay(
            // 成功提示Toast
            VStack {
                Spacer()
                if showingSuccessToast {
                    SuccessToast(message: successMessage, theme: ThemeManager.shared.currentTheme)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                        .onAppear {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                withAnimation {
                                    showingSuccessToast = false
                                }
                            }
                        }
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showingSuccessToast)
        )
    }

    // MARK: - 按键类型选择器
    private var keyTypeSelector: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(KeyType.allCases, id: \.self) { keyType in
                    KeyTypeTab(
                        keyType: keyType,
                        isSelected: selectedKeyType == keyType
                    ) {
                        selectedKeyType = keyType
                    }
                }
            }
            .padding(.horizontal, AppLayout.Spacing.medium)
            .padding(.vertical, AppLayout.Spacing.small)
        }
        .background(Color(.systemGray6))
    }

    // MARK: - 实时预览区域
    private var livePreviewSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("实时预览")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("全屏预览") {
                    showingPreview = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }

            // 按键预览区域
            VStack(spacing: 12) {
                Text("「\(selectedKeyType.displayName)」类型按键效果")
                    .font(.subheadline)
                    .foregroundColor(.secondary)

                // 示例按键预览
                HStack(spacing: 12) {
                    ForEach(getSampleKeys(for: selectedKeyType).prefix(5), id: \.self) { keyValue in
                        LivePreviewKeyButton(
                            keyValue: keyValue,
                            keyType: selectedKeyType,
                            theme: theme,
                            advancedThemeManager: advancedThemeManager
                        )
                    }

                    if getSampleKeys(for: selectedKeyType).count > 5 {
                        Text("...")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40, height: 32)
                    }
                }
                .padding()
                .background(theme.baseTheme.backgroundColor.color.opacity(0.3))
                .cornerRadius(8)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 当前类型配置
    private var currentTypeConfigSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("\(selectedKeyType.displayName)配置")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Button("编辑") {
                    if let config = theme.keyTypeConfigs[selectedKeyType] {
                        editingConfig = config
                        showingConfigEditor = true
                    }
                }
                .font(.subheadline)
                .fontWeight(.medium)
            }

            if let config = theme.keyTypeConfigs[selectedKeyType] {
                KeyTypeConfigCard(config: config) {
                    editingConfig = config
                    showingConfigEditor = true
                }
            } else {
                EmptyConfigCard(keyType: selectedKeyType) {
                    // 创建默认配置
                    let newConfig = KeyTypeConfig(keyType: selectedKeyType)
                    editingConfig = newConfig
                    showingConfigEditor = true
                }
            }
        }
    }

    // MARK: - 影响的按键列表
    private var affectedKeysSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("影响的按键")
                .font(.headline)
                .fontWeight(.semibold)

            if let config = theme.keyTypeConfigs[selectedKeyType] {
                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 6), spacing: 8) {
                    ForEach(config.affectedKeys, id: \.self) { keyValue in
                        AffectedKeyButton(
                            keyValue: keyValue,
                            config: advancedThemeManager.getEffectiveKeyConfig(
                                for: keyValue,
                                keyType: selectedKeyType,
                                in: theme
                            )
                        )
                    }
                }
            } else {
                Text("暂无配置")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
    }

    // MARK: - 快速配置选项
    private var quickConfigSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("快速配置")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.small) {
                QuickConfigButton(
                    title: "应用预设样式",
                    description: "使用预定义的样式模板",
                    icon: "paintbrush.fill"
                ) {
                    applyPresetStyle()
                }

                QuickConfigButton(
                    title: "复制其他类型",
                    description: "从其他按键类型复制配置",
                    icon: "doc.on.doc"
                ) {
                    copyFromOtherType()
                }

                QuickConfigButton(
                    title: "重置为默认",
                    description: "恢复到默认配置",
                    icon: "arrow.clockwise"
                ) {
                    resetToDefault()
                }
            }
        }
    }

    // MARK: - 辅助方法
    private func applyPresetStyle() {
        // 实现预设样式应用逻辑
    }

    private func copyFromOtherType() {
        // 实现从其他类型复制配置的逻辑
    }

    private func resetToDefault() {
        let defaultConfig = KeyTypeConfig(keyType: selectedKeyType)
        advancedThemeManager.updateKeyTypeConfig(
            for: selectedKeyType,
            in: theme,
            config: defaultConfig
        )

        // 显示成功提示
        successMessage = "「\(selectedKeyType.displayName)」已重置为默认配置"
        showingSuccessToast = true
    }

    private func getSampleKeys(for keyType: KeyType) -> [String] {
        switch keyType {
        case .letter:
            return ["A", "B", "C", "D", "E", "F", "G", "H"]
        case .number:
            return ["1", "2", "3", "4", "5", "6", "7", "8"]
        case .function:
            return ["⌫", "↵", "123", "⇧", "⌨️"]
        case .space:
            return ["空格"]
        case .shift:
            return ["⇧"]
        case .symbol:
            return ["@", "#", "$", "%", "&", "*", "+", "="]
        case .punctuation:
            return [".", ",", "?", "!", ";", ":", "'", "\""]
        }
    }

    private func saveCurrentTheme() {
        // 保存当前主题的所有配置
        advancedThemeManager.saveAdvancedTheme(theme)

        // 显示成功提示
        successMessage = "主题「\(theme.name)」配置已保存"
        showingSuccessToast = true
    }
}

// MARK: - 按键类型标签
struct KeyTypeTab: View {
    let keyType: KeyType
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Image(systemName: keyType.icon)
                    .font(.title3)

                Text(keyType.displayName)
                    .font(.caption)
                    .fontWeight(.medium)
            }
            .foregroundColor(isSelected ? .blue : .secondary)
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? Color.blue.opacity(0.1) : Color.clear)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 按键类型配置卡片
struct KeyTypeConfigCard: View {
    let config: KeyTypeConfig
    let onEdit: () -> Void

    var body: some View {
        VStack(spacing: 12) {
            // 颜色预览
            HStack(spacing: 8) {
                ColorPreviewCircle(color: config.defaultBackgroundColor, title: "背景")
                ColorPreviewCircle(color: config.defaultPressedColor, title: "按下")
                ColorPreviewCircle(color: config.defaultTextColor, title: "文字")
                ColorPreviewCircle(color: config.defaultBorderColor, title: "边框")

                Spacer()
            }

            // 配置详情
            VStack(alignment: .leading, spacing: 8) {
                ConfigDetailRow(title: "字体大小", value: "\(Int(config.defaultFontSize))pt")
                ConfigDetailRow(title: "字体粗细", value: config.defaultFontWeight.displayName)
                ConfigDetailRow(title: "圆角半径", value: "\(Int(config.defaultCornerRadius))pt")
                ConfigDetailRow(title: "边框宽度", value: "\(Int(config.defaultBorderWidth))pt")
            }

            // 编辑按钮
            Button(action: onEdit) {
                Text("编辑配置")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(8)
            }
        }
        .padding()
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 颜色预览圆圈
struct ColorPreviewCircle: View {
    let color: WidgetColor
    let title: String

    var body: some View {
        VStack(spacing: 4) {
            Circle()
                .fill(color.color)
                .frame(width: 24, height: 24)
                .overlay(
                    Circle()
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )

            Text(title)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

// MARK: - 配置详情行
struct ConfigDetailRow: View {
    let title: String
    let value: String

    var body: some View {
        HStack {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)

            Spacer()

            Text(value)
                .font(.caption)
                .fontWeight(.medium)
        }
    }
}

// MARK: - 空配置卡片
struct EmptyConfigCard: View {
    let keyType: KeyType
    let onCreate: () -> Void

    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: keyType.icon)
                .font(.system(size: 40))
                .foregroundColor(.secondary)

            VStack(spacing: 4) {
                Text("暂无\(keyType.displayName)配置")
                    .font(.subheadline)
                    .fontWeight(.medium)

                Text("点击创建自定义配置")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            Button(action: onCreate) {
                Text("创建配置")
                    .font(.subheadline)
                    .fontWeight(.medium)
                    .foregroundColor(.blue)
                    .padding(.horizontal, 20)
                    .padding(.vertical, 8)
                    .background(Color.blue.opacity(0.1))
                    .cornerRadius(16)
            }
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 30)
        .background(Color(.systemGray6))
        .cornerRadius(12)
    }
}

// MARK: - 影响的按键按钮
struct AffectedKeyButton: View {
    let keyValue: String
    let config: KeyConfig

    var body: some View {
        Button(action: {}) {
            Text(keyValue)
                .font(.system(size: CGFloat(config.fontSize * 0.7), weight: config.fontWeight.fontWeight))
                .foregroundColor(config.textColor.color)
                .frame(width: 40, height: 32)
                .background(config.backgroundColor.color)
                .cornerRadius(CGFloat(config.cornerRadius * 0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: CGFloat(config.cornerRadius * 0.8))
                        .stroke(config.borderColor.color, lineWidth: CGFloat(config.borderWidth * 0.5))
                )
        }
        .disabled(true)
    }
}

// MARK: - 快速配置按钮
struct QuickConfigButton: View {
    let title: String
    let description: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.blue)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 实时预览按键按钮
struct LivePreviewKeyButton: View {
    let keyValue: String
    let keyType: KeyType
    let theme: AdvancedKeyboardTheme
    let advancedThemeManager: AdvancedKeyboardThemeManager

    var body: some View {
        let config = advancedThemeManager.getEffectiveKeyConfig(
            for: keyValue,
            keyType: keyType,
            in: theme
        )

        Button(action: {}) {
            Text(keyValue == "空格" ? "" : keyValue)
                .font(.system(size: CGFloat(config.fontSize * 0.8), weight: config.fontWeight.fontWeight))
                .foregroundColor(config.textColor.color)
                .frame(width: 40, height: 32)
                .background(config.backgroundColor.color)
                .cornerRadius(CGFloat(config.cornerRadius * 0.8))
                .overlay(
                    RoundedRectangle(cornerRadius: CGFloat(config.cornerRadius * 0.8))
                        .stroke(config.borderColor.color, lineWidth: CGFloat(config.borderWidth * 0.5))
                )
        }
        .disabled(true)
    }
}

// MARK: - 预览
struct KeyTypeConfigView_Previews: PreviewProvider {
    static var previews: some View {
        KeyTypeConfigView(
            theme: AdvancedKeyboardTheme(
                name: "测试主题",
                baseTheme: KeyboardTheme.defaultThemes[0]
            )
        )
    }
}
