//
//  ImageThemeConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/23.
//

import SwiftUI
import PhotosUI
import MyWidgetKit

/// 图片主题配置视图
struct ImageThemeConfigView: View {
    // MARK: - 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态
    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared
    @State private var imageManager: KeyboardImageManager?

    @State private var selectedBackgroundItem: PhotosPickerItem?
    @State private var selectedKeyItem: PhotosPickerItem?
    @State private var backgroundImage: UIImage?
    @State private var keyImage: UIImage?
    @State private var themeName = ""
    @State private var imageOpacity: Double = 0.8
    @State private var showingPreview = false
    @State private var showingAlert = false
    @State private var alertMessage = ""

    // 布局
    private let columns = [
        GridItem(.adaptive(minimum: 150, maximum: 200), spacing: AppLayout.Spacing.medium)
    ]

    // MARK: - 视图主体
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 内置图片主题
                    builtInImageThemesSection

                    // 自定义图片主题创建
                    customImageThemeSection

                    // 图片主题管理
                    imageThemeManagementSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .navigationTitle("图片主题")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingPreview) {
            if let previewTheme = createPreviewTheme() {
                KeyboardPreviewView(theme: previewTheme)
                    .navigationTitle("主题预览")
                    .navigationBarTitleDisplayMode(.inline)
                    .toolbar {
                        ToolbarItem(placement: .navigationBarTrailing) {
                            Button("完成") {
                                showingPreview = false
                            }
                        }
                    }
            }
        }
        .alert("提示", isPresented: $showingAlert) {
            Button("确定") { }
        } message: {
            Text(alertMessage)
        }
        .task {
            if imageManager == nil {
                imageManager = await KeyboardImageManager.shared
            }
        }
        .onChange(of: selectedBackgroundItem) { newItem in
            _Concurrency.Task {
                if let data = try? await newItem?.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    backgroundImage = image
                }
            }
        }
        .onChange(of: selectedKeyItem) { newItem in
            _Concurrency.Task {
                if let data = try? await newItem?.loadTransferable(type: Data.self),
                   let image = UIImage(data: data) {
                    keyImage = image
                }
            }
        }
    }

    // MARK: - 内置图片主题
    private var builtInImageThemesSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("内置图片主题")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: columns, spacing: AppLayout.Spacing.medium) {
                ForEach(KeyboardTheme.builtInImageThemes, id: \.id) { theme in
                    ImageThemeCard(
                        theme: theme,
                        isSelected: keyboardThemeManager.currentTheme.id == theme.id
                    ) {
                        keyboardThemeManager.setTheme(theme)
                        showAlert("已应用主题：\(theme.name)")
                    }
                }
            }
        }
    }

    // MARK: - 自定义图片主题创建
    private var customImageThemeSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("创建自定义图片主题")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.medium) {
                // 主题名称输入
                TextField("主题名称", text: $themeName)
                    .textFieldStyle(RoundedBorderTextFieldStyle())

                // 背景图片选择
                imageSelectionCard(
                    title: "键盘背景图片",
                    description: "选择键盘的背景图片",
                    image: backgroundImage,
                    selectedItem: $selectedBackgroundItem
                )

                // 按键图片选择
                imageSelectionCard(
                    title: "按键背景图片",
                    description: "选择按键的背景图片",
                    image: keyImage,
                    selectedItem: $selectedKeyItem
                )

                // 透明度调节
                VStack(alignment: .leading, spacing: 8) {
                    Text("图片透明度")
                        .font(.subheadline)
                        .fontWeight(.medium)

                    HStack {
                        Slider(value: $imageOpacity, in: 0.1...1.0, step: 0.1)
                        Text("\(Int(imageOpacity * 100))%")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .frame(width: 40)
                    }
                }

                // 操作按钮
                HStack(spacing: AppLayout.Spacing.medium) {
                    Button("预览主题") {
                        showingPreview = true
                    }
                    .disabled(!canPreview())

                    Spacer()

                    Button("创建主题") {
                        createCustomTheme()
                    }
                    .disabled(!canCreateTheme())
                    .buttonStyle(.borderedProminent)
                }
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
    }

    // MARK: - 图片主题管理
    private var imageThemeManagementSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("自定义图片主题")
                .font(.headline)
                .fontWeight(.semibold)

            if keyboardThemeManager.customImageThemes.isEmpty {
                Text("暂无自定义图片主题")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            } else {
                LazyVGrid(columns: columns, spacing: AppLayout.Spacing.medium) {
                    ForEach(keyboardThemeManager.customImageThemes, id: \.id) { theme in
                        CustomImageThemeCard(
                            theme: theme,
                            isSelected: keyboardThemeManager.currentTheme.id == theme.id,
                            onApply: {
                                keyboardThemeManager.setTheme(theme)
                                showAlert("已应用主题：\(theme.name)")
                            },
                            onDelete: {
                                keyboardThemeManager.removeCustomTheme(theme)
                                showAlert("已删除主题：\(theme.name)")
                            }
                        )
                    }
                }
            }
        }
    }

    // MARK: - 图片选择卡片
    private func imageSelectionCard(
        title: String,
        description: String,
        image: UIImage?,
        selectedItem: Binding<PhotosPickerItem?>
    ) -> some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)

            PhotosPicker(selection: selectedItem, matching: .images) {
                HStack {
                    if let image = image {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 60, height: 60)
                            .clipShape(RoundedRectangle(cornerRadius: 8))
                    } else {
                        RoundedRectangle(cornerRadius: 8)
                            .fill(Color(.systemGray5))
                            .frame(width: 60, height: 60)
                            .overlay(
                                Image(systemName: "photo.badge.plus")
                                    .foregroundColor(.secondary)
                            )
                    }

                    VStack(alignment: .leading, spacing: 4) {
                        Text(image == nil ? "选择图片" : "已选择图片")
                            .fontWeight(.medium)
                        Text(description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "chevron.right")
                        .foregroundColor(.secondary)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(8)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(Color(.systemGray4), lineWidth: 1)
                )
            }
        }
    }

    // MARK: - 辅助方法
    private func canPreview() -> Bool {
        return backgroundImage != nil || keyImage != nil
    }

    private func canCreateTheme() -> Bool {
        return !themeName.isEmpty && (backgroundImage != nil || keyImage != nil)
    }

    private func createPreviewTheme() -> KeyboardTheme? {
        guard canPreview() else { return nil }

        let baseTheme = KeyboardTheme.defaultThemes[0]
        return KeyboardTheme(
            name: themeName.isEmpty ? "预览主题" : themeName,
            type: .custom,
            hasBackgroundImage: backgroundImage != nil,
            hasKeyImage: keyImage != nil,
            imageOpacity: imageOpacity
        )
    }

    private func createCustomTheme() {
        guard canCreateTheme() else { return }

        _Concurrency.Task {
            if let newTheme = await keyboardThemeManager.createCustomImageTheme(
                name: themeName,
                backgroundImage: backgroundImage,
                keyImage: keyImage
            ) {
                await MainActor.run {
                    // 重置输入
                    themeName = ""
                    backgroundImage = nil
                    keyImage = nil
                    selectedBackgroundItem = nil
                    selectedKeyItem = nil
                    imageOpacity = 0.8

                    showAlert("主题创建成功：\(newTheme.name)")
                }
            } else {
                await MainActor.run {
                    showAlert("主题创建失败，请重试")
                }
            }
        }
    }

    private func showAlert(_ message: String) {
        alertMessage = message
        showingAlert = true
    }
}
