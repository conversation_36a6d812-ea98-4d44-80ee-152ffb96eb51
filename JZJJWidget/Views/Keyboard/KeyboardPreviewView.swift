//
//  KeyboardPreviewView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 键盘预览视图
struct KeyboardPreviewView: View {
    let theme: KeyboardTheme

    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared
    @State private var backgroundImage: UIImage?
    @State private var keyImage: UIImage?

    // 键盘布局配置
    private let letterRows: [[String]] = [
        ["Q", "W", "E", "R", "T", "Y", "U", "I", "O", "P"],
        ["A", "S", "D", "F", "G", "H", "J", "K", "L"],
        ["Z", "X", "C", "V", "B", "N", "M"]
    ]

    private let numberRow: [String] = ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"]

    var body: some View {
        VStack(spacing: 0) {
            // 模拟输入区域
            mockInputArea

            // 键盘区域
            keyboardArea
        }
        .background(Color(.systemGray6))
    }

    // MARK: - 模拟输入区域
    private var mockInputArea: some View {
        VStack {
            Spacer()

            VStack(spacing: 16) {
                Text("键盘预览")
                    .font(.title2)
                    .fontWeight(.semibold)

                Text("这是键盘的预览效果，实际使用时会在输入框上方显示")
                    .font(.body)
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)

                // 模拟输入框
                HStack {
                    Text("Hello World!")
                        .foregroundColor(.primary)
                    Spacer()
                    Rectangle()
                        .fill(Color.blue)
                        .frame(width: 2, height: 20)
                        .animation(.easeInOut(duration: 1).repeatForever(), value: true)
                }
                .padding()
                .background(Color(.systemBackground))
                .cornerRadius(8)
                .padding(.horizontal)
            }

            Spacer()
        }
        .frame(maxHeight: .infinity)
        .task {
            await loadImages()
        }
    }

    private func loadImages() async {
        if theme.hasBackgroundImage {
            backgroundImage = await keyboardThemeManager.getBackgroundImage(for: theme)
        }
        if theme.hasKeyImage {
            keyImage = await keyboardThemeManager.getKeyImage(for: theme)
        }
    }

    // MARK: - 键盘区域
    private var keyboardArea: some View {
        ZStack {
            // 背景颜色
            theme.backgroundColor.color

            // 背景图片
            if theme.hasBackgroundImage,
               let backgroundImage = backgroundImage {
                Image(uiImage: backgroundImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .opacity(theme.imageOpacity)
            }

            // 键盘布局
            VStack(spacing: CGFloat(theme.keySpacing)) {
                // 数字行
                numberRowView

                // 字母行
                ForEach(Array(letterRows.enumerated()), id: \.offset) { index, row in
                    letterRowView(row: row, rowIndex: index)
                }

                // 底部功能行
                bottomRowView
            }
            .padding(CGFloat(theme.keySpacing))
        }
    }

    // MARK: - 数字行
    private var numberRowView: some View {
        HStack(spacing: CGFloat(theme.keySpacing)) {
            ForEach(numberRow, id: \.self) { number in
                KeyPreviewButton(
                    text: number,
                    theme: theme,
                    isSpecial: false
                )
            }
        }
    }

    // MARK: - 字母行
    private func letterRowView(row: [String], rowIndex: Int) -> some View {
        HStack(spacing: CGFloat(theme.keySpacing)) {
            // 第二行和第三行的特殊处理
            if rowIndex == 1 {
                // 第二行左侧间距
                Spacer()
                    .frame(width: 20)
            } else if rowIndex == 2 {
                // 第三行Shift键
                KeyPreviewButton(
                    text: "⇧",
                    theme: theme,
                    isSpecial: true,
                    width: 50
                )
            }

            // 字母键
            ForEach(row, id: \.self) { letter in
                KeyPreviewButton(
                    text: letter,
                    theme: theme,
                    isSpecial: false
                )
            }

            // 第二行和第三行的特殊处理
            if rowIndex == 1 {
                // 第二行右侧间距
                Spacer()
                    .frame(width: 20)
            } else if rowIndex == 2 {
                // 第三行删除键
                KeyPreviewButton(
                    text: "⌫",
                    theme: theme,
                    isSpecial: true,
                    width: 50
                )
            }
        }
    }

    // MARK: - 底部功能行
    private var bottomRowView: some View {
        HStack(spacing: CGFloat(theme.keySpacing)) {
            // 数字切换键
            KeyPreviewButton(
                text: "123",
                theme: theme,
                isSpecial: true,
                width: 60
            )

            // 地球键
            KeyPreviewButton(
                text: "🌐",
                theme: theme,
                isSpecial: true,
                width: 40
            )

            // 空格键
            KeyPreviewButton(
                text: "空格",
                theme: theme,
                isSpecial: true,
                isFlexible: true
            )

            // 回车键
            KeyPreviewButton(
                text: "回车",
                theme: theme,
                isSpecial: true,
                width: 60
            )
        }
    }
}

// MARK: - 按键预览组件
struct KeyPreviewButton: View {
    let text: String
    let theme: KeyboardTheme
    let isSpecial: Bool
    var width: CGFloat? = nil
    var isFlexible: Bool = false

    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared
    @State private var keyImage: UIImage?

    var body: some View {
        Button(action: {}) {
            ZStack {
                // 背景颜色
                backgroundColor
                    .cornerRadius(cornerRadius)

                // 按键图片
                if theme.hasKeyImage,
                   let keyImage = keyImage {
                    Image(uiImage: keyImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                        .opacity(theme.imageOpacity)
                        .cornerRadius(cornerRadius)
                }

                // 文字
                Text(text)
                    .font(.system(size: CGFloat(theme.fontSize), weight: fontWeight))
                    .foregroundColor(theme.textColor.color)
            }
            .frame(maxWidth: isFlexible ? .infinity : width, minHeight: CGFloat(theme.keyHeight))
            .frame(width: width)
            .overlay(
                RoundedRectangle(cornerRadius: cornerRadius)
                    .stroke(borderColor, lineWidth: borderWidth)
            )
            .shadow(
                color: theme.enableShadow ? theme.shadowColor.color : .clear,
                radius: CGFloat(theme.shadowRadius),
                x: 0,
                y: 1
            )
        }
        .disabled(true) // 预览模式下禁用交互
        .task {
            if theme.hasKeyImage {
                keyImage = await keyboardThemeManager.getKeyImage(for: theme)
            }
        }
    }

    private var backgroundColor: Color {
        isSpecial ? theme.specialKeyColor.color : theme.keyBackgroundColor.color
    }

    private var cornerRadius: CGFloat {
        switch theme.keyStyle {
        case .standard:
            return 6
        case .rounded:
            return 12
        case .circular:
            return 20
        case .flat:
            return 0
        }
    }

    private var borderColor: Color {
        theme.showBorder ? theme.borderColor.color : .clear
    }

    private var borderWidth: CGFloat {
        theme.showBorder ? CGFloat(theme.borderWidth) : 0
    }

    private var fontWeight: Font.Weight {
        switch theme.fontWeight {
        case .ultraLight: return .ultraLight
        case .thin: return .thin
        case .light: return .light
        case .regular: return .regular
        case .medium: return .medium
        case .semibold: return .semibold
        case .bold: return .bold
        case .heavy: return .heavy
        case .black: return .black
        }
    }
}

// MARK: - 键盘预览卡片
struct KeyboardPreviewCard: View {
    let theme: KeyboardTheme
    let onTap: () -> Void

    var body: some View {
        Button(action: onTap) {
            VStack(spacing: 12) {
                // 主题信息
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(theme.name)
                            .font(.headline)
                            .fontWeight(.semibold)

                        Text(theme.type.description)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }

                    Spacer()

                    Image(systemName: "eye.fill")
                        .foregroundColor(.blue)
                }

                // 简化的键盘预览
                VStack(spacing: 6) {
                    // 第一行
                    HStack(spacing: 4) {
                        ForEach(0..<10) { _ in
                            RoundedRectangle(cornerRadius: 4)
                                .fill(theme.keyBackgroundColor.color)
                                .frame(height: 24)
                        }
                    }

                    // 第二行
                    HStack(spacing: 4) {
                        Spacer().frame(width: 12)
                        ForEach(0..<9) { _ in
                            RoundedRectangle(cornerRadius: 4)
                                .fill(theme.keyBackgroundColor.color)
                                .frame(height: 24)
                        }
                        Spacer().frame(width: 12)
                    }

                    // 第三行
                    HStack(spacing: 4) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(theme.specialKeyColor.color)
                            .frame(width: 32, height: 24)

                        ForEach(0..<7) { _ in
                            RoundedRectangle(cornerRadius: 4)
                                .fill(theme.keyBackgroundColor.color)
                                .frame(height: 24)
                        }

                        RoundedRectangle(cornerRadius: 4)
                            .fill(theme.specialKeyColor.color)
                            .frame(width: 32, height: 24)
                    }

                    // 底部行
                    HStack(spacing: 4) {
                        RoundedRectangle(cornerRadius: 4)
                            .fill(theme.specialKeyColor.color)
                            .frame(width: 40, height: 24)

                        RoundedRectangle(cornerRadius: 4)
                            .fill(theme.specialKeyColor.color)
                            .frame(width: 30, height: 24)

                        RoundedRectangle(cornerRadius: 4)
                            .fill(theme.specialKeyColor.color)
                            .frame(height: 24)

                        RoundedRectangle(cornerRadius: 4)
                            .fill(theme.specialKeyColor.color)
                            .frame(width: 40, height: 24)
                    }
                }
                .padding(8)
                .background(theme.backgroundColor.color)
                .cornerRadius(8)
            }
            .padding()
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
struct KeyboardPreviewView_Previews: PreviewProvider {
    static var previews: some View {
        KeyboardPreviewView(theme: KeyboardTheme.defaultThemes[0])
    }
}
