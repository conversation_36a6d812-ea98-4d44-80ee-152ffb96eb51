//
//  AdvancedThemeConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 高级主题配置主界面
struct AdvancedThemeConfigView: View {
    // MARK: - 环境对象
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态管理
    @StateObject private var advancedThemeManager = AdvancedKeyboardThemeManager.shared
    @State private var selectedTheme: AdvancedKeyboardTheme?
    @State private var showingThemeCreator = false
    @State private var showingKeyTypeConfig = false
    @State private var showingIndividualKeyConfig = false
    @State private var showingImportExport = false
    @State private var showingDeleteAlert = false
    @State private var themeToDelete: AdvancedKeyboardTheme?
    @State private var showingSuccessToast = false
    @State private var successMessage = ""

    // 布局配置
    private let columns = [
        GridItem(.adaptive(minimum: 300, maximum: 400), spacing: AppLayout.Spacing.medium)
    ]

    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 当前主题状态
                    currentThemeSection

                    // 主题管理
                    themeManagementSection

                    // 可用主题列表
                    availableThemesSection

                    // 快速操作
                    quickActionsSection
                }
                .padding(AppLayout.Spacing.medium)
            }
            .navigationTitle("高级主题定制")
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("返回") {
                        dismiss()
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("新建") {
                        showingThemeCreator = true
                    }
                    .fontWeight(.semibold)
                }
            }
        }
        .sheet(isPresented: $showingThemeCreator) {
            AdvancedThemeCreatorView()
        }
        .sheet(isPresented: $showingKeyTypeConfig) {
            if let theme = selectedTheme {
                KeyTypeConfigView(theme: theme)
            }
        }
        .sheet(isPresented: $showingIndividualKeyConfig) {
            if let theme = selectedTheme {
                IndividualKeyConfigView(theme: theme)
            }
        }
        .sheet(isPresented: $showingImportExport) {
            ImportExportView()
        }
        .alert("删除主题", isPresented: $showingDeleteAlert) {
            Button("取消", role: .cancel) { }
            Button("删除", role: .destructive) {
                if let theme = themeToDelete {
                    advancedThemeManager.deleteAdvancedTheme(theme)
                }
            }
        } message: {
            if let theme = themeToDelete {
                Text("确定要删除主题「\(theme.name)」吗？此操作无法撤销。")
            }
        }
        .overlay(
            // 成功提示Toast
            VStack {
                Spacer()
                if showingSuccessToast {
                    SuccessToast(message: successMessage, theme: ThemeManager.shared.currentTheme)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                        .onAppear {
                            DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                                withAnimation {
                                    showingSuccessToast = false
                                }
                            }
                        }
                }
            }
            .animation(.easeInOut(duration: 0.3), value: showingSuccessToast)
        )
    }

    // MARK: - 当前主题状态
    private var currentThemeSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("当前主题")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                if advancedThemeManager.currentAdvancedTheme != nil {
                    HStack(spacing: 4) {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.green)
                            .font(.caption)
                        Text("已应用")
                            .font(.caption)
                            .fontWeight(.medium)
                            .foregroundColor(.green)
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(12)
                }
            }

            if let currentTheme = advancedThemeManager.currentAdvancedTheme {
                AdvancedThemeCard(
                    theme: currentTheme,
                    isSelected: true,
                    onTap: {
                        selectedTheme = currentTheme
                        showingKeyTypeConfig = true
                    },
                    onEdit: {
                        selectedTheme = currentTheme
                        showingKeyTypeConfig = true
                    },
                    onDuplicate: {
                        let _ = advancedThemeManager.duplicateAdvancedTheme(currentTheme)
                    },
                    onDelete: nil // 当前主题不能删除
                )
            } else {
                VStack(spacing: 16) {
                    HStack(spacing: 8) {
                        Image(systemName: "info.circle")
                            .foregroundColor(.blue)
                        Text("暂无应用的高级主题")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    Text("创建并应用您的第一个高级主题来开始个性化定制")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)

                    Button(action: {
                        showingThemeCreator = true
                    }) {
                        Text("立即创建")
                            .font(.subheadline)
                            .fontWeight(.semibold)
                            .foregroundColor(.white)
                            .padding(.horizontal, 24)
                            .padding(.vertical, 12)
                            .background(Color.blue)
                            .cornerRadius(20)
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color(.systemGray6))
                .cornerRadius(12)
            }
        }
    }

    // MARK: - 主题管理
    private var themeManagementSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("主题管理")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: AppLayout.Spacing.medium) {
                // 按键类型配置
                ManagementActionCard(
                    title: "按键类型配置",
                    description: "为不同类型的按键设置统一样式",
                    icon: "keyboard",
                    color: .blue
                ) {
                    if let current = advancedThemeManager.currentAdvancedTheme {
                        selectedTheme = current
                        showingKeyTypeConfig = true
                    } else {
                        showingThemeCreator = true
                    }
                }

                // 单个按键配置
                ManagementActionCard(
                    title: "单个按键配置",
                    description: "为特定按键设置独特样式",
                    icon: "key.fill",
                    color: .green
                ) {
                    if let current = advancedThemeManager.currentAdvancedTheme {
                        selectedTheme = current
                        showingIndividualKeyConfig = true
                    } else {
                        showingThemeCreator = true
                    }
                }

                // 导入导出
                ManagementActionCard(
                    title: "导入导出",
                    description: "分享和备份主题配置",
                    icon: "square.and.arrow.up",
                    color: .orange
                ) {
                    showingImportExport = true
                }

                // 重置设置
                ManagementActionCard(
                    title: "重置设置",
                    description: "恢复到默认主题配置",
                    icon: "arrow.clockwise",
                    color: .red
                ) {
                    resetToDefault()
                    successMessage = "已重置为默认主题配置"
                    showingSuccessToast = true
                }
            }
        }
    }

    // MARK: - 可用主题列表
    private var availableThemesSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            HStack {
                Text("我的主题")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                Text("\(advancedThemeManager.availableAdvancedThemes.count) 个主题")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }

            if advancedThemeManager.availableAdvancedThemes.isEmpty {
                EmptyStateView(
                    icon: "paintbrush.fill",
                    title: "暂无自定义主题",
                    description: "创建您的第一个高级主题",
                    actionTitle: "立即创建"
                ) {
                    showingThemeCreator = true
                }
            } else {
                LazyVGrid(columns: columns, spacing: AppLayout.Spacing.medium) {
                    ForEach(advancedThemeManager.availableAdvancedThemes) { theme in
                        AdvancedThemeCard(
                            theme: theme,
                            isSelected: advancedThemeManager.currentAdvancedTheme?.id == theme.id,
                            onTap: {
                                advancedThemeManager.applyAdvancedTheme(theme)
                                successMessage = "主题「\(theme.name)」已成功应用！"
                                showingSuccessToast = true
                            },
                            onEdit: {
                                selectedTheme = theme
                                showingKeyTypeConfig = true
                            },
                            onDuplicate: {
                                let _ = advancedThemeManager.duplicateAdvancedTheme(theme)
                            },
                            onDelete: {
                                themeToDelete = theme
                                showingDeleteAlert = true
                            }
                        )
                    }
                }
            }
        }
    }

    // MARK: - 快速操作
    private var quickActionsSection: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            Text("快速操作")
                .font(.headline)
                .fontWeight(.semibold)

            VStack(spacing: AppLayout.Spacing.small) {
                QuickActionButton(
                    title: "从基础主题创建",
                    description: "基于现有主题快速创建高级主题",
                    icon: "plus.rectangle.on.rectangle"
                ) {
                    showingThemeCreator = true
                }

                QuickActionButton(
                    title: "导入主题包",
                    description: "从文件或其他设备导入主题",
                    icon: "square.and.arrow.down"
                ) {
                    showingImportExport = true
                }

                QuickActionButton(
                    title: "主题预览",
                    description: "预览当前主题的键盘效果",
                    icon: "eye"
                ) {
                    // 显示预览
                }
            }
        }
    }

    // MARK: - 辅助方法
    private func resetToDefault() {
        advancedThemeManager.currentAdvancedTheme = nil
    }
}

// MARK: - 管理操作卡片
struct ManagementActionCard: View {
    let title: String
    let description: String
    let icon: String
    let color: Color
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(alignment: .leading, spacing: 12) {
                HStack {
                    Image(systemName: icon)
                        .font(.title2)
                        .foregroundColor(color)

                    Spacer()
                }

                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }

                Spacer()
            }
            .padding()
            .frame(height: 100)
            .background(Color(.systemGray6))
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 快速操作按钮
struct QuickActionButton: View {
    let title: String
    let description: String
    let icon: String
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack(spacing: 12) {
                Image(systemName: icon)
                    .font(.title3)
                    .foregroundColor(.blue)
                    .frame(width: 24)

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)

                    Text(description)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }

                Spacer()

                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.systemBackground))
            .cornerRadius(8)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(Color(.systemGray4), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}



// MARK: - 预览
struct AdvancedThemeConfigView_Previews: PreviewProvider {
    static var previews: some View {
        AdvancedThemeConfigView()
    }
}
