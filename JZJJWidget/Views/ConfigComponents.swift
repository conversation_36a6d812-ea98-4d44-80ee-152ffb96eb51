import SwiftUI
import PhotosUI

// MARK: - 配置部分容器
struct ConfigSectionContainer<Content: View>: View {
    let title: String
    let icon: String
    let theme: AppTheme
    let content: Content

    init(title: String, icon: String, theme: AppTheme, @ViewBuilder content: () -> Content) {
        self.title = title
        self.icon = icon
        self.theme = theme
        self.content = content()
    }

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            // 标题
            HStack {
                Image(systemName: icon)
                    .foregroundColor(theme.colors.accent)

                Text(title)
                    .font(theme.fonts.titleMedium)
                    .foregroundColor(theme.colors.text)
            }

            // 内容
            content
                .padding(.leading, 4)
        }
        .padding()
        .background(theme.colors.secondaryBackground)
        .cornerRadius(AppLayout.CornerRadius.medium)
    }
}

// MARK: - 背景选择视图
struct AppBackgroundSelectView: View {
    @Binding var background: WidgetBackground
    let theme: AppTheme

    @State private var showColorPicker: Bool = false
    @State private var showGradientPicker: Bool = false
    @State private var selectedItem: PhotosPickerItem?
    @State private var backgroundColor: Color = .white
    @State private var gradientColors: [Color] = [.blue, .purple]

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            // 背景类型选择
            Picker("背景类型", selection: Binding(
                get: {
                    switch background {
                    case .color: return 0
                    case .gradient: return 1
                    case .image: return 2
                    }
                },
                set: { newValue in
                    switch newValue {
                    case 0:
                        background = .color(WidgetColor.fromColor(backgroundColor))
                    case 1:
                        let colors = gradientColors.map { WidgetColor.fromColor($0) }
                        background = .gradient(WidgetGradient(
                            colors: colors,
                            startPoint: .topLeading,
                            endPoint: .bottomTrailing
                        ))
                    case 2:
                        // 保持图片背景不变，或者提示用户选择图片
                        if case .image = background {
                            // 已经是图片，不做改变
                        } else {
                            // 提示用户选择图片
                            selectedItem = nil
                        }
                    default:
                        break
                    }
                }
            )) {
                Text("纯色").tag(0)
                Text("渐变").tag(1)
                Text("图片").tag(2)
            }
            .pickerStyle(SegmentedPickerStyle())

            // 根据选择的背景类型显示不同的配置选项
            switch background {
            case .color:
                colorBackgroundOptions
            case .gradient:
                gradientBackgroundOptions
            case .image:
                imageBackgroundOptions
            }

            // 背景预览
            backgroundPreview
        }
    }

    // 纯色背景选项
    private var colorBackgroundOptions: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
            Text("选择颜色")
                .font(theme.fonts.bodyMedium)
                .foregroundColor(theme.colors.text)

            ColorPicker("背景颜色", selection: $backgroundColor)
                .onChange(of: backgroundColor) { newColor in
                    background = .color(WidgetColor.fromColor(newColor))
                }
                .onAppear {
                    if case .color(let widgetColor) = background {
                        backgroundColor = widgetColor.toColor()
                    }
                }
        }
    }

    // 渐变背景选项
    private var gradientBackgroundOptions: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
            Text("渐变颜色")
                .font(theme.fonts.bodyMedium)
                .foregroundColor(theme.colors.text)

            HStack {
                ColorPicker("起始颜色", selection: $gradientColors[0])
                    .labelsHidden()

                ColorPicker("结束颜色", selection: $gradientColors[1])
                    .labelsHidden()
            }
            .onChange(of: gradientColors) { newColors in
                let colors = newColors.map { WidgetColor.fromColor($0) }
                background = .gradient(WidgetGradient(
                    colors: colors,
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                ))
            }
            .onAppear {
                if case .gradient(let widgetGradient) = background {
                    gradientColors = widgetGradient.colors.map { $0.toColor() }
                }
            }
        }
    }

    // 图片背景选项
    private var imageBackgroundOptions: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
            Text("选择图片")
                .font(theme.fonts.bodyMedium)
                .foregroundColor(theme.colors.text)

            PhotosPicker(selection: $selectedItem, matching: .images) {
                HStack {
                    Image(systemName: "photo")
                    Text("从相册选择")
                }
                .foregroundColor(theme.colors.accent)
                .padding(.vertical, 8)
                .padding(.horizontal, 12)
                .background(theme.colors.accent.opacity(0.1))
                .cornerRadius(AppLayout.CornerRadius.small)
            }
            .onChange(of: selectedItem) { newItem in
                if let newItem = newItem {
                    Task {
                        if let data = try? await newItem.loadTransferable(type: Data.self) {
                            // 压缩图片
                            if let uiImage = UIImage(data: data) {
                                let compressedData = compressImage(uiImage, maxSizeKB: 500)
                                background = .image(compressedData)
                            }
                        }
                    }
                }
            }
        }
    }

    // 背景预览
    private var backgroundPreview: some View {
        VStack {
            Text("预览")
                .font(theme.fonts.captionMedium)
                .foregroundColor(theme.colors.subtext)
                .frame(maxWidth: .infinity, alignment: .leading)

            BackgroundView(background: background)
                .frame(height: 80)
                .cornerRadius(AppLayout.CornerRadius.small)
        }
    }

    // 压缩图片
    private func compressImage(_ image: UIImage, maxSizeKB: Int) -> Data {
        let maxSize = maxSizeKB * 1024
        var compression: CGFloat = 1.0
        var imageData = image.jpegData(compressionQuality: compression)!

        while imageData.count > maxSize && compression > 0.1 {
            compression -= 0.1
            imageData = image.jpegData(compressionQuality: compression)!
        }

        return imageData
    }
}

// MARK: - 字体选择视图
struct FontSelectView: View {
    @Binding var fontName: String
    @Binding var fontSize: Double
    @Binding var fontColor: Color
    let theme: AppTheme

    // 可用字体
    private let availableFonts = [
        "SF Pro",
        "Helvetica Neue",
        "Arial",
        "Georgia",
        "Times New Roman",
        "Courier New",
        "Avenir",
        "Futura",
        "Gill Sans"
    ]

    var body: some View {
        VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
            // 字体选择
            VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                Text("字体")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)

                Picker("字体", selection: $fontName) {
                    ForEach(availableFonts, id: \.self) { font in
                        Text(font).tag(font)
                    }
                }
                .pickerStyle(MenuPickerStyle())
            }

            // 字体大小
            VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                HStack {
                    Text("字体大小")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.text)

                    Spacer()

                    Text("\(Int(fontSize))")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.subtext)
                }

                Slider(value: $fontSize, in: 10...24, step: 1)
                    .accentColor(theme.colors.accent)
            }

            // 字体颜色
            VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                Text("字体颜色")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)

                ColorPicker("字体颜色", selection: $fontColor)
            }

            // 字体预览
            VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                Text("预览")
                    .font(theme.fonts.captionMedium)
                    .foregroundColor(theme.colors.subtext)

                Text("这是字体预览文本")
                    .font(.custom(fontName, size: fontSize))
                    .foregroundColor(fontColor)
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(theme.colors.tertiaryBackground)
                    .cornerRadius(AppLayout.CornerRadius.small)
            }
        }
    }
}
