import SwiftUI
import WidgetKit
import MyWidgetKit

struct MoonPhaseConfigView: View {
    // MARK: - 属性

    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // 状态变量
    @State private var previewSize: WidgetFamily = .systemMedium
    @State private var dropdownMenuID = "moon-phase-size-selector"
    @State private var showLunarDate: Bool = true
    @State private var displayMode: MoonPhaseDisplayMode = .detailed

    // 字体选择
    @State private var selectedFont: FontSelection = FontSelection(
        font: .system(size: 16, weight: .medium, design: .rounded),
        fontSize: 16,
        fontColor: .white
    )

    // 背景选择
    @State private var backgroundSelection: BackgroundSelection = .color(Color(red: 0.05, green: 0.05, blue: 0.2))

    // 配置数据
    @State private var widgetData: MoonPhaseWidgetData = MoonPhaseWidgetData()

    // 下拉菜单管理器
    @StateObject private var dropdownManager = DropdownMenuManager.shared

    // 支持的尺寸
    let supportedSizes: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]

    var body: some View {
        ZStack {
            // 主内容
            ScrollView {
                VStack(spacing: 20) {
                    // 预览区域
                    previewSection

                    // 配置选项
                    configSection
                }
                .padding(.bottom, 100)
            }

            // 尺寸选择器下拉菜单
            if dropdownManager.isMenuOpen(id: dropdownMenuID) {
                // 半透明背景遮罩，点击时关闭下拉菜单
                Color.black.opacity(0.1)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            dropdownManager.closeMenu(id: dropdownMenuID)
                        }
                    }
                    .zIndex(900)

                // 下拉菜单内容
                VStack(spacing: 0) {
                    ForEach(supportedSizes, id: \.self) { size in
                        Button(action: {
                            previewSize = size
                            // 触觉反馈
                            #if os(iOS)
                            let generator = UIImpactFeedbackGenerator(style: .light)
                            generator.impactOccurred()
                            #endif
                            // 关闭菜单
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                dropdownManager.closeMenu(id: dropdownMenuID)
                            }
                        }) {
                            HStack {
                                Image(systemName: sizeIcon(for: size))
                                    .font(.system(size: 16))
                                    .foregroundColor(theme.colors.accent)
                                    .frame(width: 24, height: 24)

                                Text(sizeName(for: size))
                                    .font(theme.fonts.bodyMedium)
                                    .foregroundColor(theme.colors.text)

                                Spacer()

                                if size == previewSize {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 14, weight: .semibold))
                                        .foregroundColor(theme.colors.accent)
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                size == previewSize ?
                                    theme.colors.accent.opacity(0.1) :
                                    Color.clear
                            )
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())

                        if size != supportedSizes.last {
                            Divider()
                                .padding(.horizontal, 8)
                        }
                    }
                }
                .background(theme.colors.surface)
                .cornerRadius(12)
                .shadow(color: theme.colors.shadow.opacity(0.15), radius: 10, x: 0, y: 5)
                .frame(width: 180)
                .position(x: UIScreen.main.bounds.width / 2, y: 60)
                .transition(.opacity.combined(with: .move(edge: .top)))
                .zIndex(1000)
            }
        }
        .navigationTitle("月相日历")
        .toolbar {
            ToolbarItem(placement: .principal) {
                // 自定义导航栏标题视图 - 使用封装的按钮组件
                Button(action: {
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                        dropdownManager.toggleMenu(id: dropdownMenuID)
                    }
                }) {
                    HStack(spacing: 4) {
                        Text(sizeName(for: previewSize))
                            .font(.headline)
                            .foregroundColor(theme.colors.text)

                        Image(systemName: "chevron.down")
                            .font(.caption)
                            .foregroundColor(theme.colors.subtext)
                            .rotationEffect(
                                dropdownManager.isMenuOpen(id: dropdownMenuID) ?
                                    Angle(degrees: 180) : Angle(degrees: 0)
                            )
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(theme.colors.surfaceVariant.opacity(0.5))
                    .cornerRadius(8)
                }
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    // 先更新数据，确保最新配置被保存
                    updateWidgetData()
                    saveWidgetData()

                    // 显示保存成功提示
                    #if os(iOS)
                    let generator = UINotificationFeedbackGenerator()
                    generator.notificationOccurred(.success)
                    #endif
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.colors.accent)
                }
            }
        }
        .onAppear {
            // 加载已保存的配置
            loadSavedConfig()
        }
    }

    // MARK: - 子视图

    // 预览区域
    private var previewSection: some View {
        VStack(spacing: 12) {
            Text("预览")
                .font(theme.fonts.headlineMedium)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal)

            // 预览内容
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 16)
                    .fill(theme.colors.surfaceVariant.opacity(0.3))
                    .frame(height: previewHeight)

                // 小组件预览
                UniversalWidgetPreviewView(
                    data: widgetData,
                    previewSize: $previewSize,
                    accentColor: theme.colors.accent,
                    backgroundColor: theme.colors.background,
                    surfaceColor: theme.colors.surfaceVariant.opacity(0.5),
                    textColor: theme.colors.text,
                    subtextColor: theme.colors.subtext
                ) { data, size in
                    AnyView(
                        MoonPhaseWidgetView(data: data)
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .clipped() // 确保内容不会溢出
                    )
                }
                .frame(height: previewHeight - 20) // 减去一些边距
            }
            .padding(.horizontal)
        }
        .padding(.top)
    }

    // 根据预览尺寸计算预览高度
    private var previewHeight: CGFloat {
        switch previewSize {
        case .systemSmall:
            return 180
        case .systemMedium:
            return 180
        case .systemLarge:
            return 380
        default:
            return 180
        }
    }

    // 配置选项区域
    private var configSection: some View {
        VStack(spacing: 24) {
            // 显示模式选择
            displayModeSection

            // 显示选项
            displayOptionsSection

            // 字体样式选择
            fontStyleSection

            // 背景选择
            backgroundSection
        }
        .padding()
        .background(theme.colors.surfaceVariant.opacity(0.5))
        .cornerRadius(12)
        .padding(.horizontal)
    }

    // 显示模式选择
    private var displayModeSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("显示模式")
                .font(theme.fonts.titleMedium)
                .foregroundColor(theme.colors.text)

            Picker("显示模式", selection: $displayMode) {
                ForEach(MoonPhaseDisplayMode.allCases, id: \.self) { mode in
                    Text(mode.rawValue).tag(mode)
                }
            }
            .pickerStyle(SegmentedPickerStyle())
            .onChange(of: displayMode) { newValue in
                updateWidgetData()
            }
        }
    }

    // 显示选项
    private var displayOptionsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("显示选项")
                .font(theme.fonts.titleMedium)
                .foregroundColor(theme.colors.text)

            Toggle(isOn: $showLunarDate) {
                Text("显示农历日期")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
            }
            .toggleStyle(SwitchToggleStyle(tint: theme.colors.accent))
            .onChange(of: showLunarDate) { newValue in
                updateWidgetData()
            }
        }
    }

    // 字体样式选择
    private var fontStyleSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("字体样式")
                .font(theme.fonts.titleMedium)
                .foregroundColor(theme.colors.text)

            FontSelectView(
                showFontPicker: true,
                showFontSizePicker: false,
                showFontColorPicker: true,
                onSelectionChanged: { selection in
                    selectedFont = selection
                    updateWidgetData()
                }
            )
        }
    }

    // 背景选择
    private var backgroundSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("背景设置")
                .font(theme.fonts.titleMedium)
                .foregroundColor(theme.colors.text)

            BackgroundSelectView(
                allowColor: true,
                allowImage: true,
                allowPackageImage: true,
                initialColor: Color(red: 0.05, green: 0.05, blue: 0.2),
                onSelection: { selection in
                    backgroundSelection = selection
                    updateWidgetData()
                }
            )
        }
    }

    // MARK: - 辅助方法

    // 更新小组件数据
    private func updateWidgetData() {
        // 更新字体颜色
        let fontColor = WidgetColor.fromColor(selectedFont.fontColor)

        // 更新背景
        let background = backgroundSelection.toWidgetBackground()

        // 更新小组件数据
        widgetData = MoonPhaseWidgetData(
            background: background,
            fontColor: fontColor,
            fontName: selectedFont.fontName,
            displayMode: displayMode,
            showLunarDate: showLunarDate
        )
    }

    // 加载已保存的配置
    private func loadSavedConfig() {
        if let savedData = AppGroupDataManager.shared.read(MoonPhaseWidgetData.self, for: .moonPhase, property: .config) {
            widgetData = savedData

            // 更新UI状态
            displayMode = savedData.displayMode
            showLunarDate = savedData.showLunarDate

            // 更新字体选择
            selectedFont = FontSelection(
                font: .system(size: 16),
                fontSize: 16,
                fontColor: savedData.fontColor.toColor()
            )

            // 更新背景选择
            switch savedData.background {
            case let .color(color):
                backgroundSelection = .color(color.toColor())
            case let .imageData(data):
                if let image = UIImage(data: data) {
                    backgroundSelection = .image(image)
                }
            case let .packageImage(name):
                backgroundSelection = .packageImage(name)
            default:
                backgroundSelection = .color(Color(red: 0.05, green: 0.05, blue: 0.2))
            }
        } else {
            // 使用默认配置
            updateWidgetData()
        }
    }

    // 保存小组件数据
    private func saveWidgetData() {
        // 保存到AppGroup
        AppGroupDataManager.shared.save(widgetData, for: .moonPhase, property: .config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示保存成功提示
        #if os(iOS)
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)
        #endif
    }

    // MARK: - 尺寸选择器辅助方法

    /// 获取尺寸对应的图标
    private func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        case .systemExtraLarge:
            return "square.grid.2x2"
        @unknown default:
            return "square"
        }
    }

    /// 获取尺寸对应的名称
    private func sizeName(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }
}

#Preview {
    NavigationView {
        MoonPhaseConfigView()
            .environmentObject(ThemeManager.shared)
    }
}
