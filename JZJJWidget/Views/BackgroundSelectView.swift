//
//  BackgroundSelectView.swift
//  JZJJWidget
//
//  Created by Augment on 2025/5/25.
//

import SwiftUI
import MyWidgetKit

/// 背景选择视图
/// 用于选择小组件的背景（纯色、渐变、图片）
struct LegacyBackgroundSelectView: View {
    @Binding var background: WidgetBackground
    let theme: AppTheme

    @State private var selectedTab: Int = 0
    @State private var selectedColor: Color = .white
    @State private var selectedGradient: GradientPreset = .blueToGreen
    @State private var showImagePicker: Bool = false
    @State private var selectedUIImage: UIImage?

    // 渐变预设
    enum GradientPreset: String, CaseIterable, Identifiable {
        case blueToGreen = "蓝绿渐变"
        case purpleToBlue = "紫蓝渐变"
        case orangeToRed = "橙红渐变"
        case pinkToPurple = "粉紫渐变"
        case greenToYellow = "绿黄渐变"

        var id: String { rawValue }

        var gradient: WidgetGradient {
            switch self {
            case .blueToGreen:
                return WidgetGradient(
                    colors: [
                        WidgetColor(red: 0.0, green: 0.5, blue: 1.0, alpha: 1.0),
                        WidgetColor(red: 0.0, green: 0.8, blue: 0.5, alpha: 1.0)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            case .purpleToBlue:
                return WidgetGradient(
                    colors: [
                        WidgetColor(red: 0.5, green: 0.0, blue: 0.8, alpha: 1.0),
                        WidgetColor(red: 0.0, green: 0.5, blue: 1.0, alpha: 1.0)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            case .orangeToRed:
                return WidgetGradient(
                    colors: [
                        WidgetColor(red: 1.0, green: 0.6, blue: 0.0, alpha: 1.0),
                        WidgetColor(red: 1.0, green: 0.2, blue: 0.2, alpha: 1.0)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            case .pinkToPurple:
                return WidgetGradient(
                    colors: [
                        WidgetColor(red: 1.0, green: 0.4, blue: 0.7, alpha: 1.0),
                        WidgetColor(red: 0.6, green: 0.0, blue: 0.8, alpha: 1.0)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            case .greenToYellow:
                return WidgetGradient(
                    colors: [
                        WidgetColor(red: 0.0, green: 0.8, blue: 0.4, alpha: 1.0),
                        WidgetColor(red: 1.0, green: 0.9, blue: 0.0, alpha: 1.0)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
            }
        }

        var preview: some View {
            LinearGradient(
                colors: gradient.colors.map { $0.toColor() },
                startPoint: gradient.startPoint,
                endPoint: gradient.endPoint
            )
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
    }

    var body: some View {
        VStack(spacing: 12) {
            // 选项卡
            Picker("背景类型", selection: $selectedTab) {
                Text("纯色").tag(0)
                Text("渐变").tag(1)
                Text("图片").tag(2)
            }
            .pickerStyle(SegmentedPickerStyle())
            .onChange(of: selectedTab) { newValue in
                updateBackgroundBasedOnTab()
            }

            // 背景选择内容
            switch selectedTab {
            case 0: // 纯色
                colorSelector
            case 1: // 渐变
                gradientSelector
            case 2: // 图片
                imageSelector
            default:
                EmptyView()
            }
        }
        .onAppear {
            // 初始化选择的选项卡和值
            initializeFromBackground()
        }
    }

    // 纯色选择器
    private var colorSelector: some View {
        VStack(spacing: 12) {
            // 预设颜色
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach([Color.white, Color.black, Color.blue, Color.green, Color.orange, Color.purple, Color.red, Color.pink, Color.yellow, Color.teal], id: \.self) { color in
                        Button(action: {
                            selectedColor = color
                            background = .color(WidgetColor.fromColor(color))
                        }) {
                            Circle()
                                .fill(color)
                                .frame(width: 36, height: 36)
                                .overlay(
                                    Circle()
                                        .stroke(color == selectedColor ? theme.colors.text : Color.clear, lineWidth: 2)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }

                    // 自定义颜色选择器
                    ColorPicker("", selection: $selectedColor)
                        .labelsHidden()
                        .frame(width: 36, height: 36)
                        .background(
                            Circle()
                                .fill(theme.colors.secondaryBackground)
                        )
                        .overlay(
                            Circle()
                                .stroke(theme.colors.border, lineWidth: 1)
                        )
                        .onChange(of: selectedColor) { newValue in
                            background = .color(WidgetColor.fromColor(newValue))
                        }
                }
                .padding(.vertical, 8)
            }

            // 颜色预览
            RoundedRectangle(cornerRadius: 8)
                .fill(selectedColor)
                .frame(height: 60)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(theme.colors.border, lineWidth: 1)
                )
        }
    }

    // 渐变选择器
    private var gradientSelector: some View {
        VStack(spacing: 12) {
            // 渐变预设
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(GradientPreset.allCases) { preset in
                        Button(action: {
                            selectedGradient = preset
                            background = .gradient(preset.gradient)
                        }) {
                            preset.preview
                                .frame(width: 60, height: 36)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 8)
                                        .stroke(preset == selectedGradient ? theme.colors.text : Color.clear, lineWidth: 2)
                                )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                .padding(.vertical, 8)
            }

            // 渐变预览
            selectedGradient.preview
                .frame(height: 60)
                .overlay(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(theme.colors.border, lineWidth: 1)
                )
        }
    }

    // 图片选择器
    private var imageSelector: some View {
        VStack(spacing: 12) {
            // 图片选择按钮
            Button(action: {
                showImagePicker = true
            }) {
                HStack {
                    Image(systemName: "photo")
                    Text("从相册选择图片")
                }
                .foregroundColor(theme.colors.accent)
                .padding(.vertical, 8)
                .padding(.horizontal, 16)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .stroke(theme.colors.accent, lineWidth: 1)
                )
            }
            .buttonStyle(PlainButtonStyle())

            // 图片预览
            if let selectedUIImage = selectedUIImage {
                Image(uiImage: selectedUIImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 120)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(theme.colors.border, lineWidth: 1)
                    )
            } else if case .image(let imageData) = background, let uiImage = UIImage(data: imageData) {
                Image(uiImage: uiImage)
                    .resizable()
                    .aspectRatio(contentMode: .fill)
                    .frame(height: 120)
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(theme.colors.border, lineWidth: 1)
                    )
                    .onAppear {
                        selectedUIImage = uiImage
                    }
            } else {
                RoundedRectangle(cornerRadius: 8)
                    .fill(theme.colors.secondaryBackground)
                    .frame(height: 120)
                    .overlay(
                        VStack {
                            Image(systemName: "photo")
                                .font(.system(size: 24))
                            Text("未选择图片")
                                .font(.caption)
                        }
                        .foregroundColor(theme.colors.subtext)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 8)
                            .stroke(theme.colors.border, lineWidth: 1)
                    )
            }
        }
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(selectedImage: $selectedUIImage, isPresented: $showImagePicker)
                .onDisappear {
                    if let image = selectedUIImage, let imageData = image.jpegData(compressionQuality: 0.7) {
                        background = .image(imageData)
                    }
                }
        }
    }

    // 初始化选择的选项卡和值
    private func initializeFromBackground() {
        switch background {
        case .color(let color):
            selectedTab = 0
            selectedColor = color.toColor()
        case .gradient(let gradient):
            selectedTab = 1
            // 尝试匹配预设渐变
            if let matchedPreset = GradientPreset.allCases.first(where: { preset in
                // 简单比较第一个颜色
                let presetFirstColor = preset.gradient.colors.first!
                let gradientFirstColor = gradient.colors.first!
                return abs(presetFirstColor.red - gradientFirstColor.red) < 0.1 &&
                       abs(presetFirstColor.green - gradientFirstColor.green) < 0.1 &&
                       abs(presetFirstColor.blue - gradientFirstColor.blue) < 0.1
            }) {
                selectedGradient = matchedPreset
            } else {
                // 默认使用第一个预设
                selectedGradient = .blueToGreen
            }
        case .image(let imageData):
            selectedTab = 2
            if let uiImage = UIImage(data: imageData) {
                selectedUIImage = uiImage
            }
        }
    }

    // 根据选项卡更新背景
    private func updateBackgroundBasedOnTab() {
        switch selectedTab {
        case 0: // 纯色
            background = .color(WidgetColor.fromColor(selectedColor))
        case 1: // 渐变
            background = .gradient(selectedGradient.gradient)
        case 2: // 图片
            if let image = selectedUIImage, let imageData = image.jpegData(compressionQuality: 0.7) {
                background = .image(imageData)
            }
        default:
            break
        }
    }
}

// 图片选择器
struct ImagePicker: UIViewControllerRepresentable {
    @Binding var selectedImage: UIImage?
    @Binding var isPresented: Bool

    func makeUIViewController(context: Context) -> UIImagePickerController {
        let picker = UIImagePickerController()
        picker.delegate = context.coordinator
        picker.sourceType = .photoLibrary
        return picker
    }

    func updateUIViewController(_ uiViewController: UIImagePickerController, context: Context) {}

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    class Coordinator: NSObject, UIImagePickerControllerDelegate, UINavigationControllerDelegate {
        let parent: ImagePicker

        init(_ parent: ImagePicker) {
            self.parent = parent
        }

        func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
            if let image = info[.originalImage] as? UIImage {
                parent.selectedImage = image
            }
            parent.isPresented = false
        }

        func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
            parent.isPresented = false
        }
    }
}
