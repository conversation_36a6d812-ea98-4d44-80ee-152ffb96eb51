//
//  DailyQuoteConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/13.
//

import MyWidgetKit
import SwiftUI
import WidgetKit
import UIKit

struct DailyQuoteConfigView: View {
    // MARK: - 属性

    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.presentationMode) var presentationMode
    // 支持的尺寸
    let supportedSizes: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // 下拉菜单ID
    @State private var dropdownMenuID = "daily-quote-size-selector"
    // 尺寸选择器引用
    @State private var sizeSelector: TopDropdownSizeSelector?
    // 状态变量
    /// 当前背景设置
    @State private var background: BackgroundSelection = .color(.white)
    /// 当前字体设置
    @State private var fontSelection = FontSelection(
        font: .system(size: 18),
        fontSize: 18,
        fontColor: .black
    )
    /// 内容类型 0=每日一言 1=随机内容
    @State private var contentType: Int = 0
    /// 自定义内容
    @State private var customContent: String = ""
    /// 显示成功提示
    @State private var showSuccessToast: Bool = false
    /// 预览缩放比例
    @State private var previewScale: CGFloat = 1.0

    // 内容数据
    /// 每日一言内容
    let dailyQuote: String = "生活不止眼前的苟且，还有诗和远方。"
    /// 随机内容列表
    let randomQuotes: [String] = [
        "世界很大，梦想更大。",
        "保持热爱，奔赴山海。",
        "心有猛虎，细嗅蔷薇。",
        "愿你出走半生，归来仍是少年。",
    ]

    /// 当前内容
    var currentContent: String {
        if contentType == 0 {
            return dailyQuote
        } else if contentType == 1 {
            return randomQuotes.randomElement() ?? "美好的一天从这里开始！"
        } else {
            return customContent.isEmpty ? "请输入自定义内容" : customContent
        }
    }

    /// 预览尺寸
    @State private var previewSize: WidgetFamily = .systemSmall
    /// 预览数据
    var previewData: DailyQuoteWidgetViewData {
        DailyQuoteWidgetViewData(
            quote: currentContent,
            background: {
                switch background {
                case let .color(color):
                    return .color(WidgetColor.fromColor(color))
                case let .image(img):
                    if let pngData = img.pngData() {
                        return .imageData(pngData)
                    } else {
                        return .color(WidgetColor.fromColor(.white))
                    }
                case let .packageImage(name):
                    return .packageImage(name)
                }
            }(),
            date: Date(),
            config: nil
        )
    }

    var body: some View {
        ZStack {
            // 背景
            theme.colors.background
                .ignoresSafeArea()

            // 主内容
            VStack(spacing: 0) {
                // 预览区域 - 固定在顶部
                previewSection
                    .padding(.horizontal)
                    .padding(.top, 16)
                    .padding(.bottom, 8)
                    .background(theme.colors.surface.opacity(0.5))
                    .shadow(color: theme.colors.shadow.opacity(0.1), radius: 4, x: 0, y: 2)

                // 可滚动的配置区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 内容设置
                        contentSection

                        // 样式设置
                        styleSection
                    }
                    .padding()
                }
            }

            // 成功提示
            if showSuccessToast {
                VStack {
                    Spacer()

                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.white)

                        Text("保存成功")
                            .foregroundColor(.white)
                            .font(theme.fonts.bodyMedium)
                    }
                    .padding()
                    .background(
                        Capsule()
                            .fill(theme.colors.success)
                            .shadow(color: theme.colors.shadow, radius: 8, x: 0, y: 4)
                    )
                    .padding(.bottom, 50)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .zIndex(100)
            }

            // 尺寸选择器下拉菜单
            TopDropdownSizeSelector(
                selectedSize: $previewSize,
                supportedSizes: supportedSizes,
                menuYPosition: 110, // 调整位置，确保在导航栏下方显示
                menuWidth: 180,
                menuID: dropdownMenuID
            )
            .onAppear { sizeSelector = $0 }
        }
        .navigationTitle("每日一言")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .principal) {
                navBarSizeSelector
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    saveWidget()
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.colors.accent)
                }
            }
        }
        .onAppear {
            loadExistingData()
        }
    }

    // MARK: - 子视图

    // 预览区域 - 使用优化后的紧凑版预览视图
    private var previewSection: some View {
        VStack(alignment: .center, spacing: 8) {
            DailyQuoteWidgetView(
                data: previewData,
                fontName: getFontName(),
                fontSize: fontSelection.fontSize,
                fontColor: fontSelection.fontColor,
                isPreviewMode: true // 关键：设置为预览模式
            )
            .frame(width: WidgetPreviewSizeHelper.widgetSize(for: previewSize).width, height: WidgetPreviewSizeHelper.widgetSize(for: previewSize).height)
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        }
    }

    // 获取导航栏尺寸选择器 - 可以作为导航栏的 titleView 使用
    private var navBarSizeSelector: some View {
        SizeSelectorButton(
            selectedSize: previewSize,
            onTap: {
                sizeSelector?.toggleMenu()
            },
            menuID: dropdownMenuID
        )
    }

    // 内容设置
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("内容设置")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)

            // 内容类型选择
            Picker("内容类型", selection: $contentType) {
                Text("每日一言").tag(0)
                Text("随机内容").tag(1)
                Text("自定义").tag(2)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.vertical, 8)

            // 自定义内容输入框（仅在选择自定义时显示）
            if contentType == 2 {
                TextField("请输入自定义内容", text: $customContent, axis: .vertical)
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
                    .lineLimit(3 ... 5)
                    .textFieldStyle(PlainTextFieldStyle())
                    .padding(12)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(theme.colors.surfaceVariant)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(theme.colors.border, lineWidth: 1)
                    )
            }
        }
        .padding(.vertical, 8)
    }

    // 样式设置
    private var styleSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("样式设置")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)

            // 背景设置
            VStack(alignment: .leading, spacing: 8) {
                Text("背景设置")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                BackgroundSelectView(
                    allowColor: true,
                    allowImage: true,
                    allowPackageImage: true,
                    colors: [.white, .black, .gray, .red, .blue, .green, .orange, .purple, .pink],
                    colorNames: ["白色", "黑色", "灰色", "红色", "蓝色", "绿色", "橙色", "紫色", "粉色"],
                    packageImageNames: ["background1", "background2", "background3", "background4", "background5"],
                    packageImageDisplayNames: ["渐变蓝", "渐变紫", "星空", "大理石", "抽象"],
                    initialColor: getInitialBackgroundColor(),
                    initialImage: getInitialBackgroundImage(),
                    initialPackageImageName: getInitialPackageImageName(),
                    onSelection: { selection in
                        background = selection
                    }
                )
            }

            // 字体设置
            VStack(alignment: .leading, spacing: 8) {
                Text("字体设置")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                FontSelectView(
                    showFontPicker: true,
                    showFontSizePicker: true,
                    showFontColorPicker: true,
                    onSelectionChanged: { selection in
                        fontSelection = selection
                    }
                )
            }
        }
        .padding(.vertical, 8)
    }

    // MARK: - 辅助方法

    // 获取尺寸文本
    private func sizeText(for size: WidgetFamily) -> String {
        switch size {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }

    // 根据组件尺寸返回预览高度
    private func previewHeightForFamily(_ family: WidgetFamily) -> CGFloat {
        switch family {
        case .systemSmall:
            return 320
        case .systemMedium:
            return 350
        case .systemLarge:
            return 480 // 大尺寸需要更多空间
        case .systemExtraLarge:
            return 520 // 超大尺寸需要更多空间
        @unknown default:
            return 320
        }
    }

    // 获取字体名称
    private func getFontName() -> String {
        // 从 fontSelection 中提取字体名称
        // 这里简化处理，实际应根据 FontSelection 中的 font 属性提取
        return "PingFangSC"
    }

    // 获取初始背景颜色
    private func getInitialBackgroundColor() -> Color {
        switch background {
        case let .color(color):
            return color
        default:
            return .white
        }
    }

    // 获取初始背景图片
    private func getInitialBackgroundImage() -> UIImage? {
        switch background {
        case let .image(image):
            return image
        default:
            return nil
        }
    }

    // 获取初始内置背景图片名称
    private func getInitialPackageImageName() -> String? {
        switch background {
        case let .packageImage(name):
            return name
        default:
            return nil
        }
    }

    // 加载现有数据 - 使用统一的数据模型
    private func loadExistingData() {
        // 尝试从 AppGroupDataManager 加载统一配置
        if let widgetConfig = AppGroupDataManager.shared.read(DailyQuoteWidgetConfig.self, for: .dailyQuote, property: .config) {
            // 加载背景设置
            if case let .color(widgetColor) = widgetConfig.background {
                background = .color(widgetColor.toColor())
            } else if case let .imageFile(fileName) = widgetConfig.background {
                // 构建文件名
                let backgroundImageFileName = AppGroupDataManager.shared.fileName(for: WidgetType.dailyQuote, property: WidgetPropertyKey.backgroundImage)
                // 读取数据
                if let imageData = AppGroupDataManager.shared.readData(fileName: backgroundImageFileName),
                   let image = UIImage(data: imageData)
                {
                    background = .image(image)
                }
            } else if case let .packageImage(name) = widgetConfig.background {
                // 加载内置背景图片
                background = .packageImage(name)
            }

            // 加载内容
            customContent = widgetConfig.content

            // 设置内容类型
            if !widgetConfig.content.isEmpty {
                contentType = 2 // 自定义内容
            }

            // 加载字体设置
            fontSelection = FontSelection(
                font: .system(size: widgetConfig.fontSize),
                fontSize: widgetConfig.fontSize,
                fontColor: widgetConfig.fontColor
            )
        } else {
            // 尝试从旧的数据格式加载（兼容性处理）
//            loadLegacyData()
        }
    }

    // 保存小组件 - 使用统一的数据模型
    private func saveWidget() {
        // 1. 保存背景图片（如果有）
        var widgetBackground: WidgetBackground

        switch background {
        case let .image(image):
            // 使用优化的压缩方法
            if let compressedData = image.compressForWidgetOptimized(
                targetFileSize: 60 * 1024, // 60KB 对背景图片足够
                minQuality: 0.5, // 可以接受较低质量
                minSize: CGSize(width: 300, height: 300), // 最小尺寸限制
                maxPixelArea: 800000, // 限制最大像素面积
                preserveAspectRatio: true // 保持宽高比
            ) {
                AppGroupDataManager.shared.saveAuto(compressedData, for: WidgetType.dailyQuote, property: WidgetPropertyKey.backgroundImage)
                widgetBackground = .imageFile(AppGroupDataManager.shared.fileName(for: WidgetType.dailyQuote, property: WidgetPropertyKey.backgroundImage))

                // 打印压缩信息，便于调试
                print("每日一言背景图片压缩后大小: \(compressedData.count / 1024) KB")
            } else {
                widgetBackground = .color(WidgetColor.fromColor(.white))
            }
        case let .color(color):
            widgetBackground = .color(WidgetColor.fromColor(color))
        case let .packageImage(name):
            // 使用内置背景图片
            widgetBackground = .packageImage(name)
        }

        // 2. 创建统一的配置
        let widgetConfig = DailyQuoteWidgetConfig(
            content: contentType == 2 ? customContent : currentContent,
            background: widgetBackground,
            fontName: getFontName(),
            fontSize: fontSelection.fontSize,
            fontColor: fontSelection.fontColor,
            showBorder: false,
            borderColor: .clear,
            lastUpdated: Date()
        )

        // 3. 保存统一配置到 AppGroupDataManager
        AppGroupDataManager.shared.save(widgetConfig, for: .dailyQuote, property: .config)

        // 4. 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 5. 显示成功提示
        withAnimation {
            showSuccessToast = true
        }

        // 6. 3秒后隐藏提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation {
                showSuccessToast = false
            }
        }
    }

    /// 获取尺寸对应的图标
    private func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        case .systemExtraLarge:
            return "square.grid.2x2"
        @unknown default:
            return "square"
        }
    }

    // 获取组件尺寸
    func widgetSize(for family: WidgetFamily) -> CGSize {
        switch family {
        case .systemSmall:
            return CGSize(width: 170, height: 170)
        case .systemMedium:
            return CGSize(width: 360, height: 170)
        case .systemLarge:
            return CGSize(width: 360, height: 380)
        case .systemExtraLarge:
            return CGSize(width: 360, height: 380)
        @unknown default:
            return CGSize(width: 170, height: 170)
        }
    }

    // 获取屏幕宽度
    func getScreenWidth() -> CGFloat {
        #if os(iOS)
        return UIScreen.main.bounds.width
        #else
        return 390.0 // 默认宽度
        #endif
    }
}

#Preview {
    DailyQuoteConfigView()
        .environmentObject(ThemeManager.shared)
}
