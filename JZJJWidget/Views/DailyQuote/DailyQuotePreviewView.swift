import SwiftUI
import MyWidgetKit

///// 每日一言预览视图 - 在 SwiftUI 中使用 DailyQuoteWidgetView
//struct DailyQuotePreviewView: View {
//    @State private var data: DailyQuoteData
//    @State private var widgetSize: WidgetSize = .medium
//
//    init(data: DailyQuoteData? = nil) {
//        // 如果没有提供数据，使用默认数据
//        let defaultData = DailyQuoteData(
//            quote: "生活不是等待风暴过去，而是学会在雨中跳舞。",
//            author: "佚名",
//            date: DateComponents(
//                year: Calendar.current.component(.year, from: Date()),
//                month: Calendar.current.component(.month, from: Date()),
//                day: Calendar.current.component(.day, from: Date()),
//                weekday: Calendar.current.component(.weekday, from: Date())
//            ),
//            background: .color(.blue),
//            fontColor: .white,
//            fontSize: 16
//        )
//
//        _data = State(initialValue: data ?? defaultData)
//    }
//
//    var body: some View {
//        VStack(spacing: 20) {
//            // 小组件尺寸选择器
//            Picker("小组件尺寸", selection: $widgetSize) {
//                Text("小尺寸").tag(WidgetSize.small)
//                Text("中尺寸").tag(WidgetSize.medium)
//                Text("大尺寸").tag(WidgetSize.large)
//            }
//            .pickerStyle(SegmentedPickerStyle())
//            .padding(.horizontal)
//
//            // 小组件预览
//            ZStack {
//                // 使用 DailyQuoteWidgetView 并设置为非小组件环境
//                DailyQuoteWidgetView(
//                    data: data,
//                    family: widgetSize.toWidgetFamily(),
//                    isPreviewMode: true // 关键：设置为预览模式
//                )
//                    .inWidgetEnvironment(false) // 关键：设置为非小组件环境
//                    .frame(width: widgetSize.dimensions.width, height: widgetSize.dimensions.height)
//                    .clipShape(RoundedRectangle(cornerRadius: 20))
//                    .shadow(color: Color.black.opacity(0.2), radius: 10, x: 0, y: 5)
//            }
//            .padding()
//            .frame(height: widgetSize.dimensions.height + 40)
//
//            // 编辑按钮
//            Button(action: {
//                // 这里可以添加编辑功能
//            }) {
//                Text("编辑每日一言")
//                    .font(.headline)
//                    .foregroundColor(.white)
//                    .padding()
//                    .frame(maxWidth: .infinity)
//                    .background(Color.blue)
//                    .cornerRadius(10)
//            }
//            .padding(.horizontal)
//
//            Spacer()
//        }
//        .navigationTitle("每日一言预览")
//    }
//}
//
//// 小组件尺寸辅助类型
//extension WidgetSize {
//    var dimensions: (width: CGFloat, height: CGFloat) {
//        switch self {
//        case .small:
//            return (155, 155)
//        case .medium:
//            return (329, 155)
//        case .large:
//            return (329, 345)
//        }
//    }
//
//    func toWidgetFamily() -> WidgetFamily {
//        switch self {
//        case .small:
//            return .systemSmall
//        case .medium:
//            return .systemMedium
//        case .large:
//            return .systemLarge
//        }
//    }
//}
//
//// 预览
//struct DailyQuotePreviewView_Previews: PreviewProvider {
//    static var previews: some View {
//        NavigationView {
//            DailyQuotePreviewView()
//        }
//    }
//}
