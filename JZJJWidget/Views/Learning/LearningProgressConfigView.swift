import SwiftUI
import MyWidgetKit
import WidgetKit

// WidgetBackground 扩展
extension WidgetBackground {
    var colorValue: WidgetColor? {
        if case let .color(color) = self {
            return color
        }
        return nil
    }

    var imageValue: UIImage? {
        switch self {
        case let .imageData(data):
            return UIImage(data: data)
        case let .imageFile(path):
            let fileURL = AppGroupDataManager.shared.getFileURL(fileName: path)
            return UIImage(contentsOfFile: fileURL.path)
        case .imageURL, .color, .packageImage:
            return nil
        }
    }

    var packageImageName: String? {
        switch self {
        case let .packageImage(name):
            return name
        case .color, .imageData, .imageFile, .imageURL:
            return nil
        }
    }
}

struct LearningProgressConfigView: View {
    // MARK: - 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态
    @State private var learningGoals: [LearningGoal] = []
    @State private var selectedGoalIds: [String] = []
    @State private var displayMode: LearningDisplayMode = .progress
    @State private var showPercentage: Bool = true
    @State private var showTimeSpent: Bool = true
    @State private var maxGoalsCount: Int = 4

    // 外观设置
    @State private var background: WidgetBackground = .color(WidgetColor.fromColor(.white))
    @State private var fontName: String = "SF Pro"
    @State private var fontSize: Double = 14
    @State private var fontColor: Color = .black
    @State private var accentColor: Color = .blue

    // 小组件尺寸
    @State private var selectedFamily: WidgetFamily = .systemMedium

    // 数据管理器
    private let dataManager = AppGroupDataManager.shared

    // MARK: - 主视图
    var body: some View {
        VStack(spacing: 0) {
            // 预览区域
            previewSection
                .padding(.top)

            // 配置选项
            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 学习目标
                    goalsSection

                    // 显示选项
                    displayOptionsSection

                    // 外观设置
                    appearanceSection
                }
                .padding()
            }
        }
        .background(themeManager.currentTheme.colors.background)
        .navigationTitle("学习进度")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("保存") {
                    saveConfig()
                }
                .foregroundColor(themeManager.currentTheme.colors.accent)
            }
        }
        .onAppear {
            loadData()
        }
    }

    // MARK: - 预览区域
    private var previewSection: some View {
        VStack {
            Text("预览")
                .font(themeManager.currentTheme.fonts.captionMedium)
                .foregroundColor(themeManager.currentTheme.colors.subtext)
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal)

            LearningProgressWidgetView(
                data: LearningProgressWidgetData(
                    goals: getSelectedGoals(),
                    displayMode: displayMode,
                    showPercentage: showPercentage,
                    showTimeSpent: showTimeSpent,
                    maxGoalsCount: maxGoalsCount,
                    background: background,
                    fontName: fontName,
                    fontSize: fontSize,
                    fontColor: fontColor,
                    accentColor: accentColor
                ),
                family: selectedFamily
            )
            .frame(height: getPreviewHeight())
            .padding()

            // 尺寸选择器
            Picker("尺寸", selection: $selectedFamily) {
                Text("小尺寸").tag(WidgetFamily.systemSmall)
                Text("中尺寸").tag(WidgetFamily.systemMedium)
                Text("大尺寸").tag(WidgetFamily.systemLarge)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal)
        }
        .padding(.bottom)
        .background(themeManager.currentTheme.colors.secondaryBackground)
        .cornerRadius(AppLayout.CornerRadius.medium)
        .padding(.horizontal)
    }

    // MARK: - 学习目标区域
    private var goalsSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "学习目标",
            iconName: "book.fill"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                if learningGoals.isEmpty {
                    emptyGoalsView
                } else {
                    goalsList
                }

                addGoalButton
            }
        }
    }

    // 空目标视图
    private var emptyGoalsView: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            Image(systemName: "book.fill")
                .font(.system(size: 40))
                .foregroundColor(themeManager.currentTheme.colors.subtext)

            Text("暂无学习目标")
                .font(themeManager.currentTheme.fonts.bodyMedium)
                .foregroundColor(themeManager.currentTheme.colors.subtext)

            Text("点击下方按钮添加学习目标")
                .font(themeManager.currentTheme.fonts.captionMedium)
                .foregroundColor(themeManager.currentTheme.colors.subtext)
        }
        .frame(maxWidth: .infinity)
        .padding()
    }

    // 目标列表
    private var goalsList: some View {
        ForEach(learningGoals) { goal in
            LearningGoalRow(
                goal: goal,
                isSelected: selectedGoalIds.contains(goal.id),
                theme: themeManager.currentTheme,
                onToggle: { toggleGoalSelection(goal) },
                onEdit: { editGoal(goal) },
                onDelete: { deleteGoal(goal) }
            )
        }
    }

    // 添加目标按钮
    private var addGoalButton: some View {
        Button(action: addNewGoal) {
            HStack {
                Image(systemName: "plus.circle.fill")
                Text("添加学习目标")
            }
            .foregroundColor(themeManager.currentTheme.colors.accent)
            .padding(.vertical, 8)
        }
    }

    // MARK: - 显示选项区域
    private var displayOptionsSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "显示选项",
            iconName: "eye"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                // 显示模式
                Picker("显示模式", selection: $displayMode) {
                    Text("进度条").tag(LearningDisplayMode.progress)
                    Text("环形图").tag(LearningDisplayMode.circular)
                    Text("统计视图").tag(LearningDisplayMode.statistics)
                }
                .pickerStyle(SegmentedPickerStyle())

                // 显示百分比
                Toggle("显示百分比", isOn: $showPercentage)
                    .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.colors.accent))

                // 显示学习时间
                Toggle("显示学习时间", isOn: $showTimeSpent)
                    .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.colors.accent))

                // 最大显示数量
                Stepper("最大显示数量: \(maxGoalsCount)", value: $maxGoalsCount, in: 1...6)
            }
        }
    }

    // MARK: - 外观设置区域
    private var appearanceSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "外观设置",
            iconName: "paintbrush"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                // 背景设置
                BackgroundSelectView(
                    allowColor: true,
                    allowImage: true,
                    initialColor: background.colorValue?.toColor() ?? .white,
                    initialImage: background.imageValue,
                    onSelection: { selection in
                        background = selection.toWidgetBackground()
                    }
                )

                // 字体设置
                FontSelectView(
                    showFontPicker: true,
                    showFontSizePicker: true,
                    showFontColorPicker: true,
                    onSelectionChanged: { selection in
                        fontName = selection.fontName
                        fontSize = selection.fontSize
                        fontColor = selection.fontColor
                    }
                )

                // 强调色
                ColorPicker("强调色", selection: $accentColor)
                    .foregroundColor(themeManager.currentTheme.colors.text)
            }
        }
    }

    // MARK: - 辅助方法

    // 加载数据
    private func loadData() {
        // 加载学习目标
        learningGoals = LearningDataManager.shared.getLearningGoals()

        // 加载配置
        if let config = dataManager.getLearningProgressConfig() {
            selectedGoalIds = config.goalIds
            displayMode = config.displayMode
            showPercentage = config.showPercentage
            showTimeSpent = config.showTimeSpent
            maxGoalsCount = config.maxGoalsCount
            background = config.background
            fontName = config.fontName
            fontSize = config.fontSize
            fontColor = config.fontColor
            accentColor = config.accentColor
        } else {
            // 默认选择所有目标
            selectedGoalIds = learningGoals.map { $0.id }
        }
    }

    // 保存配置
    private func saveConfig() {
        let config = LearningProgressConfig(
            goalIds: selectedGoalIds,
            displayMode: displayMode,
            showPercentage: showPercentage,
            showTimeSpent: showTimeSpent,
            maxGoalsCount: maxGoalsCount,
            background: background,
            fontName: fontName,
            fontSize: fontSize,
            fontColor: fontColor,
            accentColor: accentColor,
            lastUpdated: Date()
        )

        dataManager.saveLearningProgressConfig(config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        UINotificationFeedbackGenerator().notificationOccurred(.success)

        // 关闭视图
        dismiss()
    }

    // 切换目标选择
    private func toggleGoalSelection(_ goal: LearningGoal) {
        if selectedGoalIds.contains(goal.id) {
            selectedGoalIds.removeAll { $0 == goal.id }
        } else {
            selectedGoalIds.append(goal.id)
        }
    }

    // 添加新目标
    private func addNewGoal() {
        // 这里应该弹出添加目标的界面
        // 暂时添加一个示例目标
        let newGoal = LearningGoal(
            id: UUID().uuidString,
            name: "新学习目标 \(learningGoals.count + 1)",
            subject: "科目 \(learningGoals.count + 1)",
            totalUnits: 100,
            completedUnits: 0,
            timeSpent: 0,
            targetDate: Date().addingTimeInterval(30 * 24 * 60 * 60),
            color: [.blue, .green, .orange, .purple, .red].randomElement() ?? .blue
        )

        learningGoals.append(newGoal)
        selectedGoalIds.append(newGoal.id)
        LearningDataManager.shared.saveLearningGoals(learningGoals)
    }

    // 编辑目标
    private func editGoal(_ goal: LearningGoal) {
        // 这里应该弹出编辑目标的界面
        // 暂时模拟更新进度
        if let index = learningGoals.firstIndex(where: { $0.id == goal.id }) {
            var updatedGoal = goal
            updatedGoal.completedUnits = min(updatedGoal.completedUnits + 10, updatedGoal.totalUnits)
            updatedGoal.timeSpent += 30
            learningGoals[index] = updatedGoal
            LearningDataManager.shared.saveLearningGoals(learningGoals)
        }
    }

    // 删除目标
    private func deleteGoal(_ goal: LearningGoal) {
        learningGoals.removeAll { $0.id == goal.id }
        selectedGoalIds.removeAll { $0 == goal.id }
        LearningDataManager.shared.saveLearningGoals(learningGoals)
    }

    // 获取选中的目标
    private func getSelectedGoals() -> [LearningGoal] {
        return learningGoals.filter { selectedGoalIds.contains($0.id) }
    }

    // 获取预览高度
    private func getPreviewHeight() -> CGFloat {
        switch selectedFamily {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 170
        case .systemLarge:
            return 370
        default:
            return 170
        }
    }
}

// MARK: - 学习目标行
struct LearningGoalRow: View {
    let goal: LearningGoal
    let isSelected: Bool
    let theme: AppTheme
    let onToggle: () -> Void
    let onEdit: () -> Void
    let onDelete: () -> Void

    var body: some View {
        HStack {
            // 选择按钮
            Button(action: onToggle) {
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? theme.colors.accent : theme.colors.subtext)
            }

            VStack(alignment: .leading, spacing: 4) {
                // 名称和科目
                HStack {
                    Text(goal.name)
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.text)

                    Text(goal.subject)
                        .font(theme.fonts.captionMedium)
                        .foregroundColor(theme.colors.subtext)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(goal.color.opacity(0.1))
                        .cornerRadius(4)
                }

                // 进度
                HStack {
                    Text("\(goal.completedUnits)/\(goal.totalUnits) 单元")
                        .font(theme.fonts.captionMedium)
                        .foregroundColor(theme.colors.subtext)

                    Text("(\(Int(goal.progress * 100))%)")
                        .font(theme.fonts.captionMedium)
                        .foregroundColor(goal.color)
                }
            }

            Spacer()

            // 编辑按钮
            Button(action: onEdit) {
                Image(systemName: "pencil")
                    .foregroundColor(theme.colors.subtext)
            }
            .padding(.horizontal, 4)

            // 删除按钮
            Button(action: onDelete) {
                Image(systemName: "trash")
                    .foregroundColor(theme.colors.error)
            }
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 12)
        .background(isSelected ? theme.colors.accent.opacity(0.1) : Color.clear)
        .cornerRadius(AppLayout.CornerRadius.small)
    }
}

// MARK: - 预览
#Preview {
    NavigationStack {
        LearningProgressConfigView()
            .environmentObject(ThemeManager.shared)
    }
}
