import SwiftUI
import MyWidgetKit
import WidgetKit

struct LearningProgressWidgetView: View {
    // MARK: - 属性
    let data: LearningProgressWidgetData
    let family: WidgetFamily
    
    // MARK: - 主视图
    var body: some View {
        ZStack {
            // 背景
            BackgroundView(background: data.background)
            
            // 根据尺寸选择不同布局
            switch family {
            case .systemSmall:
                smallWidgetLayout
            case .systemMedium:
                mediumWidgetLayout
            case .systemLarge:
                largeWidgetLayout
            default:
                mediumWidgetLayout
            }
        }
    }
    
    // MARK: - 小尺寸布局
    private var smallWidgetLayout: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题
            Text("学习进度")
                .font(.custom(data.fontName, size: data.fontSize + 2))
                .foregroundColor(data.fontColor)
                .fontWeight(.bold)
            
            if data.goals.isEmpty {
                // 空状态
                Text("暂无学习目标")
                    .font(.custom(data.fontName, size: data.fontSize))
                    .foregroundColor(data.fontColor.opacity(0.7))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
            } else if data.displayMode == .circular {
                // 环形图
                if let primaryGoal = data.goals.first {
                    VStack(spacing: 8) {
                        // 目标名称
                        Text(primaryGoal.name)
                            .font(.custom(data.fontName, size: data.fontSize))
                            .foregroundColor(data.fontColor)
                            .lineLimit(1)
                            .frame(maxWidth: .infinity, alignment: .center)
                        
                        // 环形进度
                        ZStack {
                            Circle()
                                .stroke(data.fontColor.opacity(0.2), lineWidth: 10)
                            
                            Circle()
                                .trim(from: 0, to: CGFloat(primaryGoal.progress))
                                .stroke(primaryGoal.color, lineWidth: 10)
                                .rotationEffect(.degrees(-90))
                            
                            VStack {
                                if data.showPercentage {
                                    Text("\(Int(primaryGoal.progress * 100))%")
                                        .font(.custom(data.fontName, size: data.fontSize + 8))
                                        .foregroundColor(data.fontColor)
                                        .fontWeight(.bold)
                                }
                                
                                if data.showTimeSpent {
                                    Text(formatTime(primaryGoal.timeSpent))
                                        .font(.custom(data.fontName, size: data.fontSize - 2))
                                        .foregroundColor(data.fontColor.opacity(0.7))
                                }
                            }
                        }
                        .frame(width: 100, height: 100)
                        .frame(maxWidth: .infinity, alignment: .center)
                    }
                }
            } else {
                // 进度条
                VStack(spacing: 12) {
                    ForEach(data.goals.prefix(2), id: \.id) { goal in
                        VStack(alignment: .leading, spacing: 4) {
                            // 目标名称
                            Text(goal.name)
                                .font(.custom(data.fontName, size: data.fontSize))
                                .foregroundColor(data.fontColor)
                                .lineLimit(1)
                            
                            // 进度信息
                            HStack {
                                if data.showPercentage {
                                    Text("\(Int(goal.progress * 100))%")
                                        .font(.custom(data.fontName, size: data.fontSize - 2))
                                        .foregroundColor(goal.color)
                                }
                                
                                if data.showTimeSpent {
                                    Spacer()
                                    
                                    Text(formatTime(goal.timeSpent))
                                        .font(.custom(data.fontName, size: data.fontSize - 2))
                                        .foregroundColor(data.fontColor.opacity(0.7))
                                }
                            }
                            
                            // 进度条
                            GeometryReader { geometry in
                                ZStack(alignment: .leading) {
                                    // 背景
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(data.fontColor.opacity(0.2))
                                        .frame(height: 8)
                                    
                                    // 进度
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(goal.color)
                                        .frame(width: min(CGFloat(goal.progress) * geometry.size.width, geometry.size.width), height: 8)
                                }
                            }
                            .frame(height: 8)
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding()
    }
    
    // MARK: - 中尺寸布局
    private var mediumWidgetLayout: some View {
        VStack(alignment: .leading, spacing: 8) {
            // 标题
            Text("学习进度")
                .font(.custom(data.fontName, size: data.fontSize + 2))
                .foregroundColor(data.fontColor)
                .fontWeight(.bold)
            
            if data.goals.isEmpty {
                // 空状态
                Text("暂无学习目标")
                    .font(.custom(data.fontName, size: data.fontSize))
                    .foregroundColor(data.fontColor.opacity(0.7))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
            } else if data.displayMode == .circular {
                // 环形图
                HStack(spacing: 16) {
                    ForEach(data.goals.prefix(min(3, data.maxGoalsCount)), id: \.id) { goal in
                        VStack(spacing: 8) {
                            // 环形进度
                            ZStack {
                                Circle()
                                    .stroke(data.fontColor.opacity(0.2), lineWidth: 8)
                                
                                Circle()
                                    .trim(from: 0, to: CGFloat(goal.progress))
                                    .stroke(goal.color, lineWidth: 8)
                                    .rotationEffect(.degrees(-90))
                                
                                if data.showPercentage {
                                    Text("\(Int(goal.progress * 100))%")
                                        .font(.custom(data.fontName, size: data.fontSize))
                                        .foregroundColor(data.fontColor)
                                        .fontWeight(.bold)
                                }
                            }
                            .frame(width: 70, height: 70)
                            
                            // 目标名称
                            Text(goal.name)
                                .font(.custom(data.fontName, size: data.fontSize - 1))
                                .foregroundColor(data.fontColor)
                                .lineLimit(1)
                                .frame(width: 80)
                                .multilineTextAlignment(.center)
                            
                            if data.showTimeSpent {
                                Text(formatTime(goal.timeSpent))
                                    .font(.custom(data.fontName, size: data.fontSize - 3))
                                    .foregroundColor(data.fontColor.opacity(0.7))
                            }
                        }
                        .frame(maxWidth: .infinity)
                    }
                }
            } else if data.displayMode == .statistics {
                // 统计视图
                VStack(spacing: 8) {
                    // 总体进度
                    HStack {
                        Text("总体进度")
                            .font(.custom(data.fontName, size: data.fontSize))
                            .foregroundColor(data.fontColor)
                        
                        Spacer()
                        
                        Text("\(Int(averageProgress * 100))%")
                            .font(.custom(data.fontName, size: data.fontSize + 2))
                            .foregroundColor(data.accentColor)
                            .fontWeight(.bold)
                    }
                    
                    // 总体进度条
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // 背景
                            RoundedRectangle(cornerRadius: 4)
                                .fill(data.fontColor.opacity(0.2))
                                .frame(height: 8)
                            
                            // 进度
                            RoundedRectangle(cornerRadius: 4)
                                .fill(data.accentColor)
                                .frame(width: min(CGFloat(averageProgress) * geometry.size.width, geometry.size.width), height: 8)
                        }
                    }
                    .frame(height: 8)
                    
                    // 总学习时间
                    HStack {
                        Text("总学习时间")
                            .font(.custom(data.fontName, size: data.fontSize))
                            .foregroundColor(data.fontColor)
                        
                        Spacer()
                        
                        Text(formatTime(totalTimeSpent))
                            .font(.custom(data.fontName, size: data.fontSize + 2))
                            .foregroundColor(data.accentColor)
                            .fontWeight(.bold)
                    }
                    .padding(.top, 8)
                    
                    // 目标数量
                    HStack {
                        Text("学习目标")
                            .font(.custom(data.fontName, size: data.fontSize))
                            .foregroundColor(data.fontColor)
                        
                        Spacer()
                        
                        Text("\(data.goals.count)")
                            .font(.custom(data.fontName, size: data.fontSize + 2))
                            .foregroundColor(data.accentColor)
                            .fontWeight(.bold)
                    }
                }
            } else {
                // 进度条
                VStack(spacing: 12) {
                    ForEach(data.goals.prefix(data.maxGoalsCount), id: \.id) { goal in
                        VStack(alignment: .leading, spacing: 4) {
                            // 目标名称和科目
                            HStack {
                                Text(goal.name)
                                    .font(.custom(data.fontName, size: data.fontSize))
                                    .foregroundColor(data.fontColor)
                                    .lineLimit(1)
                                
                                Text(goal.subject)
                                    .font(.custom(data.fontName, size: data.fontSize - 2))
                                    .foregroundColor(data.fontColor.opacity(0.7))
                                    .padding(.horizontal, 4)
                                    .padding(.vertical, 1)
                                    .background(goal.color.opacity(0.1))
                                    .cornerRadius(4)
                            }
                            
                            // 进度信息
                            HStack {
                                Text("\(goal.completedUnits)/\(goal.totalUnits)")
                                    .font(.custom(data.fontName, size: data.fontSize - 2))
                                    .foregroundColor(data.fontColor.opacity(0.7))
                                
                                if data.showPercentage {
                                    Text("\(Int(goal.progress * 100))%")
                                        .font(.custom(data.fontName, size: data.fontSize - 2))
                                        .foregroundColor(goal.color)
                                }
                                
                                Spacer()
                                
                                if data.showTimeSpent {
                                    Text(formatTime(goal.timeSpent))
                                        .font(.custom(data.fontName, size: data.fontSize - 2))
                                        .foregroundColor(data.fontColor.opacity(0.7))
                                }
                            }
                            
                            // 进度条
                            GeometryReader { geometry in
                                ZStack(alignment: .leading) {
                                    // 背景
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(data.fontColor.opacity(0.2))
                                        .frame(height: 8)
                                    
                                    // 进度
                                    RoundedRectangle(cornerRadius: 4)
                                        .fill(goal.color)
                                        .frame(width: min(CGFloat(goal.progress) * geometry.size.width, geometry.size.width), height: 8)
                                }
                            }
                            .frame(height: 8)
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding()
    }
    
    // MARK: - 大尺寸布局
    private var largeWidgetLayout: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题
            Text("学习进度")
                .font(.custom(data.fontName, size: data.fontSize + 4))
                .foregroundColor(data.fontColor)
                .fontWeight(.bold)
            
            if data.goals.isEmpty {
                // 空状态
                Text("暂无学习目标")
                    .font(.custom(data.fontName, size: data.fontSize))
                    .foregroundColor(data.fontColor.opacity(0.7))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
            } else {
                // 总体进度
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("总体进度")
                            .font(.custom(data.fontName, size: data.fontSize + 2))
                            .foregroundColor(data.fontColor)
                            .fontWeight(.medium)
                        
                        Spacer()
                        
                        Text("\(Int(averageProgress * 100))%")
                            .font(.custom(data.fontName, size: data.fontSize + 2))
                            .foregroundColor(data.accentColor)
                            .fontWeight(.bold)
                        
                        if data.showTimeSpent {
                            Text("(\(formatTime(totalTimeSpent)))")
                                .font(.custom(data.fontName, size: data.fontSize))
                                .foregroundColor(data.fontColor.opacity(0.7))
                        }
                    }
                    
                    // 总体进度条
                    GeometryReader { geometry in
                        ZStack(alignment: .leading) {
                            // 背景
                            RoundedRectangle(cornerRadius: 6)
                                .fill(data.fontColor.opacity(0.2))
                                .frame(height: 12)
                            
                            // 进度
                            RoundedRectangle(cornerRadius: 6)
                                .fill(data.accentColor)
                                .frame(width: min(CGFloat(averageProgress) * geometry.size.width, geometry.size.width), height: 12)
                        }
                    }
                    .frame(height: 12)
                }
                .padding(.vertical, 8)
                .padding(.horizontal, 4)
                .background(data.accentColor.opacity(0.05))
                .cornerRadius(12)
                
                // 分隔线
                Rectangle()
                    .fill(data.fontColor.opacity(0.1))
                    .frame(height: 1)
                    .padding(.vertical, 4)
                
                if data.displayMode == .circular {
                    // 环形图
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 100))], spacing: 16) {
                        ForEach(data.goals.prefix(data.maxGoalsCount), id: \.id) { goal in
                            VStack(spacing: 8) {
                                // 环形进度
                                ZStack {
                                    Circle()
                                        .stroke(data.fontColor.opacity(0.2), lineWidth: 10)
                                    
                                    Circle()
                                        .trim(from: 0, to: CGFloat(goal.progress))
                                        .stroke(goal.color, lineWidth: 10)
                                        .rotationEffect(.degrees(-90))
                                    
                                    VStack {
                                        if data.showPercentage {
                                            Text("\(Int(goal.progress * 100))%")
                                                .font(.custom(data.fontName, size: data.fontSize + 2))
                                                .foregroundColor(data.fontColor)
                                                .fontWeight(.bold)
                                        }
                                        
                                        if data.showTimeSpent {
                                            Text(formatTime(goal.timeSpent))
                                                .font(.custom(data.fontName, size: data.fontSize - 2))
                                                .foregroundColor(data.fontColor.opacity(0.7))
                                        }
                                    }
                                }
                                .frame(width: 90, height: 90)
                                
                                // 目标名称
                                Text(goal.name)
                                    .font(.custom(data.fontName, size: data.fontSize))
                                    .foregroundColor(data.fontColor)
                                    .lineLimit(1)
                                    .frame(width: 100)
                                    .multilineTextAlignment(.center)
                                
                                // 科目
                                Text(goal.subject)
                                    .font(.custom(data.fontName, size: data.fontSize - 2))
                                    .foregroundColor(data.fontColor.opacity(0.7))
                                    .padding(.horizontal, 6)
                                    .padding(.vertical, 2)
                                    .background(goal.color.opacity(0.1))
                                    .cornerRadius(4)
                            }
                        }
                    }
                } else {
                    // 进度条
                    VStack(spacing: 16) {
                        ForEach(data.goals.prefix(data.maxGoalsCount), id: \.id) { goal in
                            VStack(alignment: .leading, spacing: 6) {
                                // 目标名称和科目
                                HStack {
                                    Text(goal.name)
                                        .font(.custom(data.fontName, size: data.fontSize + 2))
                                        .foregroundColor(data.fontColor)
                                        .fontWeight(.medium)
                                    
                                    Spacer()
                                    
                                    Text(goal.subject)
                                        .font(.custom(data.fontName, size: data.fontSize - 1))
                                        .foregroundColor(data.fontColor.opacity(0.7))
                                        .padding(.horizontal, 6)
                                        .padding(.vertical, 2)
                                        .background(goal.color.opacity(0.1))
                                        .cornerRadius(4)
                                }
                                
                                // 进度信息
                                HStack {
                                    Text("\(goal.completedUnits)/\(goal.totalUnits) 单元")
                                        .font(.custom(data.fontName, size: data.fontSize))
                                        .foregroundColor(data.fontColor)
                                    
                                    if data.showPercentage {
                                        Text("\(Int(goal.progress * 100))%")
                                            .font(.custom(data.fontName, size: data.fontSize))
                                            .foregroundColor(goal.color)
                                            .fontWeight(.medium)
                                    }
                                    
                                    Spacer()
                                    
                                    if data.showTimeSpent {
                                        HStack {
                                            Image(systemName: "clock")
                                                .foregroundColor(data.fontColor.opacity(0.7))
                                            
                                            Text(formatTime(goal.timeSpent))
                                                .font(.custom(data.fontName, size: data.fontSize))
                                                .foregroundColor(data.fontColor.opacity(0.7))
                                        }
                                    }
                                }
                                
                                // 进度条
                                GeometryReader { geometry in
                                    ZStack(alignment: .leading) {
                                        // 背景
                                        RoundedRectangle(cornerRadius: 6)
                                            .fill(data.fontColor.opacity(0.2))
                                            .frame(height: 10)
                                        
                                        // 进度
                                        RoundedRectangle(cornerRadius: 6)
                                            .fill(goal.color)
                                            .frame(width: min(CGFloat(goal.progress) * geometry.size.width, geometry.size.width), height: 10)
                                    }
                                }
                                .frame(height: 10)
                            }
                            .padding(8)
                            .background(goal.color.opacity(0.05))
                            .cornerRadius(10)
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding()
    }
    
    // MARK: - 辅助方法
    
    // 格式化时间
    private func formatTime(_ minutes: Int) -> String {
        if minutes < 60 {
            return "\(minutes)分钟"
        } else {
            let hours = minutes / 60
            let remainingMinutes = minutes % 60
            
            if remainingMinutes == 0 {
                return "\(hours)小时"
            } else {
                return "\(hours)小时\(remainingMinutes)分钟"
            }
        }
    }
    
    // 计算平均进度
    private var averageProgress: Double {
        if data.goals.isEmpty {
            return 0
        }
        
        let total = data.goals.reduce(0) { $0 + $1.progress }
        return total / Double(data.goals.count)
    }
    
    // 计算总学习时间
    private var totalTimeSpent: Int {
        return data.goals.reduce(0) { $0 + $1.timeSpent }
    }
}

// MARK: - 预览
#Preview {
    Group {
        LearningProgressWidgetView(
            data: LearningProgressWidgetData(
                goals: [
                    LearningGoal(id: "1", name: "Swift编程", subject: "编程", totalUnits: 100, completedUnits: 65, timeSpent: 1200, targetDate: Date().addingTimeInterval(30 * 24 * 60 * 60), color: .blue),
                    LearningGoal(id: "2", name: "高等数学", subject: "数学", totalUnits: 50, completedUnits: 20, timeSpent: 800, targetDate: Date().addingTimeInterval(60 * 24 * 60 * 60), color: .green),
                    LearningGoal(id: "3", name: "英语口语", subject: "语言", totalUnits: 30, completedUnits: 10, timeSpent: 500, targetDate: Date().addingTimeInterval(45 * 24 * 60 * 60), color: .orange)
                ],
                displayMode: .progress,
                showPercentage: true,
                showTimeSpent: true,
                maxGoalsCount: 4,
                background: .color(WidgetColor.fromColor(.white)),
                fontName: "SF Pro",
                fontSize: 14,
                fontColor: .black,
                accentColor: .blue
            ),
            family: .systemMedium
        )
        .previewContext(WidgetPreviewContext(family: .systemMedium))
        
        LearningProgressWidgetView(
            data: LearningProgressWidgetData(
                goals: [
                    LearningGoal(id: "1", name: "Swift编程", subject: "编程", totalUnits: 100, completedUnits: 65, timeSpent: 1200, targetDate: Date().addingTimeInterval(30 * 24 * 60 * 60), color: .blue),
                    LearningGoal(id: "2", name: "高等数学", subject: "数学", totalUnits: 50, completedUnits: 20, timeSpent: 800, targetDate: Date().addingTimeInterval(60 * 24 * 60 * 60), color: .green),
                    LearningGoal(id: "3", name: "英语口语", subject: "语言", totalUnits: 30, completedUnits: 10, timeSpent: 500, targetDate: Date().addingTimeInterval(45 * 24 * 60 * 60), color: .orange)
                ],
                displayMode: .circular,
                showPercentage: true,
                showTimeSpent: true,
                maxGoalsCount: 4,
                background: .color(WidgetColor.fromColor(.white)),
                fontName: "SF Pro",
                fontSize: 14,
                fontColor: .black,
                accentColor: .blue
            ),
            family: .systemLarge
        )
        .previewContext(WidgetPreviewContext(family: .systemLarge))
    }
}
