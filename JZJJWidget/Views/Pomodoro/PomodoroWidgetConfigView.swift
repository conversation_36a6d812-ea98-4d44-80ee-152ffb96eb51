import SwiftUI
import MyWidgetKit
import WidgetKit

struct PomodoroWidgetConfigView: View {
    // MARK: - 环境变量

    @Environment(\.dismiss) private var dismiss
//    @Environment(\.theme) private var theme
    @EnvironmentObject private var themeManager: ThemeManager

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }


    // MARK: - 状态变量

    // 预览缩放比例
    @State private var previewScale: CGFloat = 1.0

    // 预览尺寸
    @State private var previewFamily: WidgetFamily = .systemMedium

    // 工作时间（分钟）
    @State private var workDuration: Double = 25

    // 短休息时间（分钟）
    @State private var shortBreakDuration: Double = 5

    // 长休息时间（分钟）
    @State private var longBreakDuration: Double = 15

    // 长休息间隔
    @State private var longBreakInterval: Double = 4

    // 自动开始下一个阶段
    @State private var autoStartNextPhase: Bool = false

    // 背景设置
    @State private var background: BackgroundSelection = .color(.black)

    // 字体名称
    @State private var fontName: String = ""

    // 字体颜色
    @State private var fontColor: Color = .white

    // 小组件数据
    @State private var pomodoroWidgetData: PomodoroWidgetData?

    // MARK: - 初始化方法

    init() {
        // 从AppGroupDataManager加载配置
        if let savedConfig = AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) {
            _workDuration = State(initialValue: Double(savedConfig.workDuration))
            _shortBreakDuration = State(initialValue: Double(savedConfig.shortBreakDuration))
            _longBreakDuration = State(initialValue: Double(savedConfig.longBreakDuration))
            _longBreakInterval = State(initialValue: Double(savedConfig.longBreakInterval))
            _autoStartNextPhase = State(initialValue: savedConfig.autoStartNextPhase)
            _fontName = State(initialValue: savedConfig.fontName)
            _fontColor = State(initialValue: savedConfig.fontColor.toColor())

            // 设置背景
            switch savedConfig.background {
            case let .color(widgetColor):
                _background = State(initialValue: .color(widgetColor.toColor()))
            case let .imageData(data):
                if let image = UIImage(data: data) {
                    _background = State(initialValue: .image(image))
                }
            case let .packageImage(name):
                _background = State(initialValue: .packageImage(name))
            default:
                _background = State(initialValue: .color(.black))
            }
        }
    }

    // MARK: - 视图主体

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 预览区域
                previewSection

                // 配置区域
                configSection
            }
            .padding()
        }
        .background(theme.colors.background)
        .navigationTitle("番茄时钟")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("保存") {
                    saveConfig()
                }
                .foregroundColor(theme.colors.accent)
            }
        }
        .onAppear {
            updatePreview()
        }
    }

    // MARK: - 预览区域

    private var previewSection: some View {
        VStack(spacing: 16) {
            Text("预览")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)
                .frame(maxWidth: .infinity, alignment: .leading)

            if let pomodoroWidgetData = pomodoroWidgetData {
                ThemedWidgetPreviewContainer(family: previewFamily, theme: theme) {
                    PomodoroWidgetView(data: pomodoroWidgetData, family: previewFamily)
                }
                .scaleEffect(previewScale)
                .gesture(
                    MagnificationGesture()
                        .onChanged { value in
                            previewScale = value.magnitude
                        }
                        .onEnded { _ in
                            withAnimation {
                                previewScale = 1.0
                            }
                        }
                )
                .frame(height: 220)

                // 尺寸选择器
                HStack {
                    Spacer()

                    Button {
                        previewFamily = .systemSmall
                        updatePreview()
                    } label: {
                        Text("小")
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(previewFamily == .systemSmall ? theme.colors.accent : theme.colors.surface)
                            .foregroundColor(previewFamily == .systemSmall ? .white : theme.colors.text)
                            .cornerRadius(8)
                    }

                    Button {
                        previewFamily = .systemMedium
                        updatePreview()
                    } label: {
                        Text("中")
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(previewFamily == .systemMedium ? theme.colors.accent : theme.colors.surface)
                            .foregroundColor(previewFamily == .systemMedium ? .white : theme.colors.text)
                            .cornerRadius(8)
                    }

                    Button {
                        previewFamily = .systemLarge
                        updatePreview()
                    } label: {
                        Text("大")
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(previewFamily == .systemLarge ? theme.colors.accent : theme.colors.surface)
                            .foregroundColor(previewFamily == .systemLarge ? .white : theme.colors.text)
                            .cornerRadius(8)
                    }

                    Spacer()
                }

                Text("提示：双指缩放可调整预览大小")
                    .font(theme.fonts.bodySmall)
                    .foregroundColor(theme.colors.subtext)
            } else {
                // 加载中或无数据状态
                ThemedWidgetPreviewContainer(family: previewFamily, theme: theme) {
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: theme.colors.accent))

                        Text("加载中...")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.subtext)
                            .padding(.top, 8)
                    }
                }
                .frame(height: 220)
            }
        }
    }

    // MARK: - 配置区域

    private var configSection: some View {
        VStack(spacing: 24) {
            // 时间设置
            ConfigSectionContainer(theme: theme, title: "时间设置") {
                VStack(spacing: 16) {
                    // 工作时间
                    VStack(alignment: .leading, spacing: 8) {
                        Text("工作时间: \(Int(workDuration))分钟")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        Slider(value: $workDuration, in: 1...60, step: 1)
                            .accentColor(theme.colors.accent)
                            .onChange(of: workDuration) { _ in
                                updatePreview()
                            }
                    }

                    // 短休息时间
                    VStack(alignment: .leading, spacing: 8) {
                        Text("短休息时间: \(Int(shortBreakDuration))分钟")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        Slider(value: $shortBreakDuration, in: 1...30, step: 1)
                            .accentColor(theme.colors.accent)
                            .onChange(of: shortBreakDuration) { _ in
                                updatePreview()
                            }
                    }

                    // 长休息时间
                    VStack(alignment: .leading, spacing: 8) {
                        Text("长休息时间: \(Int(longBreakDuration))分钟")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        Slider(value: $longBreakDuration, in: 5...60, step: 1)
                            .accentColor(theme.colors.accent)
                            .onChange(of: longBreakDuration) { _ in
                                updatePreview()
                            }
                    }

                    // 长休息间隔
                    VStack(alignment: .leading, spacing: 8) {
                        Text("长休息间隔: 每\(Int(longBreakInterval))个番茄")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        Slider(value: $longBreakInterval, in: 2...8, step: 1)
                            .accentColor(theme.colors.accent)
                            .onChange(of: longBreakInterval) { _ in
                                updatePreview()
                            }
                    }

                    // 自动开始下一个阶段
                    Toggle(isOn: $autoStartNextPhase) {
                        Text("自动开始下一个阶段")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)
                    }
                    .toggleStyle(SwitchToggleStyle(tint: theme.colors.accent))
                    .onChange(of: autoStartNextPhase) { _ in
                        updatePreview()
                    }
                }
                .padding()
            }

            // 外观设置
            ConfigSectionContainer(theme: theme, title: "外观设置") {
                VStack(spacing: 16) {
                    // 背景选择
                    VStack(alignment: .leading, spacing: 8) {
                        Text("背景")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        BackgroundSelectView(
                            allowColor: true,
                            allowImage: true,
                            allowPackageImage: true,
                            packageImageNames: ["background1", "background2", "background3", "background4", "background5"],
                            packageImageDisplayNames: ["渐变蓝", "渐变紫", "星空", "大理石", "抽象"],
                            onSelection: { selection in
                                background = selection
                                updatePreview()
                            }
                        )
                    }

                    // 字体选择
                    VStack(alignment: .leading, spacing: 8) {
                        Text("字体样式")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        FontSelectView(
                            showFontPicker: true,
                            showFontSizePicker: true,
                            showFontColorPicker: true,
                            onSelectionChanged: { selection in
                                fontName = selection.fontName
                                fontColor = selection.fontColor
                                updatePreview()
                            }
                        )
                    }
                }
                .padding()
            }
        }
    }

    // MARK: - 辅助方法

    /// 更新预览
    private func updatePreview() {
        // 创建小组件数据
        let widgetData = PomodoroWidgetData(
            background: background.toWidgetBackground(),
            fontColor: WidgetColor.fromColor(fontColor),
            fontName: fontName,
            workDuration: Int(workDuration),
            shortBreakDuration: Int(shortBreakDuration),
            longBreakDuration: Int(longBreakDuration),
            longBreakInterval: Int(longBreakInterval),
            currentState: .working,
            remainingSeconds: Int(workDuration) * 60 / 2, // 显示一半的时间
            startTime: Date().addingTimeInterval(-Double(Int(workDuration) * 60) / 4), // 假设已经过去1/4的时间
            completedPomodoros: 2,
            autoStartNextPhase: autoStartNextPhase,
            lastUpdated: Date()
        )

        self.pomodoroWidgetData = widgetData
    }

    /// 保存配置
    private func saveConfig() {
        // 创建小组件数据
        let widgetData = PomodoroWidgetData(
            background: background.toWidgetBackground(),
            fontColor: WidgetColor.fromColor(fontColor),
            fontName: fontName,
            workDuration: Int(workDuration),
            shortBreakDuration: Int(shortBreakDuration),
            longBreakDuration: Int(longBreakDuration),
            longBreakInterval: Int(longBreakInterval),
            currentState: .idle,
            remainingSeconds: Int(workDuration) * 60,
            startTime: nil,
            completedPomodoros: 0,
            autoStartNextPhase: autoStartNextPhase,
            lastUpdated: Date()
        )

        // 保存配置
        AppGroupDataManager.shared.save(widgetData, for: .pomodoro, property: .config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        UINotificationFeedbackGenerator().notificationOccurred(.success)

        // 关闭视图
        dismiss()
    }
}

// MARK: - 预览

struct PomodoroWidgetConfigView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            PomodoroWidgetConfigView()
        }
    }
}
