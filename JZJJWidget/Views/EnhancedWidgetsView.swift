//
//  EnhancedWidgetsView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/15.
//

import MyWidgetKit
import SwiftUI
import WidgetKit

struct EnhancedWidgetsView: View {
    // MARK: - 属性

    // 状态对象
    @StateObject private var themeManager = ThemeManager.shared
    @ObservedObject private var historyManager = WidgetHistoryManager.shared // 新增：历史记录管理器

    // 状态
    @State private var selectedCategory: WidgetCategory = .all
    @State private var isRefreshing: Bool = false
    @State private var showEmptyState: Bool = false
    @State private var animateBackground: Bool = false

    // 从外部传入的属性
    var selectedFamily: WidgetFamily
    var onThemeButtonTapped: (() -> Void)?

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // MARK: - 数据模型

    // 小组件分类
    enum WidgetCategory: String, CaseIterable, Identifiable {
        case all = "全部"
        case information = "信息类"
        case utility = "工具类"
        case lifestyle = "生活类"
        case custom = "自定义"

        var id: String { rawValue }

        // 分类图标
        var icon: String {
            switch self {
            case .all: return "square.grid.2x2"
            case .information: return "info.circle"
            case .utility: return "wrench.and.screwdriver"
            case .lifestyle: return "heart"
            case .custom: return "paintbrush"
            }
        }
    }

    // 小组件数据
    let widgets: [(String, String, String, WidgetCategory)] = [
        ("每日灵感", "精选名言、诗词、段子，每日更新，启发思考", "text.quote", .information),
        ("二维码小组件", "自定义内容生成二维码，支持自定义颜色和背景", "qrcode", .utility),
        ("任务清单", "高效任务管理，番茄工作法助手", "list.bullet", .utility),
        ("时间小组件", "显示当前时间和日期，支持多种格式和样式", "clock.fill", .utility),
        ("水分摄入追踪器", "记录每日饮水量，养成健康饮水习惯", "drop.fill", .lifestyle),
        ("月相日历", "精确显示月相变化，预测满月和新月日期", "moon.stars", .information),
        ("快捷启动", "快速启动常用应用，自定义图标和布局", "square.grid.3x2", .utility),
        ("随机密码生成器", "一键生成安全随机密码，自动复制到剪贴板", "lock.shield", .utility),
        ("番茄时钟", "高效时间管理工具，提升工作学习效率", "timer", .utility),
        ("快速笔记", "一键添加文本/语音笔记，随时捕捉灵感", "note.text", .utility),
        ("设备信息", "显示设备名称、存储空间和电池状态等信息", "info.circle", .information),
    ]

    // 过滤后的小组件
    var filteredWidgets: [(String, String, String, WidgetCategory)] {
        widgets.filter { selectedCategory == .all || $0.3 == selectedCategory }
    }

    // 检查是否应该显示空状态
    private func checkEmptyState() {
        let isEmpty = filteredWidgets.isEmpty

        // 只有当状态需要改变时才执行动画
        if isEmpty != showEmptyState {
            withAnimation {
                showEmptyState = isEmpty
            }
        }
    }

    // MARK: - 视图主体

    var body: some View {
        // 将复杂的视图分解为更小的部分
        ZStack {
            // 背景
            theme.colors.background
                .ignoresSafeArea()

            // 主内容
            mainContentView
        }
        .onAppear(perform: onAppearSetup)
    }

    // 主内容视图
    private var mainContentView: some View {
        VStack(spacing: 0) {
            // 顶部过滤区域
            filterAreaView
                .zIndex(1)

            // 组件列表
            contentAreaView
        }
    }

    // 过滤区域视图
    private var filterAreaView: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            // 分类选择器
            categoryPickerView
        }
        .padding(.top, AppLayout.Spacing.medium)
        .background(
            theme.colors.background
                .shadow(color: theme.colors.shadow.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }

    // 分类选择器视图
    private var categoryPickerView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: AppLayout.Spacing.small) {
                ForEach(WidgetCategory.allCases) { category in

                    CategoryButton(
                        title: category.rawValue,
                        isSelected: selectedCategory == category,
                        theme: theme
                    ) {
                        withAnimation {
                            selectedCategory = category
                            hapticFeedback(style: .light)
                            // 检查并更新空状态
                            DispatchQueue.main.async {
                                checkEmptyState()
                            }
                        }
                    }
                }
            }
            .padding(.horizontal, AppLayout.Spacing.medium)
        }
        .padding(.bottom, AppLayout.Spacing.small)
    }

    // 内容区域视图
    private var contentAreaView: some View {
        ScrollView {
            if showEmptyState {
                emptyStateView
            } else {
                widgetsGridView
            }
        }
        .refreshable {
            // 下拉刷新逻辑
            await performRefresh()
        }
    }

    // onAppear 设置
    private func onAppearSetup() {
        // 启动背景动画
        withAnimation(Animation.linear(duration: 3).repeatForever(autoreverses: true)) {
            animateBackground = true
        }

        // 检查并更新空状态
        checkEmptyState()
    }

    // MARK: - 子视图

    // 空状态视图
    private var emptyStateView: some View {
        VStack(spacing: AppLayout.Spacing.large) {
            Image(systemName: "square.grid.2x2")
                .font(.system(size: 60))
                .foregroundColor(theme.colors.subtext)
                .padding(.top, 80)

            Text("没有找到匹配的组件")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)

            Text("尝试选择其他分类查看更多组件")
                .font(theme.fonts.bodyMedium)
                .foregroundColor(theme.colors.subtext)
                .multilineTextAlignment(.center)
                .padding(.horizontal, AppLayout.Spacing.large)

            Button(action: {
                withAnimation {
                    selectedCategory = .all
                    hapticFeedback(style: .medium)
                    // 检查并更新空状态
                    DispatchQueue.main.async {
                        checkEmptyState()
                    }
                }
            }) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                    Text("重置分类筛选")
                }
                .font(theme.fonts.bodyMedium)
                .foregroundColor(.white)
                .padding(.horizontal, AppLayout.Spacing.large)
                .padding(.vertical, AppLayout.Spacing.medium)
                .background(
                    Capsule()
                        .fill(theme.colors.accent)
                )
                .shadow(color: theme.colors.accent.opacity(0.3), radius: 8, x: 0, y: 4)
            }
            .padding(.top, AppLayout.Spacing.large)
        }
        .padding()
        .frame(maxWidth: .infinity)
        .transition(.opacity)
    }

    // 组件网格视图
    private var widgetsGridView: some View {
        LazyVGrid(columns: [GridItem(.flexible(), spacing: AppLayout.Spacing.medium)], spacing: AppLayout.Spacing.medium) {
            ForEach(filteredWidgets, id: \.0) { widget in
                WidgetCardWithNavigation(
                    title: widget.0,
                    description: widget.1,
                    iconName: widget.2,
                    widgetFamily: selectedFamily,
                    theme: theme,
                    destination: getConfigView(for: widget.0)
                )
            }
        }
        .padding(AppLayout.Spacing.medium)
    }

    // 带导航的小组件卡片
    struct WidgetCardWithNavigation<Destination: View>: View {
        let title: String
        let description: String
        let iconName: String
        let widgetFamily: WidgetFamily
        let theme: AppTheme
        let destination: Destination

        @ObservedObject private var historyManager = WidgetHistoryManager.shared
        @State private var isActive: Bool = false

        var body: some View {
            ZStack {
                // 隐藏的导航链接
                NavigationLink(
                    destination: destination,
                    isActive: $isActive
                ) {
                    EmptyView()
                }
                .opacity(0)
                .frame(width: 0, height: 0)

                // 实际显示的卡片
                EnhancedWidgetCard(
                    title: title,
                    description: description,
                    iconName: iconName,
                    widgetFamily: widgetFamily,
                    theme: theme,
                    onTap: {
                        isActive = true
                        historyManager.addWidgetToHistory(name: title, iconName: iconName)
                    }
                )
            }
        }
    }

    // MARK: - 辅助方法

    // 获取配置视图
    @ViewBuilder
    private func getConfigView(for widgetTitle: String) -> some View {
        switch widgetTitle {
        case "每日灵感":
            getDailyQuoteConfigView()
        case "二维码小组件":
            getQRWidgetConfigView()
        case "任务清单":
            getTodoWidgetConfigView()
        case "时间小组件":
            getTimeWidgetConfigView()
        case "水分摄入追踪器":
            getWaterIntakeConfigView()
        case "月相日历":
            getMoonPhaseConfigView()
        case "快捷启动":
            getAppLauncherConfigView()
        case "随机密码生成器":
            getPasswordGeneratorConfigView()
        case "番茄时钟":
            getPomodoroConfigView()
        case "快速笔记":
            getNoteWidgetConfigView()
        case "设备信息":
            getDeviceInfoConfigView()
        default:
            getDefaultConfigView(title: widgetTitle)
        }
    }

    // 每日灵感配置视图
    private func getDailyQuoteConfigView() -> some View {
        DailyQuoteConfigView()
            .environmentObject(themeManager)
    }



    // 二维码小组件配置视图
    private func getQRWidgetConfigView() -> some View {
        QRWidgetConfigView()
            .environmentObject(themeManager)
    }

    // 任务清单配置视图
    private func getTodoWidgetConfigView() -> some View {
        VStack {
            // 添加任务列表入口
            taskListEntryView

            // 小组件配置视图
            TodoWidgetConfigView()
                .environmentObject(themeManager)
        }
        .navigationTitle("任务清单")
    }

    // 任务列表入口视图
    private var taskListEntryView: some View {
        NavigationLink(destination: TaskListView().environmentObject(themeManager)) {
            HStack {
                Image(systemName: "list.bullet.rectangle")
                    .foregroundColor(themeManager.colors.accent)
                Text("查看和管理任务")
                    .foregroundColor(themeManager.colors.accent)
                Spacer()
                Image(systemName: "chevron.right")
                    .foregroundColor(.gray)
            }
            .padding()
            .background(Color.gray.opacity(0.1))
            .cornerRadius(10)
        }
        .padding(.horizontal)
        .padding(.top)
    }

    // 默认配置视图
    private func getDefaultConfigView(title: String) -> some View {
        Text("配置 \(title)")
            .navigationTitle(title)
            .environmentObject(themeManager)
    }









    // 时间小组件配置视图
    private func getTimeWidgetConfigView() -> some View {
        TimeWidgetConfigView()
            .environmentObject(themeManager)
    }

    // 水分摄入追踪器配置视图
    private func getWaterIntakeConfigView() -> some View {
        WaterIntakeConfigView()
            .environmentObject(themeManager)
    }

    // 随机密码生成器配置视图
    private func getPasswordGeneratorConfigView() -> some View {
        PasswordGeneratorConfigView()
            .environmentObject(themeManager)
    }

    // 番茄时钟配置视图
    private func getPomodoroConfigView() -> some View {
        PomodoroWidgetConfigView()
            .environmentObject(themeManager)
    }

    // 快速笔记配置视图
    private func getNoteWidgetConfigView() -> some View {
        NoteWidgetConfigView()
            .environmentObject(themeManager)
    }

    // 月相日历配置视图
    private func getMoonPhaseConfigView() -> some View {
        MoonPhaseConfigView()
            .environmentObject(themeManager)
    }

    // 快捷启动配置视图
    private func getAppLauncherConfigView() -> some View {
        AppLauncherConfigView()
            .environmentObject(themeManager)
    }

    // 设备信息配置视图
    private func getDeviceInfoConfigView() -> some View {
        DeviceInfoWidgetConfigView()
            .environmentObject(themeManager)
    }

    // 获取尺寸图标
    func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        default:
            return "square"
        }
    }

    // 获取尺寸名称
    func sizeName(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        default:
            return "小尺寸"
        }
    }

    // 执行刷新
    func performRefresh() async {
        // 模拟刷新操作
        isRefreshing = true
        hapticFeedback(style: .medium)

        // 延迟1秒，模拟网络请求
        //        try? await Task.sleep(nanoseconds: 1_000_000_000)

        // 完成刷新
        isRefreshing = false
    }

    // 触觉反馈
    func hapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
}

#Preview {
    NavigationStack {
        EnhancedWidgetsView(selectedFamily: .systemSmall, onThemeButtonTapped: nil)
    }
    .preferredColorScheme(.light)
}

#Preview {
    NavigationStack {
        EnhancedWidgetsView(selectedFamily: .systemMedium, onThemeButtonTapped: nil)
    }
    .preferredColorScheme(.dark)
    .environment(\.dynamicTypeSize, .large)
}


