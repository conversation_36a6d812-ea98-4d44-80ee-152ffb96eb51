import SwiftUI
import WidgetKit
import MyWidgetKit

struct EditTaskView: View {
    @EnvironmentObject var theme: ThemeManager
    @Environment(\.presentationMode) var presentationMode

    // 任务属性
    @State private var taskTitle: String
    @State private var taskPriority: Int
    @State private var taskDueDate: Date
    @State private var taskNotes: String
    @State private var hasDueDate: Bool
    @State private var isCompleted: Bool

    // 分类选择
    @State private var selectedCategoryId: UUID
    private let categories: [TaskCategory]

    // 原始任务和回调
    private let originalTask: Task
    private let onSave: (Task) -> Void

    // 初始化
    init(task: Task, categories: [TaskCategory], onSave: @escaping (Task) -> Void) {
        self.originalTask = task
        self.categories = categories
        self.onSave = onSave

        // 初始化状态变量
        self._taskTitle = State(initialValue: task.title)
        self._taskPriority = State(initialValue: task.priority.rawValue)
        self._taskDueDate = State(initialValue: task.dueDate ?? Date().addingTimeInterval(86400))
        self._taskNotes = State(initialValue: task.notes ?? "")
        self._hasDueDate = State(initialValue: task.dueDate != nil)
        self._isCompleted = State(initialValue: task.isCompleted)
        self._selectedCategoryId = State(initialValue: task.categoryId)
    }

    var body: some View {
        NavigationView {
            Form {
                // 任务标题
                Section(header: Text("任务信息")) {
                    TextField("任务标题", text: $taskTitle)

                    // 分类选择
                    Picker("分类", selection: $selectedCategoryId) {
                        ForEach(categories) { category in
                            HStack {
                                Image(systemName: category.icon)
                                    .foregroundColor(category.color)
                                Text(category.name)
                            }
                            .tag(category.id)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())

                    // 优先级选择
                    Picker("优先级", selection: $taskPriority) {
                        HStack {
                            Image(systemName: "arrow.down.circle")
                                .foregroundColor(.green)
                            Text("低")
                        }
                        .tag(0)

                        HStack {
                            Image(systemName: "equal.circle")
                                .foregroundColor(.orange)
                            Text("中")
                        }
                        .tag(1)

                        HStack {
                            Image(systemName: "exclamationmark.circle")
                                .foregroundColor(.red)
                            Text("高")
                        }
                        .tag(2)
                    }
                    .pickerStyle(SegmentedPickerStyle())

                    // 完成状态
                    Toggle("已完成", isOn: $isCompleted)

                    // 截止日期
                    Toggle("设置截止日期", isOn: $hasDueDate)

                    if hasDueDate {
                        DatePicker("截止日期", selection: $taskDueDate, displayedComponents: [.date, .hourAndMinute])
                    }

                    // 备注
                    VStack(alignment: .leading) {
                        Text("备注")
                        TextEditor(text: $taskNotes)
                            .frame(minHeight: 100)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                            )
                    }
                }

                // 保存按钮
                Section {
                    Button(action: saveTask) {
                        Text("保存修改")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding()
                            .background(taskTitle.isEmpty ? Color.gray : theme.colors.accent)
                            .cornerRadius(10)
                    }
                    .disabled(taskTitle.isEmpty)
                    .listRowInsets(EdgeInsets())
                }

                // 删除按钮
                Section {
                    Button(action: deleteTask) {
                        Text("删除任务")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding()
                            .background(Color.red)
                            .cornerRadius(10)
                    }
                    .listRowInsets(EdgeInsets())
                }
            }
            .navigationTitle("编辑任务")
            .navigationBarItems(trailing: Button("取消") {
                presentationMode.wrappedValue.dismiss()
            })
        }
    }

    // 保存任务
    private func saveTask() {
        // 创建更新后的任务
        let updatedTask = Task(
            id: originalTask.id,
            title: taskTitle,
            isCompleted: isCompleted,
            priority: TaskPriority(rawValue: taskPriority) ?? .medium,
            dueDate: hasDueDate ? taskDueDate : nil,
            categoryId: selectedCategoryId,
            notes: taskNotes.isEmpty ? nil : taskNotes,
            createdAt: originalTask.createdAt
        )

        // 调用回调
        onSave(updatedTask)

        // 刷新小组件以确保更改立即可见
        WidgetCenter.shared.reloadAllTimelines()

        // 关闭视图
        presentationMode.wrappedValue.dismiss()
    }

    // 删除任务
    private func deleteTask() {
        // 从 AppGroupDataManager 读取现有任务
        if var tasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            // 移除当前任务
            tasks.removeAll { $0.id == originalTask.id }

            // 保存更新后的任务列表
            AppGroupDataManager.shared.save(tasks, for: .todoList, property: .tasks)

            // 刷新小组件
            WidgetCenter.shared.reloadAllTimelines()
        }

        // 关闭视图
        presentationMode.wrappedValue.dismiss()
    }
}
