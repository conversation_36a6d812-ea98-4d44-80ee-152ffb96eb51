import MyWidgetKit
import Swift<PERSON>
import WidgetKit

struct TaskListView: View {
    @EnvironmentObject var theme: ThemeManager
    @State private var tasks: [Task] = []
    @State private var categories: [TaskCategory] = []
    @State private var selectedCategoryId: UUID? = nil
    @State private var showCompletedTasks: Bool = true
    @State private var searchText: String = ""
    @State private var showEditTaskSheet: Bool = false
    @State private var taskToEdit: Task? = nil
    @State private var refreshID = UUID() // 用于强制刷新视图

    var body: some View {
        VStack {
            // 分类选择器
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    CategoryButton(
                        title: "所有任务",
                        isSelected: selectedCategoryId == nil,
                        theme: theme.currentTheme,
                        action: { selectedCategoryId = nil },
                        iconName: "tray"
                    )

                    ForEach(categories) { category in
                        CategoryButton(
                            title: category.name,
                            isSelected: selectedCategoryId == category.id,
                            theme: theme.currentTheme,
                            action: { selectedCategoryId = category.id },
                            iconName: category.icon
                        )
                    }
                }
                .padding(.horizontal)
            }
            .padding(.vertical, 8)

            // 任务列表 - 使用 refreshID 强制刷新
            List {
                ForEach(filteredTasks) { task in
                    TaskRow(
                        task: task,
                        category: getCategoryFor(task),
                        onToggleComplete: {
                            toggleTaskCompletion(task)
                        },
                        onEdit: {
                            taskToEdit = task
                            // 确保在设置状态变量后再显示 sheet
                            DispatchQueue.main.async {
                                showEditTaskSheet = true
                            }
                        }
                    )
                    .id("\(task.id)-\(task.isCompleted)-\(refreshID.uuidString)") // 使用唯一ID强制刷新
                }
            }
            .listStyle(InsetGroupedListStyle())

            // 底部工具栏
            HStack {
                Toggle("显示已完成", isOn: $showCompletedTasks)
                    .toggleStyle(SwitchToggleStyle(tint: theme.colors.accent))

                Spacer()

                Button(action: {
                    // 刷新数据和界面
                    loadData()
                    refreshID = UUID() // 生成新的 UUID 强制刷新视图
                }) {
                    Image(systemName: "arrow.clockwise")
                        .foregroundColor(theme.colors.accent)
                }
                .padding(.horizontal)
            }
            .padding()
        }
        .navigationTitle("任务列表")
        .searchable(text: $searchText, prompt: "搜索任务")
        .onAppear(perform: loadData)
        .id(refreshID) // 使用 refreshID 强制整个视图刷新

        // 将 sheet 移到这里，与 NavigationView 同级
        .sheet(isPresented: $showEditTaskSheet, onDismiss: {
            // 在 sheet 关闭后刷新数据
            loadData()
        }) {
            if let task = taskToEdit {
                NavigationView {
                    EditTaskView(task: task, categories: categories) { updatedTask in
                        updateTask(updatedTask)
                        // 更新后立即关闭 sheet
                        showEditTaskSheet = false
                    }
                    .environmentObject(theme)
                }
            }
        }
    }

    // 过滤后的任务
    private var filteredTasks: [Task] {
        var result = tasks

        // 按分类筛选
        if let categoryId = selectedCategoryId {
            result = result.filter { $0.categoryId == categoryId }
        }

        // 按完成状态筛选
        if !showCompletedTasks {
            result = result.filter { !$0.isCompleted }
        }

        // 按搜索文本筛选
        if !searchText.isEmpty {
            result = result.filter { $0.title.localizedCaseInsensitiveContains(searchText) }
        }

        // 排序: 未完成 > 优先级 > 截止日期
        result.sort { task1, task2 in
            if task1.isCompleted != task2.isCompleted {
                return !task1.isCompleted
            }

            if task1.priority.rawValue != task2.priority.rawValue {
                return task1.priority.rawValue > task2.priority.rawValue
            }

            if let date1 = task1.dueDate, let date2 = task2.dueDate {
                return date1 < date2
            }

            return task1.createdAt < task2.createdAt
        }

        return result
    }

    // 加载数据
    private func loadData() {
        // 加载分类
        if let savedCategories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories) {
            categories = savedCategories
        } else {
            categories = TaskCategory.defaultCategories
            AppGroupDataManager.shared.save(categories, for: .todoList, property: .categories)
        }

        // 加载任务
        if let savedTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            // 直接使用保存的任务，不需要创建新实例
            tasks = savedTasks

            // 打印调试信息
            print("数据已重新加载，任务数量: \(tasks.count)")
        }
    }

    // 获取任务所属分类
    private func getCategoryFor(_ task: Task) -> TaskCategory? {
        return categories.first { $0.id == task.categoryId }
    }

    // 切换任务完成状态
    private func toggleTaskCompletion(_ task: Task) {
        // 直接从 AppGroupDataManager 读取最新的任务列表
        if var allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            // 找到要更新的任务
            if let index = allTasks.firstIndex(where: { $0.id == task.id }) {
                // 切换完成状态
                allTasks[index].isCompleted = !allTasks[index].isCompleted

                // 保存更新后的任务列表
                AppGroupDataManager.shared.save(allTasks, for: .todoList, property: .tasks)

                // 刷新小组件
                WidgetCenter.shared.reloadAllTimelines()

                // 生成新的 UUID 强制刷新视图
                refreshID = UUID()

                // 打印调试信息
                print("任务状态已更新: \(task.title) - 完成状态: \(allTasks[index].isCompleted)")
            }
        }
    }

    // 更新任务
    private func updateTask(_ updatedTask: Task) {
        // 直接从 AppGroupDataManager 读取最新的任务列表
        if var allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            // 找到要更新的任务
            if let index = allTasks.firstIndex(where: { $0.id == updatedTask.id }) {
                // 更新任务
                allTasks[index] = updatedTask

                // 保存更新后的任务列表
                AppGroupDataManager.shared.save(allTasks, for: .todoList, property: .tasks)

                // 刷新小组件
                WidgetCenter.shared.reloadAllTimelines()

                // 生成新的 UUID 强制刷新视图
                refreshID = UUID()

                // 打印调试信息
                print("任务已更新: \(updatedTask.title) - 完成状态: \(updatedTask.isCompleted)")
            }
        }
    }

    // 刷新小组件
    private func refreshWidgets() {
        WidgetCenter.shared.reloadAllTimelines()
    }
}



// 任务行
struct TaskRow: View {
    let task: Task
    let category: TaskCategory?
    let onToggleComplete: () -> Void
    let onEdit: () -> Void

    // 添加一个状态变量来跟踪任务的实际完成状态
    @State private var isTaskCompleted: Bool

    // 自定义初始化器，从 AppGroupDataManager 读取最新的任务状态
    init(task: Task, category: TaskCategory?, onToggleComplete: @escaping () -> Void, onEdit: @escaping () -> Void) {
        self.task = task
        self.category = category
        self.onToggleComplete = onToggleComplete
        self.onEdit = onEdit

        // 从 AppGroupDataManager 读取最新的任务状态
        var initialCompletionState = task.isCompleted
        if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks),
           let updatedTask = allTasks.first(where: { $0.id == task.id }) {
            initialCompletionState = updatedTask.isCompleted
        }

        // 初始化状态变量
        self._isTaskCompleted = State(initialValue: initialCompletionState)
    }

    var body: some View {
        HStack {
            // 完成状态按钮 - 使用 isTaskCompleted 而不是 task.isCompleted
            Button(action: {
                // 先更新本地状态，然后调用回调
                isTaskCompleted.toggle()
                onToggleComplete()
            }) {
                Circle()
                    .strokeBorder(task.priority.color, lineWidth: 1.5)
                    .background(
                        Circle()
                            .fill(isTaskCompleted ? task.priority.color.opacity(0.3) : Color.clear)
                    )
                    .frame(width: 24, height: 24)
                    .overlay(
                        Image(systemName: "checkmark")
                            .font(.system(size: 12, weight: .bold))
                            .foregroundColor(task.priority.color)
                            .opacity(isTaskCompleted ? 1 : 0)
                    )
            }
            .buttonStyle(PlainButtonStyle())

            // 任务内容 - 使用 isTaskCompleted 而不是 task.isCompleted
            VStack(alignment: .leading, spacing: 4) {
                Text(task.title)
                    .font(.body)
                    .strikethrough(isTaskCompleted)
                    .foregroundColor(isTaskCompleted ? .gray : .primary)

                HStack(spacing: 8) {
                    if let category = category {
                        Label(category.name, systemImage: category.icon)
                            .font(.caption)
                            .foregroundColor(category.color)
                    }

                    if let dueDate = task.dueDate {
                        Label(formatDate(dueDate), systemImage: "calendar")
                            .font(.caption)
                            .foregroundColor(task.isOverdue ? .red : .gray)
                    }
                }
            }
            .padding(.leading, 8)

            Spacer()

            // 编辑按钮
            Button(action: onEdit) {
                Image(systemName: "pencil")
                    .foregroundColor(.gray)
            }
            .buttonStyle(PlainButtonStyle())
        }
        .padding(.vertical, 4)
        // 添加 onAppear 修饰符，在视图出现时更新任务状态
        .onAppear {
            // 从 AppGroupDataManager 读取最新的任务状态
            if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks),
               let updatedTask = allTasks.first(where: { $0.id == task.id }) {
                isTaskCompleted = updatedTask.isCompleted
            }
        }
    }

    // 格式化日期
    private func formatDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM/dd HH:mm"
        return formatter.string(from: date)
    }
}
