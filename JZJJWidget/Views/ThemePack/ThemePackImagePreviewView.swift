//
//  ThemePackImagePreviewView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 主题包图片预览视图
struct ThemePackImagePreviewView: View {
    let themePackId: String
    let themePack: ThemePackBundle

    @StateObject private var themePackManager = ThemePackManager.shared
    @State private var backgroundImage: UIImage?
    @State private var keyImages: [KeyType: [KeyImageState: UIImage]] = [:]
    @State private var isLoading = true
    @State private var validationErrors: [String] = []

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 标题
                headerSection

                // 背景图片预览
                backgroundImageSection

                // 按键图片预览
                keyImagesSection

                // 验证结果
                validationSection
            }
            .padding()
        }
        .navigationTitle("图片预览")
        .navigationBarTitleDisplayMode(.inline)
        .task {
            await loadImages()
        }
    }

    // MARK: - 视图组件

    private var headerSection: some View {
        VStack(spacing: 8) {
            Text(themePack.metadata.name)
                .font(.title2)
                .fontWeight(.bold)

            Text(themePack.metadata.description)
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
    }

    private var backgroundImageSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("键盘背景图片")
                .font(.headline)
                .fontWeight(.semibold)

            if let backgroundImage = backgroundImage {
                Image(uiImage: backgroundImage)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxHeight: 200)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(Color.gray.opacity(0.3), lineWidth: 1)
                    )
            } else if isLoading {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.gray.opacity(0.2))
                    .frame(height: 200)
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                    )
            } else {
                RoundedRectangle(cornerRadius: 12)
                    .fill(Color.red.opacity(0.1))
                    .frame(height: 200)
                    .overlay(
                        VStack {
                            Image(systemName: "photo.badge.exclamationmark")
                                .font(.largeTitle)
                                .foregroundColor(.red)
                            Text("背景图片加载失败")
                                .font(.caption)
                                .foregroundColor(.red)
                        }
                    )
            }
        }
    }

    private var keyImagesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("按键图片")
                .font(.headline)
                .fontWeight(.semibold)

            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 16) {
                ForEach(KeyType.allCases, id: \.self) { keyType in
                    keyImageCard(for: keyType)
                }
            }
        }
    }

    private func keyImageCard(for keyType: KeyType) -> some View {
        VStack(spacing: 8) {
            Text(keyType.displayName)
                .font(.caption)
                .fontWeight(.medium)

            HStack(spacing: 4) {
                // 正常状态
                keyImageView(keyType: keyType, state: .normal, label: "正常")

                // 按下状态
                keyImageView(keyType: keyType, state: .pressed, label: "按下")
            }
        }
        .padding(8)
        .background(Color(.systemGray6))
        .cornerRadius(8)
    }

    private func keyImageView(keyType: KeyType, state: KeyImageState, label: String) -> some View {
        VStack(spacing: 4) {
            if let image = keyImages[keyType]?[state] {
                Image(uiImage: image)
                    .resizable()
                    .frame(width: 32, height: 32)
                    .cornerRadius(6)
            } else if isLoading {
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.gray.opacity(0.3))
                    .frame(width: 32, height: 32)
                    .overlay(
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            .scaleEffect(0.5)
                    )
            } else {
                RoundedRectangle(cornerRadius: 6)
                    .fill(Color.red.opacity(0.2))
                    .frame(width: 32, height: 32)
                    .overlay(
                        Image(systemName: "xmark")
                            .font(.caption)
                            .foregroundColor(.red)
                    )
            }

            Text(label)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }

    private var validationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("资源验证")
                .font(.headline)
                .fontWeight(.semibold)

            if validationErrors.isEmpty {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                    Text("所有图片资源验证通过")
                        .font(.subheadline)
                }
                .padding()
                .background(Color.green.opacity(0.1))
                .cornerRadius(8)
            } else {
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.orange)
                        Text("发现以下问题:")
                            .font(.subheadline)
                            .fontWeight(.medium)
                    }

                    ForEach(validationErrors, id: \.self) { error in
                        Text("• \(error)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                .padding()
                .background(Color.orange.opacity(0.1))
                .cornerRadius(8)
            }
        }
    }

    // MARK: - 私有方法

    private func loadImages() async {
        await MainActor.run {
            isLoading = true
        }

        // 验证图片资源
        let errors = await themePackManager.validateThemePackImages(for: themePackId)

        // 加载背景图片
        let bgImage = await themePackManager.getThemePackBackgroundImage(for: themePackId)

        // 加载按键图片
        var loadedKeyImages: [KeyType: [KeyImageState: UIImage]] = [:]
        for keyType in KeyType.allCases {
            let normalImage = await themePackManager.getKeyImage(for: themePackId, keyType: keyType, state: .normal)
            let pressedImage = await themePackManager.getKeyImage(for: themePackId, keyType: keyType, state: .pressed)

            loadedKeyImages[keyType] = [
                .normal: normalImage,
                .pressed: pressedImage
            ].compactMapValues { $0 }
        }

        // 在主线程上更新UI状态
        await MainActor.run {
            validationErrors = errors
            backgroundImage = bgImage
            keyImages = loadedKeyImages
            isLoading = false
        }
    }
}

// MARK: - 预览
struct ThemePackImagePreviewView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            ThemePackImagePreviewView(
                themePackId: "classic-light",
                themePack: ThemePackBundle(
                    metadata: ThemePackMetadata(
                        id: "classic-light",
                        name: "经典浅色",
                        description: "iOS系统风格的经典浅色主题",
                        version: "1.0.0",
                        category: "classic",
                        style: "light",
                        author: "JZJJWidget Team",
                        createdAt: Date(),
                        updatedAt: Date(),
                        compatibility: CompatibilityInfo(
                            minIOSVersion: "13.0",
                            minAppVersion: "1.0.0",
                            supportedDevices: ["iPhone", "iPad"]
                        ),
                        resources: ResourceInfo(
                            previewImage: "preview.png",
                            backgroundImages: [],
                            keyImages: [],
                            totalSize: 1024768,
                            compressedSize: 512384
                        ),
                        features: FeatureInfo(
                            supportsDarkMode: false,
                            hasAnimations: true,
                            hasSounds: true,
                            hasHaptics: true,
                            customFonts: false
                        ),
                        tags: ["经典", "浅色", "系统", "简洁"],
                        rating: 4.8,
                        downloadCount: 10000,
                        isPremium: false,
                        isBuiltIn: true
                    ),
                    config: ThemePackConfig(
                        schemaVersion: "1.0.0",
                        themeInfo: ThemeInfo(
                            id: "classic-light",
                            name: "经典浅色",
                            description: "iOS系统风格的经典浅色主题"
                        ),
                        baseTheme: BaseThemeConfig(
                            id: "classic-light-base",
                            name: "经典浅色基础",
                            type: "light",
                            keyStyle: "rounded",
                            colors: ColorConfig(
                                background: WidgetColor(red: 0.96, green: 0.96, blue: 0.97, alpha: 1.0),
                                keyBackground: WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
                                keyPressed: WidgetColor(red: 0.86, green: 0.86, blue: 0.88, alpha: 1.0),
                                text: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
                                specialKey: WidgetColor(red: 0.68, green: 0.68, blue: 0.7, alpha: 1.0),
                                border: WidgetColor(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0)
                            ),
                            images: ImageConfig(
                                hasBackgroundImage: true,
                                hasKeyImage: true,
                                backgroundImagePath: "resources/backgrounds/keyboard-bg.png",
                                keyImagePath: "resources/keys/default-key.png",
                                keyImages: nil,
                                isBuiltInImageTheme: true,
                                imageOpacity: 0.8,
                                imageBlendMode: "normal"
                            ),
                            typography: TypographyConfig(
                                fontName: "SF Pro",
                                fontSize: 16,
                                fontWeight: "medium"
                            ),
                            layout: LayoutConfig(
                                keySpacing: 6,
                                keyHeight: 44,
                                showBorder: true,
                                borderWidth: 1
                            ),
                            effects: EffectConfig(
                                enableShadow: true,
                                shadowColor: WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.2),
                                shadowRadius: 2,
                                enableHaptic: true,
                                enableSound: true
                            )
                        ),
                        advancedConfig: AdvancedThemeConfig(
                            globalSettings: GlobalSettings(
                                keySpacing: 6,
                                keyHeight: 44,
                                enableHapticFeedback: true,
                                enableSoundFeedback: true,
                                enableKeyAnimations: true,
                                animationDuration: 0.1,
                                enableGradientEffects: false,
                                enableParallaxEffect: false
                            ),
                            keyTypeConfigs: [:],
                            individualKeyConfigs: [:],
                            createdAt: Date(),
                            updatedAt: Date()
                        ),
                        validation: ValidationInfo(
                            checksum: "abc123def456",
                            fileCount: 3,
                            totalSize: 1024768
                        )
                    )
                )
            )
        }
    }
}
