import SwiftUI
import MyWidgetKit
import WidgetKit
 

struct NoteWidgetConfigView: View {
    // MARK: - 环境变量

    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var themeManager: ThemeManager

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // MARK: - 状态变量

    // 预览缩放比例
    @State private var previewScale: CGFloat = 1.0

    // 预览尺寸
    @State private var previewFamily: WidgetFamily = .systemMedium

    // 笔记列表
    @State private var notes: [NoteItem] = []

    // 背景设置
    @State private var background: BackgroundSelection = .color(.white)

    // 字体名称
    @State private var fontName: String = ""

    // 字体颜色
    @State private var fontColor: Color = .black

    // 字体大小
    @State private var fontSize: CGFloat = 14

    // 显示模式
    @State private var displayMode: NoteDisplayMode = .all

    // 最大显示笔记数量
    @State private var maxNoteCount: Int = 3

    // 是否显示创建时间
    @State private var showCreationTime: Bool = true

    // 是否显示笔记类型图标
    @State private var showTypeIcon: Bool = true

    // 小组件数据
    @State private var noteWidgetData: NoteWidgetData?

    // 是否显示添加笔记表单
    @State private var showAddNoteSheet: Bool = false

    // 新笔记标题
    @State private var newNoteTitle: String = ""

    // 新笔记内容
    @State private var newNoteContent: String = ""

    // 新笔记类型
    @State private var newNoteType: NoteType = .text

    // 是否正在录音
    @State private var isRecording: Bool = false

    // 录音URL
    @State private var recordingURL: URL?

    // 录音时长
    @State private var recordingDuration: TimeInterval = 0

    // 是否显示成功提示
    @State private var showSuccessToast: Bool = false

    // MARK: - 初始化方法

    init() {
        // 从AppGroupDataManager加载配置
        if let savedConfig = AppGroupDataManager.shared.read(NoteWidgetData.self, for: .note, property: .config) {
            _notes = State(initialValue: savedConfig.notes)
            _fontName = State(initialValue: savedConfig.fontName)
            _fontSize = State(initialValue: savedConfig.fontSize)
            _fontColor = State(initialValue: savedConfig.fontColor.toColor())
            _displayMode = State(initialValue: savedConfig.displayMode)
            _maxNoteCount = State(initialValue: savedConfig.maxNoteCount)
            _showCreationTime = State(initialValue: savedConfig.showCreationTime)
            _showTypeIcon = State(initialValue: savedConfig.showTypeIcon)

            // 设置背景
            switch savedConfig.background {
            case let .color(widgetColor):
                _background = State(initialValue: .color(widgetColor.toColor()))
            case let .imageData(data):
                if let image = UIImage(data: data) {
                    _background = State(initialValue: .image(image))
                }
            case let .packageImage(name):
                _background = State(initialValue: .packageImage(name))
            default:
                _background = State(initialValue: .color(.white))
            }
        } else {
            // 如果没有保存的配置，创建示例笔记
            _notes = State(initialValue: NoteItem.createSampleNotes())
        }
    }

    // MARK: - 视图主体

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // 预览区域
                previewSection

                // 配置区域
                configSection
            }
            .padding()
        }
        .background(themeManager.colors.background)
        .navigationTitle("快速笔记")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("保存") {
                    saveConfig()
                }
                .foregroundColor(themeManager.colors.accent)
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    showAddNoteSheet = true
                }) {
                    Image(systemName: "plus")
                        .foregroundColor(themeManager.colors.accent)
                }
            }
        }
        .onAppear {
            updatePreview()
        }
        .sheet(isPresented: $showAddNoteSheet) {
            addNoteSheet
        }
        .overlay {
            if showSuccessToast {
                VStack {
                    Spacer()

                    Text("小组件已保存")
                        .font(themeManager.fonts.bodyMedium)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.black.opacity(0.7))
                        .cornerRadius(10)
                        .padding(.bottom, 20)
                }
                .transition(.move(edge: .bottom))
                .onAppear {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                        withAnimation {
                            showSuccessToast = false
                        }
                    }
                }
            }
        }
    }

    // MARK: - 预览区域

    private var previewSection: some View {
        VStack(spacing: 16) {
            Text("预览")
                .font(themeManager.fonts.headlineMedium)
                .foregroundColor(themeManager.colors.text)
                .frame(maxWidth: .infinity, alignment: .leading)

            if let noteWidgetData = noteWidgetData {
                ThemedWidgetPreviewContainer(family: previewFamily, theme: themeManager.currentTheme) {
                    NoteWidgetView(data: noteWidgetData, family: previewFamily)
                }
                .scaleEffect(previewScale)
                .gesture(
                    MagnificationGesture()
                        .onChanged { value in
                            previewScale = value.magnitude
                        }
                        .onEnded { _ in
                            withAnimation {
                                previewScale = 1.0
                            }
                        }
                )
                .frame(height: 220)

                // 尺寸选择器
                HStack {
                    Spacer()

                    Button {
                        previewFamily = .systemSmall
                        updatePreview()
                    } label: {
                        Text("小")
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(previewFamily == .systemSmall ? themeManager.colors.accent : themeManager.colors.surface)
                            .foregroundColor(previewFamily == .systemSmall ? .white : themeManager.colors.text)
                            .cornerRadius(8)
                    }

                    Button {
                        previewFamily = .systemMedium
                        updatePreview()
                    } label: {
                        Text("中")
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(previewFamily == .systemMedium ? themeManager.colors.accent : themeManager.colors.surface)
                            .foregroundColor(previewFamily == .systemMedium ? .white : themeManager.colors.text)
                            .cornerRadius(8)
                    }

                    Button {
                        previewFamily = .systemLarge
                        updatePreview()
                    } label: {
                        Text("大")
                            .padding(.horizontal, 12)
                            .padding(.vertical, 6)
                            .background(previewFamily == .systemLarge ? themeManager.colors.accent : themeManager.colors.surface)
                            .foregroundColor(previewFamily == .systemLarge ? .white : themeManager.colors.text)
                            .cornerRadius(8)
                    }

                    Spacer()
                }

                Text("提示：双指缩放可调整预览大小")
                    .font(themeManager.fonts.bodySmall)
                    .foregroundColor(themeManager.colors.subtext)
            } else {
                // 加载中或无数据状态
                ThemedWidgetPreviewContainer(family: previewFamily, theme: themeManager.currentTheme) {
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: themeManager.colors.accent))

                        Text("加载中...")
                            .font(themeManager.fonts.bodyMedium)
                            .foregroundColor(themeManager.colors.subtext)
                            .padding(.top, 8)
                    }
                }
                .frame(height: 220)
            }
        }
    }

    // MARK: - 配置区域

    private var configSection: some View {
        VStack(spacing: 24) {
            // 笔记列表
            ConfigSectionContainer(theme: themeManager.currentTheme, title: "笔记列表") {
                VStack(spacing: 16) {
                    if notes.isEmpty {
                        VStack(spacing: 8) {
                            Image(systemName: "note.text")
                                .font(.system(size: 24))
                                .foregroundColor(themeManager.colors.subtext)

                            Text("暂无笔记")
                                .font(themeManager.fonts.bodyMedium)
                                .foregroundColor(themeManager.colors.text)

                            Text("点击右上角+按钮添加笔记")
                                .font(themeManager.fonts.bodySmall)
                                .foregroundColor(themeManager.colors.subtext)
                                .multilineTextAlignment(.center)
                        }
                        .padding()
                        .frame(maxWidth: .infinity)
                    } else {
                        ForEach(notes) { note in
                            noteItemRow(note)
                        }
                    }
                }
                .padding()
            }

            // 显示设置
            ConfigSectionContainer(theme: themeManager.currentTheme, title: "显示设置") {
                VStack(spacing: 16) {
                    // 显示模式
                    VStack(alignment: .leading, spacing: 8) {
                        Text("显示模式")
                            .font(themeManager.fonts.bodyMedium)
                            .foregroundColor(themeManager.colors.text)

                        Picker("显示模式", selection: $displayMode) {
                            ForEach(NoteDisplayMode.allCases, id: \.self) { mode in
                                Text(mode.displayName).tag(mode)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: displayMode) { _ in
                            updatePreview()
                        }
                    }

                    // 最大显示数量
                    VStack(alignment: .leading, spacing: 8) {
                        Text("最大显示数量: \(maxNoteCount)")
                            .font(themeManager.fonts.bodyMedium)
                            .foregroundColor(themeManager.colors.text)

                        Slider(value: Binding(
                            get: { Double(maxNoteCount) },
                            set: { maxNoteCount = Int($0) }
                        ), in: 1...5, step: 1)
                        .accentColor(themeManager.colors.accent)
                        .onChange(of: maxNoteCount) { _ in
                            updatePreview()
                        }
                    }

                    // 显示创建时间
                    Toggle(isOn: $showCreationTime) {
                        Text("显示创建时间")
                            .font(themeManager.fonts.bodyMedium)
                            .foregroundColor(themeManager.colors.text)
                    }
                    .toggleStyle(SwitchToggleStyle(tint: themeManager.colors.accent))
                    .onChange(of: showCreationTime) { _ in
                        updatePreview()
                    }

                    // 显示笔记类型图标
                    Toggle(isOn: $showTypeIcon) {
                        Text("显示笔记类型图标")
                            .font(themeManager.fonts.bodyMedium)
                            .foregroundColor(themeManager.colors.text)
                    }
                    .toggleStyle(SwitchToggleStyle(tint: themeManager.colors.accent))
                    .onChange(of: showTypeIcon) { _ in
                        updatePreview()
                    }
                }
                .padding()
            }

            // 外观设置
            ConfigSectionContainer(theme: themeManager.currentTheme, title: "外观设置") {
                VStack(spacing: 16) {
                    // 背景选择
                    VStack(alignment: .leading, spacing: 8) {
                        Text("背景")
                            .font(themeManager.fonts.bodyMedium)
                            .foregroundColor(themeManager.colors.text)

                        BackgroundSelectView(
                            allowColor: true,
                            allowImage: true,
                            allowPackageImage: true,
                            packageImageNames: ["background1", "background2", "background3", "background4", "background5"],
                            packageImageDisplayNames: ["渐变蓝", "渐变紫", "星空", "大理石", "抽象"],
                            onSelection: { selection in
                                background = selection
                                updatePreview()
                            }
                        )
                    }

                    // 字体选择
                    VStack(alignment: .leading, spacing: 8) {
                        Text("字体样式")
                            .font(themeManager.fonts.bodyMedium)
                            .foregroundColor(themeManager.colors.text)

                        FontSelectView(
                            showFontPicker: true,
                            showFontSizePicker: true,
                            showFontColorPicker: true,
                            onSelectionChanged: { selection in
                                fontName = selection.fontName
                                fontColor = selection.fontColor
                                fontSize = selection.fontSize
                                updatePreview()
                            }
                        )
                    }
                }
                .padding()
            }
        }
    }

    // MARK: - 笔记项目行

    private func noteItemRow(_ note: NoteItem) -> some View {
        HStack {
            // 类型图标
            Image(systemName: note.type.icon)
                .font(.system(size: 16))
                .foregroundColor(note.type.color)
                .frame(width: 24, height: 24)

            // 标题和内容
            VStack(alignment: .leading, spacing: 4) {
                Text(note.title)
                    .font(themeManager.fonts.bodyMedium)
                    .foregroundColor(themeManager.colors.text)
                    .lineLimit(1)

                Text(note.summary)
                    .font(themeManager.fonts.bodySmall)
                    .foregroundColor(themeManager.colors.subtext)
                    .lineLimit(1)
            }

            Spacer()

            // 收藏按钮
            Button(action: {
                toggleFavorite(note)
            }) {
                Image(systemName: note.isFavorite ? "star.fill" : "star")
                    .foregroundColor(note.isFavorite ? .yellow : themeManager.colors.subtext)
            }

            // 删除按钮
            Button(action: {
                deleteNote(note)
            }) {
                Image(systemName: "trash")
                    .foregroundColor(.red)
            }
        }
        .padding()
        .background(themeManager.colors.surface)
        .cornerRadius(12)
    }

    // MARK: - 添加笔记表单

    private var addNoteSheet: some View {
        NavigationView {
            Form {
                // 笔记类型选择
                Section(header: Text("笔记类型")) {
                    Picker("类型", selection: $newNoteType) {
                        ForEach(NoteType.allCases, id: \.self) { type in
                            HStack {
                                Image(systemName: type.icon)
                                Text(type.displayName)
                            }
                            .tag(type)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }

                // 笔记标题
                Section(header: Text("标题")) {
                    TextField("输入标题", text: $newNoteTitle)
                }

                // 笔记内容（文本笔记）
                if newNoteType == .text {
                    Section(header: Text("内容")) {
                        TextEditor(text: $newNoteContent)
                            .frame(minHeight: 100)
                    }
                }

                // 语音录制（语音笔记）
                if newNoteType == .voice {
                    Section(header: Text("语音录制")) {
                        VStack(spacing: 16) {
                            // 录音状态
                            HStack {
                                Image(systemName: isRecording ? "waveform" : "mic")
                                    .font(.system(size: 24))
                                    .foregroundColor(isRecording ? .red : themeManager.colors.text)

                                Text(isRecording ? "正在录音..." : "准备录音")
                                    .font(themeManager.fonts.bodyMedium)
                                    .foregroundColor(isRecording ? .red : themeManager.colors.text)

                                Spacer()

                                if isRecording {
                                    Text(formatDuration(recordingDuration))
                                        .font(themeManager.fonts.bodyMedium)
                                        .foregroundColor(.red)
                                        .monospacedDigit()
                                }
                            }

                            // 录音控制按钮
                            HStack {
                                Spacer()

                                Button(action: toggleRecording) {
                                    ZStack {
                                        Circle()
                                            .fill(isRecording ? Color.red : themeManager.colors.accent)
                                            .frame(width: 60, height: 60)

                                        Image(systemName: isRecording ? "stop.fill" : "mic.fill")
                                            .font(.system(size: 24))
                                            .foregroundColor(.white)
                                    }
                                }

                                Spacer()
                            }
                            .padding(.vertical, 8)

                            // 录音说明
                            Text("点击按钮开始/停止录音")
                                .font(themeManager.fonts.bodySmall)
                                .foregroundColor(themeManager.colors.subtext)
                                .multilineTextAlignment(.center)
                        }
                        .padding(.vertical, 8)
                    }
                }
            }
            .navigationTitle(newNoteType == .text ? "添加文本笔记" : "添加语音笔记")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        resetForm()
                        showAddNoteSheet = false
                    }
                }

                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveNote()
                        showAddNoteSheet = false
                    }
                    .disabled(newNoteTitle.isEmpty || (newNoteType == .text && newNoteContent.isEmpty) || (newNoteType == .voice && recordingURL == nil))
                }
            }
        }
    }

    // MARK: - 辅助方法

    /// 更新预览
    private func updatePreview() {
        // 创建小组件数据
        let widgetData = NoteWidgetData(
            notes: notes,
            background: background.toWidgetBackground(),
            fontColor: WidgetColor.fromColor(fontColor),
            fontName: fontName,
            fontSize: fontSize,
            displayMode: displayMode,
            maxNoteCount: maxNoteCount,
            showCreationTime: showCreationTime,
            showTypeIcon: showTypeIcon,
            lastUpdated: Date()
        )

        self.noteWidgetData = widgetData
    }

    /// 保存配置
    private func saveConfig() {
        // 创建小组件数据
        let widgetData = NoteWidgetData(
            notes: notes,
            background: background.toWidgetBackground(),
            fontColor: WidgetColor.fromColor(fontColor),
            fontName: fontName,
            fontSize: fontSize,
            displayMode: displayMode,
            maxNoteCount: maxNoteCount,
            showCreationTime: showCreationTime,
            showTypeIcon: showTypeIcon,
            lastUpdated: Date()
        )

        // 保存配置
        AppGroupDataManager.shared.save(widgetData, for: .note, property: .config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        withAnimation {
            showSuccessToast = true
        }

        // 触觉反馈
        UINotificationFeedbackGenerator().notificationOccurred(.success)
    }

    /// 切换收藏状态
    private func toggleFavorite(_ note: NoteItem) {
        if let index = notes.firstIndex(where: { $0.id == note.id }) {
            notes[index].isFavorite.toggle()
            notes[index].updatedAt = Date()
            updatePreview()
        }
    }

    /// 删除笔记
    private func deleteNote(_ note: NoteItem) {
        // 如果是语音笔记，删除音频文件
        if note.type == .voice, let audioFilePath = note.audioFilePath {
            // 获取文件URL
            let fileURL = AppGroupConstants.containerURL.appendingPathComponent(audioFilePath)

            // 删除文件
            do {
                if FileManager.default.fileExists(atPath: fileURL.path) {
                    try FileManager.default.removeItem(at: fileURL)
                }
            } catch {
                print("删除音频文件失败: \(error.localizedDescription)")
            }
        }

        // 从列表中删除
        notes.removeAll { $0.id == note.id }
        updatePreview()
    }

    /// 切换录音状态
    private func toggleRecording() {
        if isRecording {
            // 停止录音
            if let result = AudioRecorderManager.shared.stopRecording() {
                recordingURL = result.0
                recordingDuration = result.1

                // 设置笔记内容
                newNoteContent = "语音笔记 \(formatDuration(recordingDuration))"
            }

            isRecording = false
        } else {
            // 开始录音
            if let url = AudioRecorderManager.shared.startRecording() {
                recordingURL = url
                recordingDuration = 0
                isRecording = true
            }
        }
    }

    /// 保存笔记
    private func saveNote() {
        // 创建新笔记
        var newNote: NoteItem

        if newNoteType == .text {
            // 文本笔记
            newNote = NoteItem(
                title: newNoteTitle,
                content: newNoteContent,
                type: .text,
                createdAt: Date(),
                updatedAt: Date()
            )
        } else {
            // 语音笔记
            guard let url = recordingURL else { return }

            // 复制音频文件到共享容器
            let fileName = url.lastPathComponent
            let sharedURL = AppGroupConstants.containerURL.appendingPathComponent(fileName)

            do {
                if FileManager.default.fileExists(atPath: sharedURL.path) {
                    try FileManager.default.removeItem(at: sharedURL)
                }

                if !FileManager.default.fileExists(atPath: url.path) {
                    // 文件不存在，可能是已经移动到共享容器
                    if FileManager.default.fileExists(atPath: sharedURL.path) {
                        // 已经在共享容器中
                    } else {
                        print("录音文件不存在")
                        return
                    }
                } else {
                    // 复制到共享容器
                    try FileManager.default.copyItem(at: url, to: sharedURL)
                }

                // 创建语音笔记
                newNote = NoteItem(
                    title: newNoteTitle,
                    content: newNoteContent,
                    type: .voice,
                    createdAt: Date(),
                    updatedAt: Date(),
                    audioFilePath: fileName,
                    audioDuration: recordingDuration
                )
            } catch {
                print("保存音频文件失败: \(error.localizedDescription)")
                return
            }
        }

        // 添加到笔记列表
        notes.append(newNote)

        // 更新预览
        updatePreview()

        // 重置表单
        resetForm()
    }

    /// 重置表单
    private func resetForm() {
        newNoteTitle = ""
        newNoteContent = ""
        newNoteType = .text
        isRecording = false
        recordingURL = nil
        recordingDuration = 0

        // 如果正在录音，停止录音
        if AudioRecorderManager.shared.recordingState == .recording {
            _ = AudioRecorderManager.shared.stopRecording()
        }
    }

    /// 格式化时长
    private func formatDuration(_ duration: TimeInterval) -> String {
        let minutes = Int(duration) / 60
        let seconds = Int(duration) % 60

        return String(format: "%02d:%02d", minutes, seconds)
    }
}

// MARK: - 预览

struct NoteWidgetConfigView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            NoteWidgetConfigView()
        }
    }
}
