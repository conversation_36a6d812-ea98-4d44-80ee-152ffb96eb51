import SwiftUI
import WidgetKit

struct WidgetsView: View {
    // MARK: - 属性
    @StateObject private var themeManager = ThemeManager.shared
    @State private var selectedFamily: WidgetFamily = .systemMedium
    
    // MARK: - 主视图
    var body: some View {
        NavigationStack {
            EnhancedWidgetsView(
                selectedFamily: selectedFamily,
                onThemeButtonTapped: {
                    // 切换主题
                    themeManager.toggleTheme()
                }
            )
            .navigationTitle("小组件")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                // 左侧主题按钮
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(action: {
                        themeManager.toggleTheme()
                    }) {
                        Image(systemName: themeManager.isDarkMode ? "sun.max.fill" : "moon.fill")
                            .foregroundColor(themeManager.colors.accent)
                    }
                }
                
                // 中间尺寸选择器
                ToolbarItem(placement: .principal) {
                    Picker("尺寸", selection: $selectedFamily) {
                        Image(systemName: "square").tag(WidgetFamily.systemSmall)
                        Image(systemName: "rectangle").tag(WidgetFamily.systemMedium)
                        Image(systemName: "rectangle.portrait").tag(WidgetFamily.systemLarge)
                    }
                    .pickerStyle(SegmentedPickerStyle())
                    .frame(width: 180)
                }
            }
        }
    }
}

// MARK: - 预览
#Preview {
    WidgetsView()
}

// MARK: - 小组件卡片
struct EnhancedWidgetCard: View {
    // 属性
    let title: String
    let description: String
    let iconName: String
    let widgetFamily: WidgetFamily
    let theme: AppTheme
    let onTap: () -> Void
    
    // 状态
    @State private var isPressed: Bool = false
    
    var body: some View {
        Button(action: {
            hapticFeedback(style: .light)
            onTap()
        }) {
            HStack(spacing: AppLayout.Spacing.large) {
                // 图标
                Image(systemName: iconName)
                    .font(.system(size: 30))
                    .foregroundColor(.white)
                    .frame(width: 60, height: 60)
                    .background(theme.colors.accent)
                    .cornerRadius(AppLayout.CornerRadius.medium)
                
                // 文本内容
                VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                    Text(title)
                        .font(theme.fonts.titleMedium)
                        .foregroundColor(theme.colors.text)
                    
                    Text(description)
                        .font(theme.fonts.bodySmall)
                        .foregroundColor(theme.colors.subtext)
                        .lineLimit(2)
                }
                
                Spacer()
                
                // 尺寸图标
                Image(systemName: sizeIcon(for: widgetFamily))
                    .font(.system(size: 20))
                    .foregroundColor(theme.colors.subtext)
            }
            .padding(AppLayout.Spacing.large)
            .background(
                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                    .fill(theme.colors.secondaryBackground)
                    .shadow(
                        color: theme.colors.shadow.opacity(isPressed ? 0.1 : 0.2),
                        radius: isPressed ? 4 : 8,
                        x: 0,
                        y: isPressed ? 2 : 4
                    )
            )
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: 0.2), value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0.2, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
    
    // 获取尺寸图标
    private func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        default:
            return "square"
        }
    }
    
    // 触觉反馈
    private func hapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
}

// MARK: - 分类按钮
struct CategoryButton: View {
    // 属性
    let title: String
    let isSelected: Bool
    let theme: AppTheme
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(theme.fonts.bodyMedium)
                .foregroundColor(isSelected ? .white : theme.colors.text)
                .padding(.horizontal, AppLayout.Spacing.medium)
                .padding(.vertical, AppLayout.Spacing.small)
                .background(
                    Capsule()
                        .fill(isSelected ? theme.colors.accent : theme.colors.secondaryBackground)
                )
                .shadow(
                    color: isSelected ? theme.colors.accent.opacity(0.3) : theme.colors.shadow.opacity(0.1),
                    radius: 4,
                    x: 0,
                    y: 2
                )
        }
        .buttonStyle(PlainButtonStyle())
    }
}
