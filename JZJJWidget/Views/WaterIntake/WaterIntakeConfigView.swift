import SwiftUI
import WidgetKit
import MyWidgetKit

struct WaterIntakeConfigView: View {
    // MARK: - 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss

    // MARK: - 状态
    // 目标设置
    @State private var targetIntake: Double = 2000
    @State private var useHealthKit: Bool = true

    // 快捷按钮设置
    @State private var quickAddOptions: [Double] = [100, 200, 300, 500]
    @State private var newQuickAddValue: Double = 100

    // 外观设置
    @State private var background: WidgetBackground = .color(WidgetColor.fromColor(.white))
    @State private var fontName: String = "PingFangSC-Regular"
    @State private var fontSize: CGFloat = 14
    @State private var fontColor: Color = .black
    @State private var accentColor: Color = .blue
    @State private var theme: WaterIntakeTheme = .oceanBlue
    @State private var useGradient: Bool = true
    @State private var useWaveEffect: Bool = true
    @State private var useShadow: Bool = true
    @State private var showCelebration: Bool = false

    // 预览设置
    @State private var previewSize: WidgetFamily = .systemMedium
    @State private var currentIntake: Double = 800
    @State private var waterWidgetData: WaterIntakeWidgetData?

    // 成功提示
    @State private var showSuccessToast: Bool = false

    // 数据管理器
    private let dataManager = AppGroupDataManager.shared

    // MARK: - 主视图
    var body: some View {
        ZStack {
            // 背景
            themeManager.currentTheme.colors.background
                .ignoresSafeArea()

            // 主内容
            VStack(spacing: 0) {
                // 预览区域 - 固定在顶部
                previewSection
                    .padding(.horizontal)
                    .padding(.top, 16)
                    .padding(.bottom, 8)
                    .background(themeManager.currentTheme.colors.surface.opacity(0.5))
                    .shadow(color: themeManager.currentTheme.colors.shadow.opacity(0.1), radius: 4, x: 0, y: 2)

                // 可滚动的配置区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 目标设置
                        goalSection

                        // 快捷按钮设置
                        quickAddSection

                        // 外观设置
                        appearanceSection
                    }
                    .padding()
                }
            }

            // 成功提示
            if showSuccessToast {
                VStack {
                    Spacer()

                    Text("保存成功")
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(.white)
                        .padding()
                        .background(Color.green)
                        .cornerRadius(8)
                        .shadow(radius: 4)
                        .padding(.bottom, 20)
                }
                .transition(.move(edge: .bottom))
                .animation(.easeInOut, value: showSuccessToast)
            }
        }
        .navigationTitle("水分摄入追踪器")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .principal) {
                navBarSizeSelector
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    saveWaterIntakeWidget()
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                }
            }
        }
        .onAppear {
            loadExistingData()
            updateWidgetData()
        }
        .onChange(of: targetIntake) { _ in updateWidgetData() }
        .onChange(of: useHealthKit) { _ in updateWidgetData() }
        .onChange(of: quickAddOptions) { _ in updateWidgetData() }
        .onChange(of: background) { _ in updateWidgetData() }
        .onChange(of: fontName) { _ in updateWidgetData() }
        .onChange(of: fontSize) { _ in updateWidgetData() }
        .onChange(of: fontColor) { _ in updateWidgetData() }
        .onChange(of: accentColor) { _ in updateWidgetData() }
        .onChange(of: currentIntake) { _ in updateWidgetData() }
    }

    // MARK: - 导航栏尺寸选择器
    private var navBarSizeSelector: some View {
        HStack {
            Spacer()

            Button(action: {
                withAnimation {
                    switch previewSize {
                    case .systemSmall:
                        previewSize = .systemMedium
                    case .systemMedium:
                        previewSize = .systemLarge
                    case .systemLarge:
                        previewSize = .systemSmall
                    default:
                        previewSize = .systemMedium
                    }
                    updateWidgetData()
                }
            }) {
                HStack(spacing: 4) {
                    Text(widgetFamilyName)
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.accent)

                    Image(systemName: "chevron.down")
                        .font(.caption)
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                }
                .padding(.vertical, 6)
                .padding(.horizontal, 10)
                .background(themeManager.currentTheme.colors.secondaryBackground)
                .cornerRadius(8)
            }

            Spacer()
        }
    }

    // MARK: - 预览区域
    private var previewSection: some View {
        VStack(alignment: .center, spacing: 12) {
            if let waterWidgetData = waterWidgetData {
                // 使用优化后的 UniversalWidgetPreviewView
                UniversalWidgetPreviewView(
                    data: waterWidgetData,
                    previewSize: $previewSize,
                    accentColor: themeManager.currentTheme.colors.accent,
                    backgroundColor: themeManager.currentTheme.colors.background,
                    surfaceColor: themeManager.currentTheme.colors.surface,
                    textColor: themeManager.currentTheme.colors.text,
                    subtextColor: themeManager.currentTheme.colors.subtext,
                    showSizeSelector: false,
                    showTitle: true,
                    title: "预览"
                ) { data, family in
                    AnyView(WaterIntakeWidgetView(data: data as! WaterIntakeWidgetData, family: family))
                }
                .frame(height: dynamicPreviewHeight)

                // 显示当前尺寸
                Text("当前尺寸：\(widgetFamilyName)")
                    .font(themeManager.currentTheme.fonts.bodyMedium)
                    .foregroundColor(themeManager.currentTheme.colors.accent)
            }
        }
    }

    // MARK: - 目标设置区域
    private var goalSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "目标设置",
            iconName: "drop.fill"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                // 目标饮水量
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("每日目标饮水量")
                            .font(themeManager.currentTheme.fonts.bodyMedium)
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        Spacer()

                        Text("\(Int(targetIntake)) ml")
                            .font(themeManager.currentTheme.fonts.bodyMedium)
                            .foregroundColor(themeManager.currentTheme.colors.accent)
                    }

                    Slider(value: $targetIntake, in: 500...5000, step: 100)
                        .accentColor(themeManager.currentTheme.colors.accent)
                }

                // 当前饮水量（仅用于预览）
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("当前饮水量（仅用于预览）")
                            .font(themeManager.currentTheme.fonts.bodyMedium)
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        Spacer()

                        Text("\(Int(currentIntake)) ml")
                            .font(themeManager.currentTheme.fonts.bodyMedium)
                            .foregroundColor(themeManager.currentTheme.colors.accent)
                    }

                    Slider(value: $currentIntake, in: 0...targetIntake * 1.5, step: 50)
                        .accentColor(themeManager.currentTheme.colors.accent)
                }
 
            }
        }
    }

    // MARK: - 快捷按钮设置区域
    private var quickAddSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "快捷添加按钮",
            iconName: "plus.circle.fill"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                Text("设置快捷添加按钮的水量值")
                    .font(themeManager.currentTheme.fonts.bodyMedium)
                    .foregroundColor(themeManager.currentTheme.colors.text)

                // 当前快捷按钮列表
                ForEach(quickAddOptions, id: \.self) { option in
                    HStack {
                        Text("\(Int(option)) ml")
                            .font(themeManager.currentTheme.fonts.bodyMedium)
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        Spacer()

                        Button(action: {
                            withAnimation {
                                if let index = quickAddOptions.firstIndex(of: option) {
                                    quickAddOptions.remove(at: index)
                                }
                            }
                        }) {
                            Image(systemName: "minus.circle.fill")
                                .foregroundColor(.red)
                        }
                    }
                    .padding(.vertical, 4)
                }

                // 添加新快捷按钮
                HStack {
                    TextField("输入水量 (ml)", value: $newQuickAddValue, format: .number)
                        .keyboardType(.numberPad)
                        .padding(8)
                        .background(themeManager.currentTheme.colors.tertiaryBackground)
                        .cornerRadius(8)

                    Button(action: {
                        withAnimation {
                            if newQuickAddValue > 0 && !quickAddOptions.contains(newQuickAddValue) {
                                quickAddOptions.append(newQuickAddValue)
                                quickAddOptions.sort()
                                newQuickAddValue = 100
                            }
                        }
                    }) {
                        Text("添加")
                            .foregroundColor(.white)
                            .padding(.horizontal, 12)
                            .padding(.vertical, 8)
                            .background(themeManager.currentTheme.colors.accent)
                            .cornerRadius(8)
                    }
                }
                .padding(.top, 8)
            }
        }
    }

    // MARK: - 外观设置区域
    private var appearanceSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "外观设置",
            iconName: "paintbrush"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                // 主题选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("预设主题")
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)

                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            ForEach(WaterIntakeTheme.allCases, id: \.self) { themeOption in
                                Button(action: {
                                    theme = themeOption
                                    accentColor = themeOption.accentColor.toColor()
                                    updateWidgetData()
                                }) {
                                    VStack(spacing: 6) {
                                        Circle()
                                            .fill(
                                                LinearGradient(
                                                    gradient: Gradient(colors: themeOption.gradientColors),
                                                    startPoint: .topLeading,
                                                    endPoint: .bottomTrailing
                                                )
                                            )
                                            .frame(width: 40, height: 40)
                                            .overlay(
                                                Circle()
                                                    .stroke(themeManager.currentTheme.colors.accent, lineWidth: theme == themeOption ? 2 : 0)
                                            )
                                            .shadow(color: Color.black.opacity(0.1), radius: 2, x: 0, y: 1)

                                        Text(themeOption.displayName)
                                            .font(themeManager.currentTheme.fonts.captionSmall)
                                            .foregroundColor(themeManager.currentTheme.colors.text)
                                    }
                                }
                                .buttonStyle(PlainButtonStyle())
                            }
                        }
                        .padding(.vertical, 4)
                    }
                }

                // 视觉效果选项
                VStack(alignment: .leading, spacing: 8) {
                    Text("视觉效果")
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)

                    Toggle("使用渐变色", isOn: $useGradient)
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)
                        .onChange(of: useGradient) { _ in updateWidgetData() }

                    Toggle("使用波浪效果（中/大尺寸）", isOn: $useWaveEffect)
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)
                        .onChange(of: useWaveEffect) { _ in updateWidgetData() }

                    Toggle("使用阴影效果", isOn: $useShadow)
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)
                        .onChange(of: useShadow) { _ in updateWidgetData() }

                    Toggle("达成目标时显示庆祝动画", isOn: $showCelebration)
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)
                        .onChange(of: showCelebration) { _ in updateWidgetData() }
                }
                .padding(.vertical, 8)

                // 背景设置
                BackgroundSelectView(
                    allowColor: true,
                    allowImage: true,
                    initialColor: background.colorValue?.toColor() ?? .white,
                    initialImage: background.imageValue,
                    onSelection: { selection in
                        background = selection.toWidgetBackground()
                        updateWidgetData()
                    }
                )

                // 字体设置
                FontSelectView(
                    showFontPicker: true,
                    showFontSizePicker: true,
                    showFontColorPicker: true,
                    onSelectionChanged: { selection in
                        fontName = selection.fontName
                        fontSize = selection.fontSize
                        fontColor = selection.fontColor
                        updateWidgetData()
                    }
                )

                // 自定义主题颜色
                VStack(alignment: .leading, spacing: 8) {
                    Text("自定义主题颜色")
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)

                    ColorPicker("选择主题颜色", selection: $accentColor)
                        .padding(.leading, 4)
                        .onChange(of: accentColor) { _ in updateWidgetData() }
                }
            }
        }
    }

    // MARK: - 辅助方法

    // 更新小组件数据
    private func updateWidgetData() {
        waterWidgetData = WaterIntakeWidgetData(
            currentIntake: currentIntake,
            targetIntake: targetIntake,
            quickAddOptions: quickAddOptions,
            background: background,
            fontName: fontName,
            fontSize: fontSize,
            fontColor: WidgetColor.fromColor(fontColor),
            accentColor: WidgetColor.fromColor(accentColor),
            theme: theme,
            useGradient: useGradient,
            useWaveEffect: useWaveEffect,
            useShadow: useShadow,
            useHealthKit: useHealthKit,
            lastUpdated: Date(),
            showCelebration: showCelebration
        )
    }

    // 加载现有数据
    private func loadExistingData() {
        if let savedData = dataManager.read(WaterIntakeWidgetData.self, for: .waterIntake, property: .config) {
            targetIntake = savedData.targetIntake
            quickAddOptions = savedData.quickAddOptions
            background = savedData.background
            fontName = savedData.fontName
            fontSize = savedData.fontSize
            fontColor = savedData.fontColor.toColor()
            accentColor = savedData.accentColor.toColor()
            theme = savedData.theme
            useGradient = savedData.useGradient
            useWaveEffect = savedData.useWaveEffect
            useShadow = savedData.useShadow
            useHealthKit = savedData.useHealthKit
            showCelebration = savedData.showCelebration
 
            currentIntake = savedData.currentIntake
        }

        updateWidgetData()
    }

    // 保存小组件
    private func saveWaterIntakeWidget() {
        guard let widgetData = waterWidgetData else { return }

        // 保存配置
        dataManager.save(widgetData, for: .waterIntake, property: .config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        withAnimation {
            showSuccessToast = true
        }

        // 3秒后隐藏提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation {
                showSuccessToast = false
            }
        }
    }

    // 根据小部件尺寸返回预览高度
    private var widgetPreviewHeight: CGFloat {
        switch previewSize {
        case .systemSmall:
            return 200
        case .systemMedium:
            return 200
        case .systemLarge:
            return 400
        case .systemExtraLarge:
            return 450
        @unknown default:
            return 200
        }
    }

    // 动态预览高度，确保大尺寸组件有足够的显示空间
    private var dynamicPreviewHeight: CGFloat {
        switch previewSize {
        case .systemSmall:
            return 220
        case .systemMedium:
            return 240
        case .systemLarge:
            return 420 // 大尺寸需要更多空间
        case .systemExtraLarge:
            return 470 // 超大尺寸需要更多空间
        @unknown default:
            return 220
        }
    }

    // 获取小部件尺寸名称
    private var widgetFamilyName: String {
        switch previewSize {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }
}
