//
//  SharedComponents.swift
//  JZJJWidget
//
//  Created by Augment on 2025/5/25.
//

import SwiftUI
import MyWidgetKit

// MARK: - 颜色按钮
/// 用于选择颜色的按钮组件，在多个视图中共享使用
public struct ColorButton: View {
    let color: Color
    let name: String
    let isSelected: Bool
    let theme: AppTheme
    let action: () -> Void

    public var body: some View {
        Button(action: action) {
            VStack(spacing: 4) {
                Circle()
                    .fill(color)
                    .frame(width: 36, height: 36)
                    .overlay(
                        Circle()
                            .stroke(isSelected ? theme.colors.accent : Color.clear, lineWidth: 2)
                    )
                    .shadow(color: isSelected ? theme.colors.accent.opacity(0.5) : Color.clear, radius: 4, x: 0, y: 0)

                Text(name)
                    .font(.caption)
                    .foregroundColor(theme.colors.text)
            }
            .padding(.vertical, 4)
            .padding(.horizontal, 8)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(isSelected ? theme.colors.secondaryBackground : Color.clear)
            )
        }
    }
}
