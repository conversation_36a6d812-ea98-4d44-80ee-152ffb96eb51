//
//  DailyQuoteConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/13.
//

import SwiftUI
import WidgetKit
import MyWidgetKit

struct DailyQuoteConfigView: View {
    // MARK: - 属性

    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // 状态变量
    @State private var previewSize: WidgetFamily = .systemSmall
    @State private var dropdownMenuID = "daily-quote-size-selector"

    // 预览内容
    let previewContent = "生活不止眼前的苟且，还有诗和远方。"

    // 支持的尺寸
    let supportedSizes: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]

    // 下拉菜单引用
    @State private var sizeSelector: TopDropdownSizeSelector?

    var body: some View {
        ZStack {
            // 主内容
            VStack(spacing: 20) {
                // 预览区域
                VStack {
                    Text("预览")
                        .font(theme.fonts.headlineMedium)
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal)

                    // 预览内容
                    VStack {
                        Text(previewContent)
                            .font(.system(size: 18))
                            .multilineTextAlignment(.center)
                            .padding()
                    }
                    .frame(width: widgetSize(for: previewSize).width, height: widgetSize(for: previewSize).height)
                    .background(theme.colors.surface)
                    .cornerRadius(20)
                    .shadow(color: theme.colors.shadow, radius: 10)
                    .padding()
                }
                .background(theme.colors.surfaceVariant.opacity(0.5))
                .cornerRadius(12)
                .padding(.horizontal)

                Spacer()

                // 保存按钮
                Button(action: {
                    // 保存操作
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(.white)
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(theme.colors.accent)
                        .cornerRadius(10)
                }
                .padding(.horizontal)
                .padding(.bottom)
            }

            // 尺寸选择器下拉菜单
            TopDropdownSizeSelector(
                selectedSize: $previewSize,
                supportedSizes: supportedSizes,
                menuYPosition: 60,
                menuID: dropdownMenuID
            )
            .onAppear { sizeSelector = $0 }
        }
        .navigationTitle("")
        .toolbar {
            ToolbarItem(placement: .principal) {
                // 自定义导航栏标题视图 - 使用封装的按钮组件
                SizeSelectorButton(
                    selectedSize: previewSize,
                    onTap: {
                        sizeSelector?.toggleMenu()
                    },
                    menuID: dropdownMenuID
                )
            }

            #if os(iOS)
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    // 保存操作
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.colors.accent)
                }
            }
            #endif
        }
    }

    // MARK: - 辅助方法

    // 获取尺寸文本
    func sizeText(for size: WidgetFamily) -> String {
        switch size {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }

    // 获取尺寸图标
    func sizeIcon(for size: WidgetFamily) -> String {
        switch size {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        case .systemExtraLarge:
            return "square.grid.2x2"
        @unknown default:
            return "square"
        }
    }

    // 获取组件尺寸
    func widgetSize(for family: WidgetFamily) -> CGSize {
        switch family {
        case .systemSmall:
            return CGSize(width: 170, height: 170)
        case .systemMedium:
            return CGSize(width: 360, height: 170)
        case .systemLarge:
            return CGSize(width: 360, height: 380)
        case .systemExtraLarge:
            return CGSize(width: 360, height: 380)
        @unknown default:
            return CGSize(width: 170, height: 170)
        }
    }

    // 获取屏幕宽度
    func getScreenWidth() -> CGFloat {
        #if os(iOS)
        return UIScreen.main.bounds.width
        #else
        return 390.0 // 默认宽度
        #endif
    }
}

#Preview {
    NavigationView {
        DailyQuoteConfigView()
            .environmentObject(ThemeManager.shared)
    }
}
