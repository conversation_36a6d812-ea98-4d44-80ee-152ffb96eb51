//
//  EnhancedProfileView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/15.
//

import MessageUI // 导入 MessageUI 框架用于发送邮件
import MyWidgetKit
import SwiftUI

extension Bundle {
    var displayName: String? {
        object(forInfoDictionaryKey: "CFBundleDisplayName") as? String ?? object(forInfoDictionaryKey: "CFBundleName") as? String
    }

    var appVersion: String? {
        object(forInfoDictionaryKey: "CFBundleShortVersionString") as? String
    }

    /// APP 图标
    public static var appIcon: UIImage? {
        if let icons = Bundle.main.infoDictionary?["CFBundleIcons"] as? [String: Any],
           let primaryIcon = icons["CFBundlePrimaryIcon"] as? [String: Any],
           let iconFiles = primaryIcon["CFBundleIconFiles"] as? [String]
        {
            let appIcon = UIImage(named: iconFiles[0])
            return appIcon
        }
        return nil
    }
}

struct EnhancedProfileView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @ObservedObject private var historyManager = WidgetHistoryManager.shared
    @State private var showingThemeSelector: Bool = false
    @State private var showingMailView = false
    @State private var mailResult: Result<MFMailComposeResult, Error>? = nil

    // 获取设备信息
    private var deviceName: String {
        UIDevice.current.name
    }

    private var systemInfo: String {
        "\(UIDevice.current.systemName) \(UIDevice.current.systemVersion)"
    }

    // 获取设备存储信息
    private var storageInfo: String {
        let fileManager = FileManager.default
        if let path = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first?.path,
           let attributes = try? fileManager.attributesOfFileSystem(forPath: path),
           let freeSize = attributes[.systemFreeSize] as? NSNumber,
           let totalSize = attributes[.systemSize] as? NSNumber
        {
            let free = ByteCountFormatter.string(fromByteCount: freeSize.int64Value, countStyle: .file)
            let total = ByteCountFormatter.string(fromByteCount: totalSize.int64Value, countStyle: .file)
            return "可用: \(free)\n 总计: \(total)"
        }
        return "未知"
    }

    // 获取电池信息
    private var batteryInfo: String {
        UIDevice.current.isBatteryMonitoringEnabled = true
        let level = Int(UIDevice.current.batteryLevel * 100)
        let state = UIDevice.current.batteryState

        let stateString: String
        switch state {
        case .charging: stateString = "充电中"
        case .full: stateString = "已充满"
        case .unplugged: stateString = "使用电池中"
        case .unknown: stateString = ""
        @unknown default: stateString = ""
        }

        return level >= 0 ? "\(level)% \(stateString)" : "未知"
    }

    var body: some View {
        List {
            // APP 信息
            Section {
                // App 图标和基本信息
                HStack {
                    if let uiImage = Bundle.appIcon {
                        Image(uiImage: uiImage)
                            .resizable()
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 60, height: 60)
                            .clipShape(RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium))
                            .overlay(
                                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                    .stroke(themeManager.currentTheme.colors.accent.opacity(0.3), lineWidth: 1)
                            )
                            .shadow(
                                color: themeManager.currentTheme.colors.accent.opacity(0.2),
                                radius: 4,
                                x: 0,
                                y: 2
                            )
                    }

                    VStack(alignment: .leading, spacing: AppLayout.Spacing.extraSmall) {
                        Text(Bundle.main.displayName ?? "JZJJWidget")
                            .font(AppTypography.titleSmall(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.text)
                        Text("版本: \(Bundle.main.appVersion ?? "1.0")")
                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.subtext)

                        // 添加主题标识
                        HStack(spacing: 4) {
                            Circle()
                                .fill(themeManager.currentTheme.colors.primary)
                                .frame(width: 8, height: 8)
                            Text("当前主题: \(themeManager.currentTheme.rawValue)")
                                .font(AppTypography.caption(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.subtext)
                        }
                    }

                    Spacer()
                }
                .padding(.vertical, AppLayout.Spacing.medium)
                .background(
                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                        .fill(themeManager.currentTheme.colors.surface.opacity(0.5))
                )

                // 设备信息
                InfoRow(icon: "iphone", title: "设备名称", value: deviceName)
                InfoRow(icon: "gear", title: "系统版本", value: systemInfo)
                InfoRow(icon: "internaldrive", title: "存储空间", value: storageInfo)
                InfoRow(icon: "battery.100", title: "电池状态", value: batteryInfo)
            }

            // 新增：浏览历史 Section
            Section(header:
                HStack {
                    Image(systemName: "clock.arrow.circlepath")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                    Text("浏览历史")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                }
            ) {
                if historyManager.history.isEmpty {
                    HStack {
                        Image(systemName: "tray")
                            .foregroundColor(themeManager.currentTheme.colors.subtext)
                            .font(.title2)
                        VStack(alignment: .leading) {
                            Text("暂无浏览历史")
                                .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.subtext)
                            Text("开始探索小组件功能吧")
                                .font(AppTypography.caption(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.subtext.opacity(0.7))
                        }
                        Spacer()
                    }
                    .padding(.vertical, AppLayout.Spacing.medium)
                } else {
                    ForEach(historyManager.history) { item in
                        HStack(spacing: AppLayout.Spacing.medium) {
                            // 图标背景
                            ZStack {
                                Circle()
                                    .fill(themeManager.currentTheme.colors.accent.opacity(0.1))
                                    .frame(width: 40, height: 40)
                                Image(systemName: item.widgetIconName)
                                    .foregroundColor(themeManager.currentTheme.colors.accent)
                                    .font(.system(size: 18, weight: .medium))
                            }

                            VStack(alignment: .leading, spacing: 2) {
                                Text(item.widgetName)
                                    .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                    .foregroundColor(themeManager.currentTheme.colors.text)
                                Text("浏览于 \(item.viewedDate, style: .relative)前")
                                    .font(AppTypography.caption(theme: themeManager.currentTheme))
                                    .foregroundColor(themeManager.currentTheme.colors.subtext)
                            }

                            Spacer()

                            // 时间指示器
                            VStack {
                                Circle()
                                    .fill(themeManager.currentTheme.colors.primary)
                                    .frame(width: 6, height: 6)
                                Spacer()
                            }
                        }
                        .padding(.vertical, AppLayout.Spacing.small)
                    }
                    .onDelete(perform: deleteHistoryItem)

                    // 清除历史记录按钮
                    Button(action: {
                        historyManager.clearHistory()
                    }) {
                        HStack {
                            Image(systemName: "trash")
                                .foregroundColor(.red)
                            Text("清除历史记录")
                                .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                .foregroundColor(.red)
                            Spacer()
                        }
                        .padding(.vertical, AppLayout.Spacing.small)
                    }
                }
            }

            // 设置
            Section(header:
                HStack {
                    Image(systemName: "gearshape")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                    Text("设置")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                }
            ) {
                // 主题设置 - 增强样式
                Button(action: {
                    showingThemeSelector = true
                }) {
                    HStack(spacing: AppLayout.Spacing.medium) {
                        // 主题图标背景
                        ZStack {
                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.small)
                                .fill(themeManager.currentTheme.colors.accent.opacity(0.1))
                                .frame(width: 32, height: 32)
                            Image(systemName: "paintbrush.fill")
                                .foregroundColor(themeManager.currentTheme.colors.accent)
                                .font(.system(size: 16, weight: .medium))
                        }

                        VStack(alignment: .leading, spacing: 2) {
                            Text("主题设置")
                                .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.text)
                            Text("个性化应用外观")
                                .font(AppTypography.caption(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.subtext)
                        }

                        Spacer()

                        // 当前主题预览
                        HStack(spacing: 4) {
                            Circle()
                                .fill(themeManager.currentTheme.colors.primary)
                                .frame(width: 12, height: 12)
                            Circle()
                                .fill(themeManager.currentTheme.colors.secondary)
                                .frame(width: 12, height: 12)
                            Circle()
                                .fill(themeManager.currentTheme.colors.accent)
                                .frame(width: 12, height: 12)
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(
                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.small)
                                .fill(themeManager.currentTheme.colors.surface)
                        )

                        Image(systemName: "chevron.right")
                            .foregroundColor(themeManager.currentTheme.colors.subtext)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .padding(.vertical, AppLayout.Spacing.small)



                // 联系我们
                Button(action: {
                    if MFMailComposeViewController.canSendMail() {
                        self.showingMailView = true
                    } else {
                        if let url = URL(string: "mailto:<EMAIL>") {
                            UIApplication.shared.open(url)
                        }
                    }
                }) {
                    HStack(spacing: AppLayout.Spacing.medium) {
                        ZStack {
                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.small)
                                .fill(themeManager.currentTheme.colors.accent.opacity(0.1))
                                .frame(width: 32, height: 32)
                            Image(systemName: "envelope.fill")
                                .foregroundColor(themeManager.currentTheme.colors.accent)
                                .font(.system(size: 16, weight: .medium))
                        }

                        VStack(alignment: .leading, spacing: 2) {
                            Text("联系我们")
                                .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.text)
                            Text("反馈建议或问题报告")
                                .font(AppTypography.caption(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.subtext)
                        }

                        Spacer()

                        Image(systemName: "chevron.right")
                            .foregroundColor(themeManager.currentTheme.colors.subtext)
                            .font(.system(size: 14, weight: .medium))
                    }
                }
                .padding(.vertical, AppLayout.Spacing.small)

                // 关于
                NavigationLink(destination: AboutView()) {
                    HStack(spacing: AppLayout.Spacing.medium) {
                        ZStack {
                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.small)
                                .fill(themeManager.currentTheme.colors.accent.opacity(0.1))
                                .frame(width: 32, height: 32)
                            Image(systemName: "info.circle.fill")
                                .foregroundColor(themeManager.currentTheme.colors.accent)
                                .font(.system(size: 16, weight: .medium))
                        }

                        VStack(alignment: .leading, spacing: 2) {
                            Text("关于")
                                .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.text)
                            Text("应用信息和开发团队")
                                .font(AppTypography.caption(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.subtext)
                        }

                        Spacer()
                    }
                }
                .padding(.vertical, AppLayout.Spacing.small)
            }
        }
        .listStyle(InsetGroupedListStyle())
        .navigationTitle("我的")
        .navigationBarTitleDisplayMode(.large)
        .background(themeManager.currentTheme.colors.background.ignoresSafeArea())
        .sheet(isPresented: $showingThemeSelector) {
            ThemeSelectorView(themeManager: themeManager)
        }
        .sheet(isPresented: $showingMailView) {
            MailView(
                result: self.$mailResult,
                recipients: ["<EMAIL>"],
                subject: "JZJJWidget 反馈",
                messageBody: "感谢您使用 JZJJWidget！请在此处描述您的问题或建议：\n\n"
            )
        }
        .onAppear {
            // 确保主题状态同步
            themeManager.objectWillChange.send()
        }
    }

    // 可选：删除单条历史记录的方法
    private func deleteHistoryItem(at offsets: IndexSet) {
        historyManager.history.remove(atOffsets: offsets)
        // 如果 WidgetHistoryManager 中的 saveHistory 不是在 history 改变时自动触发，
        // 你可能需要在这里显式调用保存方法，但这取决于 WidgetHistoryManager 的实现。
        // 在我们之前的 WidgetHistoryManager 实现中，history 是 @Published，
        // 但删除操作后仍需手动调用 saveHistory() 来持久化。
        // 因此，最好在 WidgetHistoryManager 中添加一个 remove 方法来处理删除和保存。
        // 或者，在这里直接调用：
        // historyManager.saveHistory() // 假设 saveHistory 是 public 的
        // 为了简单起见，我们假设 WidgetHistoryManager 的 clearHistory 已经处理了保存，
        // 对于单条删除，如果需要持久化，WidgetHistoryManager 需要相应调整。
        // 目前的 WidgetHistoryManager.swift 中没有直接删除单条并保存的函数，
        // 所以这里的 .onDelete 更多是UI层面的移除，除非 WidgetHistoryManager 内部监听 @Published history 的变化并自动保存。
        // 为了确保数据一致性，建议在 WidgetHistoryManager 中添加一个 remove(atOffsets:) 的方法。
    }

    // 主题选择器视图
    struct ThemeSelectorView: View {
        @ObservedObject var themeManager: ThemeManager
        @Environment(\.presentationMode) var presentationMode

        var body: some View {
            NavigationView {
                List {
                    // 当前主题预览
                    Section {
                        VStack(spacing: AppLayout.Spacing.medium) {
                            Text("当前主题")
                                .font(AppTypography.titleSmall(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.text)

                            // 大型主题预览卡片
                            VStack(spacing: AppLayout.Spacing.medium) {
                                HStack(spacing: AppLayout.Spacing.small) {
                                    Circle()
                                        .fill(themeManager.currentTheme.colors.primary)
                                        .frame(width: 24, height: 24)
                                    Circle()
                                        .fill(themeManager.currentTheme.colors.secondary)
                                        .frame(width: 24, height: 24)
                                    Circle()
                                        .fill(themeManager.currentTheme.colors.accent)
                                        .frame(width: 24, height: 24)
                                }

                                Text(themeManager.currentTheme.rawValue)
                                    .font(AppTypography.titleSmall(theme: themeManager.currentTheme))
                                    .foregroundColor(themeManager.currentTheme.colors.text)
                            }
                            .padding(AppLayout.Spacing.large)
                            .background(
                                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                                    .fill(themeManager.currentTheme.colors.surface)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                                            .stroke(themeManager.currentTheme.colors.accent.opacity(0.3), lineWidth: 2)
                                    )
                            )
                        }
                        .frame(maxWidth: .infinity)
                        .padding(.vertical, AppLayout.Spacing.medium)
                    }

                    // 主题选择
                    Section(header: Text("选择主题").foregroundColor(themeManager.currentTheme.colors.accent)) {
                        ForEach(themeManager.getAllThemes()) { theme in
                            Button(action: {
                                themeManager.switchTheme(to: theme)
                            }) {
                                HStack(spacing: AppLayout.Spacing.medium) {
                                    // 主题颜色预览
                                    HStack(spacing: 6) {
                                        Circle()
                                            .fill(theme.colors.primary)
                                            .frame(width: 20, height: 20)
                                        Circle()
                                            .fill(theme.colors.secondary)
                                            .frame(width: 20, height: 20)
                                        Circle()
                                            .fill(theme.colors.accent)
                                            .frame(width: 20, height: 20)
                                    }
                                    .padding(AppLayout.Spacing.medium)
                                    .background(
                                        RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                            .fill(theme.colors.surface)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                                    .stroke(
                                                        themeManager.currentTheme == theme ?
                                                        themeManager.currentTheme.colors.accent :
                                                        Color.clear,
                                                        lineWidth: 2
                                                    )
                                            )
                                    )

                                    // 主题名称
                                    VStack(alignment: .leading, spacing: 2) {
                                        Text(theme.rawValue)
                                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                            .foregroundColor(themeManager.currentTheme.colors.text)

                                        if themeManager.currentTheme == theme {
                                            Text("当前使用")
                                                .font(AppTypography.caption(theme: themeManager.currentTheme))
                                                .foregroundColor(themeManager.currentTheme.colors.accent)
                                        }
                                    }

                                    Spacer()

                                    // 选中标记
                                    if themeManager.currentTheme == theme {
                                        Image(systemName: "checkmark.circle.fill")
                                            .foregroundColor(themeManager.currentTheme.colors.accent)
                                            .font(.system(size: 20, weight: .medium))
                                    }
                                }
                            }
                            .padding(.vertical, AppLayout.Spacing.small)
                        }

                        // 跟随系统
                        Button(action: {
                            themeManager.applySystemTheme()
                        }) {
                            HStack(spacing: AppLayout.Spacing.medium) {
                                ZStack {
                                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                        .fill(themeManager.currentTheme.colors.accent.opacity(0.1))
                                        .frame(width: 60, height: 40)
                                    Image(systemName: "iphone")
                                        .foregroundColor(themeManager.currentTheme.colors.accent)
                                        .font(.system(size: 20, weight: .medium))
                                }

                                VStack(alignment: .leading, spacing: 2) {
                                    Text("跟随系统")
                                        .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                        .foregroundColor(themeManager.currentTheme.colors.text)
                                    Text("自动适配系统外观")
                                        .font(AppTypography.caption(theme: themeManager.currentTheme))
                                        .foregroundColor(themeManager.currentTheme.colors.subtext)
                                }

                                Spacer()
                            }
                        }
                        .padding(.vertical, AppLayout.Spacing.small)
                    }
                }
                .listStyle(InsetGroupedListStyle())
                .navigationTitle("主题设置")
                .navigationBarTitleDisplayMode(.large)
                .navigationBarItems(trailing: Button("完成") {
                    presentationMode.wrappedValue.dismiss()
                })
                .background(themeManager.currentTheme.colors.background.ignoresSafeArea())
            }
        }
    }

    // 用于发送邮件的辅助视图 (与 ProfileView 中的 MailView 相同)
    struct MailView: UIViewControllerRepresentable {
        @Binding var result: Result<MFMailComposeResult, Error>?
        var recipients: [String]?
        var subject: String?
        var messageBody: String?

        class Coordinator: NSObject, MFMailComposeViewControllerDelegate {
            @Binding var result: Result<MFMailComposeResult, Error>?

            init(result: Binding<Result<MFMailComposeResult, Error>?>) {
                _result = result
            }

            func mailComposeController(_ controller: MFMailComposeViewController, didFinishWith result: MFMailComposeResult, error: Error?) {
                defer {
                    controller.dismiss(animated: true)
                }
                if let error = error {
                    self.result = .failure(error)
                    return
                }
                self.result = .success(result)
            }
        }

        func makeCoordinator() -> Coordinator {
            Coordinator(result: $result)
        }

        func makeUIViewController(context: Context) -> MFMailComposeViewController {
            let vc = MFMailComposeViewController()
            vc.mailComposeDelegate = context.coordinator
            vc.setToRecipients(recipients)
            vc.setSubject(subject ?? "")
            vc.setMessageBody(messageBody ?? "", isHTML: false)
            return vc
        }

        func updateUIViewController(_ uiViewController: MFMailComposeViewController, context: Context) {}
    }

    // 关于页面视图
    struct AboutView: View {
        @StateObject private var themeManager = ThemeManager.shared

        var body: some View {
            List {
                Section {
                    VStack(spacing: AppLayout.Spacing.medium) {
                        // App 图标
                        if let uiImage = Bundle.appIcon {
                            Image(uiImage: uiImage)
                                .resizable()
                                .aspectRatio(contentMode: .fit)
                                .frame(width: 80, height: 80)
                                .clipShape(RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large))
                                .shadow(
                                    color: themeManager.currentTheme.colors.accent.opacity(0.3),
                                    radius: 8,
                                    x: 0,
                                    y: 4
                                )
                        }

                        VStack(spacing: AppLayout.Spacing.small) {
                            Text("\(Bundle.main.displayName)")
                                .font(AppTypography.titleLarge(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.text)

                            Text("版本 \(Bundle.main.appVersion ?? "1.0")")
                                .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.subtext)

                            Text("精美小组件，个性化桌面")
                                .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                                .foregroundColor(themeManager.currentTheme.colors.subtext)
                                .multilineTextAlignment(.center)
                        }
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, AppLayout.Spacing.large)
                }

                Section(header: Text("功能特色").foregroundColor(themeManager.currentTheme.colors.accent)) {
                    FeatureRow(
                        icon: "square.grid.3x2",
                        title: "丰富小组件",
                        description: "13种精选小组件，满足不同需求"
                    )
                    FeatureRow(
                        icon: "paintbrush",
                        title: "主题定制",
                        description: "9种精美主题，个性化外观"
                    )
                    FeatureRow(
                        icon: "keyboard",
                        title: "键盘扩展",
                        description: "自定义键盘主题，打字更有趣"
                    )
                    FeatureRow(
                        icon: "icloud",
                        title: "数据同步",
                        description: "App Groups技术，数据安全同步"
                    )
                }

                Section(header: Text("开发团队").foregroundColor(themeManager.currentTheme.colors.accent)) {
                    VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                        Text("感谢您使用 JZJJWidget！")
                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        Text("我们致力于为用户提供最优质的小组件体验，让您的iOS桌面更加个性化和实用。")
                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.subtext)

                        Text("如有任何问题或建议，欢迎通过邮件联系我们。")
                            .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                            .foregroundColor(themeManager.currentTheme.colors.subtext)
                    }
                    .padding(.vertical, AppLayout.Spacing.small)
                }
            }
            .listStyle(InsetGroupedListStyle())
            .navigationTitle("关于")
            .navigationBarTitleDisplayMode(.large)
            .background(themeManager.currentTheme.colors.background.ignoresSafeArea())
        }
    }

    // 功能特色行组件
    struct FeatureRow: View {
        @StateObject private var themeManager = ThemeManager.shared
        let icon: String
        let title: String
        let description: String

        var body: some View {
            HStack(spacing: AppLayout.Spacing.medium) {
                ZStack {
                    Circle()
                        .fill(themeManager.currentTheme.colors.accent.opacity(0.1))
                        .frame(width: 40, height: 40)
                    Image(systemName: icon)
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                        .font(.system(size: 18, weight: .medium))
                }

                VStack(alignment: .leading, spacing: 2) {
                    Text(title)
                        .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                        .foregroundColor(themeManager.currentTheme.colors.text)
                    Text(description)
                        .font(AppTypography.caption(theme: themeManager.currentTheme))
                        .foregroundColor(themeManager.currentTheme.colors.subtext)
                }

                Spacer()
            }
            .padding(.vertical, AppLayout.Spacing.small)
        }
    }

    // 信息行组件
    struct InfoRow: View {
        @StateObject private var themeManager = ThemeManager.shared
        let icon: String
        let title: String
        let value: String

        var body: some View {
            HStack(spacing: AppLayout.Spacing.medium) {
                ZStack {
                    Circle()
                        .fill(themeManager.currentTheme.colors.accent.opacity(0.1))
                        .frame(width: 32, height: 32)
                    Image(systemName: icon)
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                        .font(.system(size: 16, weight: .medium))
                }

                Text(title)
                    .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.text)

                Spacer()

                Text(value)
                    .font(AppTypography.bodyMedium(theme: themeManager.currentTheme))
                    .foregroundColor(themeManager.currentTheme.colors.subtext)
                    .multilineTextAlignment(.trailing)
            }
            .padding(.vertical, AppLayout.Spacing.small)
        }
    }
}

#Preview {
    EnhancedProfileView()
}
