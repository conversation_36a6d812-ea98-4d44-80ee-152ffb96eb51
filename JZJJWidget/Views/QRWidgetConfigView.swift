//
//  QRWidgetConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/12.
//

import MyWidgetKit
import SwiftUI
import WidgetKit

// 导入 WidgetType 枚举
typealias WidgetType = MyWidgetKit.WidgetType
typealias WidgetPropertyKey = MyWidgetKit.WidgetPropertyKey

/// 颜色选择组件
struct ColorSelectView: View {
    // 预设颜色数组
    let colors: [Color]
    // 颜色名称数组
    let colorNames: [String]
    // 初始选中索引，-1表示自定义颜色
    let initialSelectedIndex: Int
    // 初始自定义颜色
    let initialCustomColor: Color
    // 禁用的颜色索引
    let disabledIndices: Set<Int>
    // 是否显示自定义颜色
    let showCustomColor: Bool
    // 颜色选中回调 (index, color)
    let onColorSelected: (Int, Color) -> Void

    // 当前选中索引，-1为自定义
    @State private var selectedIndex: Int
    // 当前自定义颜色
    @State private var customColor: Color

    init(colors: [Color] = [.white, .black, .gray, .red, .blue, .green, .orange, .purple, .brown],
         colorNames: [String] = ["白色", "黑色", "灰色", "红色", "蓝色", "绿色", "橙色", "紫色", "棕色"],
         initialSelectedIndex: Int = 0,
         initialCustomColor: Color = .black,
         disabledIndices: [Int] = [],
         showCustomColor: Bool = true,
         onColorSelected: @escaping (Int, Color) -> Void)
    {
        self.colors = colors
        self.colorNames = colorNames
        self.initialSelectedIndex = initialSelectedIndex
        self.initialCustomColor = initialCustomColor
        self.disabledIndices = Set(disabledIndices)
        self.showCustomColor = showCustomColor
        self.onColorSelected = onColorSelected
        _selectedIndex = State(initialValue: initialSelectedIndex)
        _customColor = State(initialValue: initialCustomColor)
    }

    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 16) {
                if showCustomColor {
                    // 自定义颜色放最前面
                    VStack {
                        ColorPicker("自定义颜色", selection: $customColor, supportsOpacity: false)
                            .labelsHidden()
                            .frame(width: 40, height: 24)
                            .onChange(of: customColor) { newColor in
                                selectedIndex = -1
                                onColorSelected(-1, newColor)
                            }
                        Text("自定义")
                            .font(.caption)
                            .foregroundColor(.primary)
                    }
                    .padding(4)
                    .background(selectedIndex == -1 ? Color.blue.opacity(0.1) : Color.clear)
                    .cornerRadius(8)
                    .scaleEffect(selectedIndex == -1 ? 1.08 : 1.0)
                    .animation(.easeInOut(duration: 0.18), value: selectedIndex)
                }

                ForEach(colors.indices, id: \.self) { index in
                    Button(action: {
                        selectedIndex = index
                        customColor = colors[index] // 选中预设色时同步ColorPicker
                        onColorSelected(index, colors[index])
                    }) {
                        VStack {
                            Circle()
                                .fill(colors[index])
                                .frame(width: 24, height: 24)
                                .overlay(
                                    Circle().stroke(
                                        selectedIndex == index ? Color.blue :
                                            (colors[index] == .white ? Color.gray.opacity(0.5) : Color.clear),
                                        lineWidth: 2
                                    )
                                )
                            Text(colorNames[index])
                                .font(.caption)
                                .foregroundColor(.primary)
                        }
                        .padding(4)
                        .background(selectedIndex == index ? Color.blue.opacity(0.1) : Color.clear)
                        .cornerRadius(8)
                        .scaleEffect(selectedIndex == index ? 1.08 : 1.0)
                        .animation(.easeInOut(duration: 0.18), value: selectedIndex)
                    }
                    .buttonStyle(PlainButtonStyle())
                    .disabled(disabledIndices.contains(index))
                    .opacity(disabledIndices.contains(index) ? 0.4 : 1.0)
                }
            }
            .padding(.vertical, 8)
        }
    }
}

/// 二维码小组件设置界面
public struct QRWidgetConfigView: View {
    // MARK: - 属性

    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.presentationMode) var presentationMode

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // 状态变量
    @State private var content: String = ""
    @State private var foregroundColor: Color = .black
    @State private var backgroundColor: Color = .white
    @State private var backgroundImage: UIImage?
    @State private var showSuccessToast: Bool = false
    @State private var isEditing: Bool = false
    @State private var previewScale: CGFloat = 1.0 

    // 二维码数据
    @State private var qrWidgetData: QRWidgetViewData?

    // 背景选择
    @State private var backgroundSelection: BackgroundSelection = .color(.white)

    // 颜色选项
    let colorOptions: [Color] = [.black, .white, .red, .orange, .yellow, .green, .blue, .purple, .pink, .brown]
    let colorNames: [String] = ["黑色", "白色", "红色", "橙色", "黄色", "绿色", "蓝色", "紫色", "粉色", "棕色"]

    // 支持的尺寸
    let supportedSizes: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]

    // 下拉菜单ID
    @State private var dropdownMenuID = "qr-widget-size-selector"
    // 尺寸选择器引用
    @State private var sizeSelector: TopDropdownSizeSelector?

    // 预览尺寸
    @State private var previewSize: WidgetFamily = .systemSmall

    // 文本框焦点状态
    @FocusState private var isTextFieldFocused: Bool

    // 初始化方法
    public init() {}

    public var body: some View {
        ZStack {
            // 背景
            theme.colors.background
                .ignoresSafeArea()

            // 主内容
            VStack(spacing: 0) {
                // 预览区域 - 固定在顶部
                previewSection
                    .padding(.horizontal)
                    .padding(.top, 16)
                    .padding(.bottom, 8)
                    .background(theme.colors.surface.opacity(0.5))
                    .shadow(color: theme.colors.shadow.opacity(0.1), radius: 4, x: 0, y: 2)

                // 可滚动的配置区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 内容设置
                        contentSection

                        // 样式设置
                        styleSection
                    }
                    .padding()
                }
            }

            // 成功提示
            if showSuccessToast {
                VStack {
                    Spacer()

                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.white)

                        Text("保存成功")
                            .foregroundColor(.white)
                            .font(theme.fonts.bodyMedium)
                    }
                    .padding()
                    .background(
                        Capsule()
                            .fill(theme.colors.success)
                            .shadow(color: theme.colors.shadow, radius: 8, x: 0, y: 4)
                    )
                    .padding(.bottom, 50)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .zIndex(100)
            }

            // 尺寸选择器下拉菜单
            TopDropdownSizeSelector(
                selectedSize: $previewSize,
                supportedSizes: supportedSizes,
                menuYPosition: 110, // 调整位置，确保在导航栏下方显示
                menuWidth: 180,
                menuID: dropdownMenuID
            )
            .onAppear { sizeSelector = $0 }
        }
        .navigationTitle("二维码组件")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .principal) {
                navBarSizeSelector
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    saveQRWidget()
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.colors.accent)
                }
            }
        }
        .onAppear {
            loadExistingData()

            // 确保二维码已生成
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                updateQRCode()
            }
        }
        .onChange(of: foregroundColor) { _ in
            updateQRCode()
        }
        .onChange(of: content) { _ in
            updateQRCode()
        }
        .onChange(of: backgroundColor) { newValue in
            backgroundSelection = .color(newValue)
            qrWidgetData?.background = WidgetBackground.color(WidgetColor.fromColor(newValue))
        }
        .onChange(of: backgroundImage) { newValue in
            if let image = newValue {
                backgroundSelection = .image(image)
                if let uiImageData = image.pngData() {
                    qrWidgetData?.background = WidgetBackground.imageData(uiImageData)
                }
            }
            updateQRCode()
        }
    }

    // MARK: - 子视图

    // 导航栏尺寸选择器
    private var navBarSizeSelector: some View {
        SizeSelectorButton(
            selectedSize: previewSize,
            onTap: {
                DropdownMenuManager.shared.toggleMenu(id: dropdownMenuID)
            },
            menuID: dropdownMenuID
        )
    }

    // 预览区域
    private var previewSection: some View {
        VStack(alignment: .center, spacing: 12) {
            if let qrWidgetData = qrWidgetData {
                // 使用优化后的 UniversalWidgetPreviewView
                UniversalWidgetPreviewView(
                    data: qrWidgetData,
                    previewSize: $previewSize,
                    accentColor: theme.colors.accent,
                    backgroundColor: theme.colors.background,
                    surfaceColor: theme.colors.surface,
                    textColor: theme.colors.text,
                    subtextColor: theme.colors.subtext,
                    showSizeSelector: false,
                    showTitle: true,
                    title: "预览"
                ) { data, _ in
                    AnyView(QRWidgetView(data: data))
                }
                .frame(height: dynamicPreviewHeight)

                // 显示当前尺寸
                Text("当前尺寸：\(widgetFamilyName)")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.accent)

                Text("提示：双指缩放可调整预览大小")
                    .font(theme.fonts.bodySmall)
                    .foregroundColor(theme.colors.subtext)
            } else {
                // 加载中或无数据状态
                VStack(spacing: 16) {
                    Text("预览")
                        .font(theme.fonts.headlineMedium)
                        .foregroundColor(theme.colors.text)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    ZStack {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(theme.colors.surface)
                            .shadow(color: theme.colors.shadow, radius: 8, x: 0, y: 4)

                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: theme.colors.accent))

                            Text("加载中...")
                                .font(theme.fonts.bodyMedium)
                                .foregroundColor(theme.colors.subtext)
                                .padding(.top, 8)
                        }
                    }
                    .frame(height: widgetPreviewHeight)
                }
            }
        }
    }

    // 根据小部件尺寸返回预览高度
    private var widgetPreviewHeight: CGFloat {
        switch previewSize {
        case .systemSmall:
            return 200
        case .systemMedium:
            return 200
        case .systemLarge:
            return 400
        case .systemExtraLarge:
            return 450
        @unknown default:
            return 200
        }
    }

    // 动态预览高度，确保大尺寸组件有足够的显示空间
    private var dynamicPreviewHeight: CGFloat {
        switch previewSize {
        case .systemSmall:
            return 220
        case .systemMedium:
            return 240
        case .systemLarge:
            return 420 // 大尺寸需要更多空间
        case .systemExtraLarge:
            return 470 // 超大尺寸需要更多空间
        @unknown default:
            return 220
        }
    }

    // 获取小部件尺寸名称
    private var widgetFamilyName: String {
        switch previewSize {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }

    // 内容设置
    private var contentSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("二维码内容")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)

            TextField("输入网址、联系方式或任何文本内容", text: $content, axis: .vertical)
                .font(theme.fonts.bodyMedium)
                .foregroundColor(theme.colors.text)
                .lineLimit(5 ... 10)
                .textFieldStyle(PlainTextFieldStyle())
                .padding(12)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(theme.colors.surfaceVariant)
                )
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(isTextFieldFocused ? theme.colors.accent : theme.colors.border, lineWidth: 1)
                )
                .focused($isTextFieldFocused)
                .submitLabel(.done)
                .onSubmit {
                    isTextFieldFocused = false
                }

            Text("提示：点击键盘上的完成按钮收起键盘")
                .font(theme.fonts.bodySmall)
                .foregroundColor(theme.colors.subtext)
        }
        .padding(.vertical, 8)
    }

    // 样式设置
    private var styleSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("样式设置")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)

            // 前景色选择
            VStack(alignment: .leading, spacing: 8) {
                Text("二维码颜色")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                ColorSelectView(
                    colors: colorOptions,
                    colorNames: colorNames,
                    initialSelectedIndex: colorOptions.firstIndex(of: foregroundColor) ?? 0,
                    initialCustomColor: foregroundColor,
                    showCustomColor: true
                ) { _, color in
                    foregroundColor = color
                }
            }

            // 背景设置
            VStack(alignment: .leading, spacing: 8) {
                Text("背景设置")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                BackgroundSelectView(
                    allowColor: true,
                    allowImage: true,
                    colors: colorOptions,
                    colorNames: colorNames,
                    initialColor: backgroundColor,
                    initialImage: backgroundImage,
                    onSelection: { selection in
                        backgroundSelection = selection

                        switch selection {
                        case let .color(color):
                            backgroundColor = color
                            backgroundImage = nil
                            qrWidgetData?.background = .color(WidgetColor.fromColor(color))
                        case let .image(image):
                            backgroundImage = image
                            if let uiImageData = image.pngData() {
                                qrWidgetData?.background = .imageData(uiImageData)
                            }
                        case let .packageImage(name):
                            backgroundImage = nil
                            qrWidgetData?.background = .packageImage(name)
                        }
                    }
                )
            }
        }
        .padding(.vertical, 8)
    }

    // MARK: - 辅助方法

    // 加载现有数据
    private func loadExistingData() {
        if let config = AppGroupDataManager.shared.read(QRWidgetViewData.self, for: WidgetType.qrImage, property: WidgetPropertyKey.config) {
            qrWidgetData = config
            foregroundColor = config.foregroundColor.toColor()
            content = config.content

            if case let .color(widgetColor) = config.background {
                backgroundColor = widgetColor.toColor()
                backgroundSelection = .color(widgetColor.toColor())
            } else if case let .imageFile(fileName) = config.background {
                // 构建文件名
                let backgroundImageFileName = AppGroupDataManager.shared.fileName(for: WidgetType.qrImage, property: WidgetPropertyKey.backgroundImage)
                // 读取数据
                if let imageData = AppGroupDataManager.shared.readData(fileName: backgroundImageFileName) {
                    let image = UIImage(data: imageData)
                    backgroundImage = image
                    if let image = image {
                        backgroundSelection = .image(image)
                    }
                }
            } else if case let .packageImage(name) = config.background {
                // 加载内置背景图片
                backgroundSelection = .packageImage(name)
            }
        } else {
            // 创建默认数据
            qrWidgetData = QRWidgetViewData(
                content: content,
                foreground: WidgetBackground.color(WidgetColor.fromColor(foregroundColor)),
                background: WidgetBackground.color(WidgetColor.fromColor(backgroundColor)),
                foregroundColor: WidgetColor.fromColor(.black)
            )

            // 生成初始二维码
            updateQRCode()
        }
    }

    // 更新二维码
    private func updateQRCode() {
        // 确保内容不为空
        let qrContent = content.isEmpty ? " " : content

        if let uiImageData = UIImage.generateQRCode(from: qrContent, color: foregroundColor, backgroundColor: .clear)?.pngData() {
            qrWidgetData?.foreground = WidgetBackground.imageData(uiImageData)
            qrWidgetData?.foregroundColor = WidgetColor.fromColor(foregroundColor)
            qrWidgetData?.content = qrContent
        } else {
            print("无法生成二维码图像")
        }
    }

    // 保存二维码小组件
    private func saveQRWidget() {
        guard var qrWidgetData = qrWidgetData else { return }

        // 保存二维码图像 - 使用优化的压缩方法
        if let uiImage = UIImage.generateQRCode(from: content, color: foregroundColor, backgroundColor: .clear) {
            // 二维码图像需要保持清晰度，使用较高质量的压缩
            if let pngData = uiImage.compressForWidgetOptimized(
                targetFileSize: 40 * 1024, // 40KB 足够清晰的二维码
                minQuality: 0.7, // 保持较高质量
                preserveAspectRatio: true // 保持宽高比
            ) {
                AppGroupDataManager.shared.saveAuto(pngData, for: WidgetType.qrImage, property: WidgetPropertyKey.image)
            }
        }

        // 根据背景选择保存背景
        switch backgroundSelection {
        case let .image(image):
            // 背景图片可以更激进地压缩
            if let compressedData = image.compressForWidgetOptimized(
                targetFileSize: 60 * 1024, // 60KB 对背景图片足够
                minQuality: 0.5, // 可以接受较低质量
                minSize: CGSize(width: 300, height: 300), // 最小尺寸限制
                maxPixelArea: 800_000, // 限制最大像素面积
                preserveAspectRatio: true // 保持宽高比
            ) {
                AppGroupDataManager.shared.saveAuto(compressedData, for: WidgetType.qrImage, property: WidgetPropertyKey.backgroundImage)
                qrWidgetData.background = .imageFile(AppGroupDataManager.shared.fileName(for: WidgetType.qrImage, property: WidgetPropertyKey.backgroundImage))

                // 打印压缩信息，便于调试
                print("背景图片压缩后大小: \(compressedData.count / 1024) KB")
            }
        case let .color(color):
            qrWidgetData.background = .color(WidgetColor.fromColor(color))
        case let .packageImage(imageName):
            qrWidgetData.background = .packageImage(imageName)
        }

        // 保存内容
        qrWidgetData.content = content

        // 保存配置
        AppGroupDataManager.shared.save(qrWidgetData, for: WidgetType.qrImage, property: WidgetPropertyKey.config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        withAnimation {
            showSuccessToast = true
        }

        // 3秒后隐藏提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation {
                showSuccessToast = false
            }
        }

        // 收起键盘
        isTextFieldFocused = false
    }
}
