//
//  CategoryButton.swift
//  JZJJWidget
//
//  Created by y<PERSON><PERSON><PERSON> on 2025/5/15.
//

import SwiftUI
import MyWidgetKit

/// 分类按钮组件
struct CategoryButton: View {
    // MARK: - 属性

    // 按钮内容
    let title: String
    let isSelected: Bool
    let theme: AppTheme
    let action: () -> Void

    // 可选图标
    var iconName: String?

    // 动画持续时间
    private let animationDuration: Double = 0.2

    // 状态
    @State private var isPressed: Bool = false

    // MARK: - 视图主体

    var body: some View {
        Button(action: {
            // 触觉反馈
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()

            // 按下动画
            withAnimation(.easeInOut(duration: animationDuration)) {
                isPressed = true
            }

            // 延迟后恢复并执行操作
            DispatchQueue.main.asyncAfter(deadline: .now() + animationDuration) {
                withAnimation(.easeInOut(duration: animationDuration)) {
                    isPressed = false
                }
                action()
            }
        }) {
            HStack(spacing: 6) {
                // 可选图标
                if let iconName = iconName {
                    Image(systemName: iconName)
                        .font(.system(size: 14))
                        .foregroundColor(isSelected ? .white : theme.colors.text)
                }

                // 标题
                Text(title)
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(isSelected ? .white : theme.colors.text)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 10)
            .background(
                Capsule()
                    .fill(isSelected ? theme.colors.accent : theme.colors.surfaceVariant)
            )
            .overlay(
                Capsule()
                    .stroke(isSelected ? Color.clear : theme.colors.border, lineWidth: 1)
            )
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: animationDuration), value: isPressed)
            .animation(.easeInOut(duration: animationDuration), value: isSelected)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        CategoryButton(
            title: "全部",
            isSelected: true,
            theme: AppTheme.iosLight,
            action: {}
        )

        CategoryButton(
            title: "信息类",
            isSelected: false,
            theme: AppTheme.iosLight,
            action: {}
        )

        CategoryButton(
            title: "工具类",
            isSelected: false,
            theme: AppTheme.iosLight,
            action: {},
            iconName: "wrench.and.screwdriver"
        )
    }
    .padding()
    .previewLayout(.sizeThatFits)
}

#Preview {
    VStack(spacing: 20) {
        CategoryButton(
            title: "全部",
            isSelected: true,
            theme: AppTheme.iosDark,
            action: {}
        )

        CategoryButton(
            title: "信息类",
            isSelected: false,
            theme: AppTheme.iosDark,
            action: {}
        )

        CategoryButton(
            title: "工具类",
            isSelected: false,
            theme: AppTheme.iosDark,
            action: {},
            iconName: "wrench.and.screwdriver"
        )
    }
    .padding()
    .previewLayout(.sizeThatFits)
    .preferredColorScheme(.dark)
}
