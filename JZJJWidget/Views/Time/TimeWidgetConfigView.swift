import MyWidgetKit
import SwiftUI
import WidgetKit

/// 时间小组件配置界面
public struct TimeWidgetConfigView: View {
    // MARK: - 环境对象

    @EnvironmentObject private var themeManager: ThemeManager

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // MARK: - 状态变量

    // 配置数据
    @State private var timeWidgetData: TimeWidgetData?

    // 字体颜色
    @State private var fontColor: Color = .white

    // 字体名称
    @State private var fontName: String = ""

    // 是否使用12小时制
    @State private var use12HourFormat: Bool = false

    // 是否显示日期
    @State private var showDate: Bool = true

    // 是否显示秒
    @State private var showSeconds: Bool = true

    // 背景颜色
    @State private var backgroundColor: Color = .black

    // 背景图片
    @State private var backgroundImage: UIImage?

    // 背景选择
    @State private var backgroundSelection: BackgroundSelection = .color(.black)

    // 成功提示
    @State private var showSuccessToast: Bool = false

    // 文本框焦点状态
    @FocusState private var isTextFieldFocused: Bool

    // 支持的尺寸
    let supportedSizes: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]

    // 下拉菜单ID
    @State private var dropdownMenuID = "time-widget-size-selector"

    // 尺寸选择器引用
    @State private var sizeSelector: TopDropdownSizeSelector?

    // 预览尺寸
    @State private var previewSize: WidgetFamily = .systemSmall

    // MARK: - 初始化方法

    public init() {}

    // MARK: - 视图主体

    public var body: some View {
        ZStack {
            // 背景
            theme.colors.background
                .ignoresSafeArea()

            // 主内容
            VStack(spacing: 0) {
                // 预览区域 - 固定在顶部
                previewSection
                    .padding(.horizontal)
                    .padding(.top, 16)
                    .padding(.bottom, 8)
                    .background(theme.colors.surface.opacity(0.5))
                    .shadow(color: theme.colors.shadow.opacity(0.1), radius: 4, x: 0, y: 2)

                // 可滚动的配置区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 时间格式设置
                        timeFormatSection

                        // 样式设置
                        styleSection
                    }
                    .padding()
                }
            }

            // 成功提示
            if showSuccessToast {
                VStack {
                    Spacer()

                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.white)

                        Text("保存成功")
                            .foregroundColor(.white)
                            .font(theme.fonts.bodyMedium)
                    }
                    .padding()
                    .background(
                        Capsule()
                            .fill(theme.colors.success)
                            .shadow(color: theme.colors.shadow, radius: 8, x: 0, y: 4)
                    )
                    .padding(.bottom, 50)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .zIndex(100)
            }

            // 尺寸选择器下拉菜单
            TopDropdownSizeSelector(
                selectedSize: $previewSize,
                supportedSizes: supportedSizes,
                menuYPosition: 110, // 调整位置，确保在导航栏下方显示
                menuWidth: 180,
                menuID: dropdownMenuID
            )
            .onAppear { sizeSelector = $0 }
        }
        .navigationTitle("时间小组件")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .principal) {
                navBarSizeSelector
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    saveTimeWidget()
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.colors.accent)
                }
            }
        }
        .onAppear {
            loadExistingData()
        }
        .onChange(of: fontColor) { _ in
            updateWidgetData()
        }
        .onChange(of: fontName) { _ in
            updateWidgetData()
        }
        .onChange(of: use12HourFormat) { _ in
            updateWidgetData()
        }
        .onChange(of: showDate) { _ in
            updateWidgetData()
        }
        .onChange(of: showSeconds) { _ in
            updateWidgetData()
        }
        .onChange(of: backgroundColor) { newValue in
            backgroundSelection = .color(newValue)
            if var widgetData = timeWidgetData {
                widgetData.background = .color(WidgetColor.fromColor(newValue))
                timeWidgetData = widgetData
                updateWidgetData()
            }
        }
        .onChange(of: backgroundImage) { newValue in
            if let image = newValue {
                backgroundSelection = .image(image)
                if var widgetData = timeWidgetData, let uiImageData = image.pngData() {
                    widgetData.background = .imageData(uiImageData)
                    timeWidgetData = widgetData
                    updateWidgetData()
                }
            }
        }
    }

    // MARK: - 子视图

    // 导航栏尺寸选择器
    private var navBarSizeSelector: some View {
        SizeSelectorButton(
            selectedSize: previewSize,
            onTap: {
                DropdownMenuManager.shared.toggleMenu(id: dropdownMenuID)
            },
            menuID: dropdownMenuID
        )
    }

    // 预览区域
    private var previewSection: some View {
        VStack(alignment: .center, spacing: 12) {
            if let timeWidgetData = timeWidgetData {
                // 使用优化后的 UniversalWidgetPreviewView
                UniversalWidgetPreviewView(
                    data: timeWidgetData,
                    previewSize: $previewSize,
                    accentColor: theme.colors.accent,
                    backgroundColor: theme.colors.background,
                    surfaceColor: theme.colors.surface,
                    textColor: theme.colors.text,
                    subtextColor: theme.colors.subtext,
                    showSizeSelector: false,
                    showTitle: true,
                    title: "预览"
                ) { data, _ in
                    AnyView(TimeWidgetView(data: data as! TimeWidgetData))
                }
                .frame(height: dynamicPreviewHeight)

                // 显示当前尺寸
                Text("当前尺寸：\(widgetFamilyName)")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.accent)

                Text("提示：双指缩放可调整预览大小")
                    .font(theme.fonts.bodySmall)
                    .foregroundColor(theme.colors.subtext)
            } else {
                // 加载中或无数据状态
                VStack(spacing: 16) {
                    Text("预览")
                        .font(theme.fonts.headlineMedium)
                        .foregroundColor(theme.colors.text)
                        .frame(maxWidth: .infinity, alignment: .leading)

                    ZStack {
                        RoundedRectangle(cornerRadius: 16)
                            .fill(theme.colors.surface)
                            .shadow(color: theme.colors.shadow, radius: 8, x: 0, y: 4)

                        VStack {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle(tint: theme.colors.accent))

                            Text("加载中...")
                                .font(theme.fonts.bodyMedium)
                                .foregroundColor(theme.colors.subtext)
                                .padding(.top, 8)
                        }
                    }
                    .frame(height: widgetPreviewHeight)
                }
            }
        }
    }

    // 时间格式设置
    private var timeFormatSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("时间格式")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)

            // 时间格式选项
            VStack(alignment: .leading, spacing: 12) {
                Toggle(isOn: $use12HourFormat) {
                    Text("使用12小时制")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.text)
                }
                .toggleStyle(SwitchToggleStyle(tint: theme.colors.accent))

                Toggle(isOn: $showDate) {
                    Text("显示日期")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.text)
                }
                .toggleStyle(SwitchToggleStyle(tint: theme.colors.accent))

                Toggle(isOn: $showSeconds) {
                    Text("显示秒")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.text)
                }
                .toggleStyle(SwitchToggleStyle(tint: theme.colors.accent))
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 12)
                    .fill(theme.colors.surfaceVariant)
            )
        }
        .padding(.vertical, 8)
    }

    // 样式设置
    private var styleSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("样式设置")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)

            // 字体样式
            VStack(alignment: .leading, spacing: 8) {
                Text("字体样式")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                FontSelectView(
                    showFontPicker: true,
                    showFontSizePicker: true,
                    showFontColorPicker: true,
                    onSelectionChanged: { selection in
                        fontColor = selection.fontColor
                        fontName = selection.fontName
                        // 立即更新预览数据
                        updateWidgetData()
                    }
                )
            }

            // 背景设置
            VStack(alignment: .leading, spacing: 8) {
                Text("背景设置")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                BackgroundSelectView(
                    allowColor: true,
                    allowImage: true,
                    colors: colorOptions,
                    colorNames: colorNames,
                    initialColor: backgroundColor,
                    initialImage: backgroundImage,
                    onSelection: { selection in
                        backgroundSelection = selection

                        switch selection {
                        case let .color(color):
                            backgroundColor = color
                            backgroundImage = nil
                            if var widgetData = timeWidgetData {
                                widgetData.background = .color(WidgetColor.fromColor(color))
                                timeWidgetData = widgetData
                                // 强制刷新预览
                                DispatchQueue.main.async {
                                    // 创建一个临时副本并重新赋值，触发视图更新
                                    let tempData = timeWidgetData
                                    timeWidgetData = nil
                                    timeWidgetData = tempData
                                }
                            }
                        case let .image(image):
                            backgroundImage = image
                            if var widgetData = timeWidgetData, let uiImageData = image.pngData() {
                                widgetData.background = .imageData(uiImageData)
                                timeWidgetData = widgetData
                                // 强制刷新预览
                                DispatchQueue.main.async {
                                    // 创建一个临时副本并重新赋值，触发视图更新
                                    let tempData = timeWidgetData
                                    timeWidgetData = nil
                                    timeWidgetData = tempData
                                }
                            }
                        case let .packageImage(name):
                            backgroundImage = nil
                            if var widgetData = timeWidgetData {
                                widgetData.background = .packageImage(name)
                                timeWidgetData = widgetData
                                // 强制刷新预览
                                DispatchQueue.main.async {
                                    // 创建一个临时副本并重新赋值，触发视图更新
                                    let tempData = timeWidgetData
                                    timeWidgetData = nil
                                    timeWidgetData = tempData
                                }
                            }
                        }
                    }
                )
            }
        }
        .padding(.vertical, 8)
    }

    // MARK: - 计算属性

    // 颜色选项
    private var colorOptions: [Color] {
        [.black, .white, .red, .orange, .yellow, .green, .blue, .purple, .pink]
    }

    // 颜色名称
    private var colorNames: [String] {
        ["黑色", "白色", "红色", "橙色", "黄色", "绿色", "蓝色", "紫色", "粉色"]
    }

    // 根据小部件尺寸返回预览高度
    private var widgetPreviewHeight: CGFloat {
        switch previewSize {
        case .systemSmall:
            return 200
        case .systemMedium:
            return 200
        case .systemLarge:
            return 400
        case .systemExtraLarge:
            return 450
        @unknown default:
            return 200
        }
    }

    // 动态预览高度，确保大尺寸组件有足够的显示空间
    private var dynamicPreviewHeight: CGFloat {
        switch previewSize {
        case .systemSmall:
            return 220
        case .systemMedium:
            return 240
        case .systemLarge:
            return 420 // 大尺寸需要更多空间
        case .systemExtraLarge:
            return 470 // 超大尺寸需要更多空间
        @unknown default:
            return 220
        }
    }

    // 获取小部件尺寸名称
    private var widgetFamilyName: String {
        switch previewSize {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }

    // MARK: - 辅助方法

    // 加载现有数据
    private func loadExistingData() {
        if let config = AppGroupDataManager.shared.read(TimeWidgetData.self, for: .timeWidget, property: .config) {
            timeWidgetData = config
            fontColor = config.fontColor.toColor()
            fontName = config.fontName
            use12HourFormat = config.use12HourFormat
            showDate = config.showDate
            showSeconds = config.showSeconds

            if case let .color(widgetColor) = config.background {
                backgroundColor = widgetColor.toColor()
                backgroundSelection = .color(widgetColor.toColor())
            } else if case let .imageFile(fileName) = config.background {
                // 构建文件名
                let backgroundImageFileName = AppGroupDataManager.shared.fileName(for: .timeWidget, property: .backgroundImage)
                // 读取数据
                if let imageData = AppGroupDataManager.shared.readData(fileName: backgroundImageFileName) {
                    let image = UIImage(data: imageData)
                    backgroundImage = image
                    if let image = image {
                        backgroundSelection = .image(image)
                    }
                }
            } else if case let .packageImage(name) = config.background {
                // 加载内置背景图片
                backgroundSelection = .packageImage(name)
            }
        } else {
            // 创建默认数据
            timeWidgetData = TimeWidgetData(
                background: .color(WidgetColor.fromColor(.red)),
                fontColor: WidgetColor.fromColor(fontColor),
                fontName: fontName,
                use12HourFormat: use12HourFormat,
                showDate: showDate,
                showSeconds: showSeconds
            )
        }
    }

    // 更新小组件数据
    private func updateWidgetData() {
        guard var widgetData = timeWidgetData else { return }

        widgetData.fontColor = WidgetColor.fromColor(fontColor)
        widgetData.fontName = fontName
        widgetData.use12HourFormat = use12HourFormat
        widgetData.showDate = showDate
        widgetData.showSeconds = showSeconds

        // 保持背景设置不变
        switch backgroundSelection {
        case let .color(color):
            widgetData.background = .color(WidgetColor.fromColor(color))
        case let .image(image):
            if let uiImageData = image.pngData() {
                widgetData.background = .imageData(uiImageData)
            }
        case let .packageImage(name):
            widgetData.background = .packageImage(name)
        }

        // 强制刷新预览
        DispatchQueue.main.async {
            // 先设置为nil再重新赋值，触发视图更新
            self.timeWidgetData = nil
            self.timeWidgetData = widgetData
        }
    }

    // 保存时间小组件
    private func saveTimeWidget() {
        guard var timeWidgetData = timeWidgetData else { return }

        // 根据背景选择保存背景
        switch backgroundSelection {
        case let .image(image):
            // 背景图片可以更激进地压缩
            if let compressedData = image.compressForWidgetOptimized(
                targetFileSize: 60 * 1024, // 60KB 对背景图片足够
                minQuality: 0.5, // 可以接受较低质量
                minSize: CGSize(width: 300, height: 300), // 最小尺寸限制
                maxPixelArea: 800000, // 限制最大像素面积
                preserveAspectRatio: true // 保持宽高比
            ) {
                AppGroupDataManager.shared.saveAuto(compressedData, for: .timeWidget, property: .backgroundImage)
                timeWidgetData.background = .imageFile(AppGroupDataManager.shared.fileName(for: .timeWidget, property: .backgroundImage))

                // 打印压缩信息，便于调试
                print("背景图片压缩后大小: \(compressedData.count / 1024) KB")
            }
        case let .color(color):
            timeWidgetData.background = .color(WidgetColor.fromColor(color))
        case let .packageImage(imageName):
            timeWidgetData.background = .packageImage(imageName)
        }

        // 保存配置
        AppGroupDataManager.shared.save(timeWidgetData, for: .timeWidget, property: .config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        withAnimation {
            showSuccessToast = true
        }

        // 3秒后隐藏提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation {
                showSuccessToast = false
            }
        }

        // 收起键盘
        isTextFieldFocused = false
    }
}
