//
//  DefaultThemeTestView.swift
//  JZJJWidget
//
//  Created by Assistant on 2025/1/27.
//

import SwiftUI
import MyWidgetKit

/// 默认主题测试视图
struct DefaultThemeTestView: View {
    @StateObject private var appThemeManager = ThemeManager.shared
    @StateObject private var keyboardThemeManager = KeyboardThemeManager.shared
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 应用主题信息
                    themeInfoSection
                    
                    // 键盘主题信息
                    keyboardThemeInfoSection
                    
                    // 默认主题列表
                    defaultThemesSection
                    
                    // 测试按钮
                    testButtonsSection
                }
                .padding()
            }
            .navigationTitle("默认主题测试")
            .background(appThemeManager.colors.background)
        }
        .environmentObject(appThemeManager)
    }
    
    // MARK: - 应用主题信息
    private var themeInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📱 应用主题")
                .font(.headline)
                .foregroundColor(appThemeManager.colors.text)
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("当前主题:")
                        .foregroundColor(appThemeManager.colors.subtext)
                    Spacer()
                    Text(appThemeManager.currentTheme.rawValue)
                        .foregroundColor(appThemeManager.colors.text)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("主色调:")
                        .foregroundColor(appThemeManager.colors.subtext)
                    Spacer()
                    Circle()
                        .fill(appThemeManager.colors.primary)
                        .frame(width: 20, height: 20)
                }
                
                HStack {
                    Text("强调色:")
                        .foregroundColor(appThemeManager.colors.subtext)
                    Spacer()
                    Circle()
                        .fill(appThemeManager.colors.accent)
                        .frame(width: 20, height: 20)
                }
            }
            .padding()
            .background(appThemeManager.colors.surface)
            .cornerRadius(12)
        }
    }
    
    // MARK: - 键盘主题信息
    private var keyboardThemeInfoSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("⌨️ 键盘主题")
                .font(.headline)
                .foregroundColor(appThemeManager.colors.text)
            
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("当前主题:")
                        .foregroundColor(appThemeManager.colors.subtext)
                    Spacer()
                    Text(keyboardThemeManager.currentTheme.name)
                        .foregroundColor(appThemeManager.colors.text)
                        .fontWeight(.medium)
                }
                
                HStack {
                    Text("主题类型:")
                        .foregroundColor(appThemeManager.colors.subtext)
                    Spacer()
                    Text(keyboardThemeManager.currentTheme.type.displayName)
                        .foregroundColor(appThemeManager.colors.text)
                }
                
                HStack {
                    Text("背景色:")
                        .foregroundColor(appThemeManager.colors.subtext)
                    Spacer()
                    Circle()
                        .fill(Color(
                            red: keyboardThemeManager.currentTheme.backgroundColor.red,
                            green: keyboardThemeManager.currentTheme.backgroundColor.green,
                            blue: keyboardThemeManager.currentTheme.backgroundColor.blue,
                            opacity: keyboardThemeManager.currentTheme.backgroundColor.alpha
                        ))
                        .frame(width: 20, height: 20)
                }
                
                HStack {
                    Text("按键色:")
                        .foregroundColor(appThemeManager.colors.subtext)
                    Spacer()
                    Circle()
                        .fill(Color(
                            red: keyboardThemeManager.currentTheme.keyBackgroundColor.red,
                            green: keyboardThemeManager.currentTheme.keyBackgroundColor.green,
                            blue: keyboardThemeManager.currentTheme.keyBackgroundColor.blue,
                            opacity: keyboardThemeManager.currentTheme.keyBackgroundColor.alpha
                        ))
                        .frame(width: 20, height: 20)
                }
            }
            .padding()
            .background(appThemeManager.colors.surface)
            .cornerRadius(12)
        }
    }
    
    // MARK: - 默认主题列表
    private var defaultThemesSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("📋 默认主题列表")
                .font(.headline)
                .foregroundColor(appThemeManager.colors.text)
            
            VStack(spacing: 8) {
                ForEach(Array(KeyboardTheme.defaultThemes.enumerated()), id: \.offset) { index, theme in
                    HStack {
                        Text("\(index)")
                            .font(.caption)
                            .foregroundColor(appThemeManager.colors.subtext)
                            .frame(width: 20)
                        
                        Text(theme.name)
                            .foregroundColor(appThemeManager.colors.text)
                        
                        Spacer()
                        
                        Text(theme.type.displayName)
                            .font(.caption)
                            .foregroundColor(appThemeManager.colors.subtext)
                        
                        if index == 0 {
                            Text("默认")
                                .font(.caption)
                                .foregroundColor(.white)
                                .padding(.horizontal, 8)
                                .padding(.vertical, 2)
                                .background(appThemeManager.colors.accent)
                                .cornerRadius(4)
                        }
                    }
                    .padding(.horizontal)
                    .padding(.vertical, 8)
                    .background(index == 0 ? appThemeManager.colors.accent.opacity(0.1) : appThemeManager.colors.surface)
                    .cornerRadius(8)
                }
            }
        }
    }
    
    // MARK: - 测试按钮
    private var testButtonsSection: some View {
        VStack(spacing: 12) {
            Button("🧡 应用暖橙主题") {
                appThemeManager.switchTheme(to: .warmOrange)
                keyboardThemeManager.setTheme(KeyboardTheme.defaultThemes[0])
            }
            .buttonStyle(TestButtonStyle(color: appThemeManager.colors.accent))
            
            Button("🔄 重置为默认主题") {
                resetToDefaultTheme()
            }
            .buttonStyle(TestButtonStyle(color: appThemeManager.colors.primary))
            
            Button("📋 打印主题信息") {
                printThemeInfo()
            }
            .buttonStyle(TestButtonStyle(color: appThemeManager.colors.secondary))
        }
    }
    
    // MARK: - 私有方法
    private func resetToDefaultTheme() {
        // 清除保存的主题设置
        UserDefaults.standard.removeObject(forKey: "app_selected_theme")
        
        // 清除键盘主题设置
        if let appGroupDefaults = UserDefaults(suiteName: "group.com.abc.JZJJWidgetAPP") {
            appGroupDefaults.removeObject(forKey: "keyboard_theme")
            appGroupDefaults.removeObject(forKey: "keyboard_simplified_theme")
            appGroupDefaults.removeObject(forKey: "current_advanced_keyboard_theme")
        }
        
        // 应用默认主题
        appThemeManager.switchTheme(to: .warmOrange)
        keyboardThemeManager.setTheme(KeyboardTheme.defaultThemes[0])
        
        print("✅ 已重置为默认主题")
    }
    
    private func printThemeInfo() {
        print("\n🧡 当前主题信息:")
        print("📱 应用主题: \(appThemeManager.currentTheme.rawValue)")
        print("⌨️ 键盘主题: \(keyboardThemeManager.currentTheme.name)")
        print("📋 默认主题列表:")
        for (index, theme) in KeyboardTheme.defaultThemes.enumerated() {
            print("  \(index): \(theme.name) (\(theme.type.displayName))")
        }
    }
}

// MARK: - 测试按钮样式
struct TestButtonStyle: ButtonStyle {
    let color: Color
    
    func makeBody(configuration: Configuration) -> some View {
        configuration.label
            .foregroundColor(.white)
            .padding()
            .frame(maxWidth: .infinity)
            .background(color)
            .cornerRadius(12)
            .scaleEffect(configuration.isPressed ? 0.95 : 1.0)
            .animation(.easeInOut(duration: 0.1), value: configuration.isPressed)
    }
}

// MARK: - 预览
struct DefaultThemeTestView_Previews: PreviewProvider {
    static var previews: some View {
        DefaultThemeTestView()
            .environmentObject(ThemeManager.shared)
    }
}
