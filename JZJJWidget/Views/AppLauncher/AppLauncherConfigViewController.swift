import UIKit
import MyWidgetKit
import SwiftUI

/// 应用快捷启动器配置视图控制器
class AppLauncherConfigViewController: UIViewController {
    
    // MARK: - 属性
    
    private var hostingController: UIHostingController<AnyView>?
    
    // MARK: - 生命周期方法
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
    }
    
    // MARK: - UI 设置
    
    private func setupUI() {
        // 设置标题
        title = "快捷启动"
        
        // 创建 SwiftUI 视图
        let appLauncherConfigView = AppLauncherConfigView()
            .environmentObject(ThemeManager.shared)
        
        // 创建 UIHostingController
        let hostingController = UIHostingController(rootView: AnyView(appLauncherConfigView))
        
        // 添加为子控制器
        addChild(hostingController)
        view.addSubview(hostingController.view)
        hostingController.didMove(toParent: self)
        
        // 设置约束
        hostingController.view.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            hostingController.view.topAnchor.constraint(equalTo: view.topAnchor),
            hostingController.view.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            hostingController.view.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            hostingController.view.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
        
        // 保存引用
        self.hostingController = hostingController
        
        // 添加保存按钮
        let saveButton = UIBarButtonItem(
            image: UIImage(systemName: "checkmark"),
            style: .plain,
            target: self,
            action: #selector(saveButtonTapped)
        )
        navigationItem.rightBarButtonItem = saveButton
    }
    
    // MARK: - 操作方法
    
    @objc private func saveButtonTapped() {
        // 发送保存通知
        NotificationCenter.default.post(name: .saveAppLauncherConfig, object: nil)
    }
}
