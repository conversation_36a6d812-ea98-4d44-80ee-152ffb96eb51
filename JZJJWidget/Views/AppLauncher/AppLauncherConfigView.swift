import MyWidgetKit
import SwiftUI
import WidgetKit
import Combine

// MARK: - 通知名称常量
extension Notification.Name {
    static let saveAppLauncherConfig = Notification.Name("saveAppLauncherConfig")
}

// MARK: - Binding扩展，用于添加onChange回调
extension Binding {
    func onChange(_ handler: @escaping (Value) -> Void) -> Binding<Value> {
        Binding(
            get: { self.wrappedValue },
            set: { newValue in
                self.wrappedValue = newValue
                handler(newValue)
            }
        )
    }
}

// MARK: - Toast视图
struct ToastView: View {
    let message: String
    let isSuccess: Bool

    var body: some View {
        HStack(spacing: 12) {
            if isSuccess {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.white)
            }

            Text(message)
                .font(.subheadline)
                .foregroundColor(.white)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 10)
        .background(
            Capsule()
                .fill(isSuccess ? Color.green : Color.gray)
                .opacity(0.9)
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .transition(.move(edge: .top).combined(with: .opacity))
    }
}

// MARK: - AppLauncherConfigViewModel

class AppLauncherConfigViewModel: ObservableObject {
    // 小组件尺寸
    @Published var selectedFamily: WidgetFamily = .systemMedium

    // 应用项目
    @Published var items: [AppLauncherItem] = []

    // 图标样式
    @Published var iconStyle: AppIconStyle = .rounded

    // 布局类型
    @Published var layoutType: AppLauncherLayoutType = .grid

    // 背景设置
    @Published var background: WidgetBackground = .color(WidgetColor.fromColor(.white))

    // 字体设置
    @Published var fontName: String = "SF Pro"
    @Published var fontSize: Double = 12
    @Published var fontColor: Color = .black

    // 显示标签
    @Published var showLabels: Bool = true

    // 列数和行数
    @Published var columns: Int = 4
    @Published var rows: Int = 2

    // 间距
    @Published var spacing: Double = 10

    // 数据管理器
    private let dataManager = AppGroupDataManager.shared

    // 加载数据
    func loadData() {
        if let savedConfig = dataManager.read(AppLauncherWidgetData.self, for: .appLauncher, property: .config) {
            items = savedConfig.items
            iconStyle = savedConfig.iconStyle
            layoutType = savedConfig.layoutType
            background = savedConfig.background
            fontName = savedConfig.fontName
            fontSize = savedConfig.fontSize
            fontColor = savedConfig.fontColor
            showLabels = savedConfig.showLabels
            columns = savedConfig.columns
            rows = savedConfig.rows
            spacing = savedConfig.spacing
        }
    }

    // 保存配置
    func saveConfig() -> Bool {
        // 创建配置数据对象
        var config = AppLauncherWidgetData()

        // 设置各个属性
        config.items = items
        config.iconStyle = iconStyle
        config.layoutType = layoutType
        config.background = background
        config.fontName = fontName
        config.fontSize = fontSize
        config.fontColor = fontColor
        config.showLabels = showLabels
        config.columns = columns
        config.rows = rows
        config.spacing = spacing
        config.lastUpdated = Date()

        // 打印保存的配置，用于调试
        print("保存的配置: \(config)")
        print("保存的背景: \(background)")

        // 保存配置
        dataManager.save(config, for: .appLauncher, property: .config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        UINotificationFeedbackGenerator().notificationOccurred(.success)

        return true
    }

    // 删除项目
    func deleteItem(_ item: AppLauncherItem) {
        if let index = items.firstIndex(where: { $0.id == item.id }) {
            items.remove(at: index)

            // 更新剩余项目的位置
            for i in 0..<items.count {
                items[i].position = i
            }
        }
    }

    // 添加热门应用
    func addPopularApp(_ app: AppLauncherItem) {
        // 检查应用是否已存在
        if let existingIndex = items.firstIndex(where: { $0.urlScheme.lowercased() == app.urlScheme.lowercased() }) {
            // 应用已存在，更新现有配置
            items[existingIndex].name = app.name
            items[existingIndex].iconName = app.iconName
            items[existingIndex].color = app.color
            return
        }

        // 创建新应用并设置正确的位置
        var newApp = app
        newApp.position = items.count

        // 添加到列表
        items.append(newApp)
    }

    // 获取所有应用ID
    var allAppIds: [String] {
        return items.map { $0.id }
    }

    // 创建预览数据
    func createPreviewData() -> AppLauncherWidgetData {
        // 打印当前背景设置，用于调试
        print("创建预览数据时的背景设置: \(background)")

        // 创建预览数据
        let previewData = AppLauncherWidgetData(
            items: items,
            iconStyle: iconStyle,
            layoutType: layoutType,
            background: background,
            fontName: fontName,
            fontSize: fontSize,
            fontColor: fontColor,
            showLabels: showLabels,
            columns: columns,
            rows: rows,
            spacing: spacing
        )

        return previewData
    }
}

struct AppLauncherConfigView: View {
    // MARK: - 环境对象

    @EnvironmentObject private var themeManager: ThemeManager
    @Environment(\.dismiss) private var dismiss

    // MARK: - 视图模型

    @StateObject private var viewModel = AppLauncherConfigViewModel()

    // MARK: - 状态属性

    // 编辑模式
    @State private var isEditingItem: Bool = false
    @State private var editingItem: AppLauncherItem?

    // 添加新项目
    @State private var showAddItemSheet: Bool = false

    // Toast提示
    @State private var showToast: Bool = false
    @State private var toastMessage: String = ""

    // 背景更新计数器，用于强制刷新预览
    @State private var backgroundUpdateCounter: Int = 0

    // MARK: - 初始化

    init() {
        // 初始化时不需要做特殊处理，ViewModel会在onAppear时加载数据
    }

    // MARK: - 视图主体

    var body: some View {
        ZStack {
            // 主内容
            mainContentView
                .background(themeManager.currentTheme.colors.background)
                .navigationTitle("快捷启动")
                .navigationBarTitleDisplayMode(.inline)
                .onAppear {
                    viewModel.loadData()

                    // 添加通知监听器
                    NotificationCenter.default.addObserver(
                        forName: .saveAppLauncherConfig,
                        object: nil,
                        queue: .main
                    ) { [self] _ in
                        saveConfiguration()
                    }
                }
                .onDisappear {
                    // 移除通知监听器
                    NotificationCenter.default.removeObserver(self, name: .saveAppLauncherConfig, object: nil)
                }
                .sheet(isPresented: $showAddItemSheet) {
                    addItemSheet
                }
                .sheet(isPresented: $isEditingItem, onDismiss: {
                    editingItem = nil
                }) {
                    if let item = editingItem {
                        editItemSheet(item: item)
                    }
                }

            // Toast提示
            VStack {
                if showToast {
                    ToastView(message: toastMessage, isSuccess: true)
                        .padding(.top, 60) // 确保Toast不会被导航栏遮挡
                }
                Spacer()
            }
            .animation(.easeInOut(duration: 0.3), value: showToast)
            .zIndex(1) // 确保Toast显示在最上层
        }
    }

    // 主内容视图
    private var mainContentView: some View {
        VStack(spacing: 0) {
            // 预览区域
            previewSection
                .padding(.top)

            // 配置选项
            configurationScrollView
        }
    }

    // 配置滚动视图
    private var configurationScrollView: some View {
        ScrollView {
            VStack(spacing: AppLayout.Spacing.large) {
                // 应用项目管理
                itemsSection

                // 布局设置
                layoutSection

                // 外观设置
                appearanceSection
            }
            .padding()
        }
    }

    // 保存配置方法
    private func saveConfiguration() {
        // 打印当前背景设置，用于调试
        print("保存前的背景设置: \(viewModel.background)")

        if viewModel.saveConfig() {
            // 确保刷新小组件
            WidgetCenter.shared.reloadAllTimelines()

            // 触觉反馈
            UINotificationFeedbackGenerator().notificationOccurred(.success)

            // 显示Toast提示
            toastMessage = "设置已保存"
            showToast = true

            // 1.5秒后自动隐藏Toast
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                showToast = false

                // Toast消失后关闭视图
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    dismiss()
                }
            }
        }
    }

    // 添加按钮
    private var addButton: some View {
        Button(action: {
            showAddItemSheet = true
        }) {
            Image(systemName: "plus")
                .foregroundColor(themeManager.currentTheme.colors.accent)
        }
    }

    // MARK: - 预览区域

    private var previewSection: some View {
        VStack(spacing: 8) {
            previewSectionHeader
            previewContainer
            sizeSelector
        }
    }

    // 预览区域标题
    private var previewSectionHeader: some View {
        Text("预览")
            .font(themeManager.currentTheme.fonts.headlineMedium)
            .foregroundColor(themeManager.currentTheme.colors.text)
            .frame(maxWidth: .infinity, alignment: .leading)
            .padding(.horizontal)
    }

    // 预览容器
    private var previewContainer: some View {
        ZStack {
            RoundedRectangle(cornerRadius: 20)
                .fill(themeManager.currentTheme.colors.surface)
                .shadow(color: themeManager.currentTheme.colors.shadow.opacity(0.1), radius: 5, x: 0, y: 2)

            // 预览内容
            widgetPreview
        }
        .frame(height: getPreviewHeight() + 20)
        .padding(.horizontal)
    }

    // 小组件预览
    private var widgetPreview: some View {
        let previewData = viewModel.createPreviewData()

        // 打印预览数据，用于调试
        print("预览数据背景: \(previewData.background)")

        return AppLauncherWidgetView(
            data: previewData,
            family: viewModel.selectedFamily
        )
        .frame(width: getPreviewWidth(), height: getPreviewHeight())
        .clipShape(RoundedRectangle(cornerRadius: 15))
        // 使用backgroundUpdateCounter作为id，当背景变化时强制刷新视图
        .id(backgroundUpdateCounter)
    }

    // 尺寸选择器
    private var sizeSelector: some View {
        HStack(spacing: 20) {
            ForEach([WidgetFamily.systemSmall, .systemMedium, .systemLarge], id: \.self) { family in
                sizeSelectorButton(for: family)
            }
        }
        .padding(.bottom, 8)
    }

    // 尺寸选择按钮
    private func sizeSelectorButton(for family: WidgetFamily) -> some View {
        Button(action: {
            viewModel.selectedFamily = family
        }) {
            let isSelected = viewModel.selectedFamily == family
            let textColor = isSelected ? themeManager.currentTheme.colors.accent : themeManager.currentTheme.colors.subtext
            let bgColor = isSelected ? themeManager.currentTheme.colors.accent.opacity(0.1) : Color.clear

            VStack {
                Image(systemName: sizeIcon(for: family))
                    .font(.system(size: 20))
                    .foregroundColor(textColor)

                Text(sizeName(for: family))
                    .font(themeManager.currentTheme.fonts.captionMedium)
                    .foregroundColor(textColor)
            }
            .padding(.vertical, 8)
            .padding(.horizontal, 12)
            .background(
                RoundedRectangle(cornerRadius: 8)
                    .fill(bgColor)
            )
        }
    }

    // MARK: - 应用项目管理区域

    private var itemsSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "应用项目管理",
            iconName: "apps.iphone"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                // 热门应用快捷添加区域
                popularAppsSection

                // 分隔线
                Divider()
                    .padding(.vertical, 8)

                // 项目列表
                if viewModel.items.isEmpty {
                    emptyItemsView
                } else {
                    ForEach(viewModel.items) { item in
                        itemRow(item: item)
                    }
                }

                // 添加按钮
                Button(action: {
                    showAddItemSheet = true
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(themeManager.currentTheme.colors.accent)

                        Text("添加应用")
                            .foregroundColor(themeManager.currentTheme.colors.accent)
                    }
                    .padding()
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(themeManager.currentTheme.colors.surface)
                    .cornerRadius(AppLayout.CornerRadius.medium)
                }
            }
        }
    }

    // 热门应用快捷添加区域
    private var popularAppsSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            PopularAppsView(
                onAppSelected: { app in
                    // 添加热门应用
                    viewModel.addPopularApp(app)

                    // 强制刷新预览
                    viewModel.objectWillChange.send()

                    // 触觉反馈
                    UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                },
                existingAppIds: viewModel.allAppIds
            )
            .environmentObject(themeManager)
        }
        .padding(.bottom, 4)
    }

    // 空列表视图
    private var emptyItemsView: some View {
        VStack(spacing: 16) {
            Image(systemName: "square.grid.2x2")
                .font(.system(size: 40))
                .foregroundColor(themeManager.currentTheme.colors.subtext)

            Text("暂无应用")
                .font(themeManager.currentTheme.fonts.bodyMedium)
                .foregroundColor(themeManager.currentTheme.colors.subtext)

            Text("从上方热门应用中选择，或点击下方按钮添加")
                .font(themeManager.currentTheme.fonts.captionMedium)
                .foregroundColor(themeManager.currentTheme.colors.subtext)
                .multilineTextAlignment(.center)
                .padding(.horizontal)
        }
        .frame(maxWidth: .infinity)
        .padding(.vertical, 30)
    }

    // 项目行视图
    private func itemRow(item: AppLauncherItem) -> some View {
        HStack(spacing: 12) {
            // 图标
            ZStack {
                Circle()
                    .fill(item.color)
                    .frame(width: 36, height: 36)

                Image(systemName: item.iconName)
                    .font(.system(size: 18))
                    .foregroundColor(.white)
            }

            // 名称
            Text(item.name)
                .font(themeManager.currentTheme.fonts.bodyMedium)
                .foregroundColor(themeManager.currentTheme.colors.text)

            Spacer()

            // 编辑按钮
            Button(action: {
                editingItem = item
                isEditingItem = true
            }) {
                Image(systemName: "pencil")
                    .foregroundColor(themeManager.currentTheme.colors.accent)
            }
            .padding(.horizontal, 8)

            // 删除按钮
            Button(action: {
                deleteItem(item)
            }) {
                Image(systemName: "trash")
                    .foregroundColor(themeManager.currentTheme.colors.error)
            }
        }
        .padding()
        .background(themeManager.currentTheme.colors.surface)
        .cornerRadius(AppLayout.CornerRadius.medium)
    }

    // MARK: - 布局设置区域

    private var layoutSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "布局设置",
            iconName: "square.grid.2x2"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                // 图标样式选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("图标样式")
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)

                    Picker("图标样式", selection: $viewModel.iconStyle.onChange { _ in
                        // 强制刷新预览
                        viewModel.objectWillChange.send()
                    }) {
                        ForEach(AppIconStyle.allCases, id: \.self) { style in
                            Text(style.name).tag(style)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }

                // 布局类型选择
                VStack(alignment: .leading, spacing: 8) {
                    Text("布局类型")
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)

                    Picker("布局类型", selection: $viewModel.layoutType.onChange { _ in
                        // 强制刷新预览
                        viewModel.objectWillChange.send()
                    }) {
                        ForEach(AppLauncherLayoutType.allCases, id: \.self) { type in
                            Text(type.name).tag(type)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }

                // 显示标签开关
                Toggle(isOn: $viewModel.showLabels.onChange { _ in
                    // 强制刷新预览
                    viewModel.objectWillChange.send()
                }) {
                    Text("显示应用名称")
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)
                }
                .toggleStyle(SwitchToggleStyle(tint: themeManager.currentTheme.colors.accent))

                // 网格设置（仅在网格布局时显示）
                if viewModel.layoutType == .grid {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("网格设置")
                            .font(themeManager.currentTheme.fonts.bodyMedium)
                            .foregroundColor(themeManager.currentTheme.colors.text)

                        HStack {
                            Text("列数: \(viewModel.columns)")
                                .font(themeManager.currentTheme.fonts.bodyMedium)
                                .foregroundColor(themeManager.currentTheme.colors.text)

                            Slider(value: Binding(
                                get: { Double(viewModel.columns) },
                                set: { newValue in
                                    viewModel.columns = Int(newValue)
                                    // 强制刷新预览
                                    viewModel.objectWillChange.send()
                                }
                            ), in: 2...6, step: 1)
                            .accentColor(themeManager.currentTheme.colors.accent)
                        }

                        HStack {
                            Text("行数: \(viewModel.rows)")
                                .font(themeManager.currentTheme.fonts.bodyMedium)
                                .foregroundColor(themeManager.currentTheme.colors.text)

                            Slider(value: Binding(
                                get: { Double(viewModel.rows) },
                                set: { newValue in
                                    viewModel.rows = Int(newValue)
                                    // 强制刷新预览
                                    viewModel.objectWillChange.send()
                                }
                            ), in: 1...4, step: 1)
                            .accentColor(themeManager.currentTheme.colors.accent)
                        }
                    }
                }

                // 间距设置
                HStack {
                    Text("图标间距: \(Int(viewModel.spacing))")
                        .font(themeManager.currentTheme.fonts.bodyMedium)
                        .foregroundColor(themeManager.currentTheme.colors.text)

                    Slider(value: Binding(
                        get: { viewModel.spacing },
                        set: { newValue in
                            viewModel.spacing = newValue
                            // 强制刷新预览
                            viewModel.objectWillChange.send()
                        }
                    ), in: 4...20, step: 1)
                        .accentColor(themeManager.currentTheme.colors.accent)
                }
            }
        }
    }

    // MARK: - 外观设置区域

    private var appearanceSection: some View {
        ConfigSectionContainer(
            theme: themeManager.currentTheme,
            title: "外观设置",
            iconName: "paintbrush"
        ) {
            VStack(alignment: .leading, spacing: AppLayout.Spacing.medium) {
                // 背景设置
                BackgroundSelectView(
                    allowColor: true,
                    allowImage: true,
                    allowPackageImage: true,
                    initialColor: viewModel.background.colorValue?.toColor() ?? .white,
                    initialImage: viewModel.background.imageValue,
                    initialPackageImageName: getPackageImageName(from: viewModel.background),
                    onSelection: { selection in
                        // 更新背景设置
                        viewModel.background = selection.toWidgetBackground()

                        // 打印日志以便调试
                        print("背景已更新: \(selection)")

                        // 增加背景更新计数器，强制刷新预览
                        backgroundUpdateCounter += 1

                        // 强制刷新预览
                        viewModel.objectWillChange.send()
                    }
                )

                // 字体设置
                FontSelectView(
                    showFontPicker: true,
                    showFontSizePicker: true,
                    showFontColorPicker: true,
                    onSelectionChanged: { selection in
                        viewModel.fontName = selection.fontName
                        viewModel.fontSize = selection.fontSize
                        viewModel.fontColor = selection.fontColor
                        // 强制刷新预览
                        viewModel.objectWillChange.send()
                    },
                    // 使用默认索引值，FontSelectView内部会处理
                    initialFontIndex: 0, // 默认使用第一个字体
                    initialFontSizeIndex: getFontSizeIndex(for: viewModel.fontSize), // 根据当前字体大小找到最接近的索引
                    initialFontColorIndex: getFontColorIndex(for: viewModel.fontColor), // 根据当前颜色找到最接近的索引
                    initialCustomColor: viewModel.fontColor // 如果是自定义颜色，则使用当前颜色
                )
            }
        }
    }
    // MARK: - 添加项目表单

    private var addItemSheet: some View {
        NavigationView {
            Form {
                Section(header: Text("应用信息")) {
                    TextField("应用名称", text: Binding(
                        get: {
                            return editingItem?.name ?? ""
                        },
                        set: { newValue in
                            if editingItem != nil {
                                editingItem!.name = newValue
                            } else {
                                editingItem = AppLauncherItem(
                                    name: newValue,
                                    iconName: "app",
                                    urlScheme: "",
                                    color: .blue,
                                    position: viewModel.items.count
                                )
                            }
                        }
                    ))

                    TextField("URL Scheme", text: Binding(
                        get: {
                            return editingItem?.urlScheme ?? ""
                        },
                        set: { newValue in
                            if editingItem != nil {
                                editingItem!.urlScheme = newValue
                            }
                        }
                    ))
                    .autocapitalization(.none)
                    .keyboardType(.URL)

                    // 图标选择器
                    NavigationLink(destination: IconPickerView(selectedIcon: Binding(
                        get: {
                            return editingItem?.iconName ?? "app"
                        },
                        set: { newValue in
                            if editingItem != nil {
                                editingItem!.iconName = newValue
                            }
                        }
                    ))) {
                        HStack {
                            Text("图标")
                            Spacer()
                            if let iconName = editingItem?.iconName {
                                Image(systemName: iconName)
                                    .foregroundColor(themeManager.currentTheme.colors.accent)
                            }
                        }
                    }

                    // 颜色选择器
                    ColorPicker("图标颜色", selection: Binding(
                        get: {
                            return editingItem?.color ?? .blue
                        },
                        set: { newValue in
                            if editingItem != nil {
                                editingItem!.color = newValue
                            }
                        }
                    ))
                }

                Section {
                    Button("添加") {
                        if var item = editingItem, !item.name.isEmpty {
                            item.position = viewModel.items.count
                            viewModel.items.append(item)
                            editingItem = nil
                            showAddItemSheet = false
                        }
                    }
                    .disabled(editingItem == nil || editingItem!.name.isEmpty)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .foregroundColor(themeManager.currentTheme.colors.accent)
                }
            }
            .navigationTitle("添加应用")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        editingItem = nil
                        showAddItemSheet = false
                    }
                }
            }
            .onAppear {
                editingItem = AppLauncherItem(name: "", iconName: "app", urlScheme: "", color: .blue, position: viewModel.items.count)
            }
            .onDisappear {
                if !showAddItemSheet {
                    editingItem = nil
                }
            }
        }
    }

    // 编辑项目表单
    private func editItemSheet(item: AppLauncherItem) -> some View {
        NavigationView {
            Form {
                Section(header: Text("应用信息")) {
                    TextField("应用名称", text: Binding(
                        get: {
                            return editingItem?.name ?? item.name
                        },
                        set: { newValue in
                            if editingItem != nil {
                                editingItem!.name = newValue
                            }
                        }
                    ))

                    TextField("URL Scheme", text: Binding(
                        get: {
                            return editingItem?.urlScheme ?? item.urlScheme
                        },
                        set: { newValue in
                            if editingItem != nil {
                                editingItem!.urlScheme = newValue
                            }
                        }
                    ))
                    .autocapitalization(.none)
                    .keyboardType(.URL)

                    // 图标选择器
                    NavigationLink(destination: IconPickerView(selectedIcon: Binding(
                        get: {
                            return editingItem?.iconName ?? item.iconName
                        },
                        set: { newValue in
                            if editingItem != nil {
                                editingItem!.iconName = newValue
                            }
                        }
                    ))) {
                        HStack {
                            Text("图标")
                            Spacer()
                            let iconName = editingItem?.iconName ?? item.iconName
                            Image(systemName: iconName)
                                .foregroundColor(themeManager.currentTheme.colors.accent)
                        }
                    }

                    // 颜色选择器
                    ColorPicker("图标颜色", selection: Binding(
                        get: {
                            return editingItem?.color ?? item.color
                        },
                        set: { newValue in
                            if editingItem != nil {
                                editingItem!.color = newValue
                            }
                        }
                    ))
                }

                Section {
                    Button("保存") {
                        if let updatedItem = editingItem, !updatedItem.name.isEmpty {
                            if let index = viewModel.items.firstIndex(where: { $0.id == item.id }) {
                                viewModel.items[index] = updatedItem
                            }
                            editingItem = nil
                            isEditingItem = false
                        }
                    }
                    .disabled(editingItem == nil || editingItem!.name.isEmpty)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .foregroundColor(themeManager.currentTheme.colors.accent)
                }
            }
            .navigationTitle("编辑应用")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        editingItem = nil
                        isEditingItem = false
                    }
                }
            }
            .onAppear {
                editingItem = item
            }
        }
    }

    // MARK: - 图标选择器视图

    struct IconPickerView: View {
        @Binding var selectedIcon: String
        @Environment(\.presentationMode) private var presentationMode
        @EnvironmentObject private var themeManager: ThemeManager

        // 常用图标分类
        private let iconCategories: [(String, [String])] = [
            ("常用", ["app", "app.fill", "apps.iphone", "square.grid.2x2", "square.grid.3x2", "square.grid.4x3.fill"]),
            ("通讯", ["message", "message.fill", "phone", "phone.fill", "envelope", "envelope.fill", "bubble.left", "bubble.left.fill"]),
            ("社交", ["person", "person.fill", "person.2", "person.2.fill", "heart", "heart.fill", "hand.thumbsup", "hand.thumbsup.fill"]),
            ("媒体", ["play", "play.fill", "music.note", "music.note.list", "photo", "photo.fill", "video", "video.fill"]),
            ("工具", ["gear", "gear.circle", "wrench", "wrench.fill", "hammer", "hammer.fill", "doc", "doc.fill"]),
            ("购物", ["cart", "cart.fill", "bag", "bag.fill", "creditcard", "creditcard.fill", "gift", "gift.fill"]),
            ("食物", ["cup.and.saucer", "fork.knife", "takeoutbag.and.cup.and.straw", "wineglass", "birthday.cake"]),
            ("交通", ["car", "car.fill", "bus", "bus.fill", "airplane", "airplane.circle.fill", "tram.fill"]),
            ("天气", ["sun.max", "sun.max.fill", "cloud", "cloud.fill", "cloud.rain", "cloud.rain.fill"]),
            ("其他", ["star", "star.fill", "bell", "bell.fill", "bookmark", "bookmark.fill", "tag", "tag.fill"])
        ]

        @State private var searchText: String = ""
        @State private var selectedCategory: Int = 0

        var body: some View {
            VStack {
                // 搜索栏
                SearchBar(text: $searchText, placeholder: "搜索图标")
                    .padding(.horizontal)

                // 分类选择器
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 12) {
                        ForEach(0..<iconCategories.count, id: \.self) { index in
                            Button(action: {
                                selectedCategory = index
                            }) {
                                let isSelected = selectedCategory == index
                                let backgroundColor = isSelected ? themeManager.currentTheme.colors.accent : themeManager.currentTheme.colors.surface
                                let textColor = isSelected ? Color.white : themeManager.currentTheme.colors.text

                                Text(iconCategories[index].0)
                                    .font(themeManager.currentTheme.fonts.bodyMedium)
                                    .padding(.vertical, 8)
                                    .padding(.horizontal, 12)
                                    .background(
                                        RoundedRectangle(cornerRadius: 16)
                                            .fill(backgroundColor)
                                    )
                                    .foregroundColor(textColor)
                            }
                        }
                    }
                    .padding(.horizontal)
                }
                .padding(.vertical, 8)

                // 图标网格
                ScrollView {
                    LazyVGrid(columns: [GridItem(.adaptive(minimum: 60))], spacing: 20) {
                        ForEach(filteredIcons, id: \.self) { iconName in
                            Button(action: {
                                selectedIcon = iconName
                                presentationMode.wrappedValue.dismiss()
                            }) {
                                let isSelected = selectedIcon == iconName
                                let circleColor = isSelected ? themeManager.currentTheme.colors.accent : themeManager.currentTheme.colors.surface
                                let iconColor = isSelected ? Color.white : themeManager.currentTheme.colors.text

                                VStack {
                                    ZStack {
                                        Circle()
                                            .fill(circleColor)
                                            .frame(width: 50, height: 50)

                                        Image(systemName: iconName)
                                            .font(.system(size: 24))
                                            .foregroundColor(iconColor)
                                    }

                                    Text(iconName)
                                        .font(themeManager.currentTheme.fonts.captionMedium)
                                        .foregroundColor(themeManager.currentTheme.colors.subtext)
                                        .lineLimit(1)
                                        .truncationMode(.middle)
                                        .frame(width: 80)
                                }
                            }
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle("选择图标")
            .navigationBarTitleDisplayMode(.inline)
        }

        // 搜索过滤图标
        private var filteredIcons: [String] {
            let categoryIcons = iconCategories[selectedCategory].1

            if searchText.isEmpty {
                return categoryIcons
            } else {
                return categoryIcons.filter { $0.localizedCaseInsensitiveContains(searchText) }
            }
        }
    }

    // MARK: - 搜索栏

    struct SearchBar: View {
        @Binding var text: String
        var placeholder: String

        var body: some View {
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.gray)

                TextField(placeholder, text: $text)
                    .foregroundColor(.primary)

                if !text.isEmpty {
                    Button(action: {
                        text = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                    }
                }
            }
            .padding(8)
            .background(Color(.systemGray6))
            .cornerRadius(10)
        }
    }

    // MARK: - 辅助方法

    // 删除项目
    private func deleteItem(_ item: AppLauncherItem) {
        viewModel.deleteItem(item)
    }



    // 获取尺寸图标
    func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        default:
            return "square"
        }
    }

    // 获取尺寸名称
    func sizeName(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        default:
            return "小尺寸"
        }
    }

    // 获取预览宽度
    private func getPreviewWidth() -> CGFloat {
        switch viewModel.selectedFamily {
        case .systemSmall:
            return 150
        case .systemMedium:
            return 320
        case .systemLarge:
            return 320
        default:
            return 150
        }
    }

    // 获取预览高度
    private func getPreviewHeight() -> CGFloat {
        switch viewModel.selectedFamily {
        case .systemSmall:
            return 150
        case .systemMedium:
            return 150
        case .systemLarge:
            return 320
        default:
            return 150
        }
    }

    // 从WidgetBackground中获取包图片名称
    private func getPackageImageName(from background: WidgetBackground) -> String? {
        if case let .packageImage(name) = background {
            return name
        }
        return nil
    }

    // 根据字体大小获取最接近的索引
    private func getFontSizeIndex(for fontSize: Double) -> Int {
        // FontSelectView中定义的字体大小数组
        let fontSizes: [CGFloat] = [12, 14, 16, 18, 20, 24, 28, 32]

        // 找到最接近的字体大小
        var closestIndex = 0
        var minDifference = abs(fontSizes[0] - fontSize)

        for (index, size) in fontSizes.enumerated() {
            let difference = abs(size - fontSize)
            if difference < minDifference {
                minDifference = difference
                closestIndex = index
            }
        }

        return closestIndex
    }

    // 根据颜色获取最接近的索引
    private func getFontColorIndex(for color: Color) -> Int {
        // FontSelectView中定义的颜色数组
        let fontColors: [Color] = [.white, .black, .gray, .red, .blue, .green, .orange, .purple, .brown]

        // 将Color转换为UIColor以便比较
        let uiColor = UIColor(color)
        var red1: CGFloat = 0, green1: CGFloat = 0, blue1: CGFloat = 0, alpha1: CGFloat = 0
        uiColor.getRed(&red1, green: &green1, blue: &blue1, alpha: &alpha1)

        // 找到最接近的颜色
        var closestIndex = -1 // 默认为自定义颜色
        var minDifference = CGFloat.greatestFiniteMagnitude

        for (index, fontColor) in fontColors.enumerated() {
            let uiFontColor = UIColor(fontColor)
            var red2: CGFloat = 0, green2: CGFloat = 0, blue2: CGFloat = 0, alpha2: CGFloat = 0
            uiFontColor.getRed(&red2, green: &green2, blue: &blue2, alpha: &alpha2)

            // 计算颜色差异（简单的欧几里得距离）
            let difference = sqrt(pow(red1 - red2, 2) + pow(green1 - green2, 2) + pow(blue1 - blue2, 2))

            if difference < minDifference {
                minDifference = difference
                closestIndex = index
            }
        }

        // 如果差异太大，认为是自定义颜色
        return minDifference < 0.1 ? closestIndex : -1
    }
}
