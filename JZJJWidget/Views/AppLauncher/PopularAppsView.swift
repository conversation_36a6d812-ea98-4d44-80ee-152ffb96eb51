import SwiftUI
import MyWidgetKit

/// 热门应用快捷添加视图
struct PopularAppsView: View {
    // MARK: - 属性

    @EnvironmentObject private var themeManager: ThemeManager

    // 热门应用列表
    private let popularApps: [PopularApp] = [
        PopularApp(name: "微信", iconName: "message.fill", urlScheme: "wechat://", color: .green),
        PopularApp(name: "支付宝", iconName: "creditcard.fill", urlScheme: "alipay://", color: .blue),
        PopularApp(name: "淘宝", iconName: "cart.fill", urlScheme: "taobao://", color: .orange),
        PopularApp(name: "QQ", iconName: "bubble.left.fill", urlScheme: "mqq://", color: Color(red: 0.0, green: 0.6, blue: 0.9)),
        PopularApp(name: "微博", iconName: "flame.fill", urlScheme: "sinaweibo://", color: .red),
        PopularApp(name: "京东", iconName: "bag.fill", urlScheme: "openapp.jdmobile://", color: .red),
        PopularApp(name: "抖音", iconName: "music.note", urlScheme: "snssdk1128://", color: .pink),
        PopularApp(name: "美团", iconName: "fork.knife", urlScheme: "meituanwaimai://", color: .yellow),
        PopularApp(name: "知乎", iconName: "questionmark.circle.fill", urlScheme: "zhihu://", color: Color(red: 0.1, green: 0.5, blue: 0.9)),
        PopularApp(name: "哔哩哔哩", iconName: "play.tv.fill", urlScheme: "bilibili://", color: Color(red: 0.9, green: 0.5, blue: 0.7))
    ]

    // 回调函数
    var onAppSelected: (AppLauncherItem) -> Void

    // 已添加的应用ID列表，用于检查应用是否已存在
    var existingAppIds: [String]

    // 状态
    @State private var showToast: Bool = false
    @State private var toastMessage: String = ""
    @State private var selectedAppName: String = ""

    // MARK: - 视图主体

    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("热门应用")
                .font(themeManager.currentTheme.fonts.headlineSmall)
                .foregroundColor(themeManager.currentTheme.colors.text)
                .padding(.horizontal, 16)

            // 应用网格
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 12) {
                    ForEach(popularApps, id: \.urlScheme) { app in
                        popularAppButton(for: app)
                    }
                }
                .padding(.horizontal, 16)
            }
        }
        .overlay(
            // Toast提示
            VStack {
                if showToast {
                    ToastView(message: toastMessage, isSuccess: true)
                        .transition(.move(edge: .top).combined(with: .opacity))
                        .zIndex(1)
                }
                Spacer()
            }
            .animation(.easeInOut(duration: 0.3), value: showToast)
        )
    }

    // MARK: - 热门应用按钮

    private func popularAppButton(for app: PopularApp) -> some View {
        // 检查应用是否已存在
        let isExisting = isAppExisting(app)

        return Button(action: {
            addApp(app)
        }) {
            VStack(spacing: 8) {
                // 图标
                ZStack {
                    // 背景
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [app.color, app.color.opacity(0.7)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 50, height: 50)
                        .shadow(color: app.color.opacity(0.3), radius: 3, x: 0, y: 2)

                    // 图标
                    Image(systemName: app.iconName)
                        .font(.system(size: 24))
                        .foregroundColor(.white)

                    // 已添加标记
                    if isExisting {
                        Circle()
                            .fill(Color.black.opacity(0.7))
                            .frame(width: 50, height: 50)

                        Image(systemName: "checkmark")
                            .font(.system(size: 20, weight: .bold))
                            .foregroundColor(.white)
                    }
                }

                // 名称
                Text(app.name)
                    .font(.system(size: 12))
                    .foregroundColor(themeManager.currentTheme.colors.text)
                    .lineLimit(1)
            }
            .frame(width: 60)
            .opacity(isExisting ? 0.7 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
    }

    // MARK: - 辅助方法

    /// 检查应用是否已存在
    private func isAppExisting(_ app: PopularApp) -> Bool {
        // 从AppGroupDataManager获取所有应用
        if let config = AppGroupDataManager.shared.read(AppLauncherWidgetData.self, for: .appLauncher, property: .config) {
            // 检查是否有匹配的URL Scheme
            return config.items.contains { item in
                return item.urlScheme.lowercased() == app.urlScheme.lowercased()
            }
        }

        return false
    }

    /// 添加应用
    private func addApp(_ app: PopularApp) {
        // 检查应用是否已存在
        if isAppExisting(app) {
            // 显示已存在提示
            toastMessage = "\(app.name) 已添加"
            selectedAppName = app.name
            showToast = true

            // 触觉反馈
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()

            // 1.5秒后隐藏提示
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
                if selectedAppName == app.name {
                    showToast = false
                }
            }

            return
        }

        // 创建新应用
        let newApp = AppLauncherItem(
            name: app.name,
            iconName: app.iconName,
            urlScheme: app.urlScheme,
            color: app.color,
            position: 0 // 位置将在添加时更新
        )

        // 调用回调函数
        onAppSelected(newApp)

        // 显示成功提示
        toastMessage = "已添加 \(app.name)"
        selectedAppName = app.name
        showToast = true

        // 触觉反馈
        UIImpactFeedbackGenerator(style: .medium).impactOccurred()

        // 1.5秒后隐藏提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            if selectedAppName == app.name {
                showToast = false
            }
        }
    }
}

/// 热门应用数据模型
struct PopularApp: Identifiable {
    var id: String { urlScheme }
    let name: String
    let iconName: String
    let urlScheme: String
    let color: Color
}
