import MyWidgetKit
import SwiftUI
import WidgetKit

struct TodoWidgetConfigView: View {
    @EnvironmentObject var theme: ThemeManager

    // 分类选择
    @State private var selectedCategory: TaskCategory?

    // 显示选项
    @State private var showCompletedTasks: Bool = true // 默认显示已完成任务
    @State private var maxTasksToShow: Int = 5
    @State private var selectedPriorityFilter: PriorityFilter = .all

    // 外观设置
    @State private var background: BackgroundSelection = .color(.white)
    @State private var fontSelection = FontSelection(font: .system(size: 15), fontSize: 15, fontColor: .black)

    // 预览设置
    @State private var previewSize: WidgetFamily = .systemMedium
    @State private var previewView: UniversalWidgetPreviewView<TodoWidgetData>?

    // 成功提示
    @State private var showSuccessToast: Bool = false

    // 添加分类弹窗
    @State private var showAddCategorySheet: Bool = false
    @State private var newCategoryName: String = ""
    @State private var newCategoryColor: Color = .blue
    @State private var newCategoryIcon: String = "list.bullet"

    // 添加任务弹窗
    @State private var showAddTaskSheet: Bool = false
    @State private var newTaskTitle: String = ""
    @State private var newTaskCategoryId: UUID
    @State private var newTaskPriority: Int = 1 // 默认中优先级
    @State private var newTaskDueDate: Date = Date().addingTimeInterval(86400) // 默认明天
    @State private var newTaskNotes: String = ""
    @State private var newTaskHasDueDate: Bool = false

    // 初始化方法
    init() {
        // 获取默认分类的ID
        let categories = TaskCategory.defaultCategories
        let defaultCategoryId = categories.first?.id ?? UUID()
        self._newTaskCategoryId = State(initialValue: defaultCategoryId)
    }

    var body: some View {
        Form {
            categorySection

            // 任务管理部分
            Section(header: Text("任务管理")) {
                Button(action: {
                    showAddTaskSheet = true
                }) {
                    HStack {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(theme.colors.accent)
                        Text("添加新任务")
                            .foregroundColor(theme.colors.accent)
                    }
                }
            }

            displayOptionsSection
            appearanceSection
            previewSection
        }
        .navigationTitle("任务清单小组件")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .principal) {
                previewView?.navBarSizeSelectorView
            }
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    saveWidget()
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.colors.accent)
                }
            }
        }
        .onAppear {
            setupPreviewView()
            loadExistingData()
        }
        .overlay(
            // 成功提示
            VStack {
                Spacer()
                if showSuccessToast {
                    Text("小组件已保存")
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color.black.opacity(0.7))
                        )
                        .foregroundColor(.white)
                        .padding(.bottom, 20)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
        )
        .sheet(isPresented: $showAddCategorySheet) {
            addCategoryView
        }
        .sheet(isPresented: $showAddTaskSheet) {
            addTaskView
        }
    }

    // MARK: - 分类选择部分

    private var categorySection: some View {
        Section(header: Text("任务分类")) {
            List {
                Button(action: {
                    selectedCategory = nil
                    updatePreview()
                }) {
                    HStack {
                        Image(systemName: "tray")
                            .foregroundColor(.gray)

                        Text("所有任务")
                            .font(theme.fonts.bodyMedium)

                        Spacer()

                        if selectedCategory == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(theme.colors.accent)
                        }
                    }
                }

                ForEach(loadCategories()) { category in
                    Button(action: {
                        selectedCategory = category
                        updatePreview()
                    }) {
                        HStack {
                            Image(systemName: category.icon)
                                .foregroundColor(category.color)

                            Text(category.name)
                                .font(theme.fonts.bodyMedium)

                            Spacer()

                            if selectedCategory?.id == category.id {
                                Image(systemName: "checkmark")
                                    .foregroundColor(theme.colors.accent)
                            }
                        }
                    }
                }

                Button(action: {
                    showAddCategorySheet = true
                }) {
                    Label("添加分类", systemImage: "plus.circle")
                        .foregroundColor(theme.colors.accent)
                }
            }
        }
    }

    // MARK: - 显示选项部分

    private var displayOptionsSection: some View {
        Section(header: Text("显示选项")) {
            Toggle("显示已完成任务", isOn: $showCompletedTasks)
                .onChange(of: showCompletedTasks) { _ in updatePreview() }

            VStack(alignment: .leading, spacing: 8) {
                Text("最多显示任务数")
                    .font(theme.fonts.bodyMedium)

                Picker("最多显示任务数", selection: $maxTasksToShow) {
                    Text("3个").tag(3)
                    Text("5个").tag(5)
                    Text("7个").tag(7)
                    Text("10个").tag(10)
                }
                .pickerStyle(SegmentedPickerStyle())
                .onChange(of: maxTasksToShow) { _ in updatePreview() }
            }

            VStack(alignment: .leading, spacing: 8) {
                Text("优先级筛选")
                    .font(theme.fonts.bodyMedium)

                Picker("优先级筛选", selection: $selectedPriorityFilter) {
                    ForEach(PriorityFilter.allCases, id: \.self) { filter in
                        Text(filter.displayName).tag(filter)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .onChange(of: selectedPriorityFilter) { _ in updatePreview() }
            }
        }
    }

    // MARK: - 外观设置部分

    private var appearanceSection: some View {
        Section(header: Text("外观设置")) {
            // 背景选择
            BackgroundSelectView(
                allowColor: true,
                allowImage: true,
                colors: theme.colorOptions,
                colorNames: theme.colorNames,
                initialColor: background.colorValue ?? .white,
                initialImage: background.imageValue,
                onSelection: { selection in
                    background = selection
                    updatePreview()
                }
            )

            // 字体设置
            FontSelectView(
                showFontPicker: true,
                showFontSizePicker: true,
                showFontColorPicker: true,
                onSelectionChanged: { newSelection in
                    fontSelection = newSelection
                    updatePreview()
                }
            )
        }
    }

    // MARK: - 预览部分

    private var previewSection: some View {
        Section {
            VStack {
                if let previewView = previewView {
                    previewView.compactPreviewAreaView
                        .frame(height: previewView.compactPreviewHeight)
                } else {
                    Text("加载预览...")
                        .frame(height: 200)
                }
            }
            .padding(.vertical, 8)
        }
        .listRowInsets(EdgeInsets())
        .background(Color.clear)
    }

    // MARK: - 添加任务视图

    private var addTaskView: some View {
        NavigationView {
            Form {
                // 任务标题
                Section(header: Text("任务信息")) {
                    TextField("任务标题", text: $newTaskTitle)

                    // 分类选择
                    Picker("分类", selection: $newTaskCategoryId) {
                        ForEach(loadCategories()) { category in
                            HStack {
                                Image(systemName: category.icon)
                                    .foregroundColor(category.color)
                                Text(category.name)
                            }
                            .tag(category.id)
                        }
                    }
                    .pickerStyle(MenuPickerStyle())

                    // 优先级选择
                    Picker("优先级", selection: $newTaskPriority) {
                        HStack {
                            Image(systemName: "arrow.down.circle")
                                .foregroundColor(.green)
                            Text("低")
                        }
                        .tag(0)

                        HStack {
                            Image(systemName: "equal.circle")
                                .foregroundColor(.orange)
                            Text("中")
                        }
                        .tag(1)

                        HStack {
                            Image(systemName: "exclamationmark.circle")
                                .foregroundColor(.red)
                            Text("高")
                        }
                        .tag(2)
                    }
                    .pickerStyle(SegmentedPickerStyle())

                    // 截止日期
                    Toggle("设置截止日期", isOn: $newTaskHasDueDate)

                    if newTaskHasDueDate {
                        DatePicker("截止日期", selection: $newTaskDueDate, displayedComponents: [.date, .hourAndMinute])
                    }

                    // 备注
                    VStack(alignment: .leading) {
                        Text("备注")
                        TextEditor(text: $newTaskNotes)
                            .frame(minHeight: 100)
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                            )
                    }
                }

                // 保存按钮
                Section {
                    Button(action: saveNewTask) {
                        Text("保存任务")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding()
                            .background(newTaskTitle.isEmpty ? Color.gray : theme.colors.accent)
                            .cornerRadius(10)
                    }
                    .disabled(newTaskTitle.isEmpty)
                    .listRowInsets(EdgeInsets())
                }
            }
            .navigationTitle("添加任务")
            .navigationBarItems(trailing: Button("取消") {
                showAddTaskSheet = false
            })
        }
    }

    // MARK: - 添加分类视图

    private var addCategoryView: some View {
        NavigationView {
            Form {
                Section(header: Text("分类信息")) {
                    TextField("分类名称", text: $newCategoryName)

                    ColorPicker("分类颜色", selection: $newCategoryColor)

                    // 图标选择器
                    NavigationLink(destination: IconPickerView(selectedIcon: $newCategoryIcon)) {
                        HStack {
                            Text("分类图标")
                            Spacer()
                            Image(systemName: newCategoryIcon)
                                .foregroundColor(newCategoryColor)
                            Text(newCategoryIcon)
                                .foregroundColor(.gray)
                        }
                    }
                }

                Section {
                    Button(action: {
                        addNewCategory()
                        showAddCategorySheet = false
                    }) {
                        Text("添加分类")
                            .frame(maxWidth: .infinity)
                            .foregroundColor(.white)
                            .padding()
                            .background(newCategoryName.isEmpty ? Color.gray : theme.colors.accent)
                            .cornerRadius(10)
                    }
                    .disabled(newCategoryName.isEmpty)
                    .listRowInsets(EdgeInsets())
                }
            }
            .navigationTitle("添加分类")
            .navigationBarItems(trailing: Button("取消") {
                showAddCategorySheet = false
            })
        }
    }

    // MARK: - 辅助方法

    // 加载分类
    private func loadCategories() -> [TaskCategory] {
        // 从 AppGroupDataManager 直接读取分类，避免使用 TaskManager.shared
        if let categories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories) {
            return categories
        } else {
            // 如果没有保存的分类，使用默认分类
            let defaultCategories = TaskCategory.defaultCategories
            // 保存默认分类
            AppGroupDataManager.shared.save(defaultCategories, for: .todoList, property: .categories)
            return defaultCategories
        }
    }

    // 设置预览视图
    private func setupPreviewView() {
        let data = createPreviewData()
        previewView = UniversalWidgetPreviewView(
            data: data,
            previewSize: $previewSize
        ) { data, family in
            AnyView(TodoWidgetView(data: data, family: family))
        }
    }

    // 更新预览
    private func updatePreview() {
        // 由于 UniversalWidgetPreviewView 没有 updateData 方法
        // 我们需要重新创建预览视图
        setupPreviewView()
    }

    // 创建预览数据
    private func createPreviewData() -> TodoWidgetData {
        var tasks: [Task]?

        // 从 AppGroupDataManager 直接读取任务，避免使用 TaskManager.shared
        if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            if let category = selectedCategory {
                // 筛选特定分类的任务
                tasks = allTasks.filter { $0.categoryId == category.id }
            } else {
                // 所有任务
                tasks = allTasks
            }
        }

        return TodoWidgetData(
            category: selectedCategory,
            showCompleted: showCompletedTasks,
            maxTaskCount: maxTasksToShow,
            priorityFilter: selectedPriorityFilter,
            background: background.toWidgetBackground(),
            fontName: fontSelection.fontName,
            fontSize: fontSelection.fontSize,
            fontColor: fontSelection.fontColor,
            tasks: tasks
        )
    }

    // 加载现有数据
    private func loadExistingData() {
        if let config = AppGroupDataManager.shared.read(TodoWidgetConfig.self, for: .todoList, property: .config) {
            // 加载分类
            if let categoryIdString = config.categoryId, let categoryId = UUID(uuidString: categoryIdString) {
                // 从 AppGroupDataManager 直接读取分类，避免使用 TaskManager.shared
                if let categories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories) {
                    selectedCategory = categories.first { $0.id == categoryId }
                }
            }

            // 加载显示选项
            showCompletedTasks = config.showCompleted
            maxTasksToShow = config.maxTaskCount
            selectedPriorityFilter = config.priorityFilter

            // 加载背景
            if case let .color(widgetColor) = config.background {
                background = .color(widgetColor.toColor())
            } else if case let .imageFile(fileName) = config.background {
                let backgroundImageFileName = AppGroupDataManager.shared.fileName(for: .todoList, property: .backgroundImage)
                if let imageData = AppGroupDataManager.shared.readData(fileName: backgroundImageFileName),
                   let image = UIImage(data: imageData)
                {
                    background = .image(image)
                }
            }

            // 加载字体设置
            fontSelection = FontSelection(
                font: .custom(config.fontName, size: config.fontSize),
                fontSize: config.fontSize,
                fontColor: config.fontColor
            )
        }

        // 更新预览
        updatePreview()
    }

    // 保存小组件
    private func saveWidget() {
        // 调试信息：打印当前任务数据
        if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            print("保存小组件前的任务数量: \(allTasks.count)")
            for task in allTasks {
                print("任务: \(task.title), 完成状态: \(task.isCompleted), 分类ID: \(task.categoryId), 优先级: \(task.priority.rawValue)")
            }

            // 检查是否有高优先级任务
            let hasHighPriorityTask = allTasks.contains { $0.priority == .high }

            // 如果没有任务，创建示例任务
            if allTasks.isEmpty {
                print("没有任务，创建示例任务")
                let categories = loadCategories()
                if let category = categories.first {
                    let sampleTasks = Task.createSampleTasks(for: category)
                    AppGroupDataManager.shared.save(sampleTasks, for: .todoList, property: .tasks)
                    print("已创建 \(sampleTasks.count) 个示例任务")
                }
            }
            // 如果没有高优先级任务，创建一个高优先级任务
            else if !hasHighPriorityTask && selectedPriorityFilter == .high {
                print("没有高优先级任务，但优先级筛选设置为高，创建一个高优先级任务")
                let categories = loadCategories()
                if let category = categories.first {
                    var tasks = allTasks
                    let highPriorityTask = Task(
                        title: "高优先级任务示例",
                        isCompleted: false,
                        priority: .high,
                        categoryId: category.id,
                        notes: "这是一个自动创建的高优先级任务示例"
                    )
                    tasks.append(highPriorityTask)
                    AppGroupDataManager.shared.save(tasks, for: .todoList, property: .tasks)
                    print("已创建高优先级任务")
                }
            }
        } else {
            print("保存小组件前未找到任务数据")
            // 创建默认任务
            let categories = loadCategories()
            if let category = categories.first {
                let sampleTasks = Task.createSampleTasks(for: category)
                // 确保至少有一个高优先级任务
                var tasks = sampleTasks
                let highPriorityTask = Task(
                    title: "高优先级任务示例",
                    isCompleted: false,
                    priority: .high,
                    categoryId: category.id,
                    notes: "这是一个自动创建的高优先级任务示例"
                )
                tasks.append(highPriorityTask)
                AppGroupDataManager.shared.save(tasks, for: .todoList, property: .tasks)
                print("已创建 \(tasks.count) 个示例任务，包括一个高优先级任务")
            }
        }

        // 1. 准备背景
        var widgetBackground: WidgetBackground

        switch background {
        case let .image(image):
            // 压缩图片
            if let compressedData = image.compressForWidgetOptimized(
                targetFileSize: 60 * 1024,
                minQuality: 0.5,
                minSize: CGSize(width: 300, height: 300),
                maxPixelArea: 800000,
                preserveAspectRatio: true
            ) {
                AppGroupDataManager.shared.saveAuto(compressedData, for: .todoList, property: .backgroundImage)
                widgetBackground = .imageFile(AppGroupDataManager.shared.fileName(for: .todoList, property: .backgroundImage))

                print("任务清单背景图片压缩后大小: \(compressedData.count / 1024) KB")
            } else {
                widgetBackground = .color(WidgetColor.fromColor(.white))
            }
        case let .color(color):
            widgetBackground = .color(WidgetColor.fromColor(color))
        case let .packageImage(imageName):
            // 使用包内图片资源
            widgetBackground = .packageImage(imageName)
        }

        // 2. 创建配置
        let widgetConfig = TodoWidgetConfig(
            categoryId: selectedCategory?.id.uuidString,
            showCompleted: showCompletedTasks,
            maxTaskCount: maxTasksToShow,
            priorityFilter: selectedPriorityFilter,
            background: widgetBackground,
            fontName: fontSelection.fontName,
            fontSize: fontSelection.fontSize,
            fontColor: fontSelection.fontColor,
            lastUpdated: Date()
        )

        // 3. 保存配置
        AppGroupDataManager.shared.save(widgetConfig, for: .todoList, property: .config)

        // 4. 强制刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 延迟一秒后再次刷新，确保数据更新
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            WidgetCenter.shared.reloadAllTimelines()
            print("已强制刷新小组件")
        }

        // 5. 显示成功提示
        withAnimation {
            showSuccessToast = true
        }

        // 6. 3秒后隐藏提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation {
                showSuccessToast = false
            }
        }
    }

    // 添加新分类
    private func addNewCategory() {
        guard !newCategoryName.isEmpty else { return }

        let newCategory = TaskCategory(
            name: newCategoryName,
            color: newCategoryColor,
            icon: newCategoryIcon
        )

        // 从 AppGroupDataManager 直接读取和保存分类，避免使用 TaskManager.shared
        var categories = loadCategories()
        categories.append(newCategory)
        AppGroupDataManager.shared.save(categories, for: .todoList, property: .categories)

        // 重置输入
        newCategoryName = ""
        newCategoryColor = .blue
        newCategoryIcon = "list.bullet"

        // 选择新创建的分类
        selectedCategory = newCategory
        updatePreview()
    }

    // 保存新任务
    private func saveNewTask() {
        guard !newTaskTitle.isEmpty else { return }

        // 获取选中的分类
        let categories = loadCategories()
        let selectedCategory = categories.first { $0.id == newTaskCategoryId } ?? categories.first!

        // 创建新任务
        let newTask = Task(
            id: UUID(),
            title: newTaskTitle,
            isCompleted: false,
            priority: TaskPriority(rawValue: newTaskPriority) ?? .medium,
            dueDate: newTaskHasDueDate ? newTaskDueDate : nil,
            categoryId: selectedCategory.id,
            notes: newTaskNotes.isEmpty ? nil : newTaskNotes
        )

        // 从 AppGroupDataManager 读取现有任务
        var tasks: [Task] = []
        if let existingTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            tasks = existingTasks
        }

        // 添加新任务
        tasks.append(newTask)

        // 保存任务
        AppGroupDataManager.shared.save(tasks, for: .todoList, property: .tasks)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 重置输入
        newTaskTitle = ""
        newTaskNotes = ""
        newTaskHasDueDate = false

        // 关闭弹窗
        showAddTaskSheet = false

        // 更新预览
        updatePreview()
    }
}

// MARK: - 图标选择器视图

struct IconPickerView: View {
    @Binding var selectedIcon: String

    // 常用图标列表
    let icons = [
        "list.bullet", "checkmark.circle", "calendar", "book.closed",
        "briefcase", "cart", "bag", "gift", "heart", "house",
        "person", "star", "tag", "flag", "bell", "car", "airplane",
        "gamecontroller", "sportscourt", "fork.knife", "cup.and.saucer",
        "pills", "cross", "dollarsign.circle", "creditcard",
    ]

    var body: some View {
        ScrollView {
            LazyVGrid(columns: [GridItem(.adaptive(minimum: 60))], spacing: 20) {
                ForEach(icons, id: \.self) { icon in
                    Button(action: {
                        selectedIcon = icon
                    }) {
                        VStack {
                            Image(systemName: icon)
                                .font(.system(size: 24))
                                .frame(width: 50, height: 50)
                                .background(selectedIcon == icon ? Color.blue.opacity(0.2) : Color.clear)
                                .cornerRadius(10)

                            Text(icon)
                                .font(.caption)
                                .lineLimit(1)
                                .truncationMode(.middle)
                        }
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .padding()
        }
        .navigationTitle("选择图标")
    }
}

// MARK: - 预览

struct TodoWidgetConfigView_Previews: PreviewProvider {
    static var previews: some View {
        NavigationView {
            TodoWidgetConfigView()
                .environmentObject(ThemeManager.shared)
        }
    }
}
