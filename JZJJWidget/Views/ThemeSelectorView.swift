//
//  ThemeSelectorView.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/16.
//

import SwiftUI
import MyWidgetKit

/// 主题选择器视图
struct ThemeSelectorView: View {
    // MARK: - 属性
    
    // 状态对象
    @ObservedObject var themeManager: ThemeManager
    
    // 环境
    @Environment(\.presentationMode) var presentationMode
    
    // 状态
    @State private var selectedTheme: AppTheme
    @State private var animateBackground: Bool = false
    
    // 布局
    private let columns = [
        GridItem(.adaptive(minimum: 150, maximum: 180), spacing: AppLayout.Spacing.medium)
    ]
    
    // 初始化
    init(themeManager: ThemeManager) {
        self.themeManager = themeManager
        self._selectedTheme = State(initialValue: themeManager.currentTheme)
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景
                themeManager.currentTheme.colors.background
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(alignment: .leading, spacing: AppLayout.Spacing.large) {
                        // 标题
                        Text("选择主题")
                            .font(themeManager.currentTheme.fonts.titleMedium)
                            .foregroundColor(themeManager.currentTheme.colors.text)
                            .padding(.horizontal, AppLayout.Spacing.large)
                            .padding(.top, AppLayout.Spacing.large)
                        
                        // 主题网格
                        LazyVGrid(columns: columns, spacing: AppLayout.Spacing.large) {
                            ForEach(AppTheme.allCases) { theme in
                                ThemePreviewCard(
                                    theme: theme,
                                    isSelected: selectedTheme == theme,
                                    onSelect: {
                                        withAnimation {
                                            selectedTheme = theme
                                            hapticFeedback(style: .light)
                                        }
                                    }
                                )
                            }
                        }
                        .padding(.horizontal, AppLayout.Spacing.large)
                        .padding(.bottom, AppLayout.Spacing.large)
                    }
                }
            }
            .navigationBarTitle("主题设置", displayMode: .inline)
            .navigationBarItems(
                leading: Button(action: {
                    presentationMode.wrappedValue.dismiss()
                    hapticFeedback(style: .light)
                }) {
                    Text("取消")
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                },
                trailing: Button(action: {
                    themeManager.switchTheme(to: selectedTheme)
                    presentationMode.wrappedValue.dismiss()
                    hapticFeedback(style: .medium)
                }) {
                    Text("应用")
                        .fontWeight(.semibold)
                        .foregroundColor(themeManager.currentTheme.colors.accent)
                }
            )
            .onAppear {
                // 启动背景动画
                withAnimation(Animation.linear(duration: 3).repeatForever(autoreverses: true)) {
                    animateBackground = true
                }
            }
        }
    }
    
    // 触觉反馈
    private func hapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
}

/// 主题预览卡片
struct ThemePreviewCard: View {
    // MARK: - 属性
    
    // 属性
    let theme: AppTheme
    let isSelected: Bool
    let onSelect: () -> Void
    
    // 状态
    @State private var isPressed: Bool = false
    @State private var isHovered: Bool = false
    
    // MARK: - 视图主体
    
    var body: some View {
        Button(action: onSelect) {
            VStack(spacing: AppLayout.Spacing.medium) {
                // 主题预览
                ZStack {
                    // 背景
                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                        .fill(theme.colors.background)
                        .frame(height: 160)
                        .overlay(
                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                                .stroke(
                                    isSelected ? theme.colors.accent : theme.colors.border,
                                    lineWidth: isSelected ? AppLayout.Border.medium : AppLayout.Border.hairline
                                )
                        )
                    
                    // 预览内容
                    VStack(spacing: AppLayout.Spacing.medium) {
                        // 标题栏
                        HStack {
                            Circle()
                                .fill(theme.colors.accent)
                                .frame(width: 12, height: 12)
                            
                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.small)
                                .fill(theme.colors.text)
                                .frame(width: 60, height: 6)
                            
                            Spacer()
                        }
                        .padding(.horizontal, AppLayout.Spacing.small)
                        
                        // 内容区域
                        VStack(spacing: AppLayout.Spacing.small) {
                            // 卡片
                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                .fill(theme.colors.surface)
                                .frame(height: 40)
                                .overlay(
                                    HStack {
                                        Circle()
                                            .fill(theme.colors.accent)
                                            .frame(width: 20, height: 20)
                                        
                                        VStack(alignment: .leading, spacing: 4) {
                                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.small)
                                                .fill(theme.colors.text)
                                                .frame(width: 40, height: 4)
                                            
                                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.small)
                                                .fill(theme.colors.subtext)
                                                .frame(width: 30, height: 3)
                                        }
                                        
                                        Spacer()
                                    }
                                    .padding(.horizontal, 8)
                                )
                            
                            // 按钮
                            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                .fill(theme.colors.accent)
                                .frame(height: 24)
                        }
                        .padding(.horizontal, AppLayout.Spacing.small)
                    }
                    .padding(AppLayout.Spacing.small)
                }
                .shadow(
                    color: isSelected ? theme.colors.accent.opacity(0.3) : theme.colors.shadow.opacity(0.1),
                    radius: isSelected ? 8 : 4,
                    x: 0,
                    y: isSelected ? 4 : 2
                )
                
                // 主题名称
                Text(theme.rawValue)
                    .font(.subheadline)
                    .fontWeight(isSelected ? .semibold : .regular)
                    .foregroundColor(isSelected ? theme.colors.accent : theme.colors.text)
            }
            .scaleEffect(isPressed ? 0.95 : (isHovered ? 1.02 : 1.0))
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isPressed)
            .animation(.spring(response: 0.3, dampingFraction: 0.7), value: isHovered)
        }
        .buttonStyle(PlainButtonStyle())
        .onHover { hovering in
            withAnimation {
                isHovered = hovering
            }
        }
        .simultaneousGesture(
            DragGesture(minimumDistance: 0)
                .onChanged { _ in
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = true
                    }
                }
                .onEnded { _ in
                    withAnimation(.easeInOut(duration: 0.1)) {
                        isPressed = false
                    }
                }
        )
    }
}

#Preview {
    ThemeSelectorView(themeManager: ThemeManager.shared)
}
