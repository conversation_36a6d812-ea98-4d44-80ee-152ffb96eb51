//
//  WidgetPreviewContainer.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/15.
//

import SwiftUI
import MyWidgetKit
import WidgetKit

/// 通用小组件预览容器
struct WidgetPreviewContainer<Content: View>: View {
    // MARK: - 属性
    
    // 主题
    let theme: AppTheme
    
    // 标题
    let title: String
    
    // 小组件尺寸
    @Binding var selectedFamily: WidgetFamily
    
    // 内容构建器
    let content: (WidgetFamily) -> Content
    
    // 状态
    @State private var previewScale: CGFloat = 1.0
    @State private var lastScale: CGFloat = 1.0
    @GestureState private var magnificationState: CGFloat = 1.0
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(spacing: 16) {
            // 标题和尺寸选择器
            HStack {
                Text(title)
                    .font(theme.fonts.headlineMedium)
                    .foregroundColor(theme.colors.text)
                
                Spacer()
                
                // 尺寸选择器
                Picker("尺寸", selection: $selectedFamily) {
                    Text("小").tag(WidgetFamily.systemSmall)
                    Text("中").tag(WidgetFamily.systemMedium)
                    Text("大").tag(WidgetFamily.systemLarge)
                }
                .pickerStyle(SegmentedPickerStyle())
                .frame(width: 150)
            }
            
            // 预览内容
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 16)
                    .fill(theme.colors.surfaceVariant.opacity(0.5))
                
                // 小组件内容
                content(selectedFamily)
                    .frame(
                        width: getPreviewWidth() * previewScale,
                        height: getPreviewHeight() * previewScale
                    )
                    .clipShape(RoundedRectangle(cornerRadius: 16 * previewScale))
                    .shadow(
                        color: theme.colors.shadow.opacity(0.1),
                        radius: 10 * previewScale,
                        x: 0,
                        y: 4 * previewScale
                    )
                    // 添加缩放手势
                    .gesture(
                        MagnificationGesture()
                            .updating($magnificationState) { currentState, gestureState, _ in
                                gestureState = currentState
                            }
                            .onChanged { value in
                                let delta = value / lastScale
                                lastScale = value
                                
                                // 限制缩放范围
                                let newScale = previewScale * delta
                                previewScale = min(max(newScale, 0.5), 1.5)
                            }
                            .onEnded { value in
                                lastScale = 1.0
                            }
                    )
            }
            .frame(height: getContainerHeight())
            .animation(.easeInOut, value: selectedFamily)
            
            // 提示文本
            Text("提示：双指缩放可调整预览大小")
                .font(theme.fonts.bodySmall)
                .foregroundColor(theme.colors.subtext)
                .frame(maxWidth: .infinity, alignment: .center)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(theme.colors.surface)
                .shadow(color: theme.colors.shadow.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
    
    // MARK: - 辅助方法
    
    // 获取预览宽度
    private func getPreviewWidth() -> CGFloat {
        switch selectedFamily {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 360
        case .systemLarge:
            return 360
        default:
            return 170
        }
    }
    
    // 获取预览高度
    private func getPreviewHeight() -> CGFloat {
        switch selectedFamily {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 170
        case .systemLarge:
            return 380
        default:
            return 170
        }
    }
    
    // 获取容器高度
    private func getContainerHeight() -> CGFloat {
        switch selectedFamily {
        case .systemSmall:
            return 200
        case .systemMedium:
            return 200
        case .systemLarge:
            return 420
        default:
            return 200
        }
    }
}

// MARK: - 预览
#Preview {
    WidgetPreviewContainer(
        theme: AppTheme.iosLight,
        title: "预览",
        selectedFamily: .constant(.systemSmall)
    ) { family in
        ZStack {
            Color.white
            
            VStack {
                Image(systemName: "star.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.yellow)
                
                Text("示例小组件")
                    .font(.headline)
            }
        }
    }
    .padding()
    .previewLayout(.sizeThatFits)
}

#Preview {
    WidgetPreviewContainer(
        theme: AppTheme.iosDark,
        title: "预览",
        selectedFamily: .constant(.systemMedium)
    ) { family in
        ZStack {
            Color.black
            
            VStack {
                Image(systemName: "moon.stars.fill")
                    .font(.system(size: 24))
                    .foregroundColor(.yellow)
                
                Text("示例小组件")
                    .font(.headline)
                    .foregroundColor(.white)
            }
        }
    }
    .padding()
    .previewLayout(.sizeThatFits)
    .preferredColorScheme(.dark)
}
