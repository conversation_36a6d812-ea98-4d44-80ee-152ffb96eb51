//
//  EnhancedWallpaperView.swift
//  JZJJWidget
//
//  Created by yj<PERSON>g on 2025/5/15.
//

import MyWidgetKit
import SwiftUI
import WidgetKit

struct EnhancedWallpaperView: View {
    @StateObject private var themeManager = ThemeManager.shared
    @State private var searchText: String = ""
    @State private var selectedCategory: WallpaperCategory = .all
    @State private var selectedWallpaper: Wallpaper? = nil
    @State private var showingPreview: Bool = false
    @State private var showingWidgetRecommendation: Bool = false

    enum WallpaperCategory: String, CaseIterable, Identifiable {
        case all = "全部"
        case minimalist = "极简风"
        case nature = "自然"
        case abstract = "抽象"
        case geometric = "几何"
        case dark = "暗色"
        case light = "亮色"

        var id: String { rawValue }
    }

    struct Wallpaper: Identifiable {
        let id = UUID()
        let name: String
        let image: String // 图片名称或URL
        let categories: [WallpaperCategory]
        let recommendedWidgets: [String] // 推荐搭配的小组件
        let colors: [Color] // 主色调

        // 示例壁纸数据
        static let samples: [Wallpaper] = [
            Wallpaper(
                name: "极简白",
                image: "wallpaper_minimal_white",
                categories: [.minimalist, .light],
                recommendedWidgets: ["每日灵感", "时光提醒", "二维码小组件"],
                colors: [Color("FFFFFF"), Color("F5F5F5")]
            ),
            Wallpaper(
                name: "极简黑",
                image: "wallpaper_minimal_black",
                categories: [.minimalist, .dark],
                recommendedWidgets: ["每日灵感", "时光提醒", "健康助手"],
                colors: [Color("121212"), Color("1E1E1E")]
            ),
            Wallpaper(
                name: "几何蓝",
                image: "wallpaper_geometric_blue",
                categories: [.geometric, .light],
                recommendedWidgets: ["实时天气", "学习进度", "任务清单"],
                colors: [Color("E1F5FE"), Color("B3E5FC")]
            ),
            Wallpaper(
                name: "自然绿",
                image: "wallpaper_nature_green",
                categories: [.nature, .light],
                recommendedWidgets: ["习惯养成", "健康助手", "每日灵感"],
                colors: [Color("F1F8E9"), Color("DCEDC8")]
            ),
            Wallpaper(
                name: "抽象橙",
                image: "wallpaper_abstract_orange",
                categories: [.abstract, .light],
                recommendedWidgets: ["时光提醒", "任务清单", "精选图集"],
                colors: [Color("FFF3E0"), Color("FFECB3")]
            ),
            Wallpaper(
                name: "深邃紫",
                image: "wallpaper_abstract_purple",
                categories: [.abstract, .dark],
                recommendedWidgets: ["心情日记", "学习进度", "每日灵感"],
                colors: [Color("311B92"), Color("4527A0")]
            ),
            Wallpaper(
                name: "几何灰",
                image: "wallpaper_geometric_gray",
                categories: [.geometric, .light],
                recommendedWidgets: ["二维码小组件", "任务清单", "时光提醒"],
                colors: [Color("ECEFF1"), Color("CFD8DC")]
            ),
            Wallpaper(
                name: "自然棕",
                image: "wallpaper_nature_brown",
                categories: [.nature, .dark],
                recommendedWidgets: ["习惯养成", "每日灵感", "精选图集"],
                colors: [Color("5D4037"), Color("795548")]
            ),
        ]
    }

    var filteredWallpapers: [Wallpaper] {
        Wallpaper.samples
            .filter { selectedCategory == .all || $0.categories.contains(selectedCategory) }
            .filter { searchText.isEmpty || $0.name.localizedCaseInsensitiveContains(searchText) }
    }

    var body: some View {
        // 将复杂的视图分解为更小的部分
        mainContentView
            .background(themeManager.currentTheme.colors.background.ignoresSafeArea())
            .navigationTitle("壁纸")
            .sheet(isPresented: $showingPreview) {
                previewSheetContent
            }
            .sheet(isPresented: $showingWidgetRecommendation) {
                recommendationSheetContent
            }
    }

    // 主内容视图
    private var mainContentView: some View {
        VStack(spacing: 0) {
            // 搜索栏
            searchBarView

            // 分类选择器
            categoryPickerView

            // 壁纸网格
            wallpaperGridView
        }
    }

    // 搜索栏视图
    private var searchBarView: some View {
        SearchBar(text: $searchText, placeholder: "搜索壁纸...", theme: themeManager.currentTheme)
            .padding(.horizontal)
            .padding(.top)
    }

    // 分类选择器视图
    private var categoryPickerView: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: AppLayout.Spacing.medium) {
                ForEach(WallpaperCategory.allCases) { category in
                    // 使用 MyWidgetKit 中的 CategoryButton
                    CategoryButton(
                        title: category.rawValue,
                        isSelected: selectedCategory == category,
                        theme: themeManager.currentTheme,
                        action: {
                            withAnimation {
                                selectedCategory = category
                            }
                        }
                    )
                }
            }
            .padding(.horizontal)
            .padding(.vertical, AppLayout.Spacing.small)
        }
    }

    // 壁纸网格视图
    private var wallpaperGridView: some View {
        ScrollView {
            LazyVGrid(
                columns: [GridItem(.adaptive(minimum: 160, maximum: 200), spacing: AppLayout.Spacing.medium)],
                spacing: AppLayout.Spacing.medium
            ) {
                ForEach(filteredWallpapers) { wallpaper in
                    WallpaperCard(
                        wallpaper: wallpaper,
                        theme: themeManager.currentTheme,
                        onTap: {
                            selectedWallpaper = wallpaper
                            showingPreview = true
                        }
                    )
                }
            }
            .padding()
        }
    }

    // 预览弹窗内容
    private var previewSheetContent: some View {
        Group {
            if let wallpaper = selectedWallpaper {
                WallpaperPreviewView(
                    wallpaper: wallpaper,
                    theme: themeManager.currentTheme,
                    onClose: { showingPreview = false },
                    onShowWidgetRecommendation: {
                        showingPreview = false
                        showingWidgetRecommendation = true
                    }
                )
            }
        }
    }

    // 推荐弹窗内容
    private var recommendationSheetContent: some View {
        Group {
            if let wallpaper = selectedWallpaper {
                WidgetRecommendationView(
                    wallpaper: wallpaper,
                    theme: themeManager.currentTheme,
                    onClose: { showingWidgetRecommendation = false }
                )
            }
        }
    }
}

// 壁纸卡片组件
struct WallpaperCard: View {
    let wallpaper: EnhancedWallpaperView.Wallpaper
    let theme: AppTheme
    let onTap: () -> Void
    @State private var isPressed: Bool = false

    var body: some View {
        VStack(spacing: AppLayout.Spacing.small) {
            // 壁纸预览
            ZStack {
                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                    .fill(LinearGradient(colors: wallpaper.colors, startPoint: .topLeading, endPoint: .bottomTrailing))
                    .aspectRatio(1, contentMode: .fit)

                // 这里可以替换为实际的壁纸图片
                // Image(wallpaper.image)
                //     .resizable()
                //     .aspectRatio(contentMode: .fill)
                //     .clipShape(RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium))
            }
            .shadow(color: AppLayout.ShadowStyle.small.0, radius: AppLayout.ShadowStyle.small.1, x: AppLayout.ShadowStyle.small.2, y: AppLayout.ShadowStyle.small.3)

            // 壁纸名称
            Text(wallpaper.name)
                .font(AppTypography.bodyMedium(theme: theme))
                .foregroundColor(theme.colors.text)
                .lineLimit(1)
        }
        .padding(AppLayout.Spacing.small)
        .background(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                .fill(theme.colors.surface.opacity(0.5))
        )
        .cardPress(isPressed: isPressed)
        .onTapGesture {
            withAnimation {
                isPressed = true
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation {
                        isPressed = false
                        onTap()
                    }
                }
            }
        }
    }
}

// 壁纸预览视图
struct WallpaperPreviewView: View {
    let wallpaper: EnhancedWallpaperView.Wallpaper
    let theme: AppTheme
    let onClose: () -> Void
    let onShowWidgetRecommendation: () -> Void

    var body: some View {
        VStack(spacing: AppLayout.Spacing.large) {
            // 标题
            Text(wallpaper.name)
                .font(AppTypography.titleLarge(theme: theme))
                .foregroundColor(theme.colors.text)

            // 壁纸预览
            ZStack {
                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                    .fill(LinearGradient(colors: wallpaper.colors, startPoint: .topLeading, endPoint: .bottomTrailing))
                    .aspectRatio(0.5, contentMode: .fit)

                // 这里可以替换为实际的壁纸图片
                // Image(wallpaper.image)
                //     .resizable()
                //     .aspectRatio(contentMode: .fill)
                //     .clipShape(RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large))
            }
            .shadow(color: AppLayout.ShadowStyle.medium.0, radius: AppLayout.ShadowStyle.medium.1, x: AppLayout.ShadowStyle.medium.2, y: AppLayout.ShadowStyle.medium.3)

            // 操作按钮
            VStack(spacing: AppLayout.Spacing.medium) {
                Button(action: onShowWidgetRecommendation) {
                    HStack {
                        Image(systemName: "square.grid.2x2")
                        Text("查看推荐小组件")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(AppInteraction.ButtonStyle.primary(theme: theme))

                Button(action: {
                    // 设置为壁纸的逻辑
                }) {
                    HStack {
                        Image(systemName: "photo")
                        Text("设置为壁纸")
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(AppInteraction.ButtonStyle.secondary(theme: theme))

                Button(action: onClose) {
                    Text("关闭")
                        .frame(maxWidth: .infinity)
                }
                .buttonStyle(AppInteraction.ButtonStyle.ghost(theme: theme))
            }
            .padding(.horizontal, AppLayout.Spacing.large)
        }
        .padding(AppLayout.Spacing.large)
        .background(theme.colors.background)
    }
}

// 小组件推荐视图
struct WidgetRecommendationView: View {
    let wallpaper: EnhancedWallpaperView.Wallpaper
    let theme: AppTheme
    let onClose: () -> Void

    var body: some View {
        VStack(spacing: AppLayout.Spacing.large) {
            // 标题
            Text("\(wallpaper.name) 推荐小组件")
                .font(AppTypography.titleLarge(theme: theme))
                .foregroundColor(theme.colors.text)

            // 壁纸预览
            ZStack {
                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                    .fill(LinearGradient(colors: wallpaper.colors, startPoint: .topLeading, endPoint: .bottomTrailing))
                    .frame(height: 120)

                // 这里可以替换为实际的壁纸图片
                // Image(wallpaper.image)
                //     .resizable()
                //     .aspectRatio(contentMode: .fill)
                //     .frame(height: 120)
                //     .clipShape(RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large))
            }
            .shadow(color: AppLayout.ShadowStyle.medium.0, radius: AppLayout.ShadowStyle.medium.1, x: AppLayout.ShadowStyle.medium.2, y: AppLayout.ShadowStyle.medium.3)

            // 推荐小组件列表
            ScrollView {
                VStack(spacing: AppLayout.Spacing.medium) {
                    ForEach(wallpaper.recommendedWidgets, id: \.self) { widgetName in
                        RecommendedWidgetCard(
                            widgetName: widgetName,
                            theme: theme
                        )
                    }
                }
                .padding(.horizontal)
            }

            // 操作按钮
            Button(action: onClose) {
                Text("关闭")
                    .frame(maxWidth: .infinity)
            }
            .buttonStyle(AppInteraction.ButtonStyle.ghost(theme: theme))
            .padding(.horizontal, AppLayout.Spacing.large)
        }
        .padding(AppLayout.Spacing.large)
        .background(theme.colors.background)
    }
}

// 推荐小组件卡片
struct RecommendedWidgetCard: View {
    let widgetName: String
    let theme: AppTheme

    var body: some View {
        HStack {
            // 小组件图标
            Image(systemName: getIconName(for: widgetName))
                .font(.system(size: AppLayout.IconSize.medium))
                .foregroundColor(theme.colors.accent)
                .frame(width: AppLayout.IconSize.large, height: AppLayout.IconSize.large)
                .background(
                    Circle()
                        .fill(theme.colors.accent.opacity(0.1))
                )

            // 小组件名称
            Text(widgetName)
                .font(AppTypography.bodyLarge(theme: theme))
                .foregroundColor(theme.colors.text)

            Spacer()

            // 添加按钮
            Button(action: {
                // 添加小组件的逻辑
            }) {
                Text("添加")
                    .font(AppTypography.bodyMedium(theme: theme))
                    .foregroundColor(theme.colors.accent)
                    .padding(.horizontal, AppLayout.Spacing.medium)
                    .padding(.vertical, AppLayout.Spacing.small)
                    .background(theme.colors.accent.opacity(0.1))
                    .cornerRadius(AppLayout.CornerRadius.medium)
            }
        }
        .padding(AppLayout.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                .fill(theme.colors.surface)
        )
        .shadow(color: AppLayout.ShadowStyle.small.0, radius: AppLayout.ShadowStyle.small.1, x: AppLayout.ShadowStyle.small.2, y: AppLayout.ShadowStyle.small.3)
    }

    // 根据小组件名称获取图标
    private func getIconName(for widgetName: String) -> String {
        switch widgetName {
        case "每日灵感":
            return "text.quote"
        case "时光提醒":
            return "calendar"
        case "二维码小组件":
            return "qrcode"
        case "习惯养成":
            return "checkmark.circle"
        case "心情日记":
            return "heart.fill"
        case "健康助手":
            return "figure.walk"
        case "实时天气":
            return "cloud.sun.fill"
        case "任务清单":
            return "list.bullet"
        case "学习进度":
            return "chart.bar.fill"
        case "精选图集":
            return "photo.fill"
        default:
            return "square.grid.2x2"
        }
    }
}

#Preview {
    NavigationView {
        EnhancedWallpaperView()
    }
}
