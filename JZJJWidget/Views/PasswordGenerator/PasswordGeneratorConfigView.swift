import SwiftUI
import WidgetKit
import MyWidgetKit

struct PasswordGeneratorConfigView: View {
    // MARK: - 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    
    // MARK: - 状态变量
    // 配置数据
    @State private var passwordGeneratorConfig: PasswordGeneratorConfig?
    
    // 密码长度
    @State private var passwordLength: Double = 12
    
    // 密码复杂度
    @State private var complexity: PasswordComplexity = .strong
    
    // 自动复制到剪贴板
    @State private var autoCopyToClipboard: Bool = true
    
    // 显示密码
    @State private var showPassword: Bool = true
    
    // 字体设置
    @State private var fontName: String = "SF Pro"
    @State private var fontSize: CGFloat = 14
    @State private var fontColor: Color = .black
    
    // 强调色
    @State private var accentColor: Color = .blue
    
    // 背景选择
    @State private var backgroundSelection: BackgroundSelection = .color(.white)
    
    // 成功提示
    @State private var showSuccessToast: Bool = false
    
    // 预览尺寸
    @State private var previewSize: WidgetFamily = .systemMedium
    
    // 当前生成的密码
    @State private var currentPassword: String = ""
    
    // 文本框焦点状态
    @FocusState private var isTextFieldFocused: Bool
    
    // MARK: - 计算属性
    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }
    
    // 根据小部件尺寸返回预览高度
    private var dynamicPreviewHeight: CGFloat {
        switch previewSize {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 170
        case .systemLarge:
            return 370
        default:
            return 170
        }
    }
    
    // 获取小组件尺寸名称
    private var widgetFamilyName: String {
        switch previewSize {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        default:
            return "未知尺寸"
        }
    }
    
    // MARK: - 主视图
    var body: some View {
        ZStack {
            // 背景
            theme.colors.background
                .ignoresSafeArea()
            
            // 主内容
            VStack(spacing: 0) {
                // 预览区域 - 固定在顶部
                previewSection
                    .padding(.horizontal)
                    .padding(.top, 16)
                    .padding(.bottom, 8)
                    .background(theme.colors.surface.opacity(0.5))
                    .shadow(color: theme.colors.shadow.opacity(0.1), radius: 4, x: 0, y: 2)
                
                // 可滚动的配置区域
                ScrollView {
                    VStack(spacing: 20) {
                        // 密码设置
                        passwordSettingsSection
                        
                        // 外观设置
                        appearanceSection
                    }
                    .padding()
                }
            }
            
            // 成功提示
            if showSuccessToast {
                VStack {
                    Spacer()
                    
                    Text("小组件已保存")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(.white)
                        .padding()
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(Color.green)
                        )
                        .padding(.bottom, 20)
                        .transition(.move(edge: .bottom).combined(with: .opacity))
                }
            }
        }
        .navigationTitle("密码生成器")
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Button(action: {
                    savePasswordGenerator()
                }) {
                    Text("保存")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.colors.accent)
                }
            }
        }
        .onAppear {
            loadExistingData()
        }
        .onChange(of: passwordLength) { _ in
            updateConfig()
        }
        .onChange(of: complexity) { _ in
            updateConfig()
        }
        .onChange(of: autoCopyToClipboard) { _ in
            updateConfig()
        }
        .onChange(of: showPassword) { _ in
            updateConfig()
        }
        .onChange(of: fontColor) { _ in
            updateConfig()
        }
        .onChange(of: accentColor) { _ in
            updateConfig()
        }
    }
    
    // MARK: - 子视图
    
    // 预览区域
    private var previewSection: some View {
        VStack(alignment: .center, spacing: 12) {
            if let config = passwordGeneratorConfig {
                // 使用优化后的 UniversalWidgetPreviewView
                UniversalWidgetPreviewView(
                    data: config,
                    previewSize: $previewSize,
                    accentColor: theme.colors.accent,
                    backgroundColor: theme.colors.background,
                    surfaceColor: theme.colors.surface,
                    textColor: theme.colors.text,
                    subtextColor: theme.colors.subtext,
                    showSizeSelector: false,
                    showTitle: true,
                    title: "预览"
                ) { data, family in
                    AnyView(PasswordGeneratorWidgetView(config: data as! PasswordGeneratorConfig, family: family))
                }
                .frame(height: dynamicPreviewHeight)
                
                // 显示当前尺寸
                Text("当前尺寸：\(widgetFamilyName)")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.accent)
            } else {
                // 加载中状态
                VStack {
                    ProgressView()
                        .progressViewStyle(CircularProgressViewStyle(tint: theme.colors.accent))
                    
                    Text("加载中...")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.subtext)
                        .padding(.top, 8)
                }
                .frame(height: dynamicPreviewHeight)
            }
            
            // 尺寸选择器
            Picker("尺寸", selection: $previewSize) {
                Text("小").tag(WidgetFamily.systemSmall)
                Text("中").tag(WidgetFamily.systemMedium)
                Text("大").tag(WidgetFamily.systemLarge)
            }
            .pickerStyle(SegmentedPickerStyle())
            .padding(.horizontal, 20)
        }
    }
    
    // 密码设置区域
    private var passwordSettingsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("密码设置")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)
            
            // 密码长度滑块
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    Text("密码长度: \(Int(passwordLength))")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.text)
                    
                    Spacer()
                    
                    // 减少按钮
                    Button(action: {
                        if passwordLength > 6 {
                            passwordLength -= 1
                        }
                    }) {
                        Image(systemName: "minus.circle.fill")
                            .foregroundColor(theme.colors.accent)
                    }
                    .disabled(passwordLength <= 6)
                    .opacity(passwordLength <= 6 ? 0.5 : 1)
                    
                    // 增加按钮
                    Button(action: {
                        if passwordLength < 32 {
                            passwordLength += 1
                        }
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .foregroundColor(theme.colors.accent)
                    }
                    .disabled(passwordLength >= 32)
                    .opacity(passwordLength >= 32 ? 0.5 : 1)
                }
                
                Slider(value: $passwordLength, in: 6...32, step: 1)
                    .accentColor(theme.colors.accent)
            }
            
            // 密码复杂度选择
            VStack(alignment: .leading, spacing: 8) {
                Text("密码复杂度")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
                
                VStack(spacing: 8) {
                    ForEach(PasswordComplexity.allCases) { option in
                        Button(action: {
                            complexity = option
                        }) {
                            HStack {
                                Image(systemName: option == complexity ? "checkmark.circle.fill" : "circle")
                                    .foregroundColor(option == complexity ? option.color : theme.colors.subtext)
                                
                                VStack(alignment: .leading, spacing: 2) {
                                    Text(option.rawValue)
                                        .font(theme.fonts.bodyMedium)
                                        .foregroundColor(theme.colors.text)
                                    
                                    Text(option.description)
                                        .font(theme.fonts.bodySmall)
                                        .foregroundColor(theme.colors.subtext)
                                }
                                
                                Spacer()
                            }
                            .padding(10)
                            .background(
                                RoundedRectangle(cornerRadius: 8)
                                    .fill(option == complexity ? option.color.opacity(0.1) : Color.clear)
                            )
                            .overlay(
                                RoundedRectangle(cornerRadius: 8)
                                    .stroke(option == complexity ? option.color.opacity(0.3) : theme.colors.subtext.opacity(0.2), lineWidth: 1)
                            )
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
            }
            
            // 其他选项
            VStack(alignment: .leading, spacing: 12) {
                Toggle(isOn: $autoCopyToClipboard) {
                    Text("自动复制到剪贴板")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.text)
                }
                .toggleStyle(SwitchToggleStyle(tint: theme.colors.accent))
                
                Toggle(isOn: $showPassword) {
                    Text("显示密码")
                        .font(theme.fonts.bodyMedium)
                        .foregroundColor(theme.colors.text)
                }
                .toggleStyle(SwitchToggleStyle(tint: theme.colors.accent))
            }
            
            // 生成新密码按钮
            Button(action: {
                generateNewPassword()
            }) {
                HStack {
                    Image(systemName: "arrow.triangle.2.circlepath")
                    Text("生成新密码")
                }
                .font(theme.fonts.bodyMedium)
                .foregroundColor(.white)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(theme.colors.accent)
                .cornerRadius(10)
            }
            .padding(.top, 8)
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(theme.colors.surface)
        )
    }
    
    // 外观设置区域
    private var appearanceSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 标题
            Text("外观设置")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)
            
            // 背景设置
            BackgroundSelectView(
                allowColor: true,
                allowImage: true,
                initialColor: backgroundSelection.colorValue ?? .white,
                initialImage: backgroundSelection.imageValue,
                onSelection: { selection in
                    backgroundSelection = selection
                    updateBackground()
                }
            )
            
            // 字体设置
            FontSelectView(
                showFontPicker: true,
                showFontSizePicker: true,
                showFontColorPicker: true,
                onSelectionChanged: { selection in
                    fontName = selection.fontName
                    fontSize = selection.fontSize
                    fontColor = selection.fontColor
                    updateConfig()
                }
            )
            
            // 强调色
            VStack(alignment: .leading, spacing: 8) {
                Text("强调色")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
                
                ColorPicker("选择强调色", selection: $accentColor)
                    .labelsHidden()
            }
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(theme.colors.surface)
        )
    }
    
    // MARK: - 方法
    
    // 加载现有数据
    private func loadExistingData() {
        if let savedConfig = AppGroupDataManager.shared.read(PasswordGeneratorConfig.self, for: .passwordGenerator, property: .config) {
            // 加载配置
            passwordLength = Double(savedConfig.passwordLength)
            complexity = savedConfig.complexity
            autoCopyToClipboard = savedConfig.autoCopyToClipboard
            showPassword = savedConfig.showPassword
            fontName = savedConfig.fontName
            fontSize = savedConfig.fontSize
            fontColor = savedConfig.fontColor.toColor()
            accentColor = savedConfig.accentColor.toColor()
            currentPassword = savedConfig.currentPassword
            
            // 设置背景选择
            switch savedConfig.background {
            case let .color(color):
                backgroundSelection = .color(color.toColor())
            case let .imageData(data):
                if let image = UIImage(data: data) {
                    backgroundSelection = .image(image)
                }
            case let .packageImage(name):
                backgroundSelection = .packageImage(name)
            default:
                backgroundSelection = .color(.white)
            }
            
            // 更新配置
            passwordGeneratorConfig = savedConfig
        } else {
            // 创建默认配置
            let defaultConfig = PasswordGeneratorConfig.createDefault()
            passwordGeneratorConfig = defaultConfig
            currentPassword = defaultConfig.currentPassword
        }
    }
    
    // 更新配置
    private func updateConfig() {
        guard var config = passwordGeneratorConfig else { return }
        
        config.passwordLength = Int(passwordLength)
        config.complexity = complexity
        config.autoCopyToClipboard = autoCopyToClipboard
        config.showPassword = showPassword
        config.fontName = fontName
        config.fontSize = fontSize
        config.fontColor = WidgetColor.fromColor(fontColor)
        config.accentColor = WidgetColor.fromColor(accentColor)
        config.currentPassword = currentPassword
        
        // 更新配置
        passwordGeneratorConfig = config
    }
    
    // 更新背景
    private func updateBackground() {
        guard var config = passwordGeneratorConfig else { return }
        
        switch backgroundSelection {
        case let .color(color):
            config.background = .color(WidgetColor.fromColor(color))
        case let .image(image):
            if let imageData = image.pngData() {
                config.background = .imageData(imageData)
            }
        case let .packageImage(name):
            config.background = .packageImage(name)
        }
        
        // 更新配置
        passwordGeneratorConfig = config
    }
    
    // 生成新密码
    private func generateNewPassword() {
        let newPassword = PasswordGenerator.generatePassword(length: Int(passwordLength), complexity: complexity)
        currentPassword = newPassword
        
        if var config = passwordGeneratorConfig {
            config.currentPassword = newPassword
            config.lastUpdated = Date()
            passwordGeneratorConfig = config
        }
    }
    
    // 保存密码生成器配置
    private func savePasswordGenerator() {
        guard var config = passwordGeneratorConfig else { return }
        
        // 更新背景
        switch backgroundSelection {
        case let .color(color):
            config.background = .color(WidgetColor.fromColor(color))
        case let .image(image):
            if let imageData = image.pngData() {
                config.background = .imageData(imageData)
            }
        case let .packageImage(name):
            config.background = .packageImage(name)
        }
        
        // 保存配置
        AppGroupDataManager.shared.save(config, for: .passwordGenerator, property: .config)
        
        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()
        
        // 显示成功提示
        withAnimation {
            showSuccessToast = true
        }
        
        // 3秒后隐藏提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation {
                showSuccessToast = false
            }
        }
        
        // 收起键盘
        isTextFieldFocused = false
    }
}
