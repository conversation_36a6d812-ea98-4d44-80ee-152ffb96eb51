//
//  ConfigSectionContainer.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/15.
//

import SwiftUI
import MyWidgetKit

/// 配置部分容器视图
struct ConfigSectionContainer<Content: View>: View {
    // MARK: - 属性
    
    // 主题
    let theme: AppTheme
    
    // 标题
    let title: String
    
    // 图标
    var iconName: String?
    
    // 内容
    let content: Content
    
    // 状态
    @State private var isExpanded: Bool = true
    
    // 初始化
    init(
        theme: AppTheme,
        title: String,
        iconName: String? = nil,
        @ViewBuilder content: () -> Content
    ) {
        self.theme = theme
        self.title = title
        self.iconName = iconName
        self.content = content()
    }
    
    // MARK: - 视图主体
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // 标题栏（可点击展开/折叠）
            Button(action: {
                withAnimation(.easeInOut(duration: 0.2)) {
                    isExpanded.toggle()
                }
                
                // 触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .light)
                generator.impactOccurred()
            }) {
                HStack {
                    // 图标（如果有）
                    if let iconName = iconName {
                        Image(systemName: iconName)
                            .font(.system(size: 18))
                            .foregroundColor(theme.colors.accent)
                            .frame(width: 24, height: 24)
                    }
                    
                    // 标题
                    Text(title)
                        .font(theme.fonts.titleMedium)
                        .foregroundColor(theme.colors.text)
                    
                    Spacer()
                    
                    // 展开/折叠图标
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.system(size: 14))
                        .foregroundColor(theme.colors.subtext)
                        .frame(width: 24, height: 24)
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            // 内容（可展开/折叠）
            if isExpanded {
                content
                    .transition(.opacity.combined(with: .move(edge: .top)))
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(theme.colors.surface)
                .shadow(color: theme.colors.shadow.opacity(0.05), radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - 预览
#Preview {
    ConfigSectionContainer(
        theme: AppTheme.iosLight,
        title: "样式设置",
        iconName: "paintbrush.fill"
    ) {
        VStack(alignment: .leading, spacing: 16) {
            Text("这是一个配置部分的示例内容")
                .font(.body)
                .foregroundColor(.primary)
            
            HStack {
                ForEach(0..<5) { i in
                    Circle()
                        .fill(Color.blue.opacity(Double(i) / 5.0))
                        .frame(width: 30, height: 30)
                }
            }
            
            TextField("示例输入", text: .constant(""))
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
        .padding(.top, 8)
    }
    .padding()
    .previewLayout(.sizeThatFits)
}

#Preview {
    ConfigSectionContainer(
        theme: AppTheme.iosDark,
        title: "内容设置"
    ) {
        VStack(alignment: .leading, spacing: 16) {
            Text("这是一个配置部分的示例内容")
                .font(.body)
                .foregroundColor(.primary)
            
            HStack {
                ForEach(0..<5) { i in
                    Circle()
                        .fill(Color.purple.opacity(Double(i) / 5.0))
                        .frame(width: 30, height: 30)
                }
            }
            
            TextField("示例输入", text: .constant(""))
                .textFieldStyle(RoundedBorderTextFieldStyle())
        }
        .padding(.top, 8)
    }
    .padding()
    .previewLayout(.sizeThatFits)
    .preferredColorScheme(.dark)
}
