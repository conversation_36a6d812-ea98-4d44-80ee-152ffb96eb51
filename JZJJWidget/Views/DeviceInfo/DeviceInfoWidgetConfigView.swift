import SwiftUI
import WidgetKit
import MyWidgetKit
 

struct DeviceInfoWidgetConfigView: View {
    // MARK: - 环境对象
    @EnvironmentObject private var themeManager: ThemeManager

    // MARK: - 状态变量
    @State private var deviceInfoData: DeviceInfoWidgetData?
    @State private var previewSize: WidgetFamily = .systemMedium
    @State private var showSuccessToast = false
    @State private var backgroundSelection: BackgroundSelection = .color(.white)
    @State private var fontSelection = FontSelection(font: .system(size: 14), fontSize: 14, fontColor: .black)
    @State private var showDeviceName = true
    @State private var showOSInfo = true
    @State private var showBatteryInfo = true
    @State private var showStorageInfo = true
    @State private var progressStyle: DeviceInfoWidgetData.ProgressStyle = .bar
    @State private var textAlignment: DeviceInfoWidgetData.TextAlignment = .leading
    @State private var accentColor: Color = .blue
    @State private var dropdownMenuID = "device-info-size-menu"
    @State private var sizeSelector: TopDropdownSizeSelector?
    @State private var backgroundUpdateCounter = 0

    // MARK: - 计算属性
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // MARK: - 生命周期方法
    var body: some View {
        ZStack {
            // 主内容
            ScrollView {
                VStack(spacing: 20) {
                    // 预览区域
                    previewSection

                    // 配置选项
                    configSection
                }
                .padding()
            }

            // 尺寸选择器下拉菜单
            TopDropdownSizeSelector(
                selectedSize: $previewSize,
                supportedSizes: [.systemSmall, .systemMedium, .systemLarge],
                menuYPosition: 60,
                menuID: dropdownMenuID
            )
            .onAppear { sizeSelector = $0 }

            // 成功提示
            if showSuccessToast {
                SuccessToast(message: "设置已保存", theme: theme)
                    .onAppear {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                            withAnimation {
                                showSuccessToast = false
                            }
                        }
                    }
            }
        }
        .navigationTitle("")
        .toolbar {
            ToolbarItem(placement: .principal) {
                SizeSelectorButton(
                    selectedSize: previewSize,
                    onTap: {
                        sizeSelector?.toggleMenu()
                    },
                    menuID: dropdownMenuID
                )
            }

            ToolbarItem(placement: .navigationBarTrailing) {
                Button("保存") {
                    saveWidgetData()
                }
                .foregroundColor(theme.colors.accent)
            }
        }
        .onAppear {
            loadWidgetData()
        }
    }

    // MARK: - 子视图

    // 预览区域
    private var previewSection: some View {
        VStack(alignment: .center, spacing: 8) {
            if let data = deviceInfoData {
                DeviceInfoWidgetView(data: data, family: previewSize)
                    .frame(width: getPreviewWidth(), height: getPreviewHeight())
                    .clipShape(RoundedRectangle(cornerRadius: 20))
                    .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
                    .id(backgroundUpdateCounter) // 强制刷新预览
            } else {
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: theme.colors.accent))
                    .frame(width: getPreviewWidth(), height: getPreviewHeight())
            }
        }
    }

    // 配置选项区域
    private var configSection: some View {
        VStack(spacing: 20) {
            // 显示设置
            ConfigSectionContainer(theme: theme, title: "显示设置", iconName: "eye") {
                VStack(alignment: .leading, spacing: 12) {
                    Toggle("显示设备名称", isOn: $showDeviceName)
                        .onChange(of: showDeviceName) { _ in updateWidgetData() }

                    Toggle("显示系统信息", isOn: $showOSInfo)
                        .onChange(of: showOSInfo) { _ in updateWidgetData() }

                    Toggle("显示电池信息", isOn: $showBatteryInfo)
                        .onChange(of: showBatteryInfo) { _ in updateWidgetData() }

                    Toggle("显示存储信息", isOn: $showStorageInfo)
                        .onChange(of: showStorageInfo) { _ in updateWidgetData() }
                }
                .padding(.vertical, 8)
            }

            // 样式设置
            ConfigSectionContainer(theme: theme, title: "样式设置", iconName: "paintbrush") {
                VStack(alignment: .leading, spacing: 16) {
                    // 进度条样式
                    VStack(alignment: .leading, spacing: 8) {
                        Text("进度条样式")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        Picker("进度条样式", selection: $progressStyle) {
                            ForEach(DeviceInfoWidgetData.ProgressStyle.allCases, id: \.self) { style in
                                HStack {
                                    Image(systemName: style.icon)
                                    Text(style.displayName)
                                }
                                .tag(style)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: progressStyle) { _ in updateWidgetData() }
                    }

                    // 文本对齐方式
                    VStack(alignment: .leading, spacing: 8) {
                        Text("文本对齐方式")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        Picker("文本对齐方式", selection: $textAlignment) {
                            ForEach(DeviceInfoWidgetData.TextAlignment.allCases, id: \.self) { alignment in
                                HStack {
                                    Image(systemName: alignment.icon)
                                    Text(alignment.displayName)
                                }
                                .tag(alignment)
                            }
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: textAlignment) { _ in updateWidgetData() }
                    }

                    // 强调色选择
                    VStack(alignment: .leading, spacing: 8) {
                        Text("强调色")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        ColorPicker("选择强调色", selection: $accentColor)
                            .onChange(of: accentColor) { _ in updateWidgetData() }
                    }

                    // 字体设置
                    FontSelectionView(
                        fontSelection: $fontSelection,
                        theme: theme,
                        onFontChange: {
                            updateWidgetData()
                        }
                    )

                    // 背景设置
                    BackgroundSelectView(
                        allowColor: true,
                        allowImage: true,
                        allowPackageImage: true,
                        initialColor: backgroundSelection.colorValue ?? .white,
                        initialImage: backgroundSelection.imageValue,
                        initialPackageImageName: getPackageImageName(from: deviceInfoData?.background),
                        onSelection: { selection in
                            backgroundSelection = selection
                            updateWidgetData()
                            backgroundUpdateCounter += 1
                        }
                    )
                }
                .padding(.vertical, 8)
            }
        }
    }

    // MARK: - 辅助方法

    // 加载小组件数据
    private func loadWidgetData() {
        if let config = AppGroupDataManager.shared.read(DeviceInfoWidgetData.self, for: .deviceInfo, property: .config) {
            deviceInfoData = config

            // 更新UI状态
            showDeviceName = config.showDeviceName
            showOSInfo = config.showOSInfo
            showBatteryInfo = config.showBatteryInfo
            showStorageInfo = config.showStorageInfo
            progressStyle = config.progressStyle
            textAlignment = config.textAlignment
            accentColor = config.accentColor.toColor()

            // 设置字体选择
            fontSelection = FontSelection(
                font: .custom(config.fontName, size: config.fontSize),
                fontSize: config.fontSize,
                fontColor: config.fontColor.toColor()
            )

            // 设置背景选择
            switch config.background {
            case let .color(color):
                backgroundSelection = .color(color.toColor())
            case let .imageData(data):
                if let image = UIImage(data: data) {
                    backgroundSelection = .image(image)
                }
            case let .packageImage(name):
                backgroundSelection = .packageImage(name)
            default:
                backgroundSelection = .color(.white)
            }
        } else {
            // 使用默认配置
            createDefaultWidgetData()
        }
    }

    // 创建默认小组件数据
    @MainActor
    private func createDefaultWidgetData() {
        // 获取设备信息
        let deviceName = UIDevice.current.name

        // 获取存储信息
        let fileManager = FileManager.default
        var totalStorage: Int64 = 0
        var freeStorage: Int64 = 0
        var usedStorage: Int64 = 0
        var storagePercentage: Double = 0

        if let path = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first?.path,
           let attributes = try? fileManager.attributesOfFileSystem(forPath: path),
           let freeSize = attributes[.systemFreeSize] as? NSNumber,
           let totalSize = attributes[.systemSize] as? NSNumber
        {
            totalStorage = totalSize.int64Value
            freeStorage = freeSize.int64Value
            usedStorage = totalStorage - freeStorage
            storagePercentage = Double(usedStorage) / Double(totalStorage) * 100
        }

        // 创建默认配置
        deviceInfoData = DeviceInfoWidgetData(
            deviceName: deviceName,
            totalStorage: totalStorage,
            usedStorage: usedStorage,
            freeStorage: freeStorage,
            storagePercentage: storagePercentage,
            background: .color(WidgetColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1)),
            fontName: "SF Pro",
            fontSize: 14,
            fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
            accentColor: WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
            showDeviceName: true,
            showOSInfo: true,
            showBatteryInfo: true,
            showStorageInfo: true,
            progressStyle: .bar,
            textAlignment: .leading,
            lastUpdated: Date()
        )

        // 更新UI状态
        backgroundSelection = .color(Color(red: 0.95, green: 0.95, blue: 0.97))
        fontSelection = FontSelection(
            font: .custom("SF Pro", size: 14),
            fontSize: 14,
            fontColor: .black
        )
        accentColor = Color(red: 0, green: 0.5, blue: 1)
    }

    // 更新小组件数据
    private func updateWidgetData() {
        guard var widgetData = deviceInfoData else { return }

        // 更新显示设置
        widgetData.showDeviceName = showDeviceName
        widgetData.showOSInfo = showOSInfo
        widgetData.showBatteryInfo = showBatteryInfo
        widgetData.showStorageInfo = showStorageInfo

        // 更新样式设置
        widgetData.progressStyle = progressStyle
        widgetData.textAlignment = textAlignment
        widgetData.accentColor = WidgetColor.fromColor(accentColor)

        // 更新字体设置
        widgetData.fontName = fontSelection.fontName
        widgetData.fontSize = fontSelection.fontSize
        widgetData.fontColor = WidgetColor.fromColor(fontSelection.fontColor)

        // 更新背景设置
        widgetData.background = backgroundSelection.toWidgetBackground()

        // 更新时间
        widgetData.lastUpdated = Date()

        // 更新存储信息
        let fileManager = FileManager.default
        if let path = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first?.path,
           let attributes = try? fileManager.attributesOfFileSystem(forPath: path),
           let freeSize = attributes[.systemFreeSize] as? NSNumber,
           let totalSize = attributes[.systemSize] as? NSNumber
        {
            widgetData.totalStorage = totalSize.int64Value
            widgetData.freeStorage = freeSize.int64Value
            widgetData.usedStorage = widgetData.totalStorage - widgetData.freeStorage
            widgetData.storagePercentage = Double(widgetData.usedStorage) / Double(widgetData.totalStorage) * 100
        }

        // 更新数据
        deviceInfoData = widgetData
    }

    // 保存小组件数据
    private func saveWidgetData() {
        guard let widgetData = deviceInfoData else { return }

        // 保存到AppGroup
        AppGroupDataManager.shared.save(widgetData, for: .deviceInfo, property: .config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        withAnimation {
            showSuccessToast = true
        }

        // 触发震动反馈
        #if os(iOS)
        let generator = UINotificationFeedbackGenerator()
        generator.notificationOccurred(.success)
        #endif
    }

    // 获取包内图片名称
    private func getPackageImageName(from background: WidgetBackground?) -> String? {
        if case let .packageImage(name) = background {
            return name
        }
        return nil
    }

    // 获取预览宽度
    private func getPreviewWidth() -> CGFloat {
        switch previewSize {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 364
        case .systemLarge:
            return 364
        default:
            return 170
        }
    }

    // 获取预览高度
    private func getPreviewHeight() -> CGFloat {
        switch previewSize {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 170
        case .systemLarge:
            return 382
        default:
            return 170
        }
    }
}

#Preview {
    NavigationStack {
        DeviceInfoWidgetConfigView()
            .environmentObject(ThemeManager.shared)
    }
}
