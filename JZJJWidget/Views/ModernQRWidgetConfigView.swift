//
//  ModernQRWidgetConfigView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/16.
//

import MyWidgetKit
import SwiftUI
import WidgetKit

/// 现代化二维码小组件设置界面
public struct ModernQRWidgetConfigView: View {
    // MARK: - 属性

    // 环境对象
    @EnvironmentObject var themeManager: ThemeManager
    @Environment(\.presentationMode) var presentationMode

    // 状态
    @State private var content: String = ""
    @State private var foregroundColor: Color = .black
    @State private var backgroundColor: Color = .white
    @State private var useImageBackground: Bool = false
    @State private var backgroundImage: UIImage?
    @State private var showImagePicker: Bool = false
    @State private var showColorPicker: Bool = false
    @State private var activeColorPicker: ColorPickerType = .foreground
    @State private var selectedFontIndex: Int = 0
    @State private var selectedBackgroundType: BackgroundType = .color
    @State private var qrWidgetData: QRWidgetViewData?
    @State private var showSuccessToast: Bool = false
    @State private var previewScale: CGFloat = 1.0
    @State private var isEditing: Bool = false

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // 数据模型
    enum ColorPickerType {
        case foreground, background
    }

    enum BackgroundType: String, CaseIterable, Hashable {
        case color, image, gallery, packageImage
    }

    // 颜色选项
    let colorOptions: [Color] = [
        .black, .white, .red, .orange, .yellow, .green, .blue, .purple,
        Color(hex: "FF5722"), Color(hex: "9C27B0"), Color(hex: "3F51B5"),
        Color(hex: "009688"), Color(hex: "607D8B"), Color(hex: "795548")
    ]

    // 初始化
    public init() {}

    // MARK: - 视图主体

    public var body: some View {
        ZStack {
            // 背景
            theme.colors.background
                .ignoresSafeArea()

            ScrollView {
                VStack(spacing: AppLayout.Spacing.large) {
                    // 预览区域
                    previewSection

                    // 内容设置
                    contentSection

                    // 样式设置
                    styleSection

                    // 保存按钮
                    saveButton
                }
                .padding(AppLayout.Spacing.large)
            }

            // 成功提示
            if showSuccessToast {
                VStack {
                    Spacer()

                    HStack {
                        Image(systemName: "checkmark.circle.fill")
                            .foregroundColor(.white)

                        Text("保存成功")
                            .foregroundColor(.white)
                            .font(theme.fonts.bodyMedium)
                    }
                    .padding()
                    .background(
                        Capsule()
                            .fill(theme.colors.success)
                            .shadow(color: theme.colors.shadow, radius: 8, x: 0, y: 4)
                    )
                    .padding(.bottom, 50)
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                }
                .zIndex(100)
            }
        }
        .navigationTitle("二维码小组件")
        .navigationBarItems(
            trailing: Button(action: {
                saveQRWidget()
            }) {
                Text("保存")
                    .fontWeight(.semibold)
                    .foregroundColor(theme.colors.accent)
            }
        )
        .sheet(isPresented: $showImagePicker) {
            ImagePicker(image: $backgroundImage)
        }
        .sheet(isPresented: $showColorPicker) {
            colorPickerSheet
        }
        .onAppear {
            loadExistingData()
        }
        .onChange(of: foregroundColor) { newValue in
            updateQRCode()
        }
        .onChange(of: content) { _ in
            updateQRCode()
        }
        .onChange(of: backgroundColor) { newValue in
            selectedBackgroundType = .color
            qrWidgetData?.background = WidgetBackground.color(WidgetColor.fromColor(newValue))
        }
        .onChange(of: backgroundImage) { newValue in
            selectedBackgroundType = .image
            if let uiImageData = newValue?.pngData() {
                qrWidgetData?.background = WidgetBackground.imageData(uiImageData)
            }
            updateQRCode()
        }
    }

    // MARK: - 子视图

    // 预览区域
    private var previewSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            Text("预览")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)
                .frame(maxWidth: .infinity, alignment: .leading)

            if let qrWidgetData = qrWidgetData {
                ThemedWidgetPreviewContainer(family: .systemSmall, theme: theme) {
                    QRWidgetView(data: qrWidgetData)
                }
                .scaleEffect(previewScale)
                .gesture(
                    MagnificationGesture()
                        .onChanged { value in
                            previewScale = value.magnitude
                        }
                        .onEnded { _ in
                            withAnimation {
                                previewScale = 1.0
                            }
                        }
                )
                .frame(height: 220)
            } else {
                // 加载中或无数据状态
                ThemedWidgetPreviewContainer(family: .systemSmall, theme: theme) {
                    VStack {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: theme.colors.accent))

                        Text("加载中...")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.subtext)
                            .padding(.top, AppLayout.Spacing.small)
                    }
                }
                .frame(height: 220)
            }
        }
        .padding(AppLayout.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                .fill(theme.colors.surface)
                .shadow(color: theme.colors.shadow.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // 内容设置
    private var contentSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            Text("内容设置")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)
                .frame(maxWidth: .infinity, alignment: .leading)

            VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                Text("二维码内容")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                TextEditor(text: $content)
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
                    .frame(minHeight: 100)
                    .padding(AppLayout.Spacing.small)
                    .background(
                        RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                            .fill(theme.colors.surfaceVariant)
                    )
                    .overlay(
                        RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                            .stroke(isEditing ? theme.colors.accent : theme.colors.border, lineWidth: AppLayout.Border.thin)
                    )
                    .onTapGesture {
                        isEditing = true
                    }
                    .onSubmit {
                        isEditing = false
                    }
            }
        }
        .padding(AppLayout.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                .fill(theme.colors.surface)
                .shadow(color: theme.colors.shadow.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // 样式设置
    private var styleSection: some View {
        VStack(spacing: AppLayout.Spacing.medium) {
            Text("样式设置")
                .font(theme.fonts.headlineMedium)
                .foregroundColor(theme.colors.text)
                .frame(maxWidth: .infinity, alignment: .leading)

            // 前景色设置
            VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                Text("二维码颜色")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: AppLayout.Spacing.medium) {
                        // 自定义颜色选择器
                        Button(action: {
                            activeColorPicker = .foreground
                            showColorPicker = true
                            hapticFeedback(style: .light)
                        }) {
                            ZStack {
                                Circle()
                                    .fill(foregroundColor)
                                    .frame(width: 36, height: 36)

                                Circle()
                                    .stroke(theme.colors.border, lineWidth: AppLayout.Border.thin)
                                    .frame(width: 36, height: 36)

                                Image(systemName: "plus")
                                    .font(.system(size: 12, weight: .bold))
                                    .foregroundColor(foregroundColor.isBright() ? .black : .white)
                            }
                        }

                        // 预设颜色选项
                        ForEach(colorOptions, id: \.self) { color in
                            Button(action: {
                                foregroundColor = color
                                hapticFeedback(style: .light)
                            }) {
                                ZStack {
                                    Circle()
                                        .fill(color)
                                        .frame(width: 36, height: 36)

                                    if foregroundColor == color {
                                        Circle()
                                            .stroke(theme.colors.accent, lineWidth: 2)
                                            .frame(width: 36, height: 36)

                                        Image(systemName: "checkmark")
                                            .font(.system(size: 12, weight: .bold))
                                            .foregroundColor(color.isBright() ? .black : .white)
                                    }
                                }
                            }
                        }
                    }
                    .padding(.vertical, AppLayout.Spacing.small)
                }
            }

            // 背景设置
            VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                Text("背景设置")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.subtext)

                HStack(spacing: AppLayout.Spacing.medium) {
                    // 颜色背景
                    Button(action: {
                        selectedBackgroundType = .color
                        hapticFeedback(style: .light)
                    }) {
                        VStack(spacing: AppLayout.Spacing.tiny) {
                            ZStack {
                                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                    .fill(backgroundColor)
                                    .frame(width: 60, height: 60)

                                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                    .stroke(selectedBackgroundType == .color ? theme.colors.accent : theme.colors.border, lineWidth: selectedBackgroundType == .color ? 2 : 1)
                                    .frame(width: 60, height: 60)

                                if selectedBackgroundType == .color {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 16, weight: .bold))
                                        .foregroundColor(backgroundColor.isBright() ? .black : .white)
                                }
                            }

                            Text("纯色")
                                .font(theme.fonts.captionLarge)
                                .foregroundColor(selectedBackgroundType == .color ? theme.colors.accent : theme.colors.text)
                        }
                    }

                    // 图片背景
                    Button(action: {
                        selectedBackgroundType = .image
                        showImagePicker = true
                        hapticFeedback(style: .light)
                    }) {
                        VStack(spacing: AppLayout.Spacing.tiny) {
                            ZStack {
                                if let image = backgroundImage {
                                    Image(uiImage: image)
                                        .resizable()
                                        .scaledToFill()
                                        .frame(width: 60, height: 60)
                                        .clipShape(RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium))
                                } else {
                                    RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                        .fill(theme.colors.surfaceVariant)
                                        .frame(width: 60, height: 60)

                                    Image(systemName: "photo")
                                        .font(.system(size: 24))
                                        .foregroundColor(theme.colors.subtext)
                                }

                                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                    .stroke(selectedBackgroundType == .image ? theme.colors.accent : theme.colors.border, lineWidth: selectedBackgroundType == .image ? 2 : 1)
                                    .frame(width: 60, height: 60)

                                if selectedBackgroundType == .image && backgroundImage != nil {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 16, weight: .bold))
                                        .foregroundColor(.white)
                                }
                            }

                            Text("图片")
                                .font(theme.fonts.captionLarge)
                                .foregroundColor(selectedBackgroundType == .image ? theme.colors.accent : theme.colors.text)
                        }
                    }

                    // 图库背景
                    Button(action: {
                        selectedBackgroundType = .gallery
                        hapticFeedback(style: .light)
                    }) {
                        VStack(spacing: AppLayout.Spacing.tiny) {
                            ZStack {
                                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                    .fill(theme.colors.surfaceVariant)
                                    .frame(width: 60, height: 60)

                                Image(systemName: "square.grid.2x2")
                                    .font(.system(size: 24))
                                    .foregroundColor(theme.colors.subtext)

                                RoundedRectangle(cornerRadius: AppLayout.CornerRadius.medium)
                                    .stroke(selectedBackgroundType == .gallery ? theme.colors.accent : theme.colors.border, lineWidth: selectedBackgroundType == .gallery ? 2 : 1)
                                    .frame(width: 60, height: 60)

                                if selectedBackgroundType == .gallery {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 16, weight: .bold))
                                        .foregroundColor(theme.colors.accent)
                                }
                            }

                            Text("图库")
                                .font(theme.fonts.captionLarge)
                                .foregroundColor(selectedBackgroundType == .gallery ? theme.colors.accent : theme.colors.text)
                        }
                    }

                    Spacer()
                }

                // 背景颜色选择器
                if selectedBackgroundType == .color {
                    VStack(alignment: .leading, spacing: AppLayout.Spacing.small) {
                        Text("背景颜色")
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.subtext)
                            .padding(.top, AppLayout.Spacing.small)

                        ScrollView(.horizontal, showsIndicators: false) {
                            HStack(spacing: AppLayout.Spacing.medium) {
                                // 自定义颜色选择器
                                Button(action: {
                                    activeColorPicker = .background
                                    showColorPicker = true
                                    hapticFeedback(style: .light)
                                }) {
                                    ZStack {
                                        Circle()
                                            .fill(backgroundColor)
                                            .frame(width: 36, height: 36)

                                        Circle()
                                            .stroke(theme.colors.border, lineWidth: AppLayout.Border.thin)
                                            .frame(width: 36, height: 36)

                                        Image(systemName: "plus")
                                            .font(.system(size: 12, weight: .bold))
                                            .foregroundColor(backgroundColor.isBright() ? .black : .white)
                                    }
                                }

                                // 预设颜色选项
                                ForEach(colorOptions, id: \.self) { color in
                                    Button(action: {
                                        backgroundColor = color
                                        hapticFeedback(style: .light)
                                    }) {
                                        ZStack {
                                            Circle()
                                                .fill(color)
                                                .frame(width: 36, height: 36)

                                            if backgroundColor == color {
                                                Circle()
                                                    .stroke(theme.colors.accent, lineWidth: 2)
                                                    .frame(width: 36, height: 36)

                                                Image(systemName: "checkmark")
                                                    .font(.system(size: 12, weight: .bold))
                                                    .foregroundColor(color.isBright() ? .black : .white)
                                            }
                                        }
                                    }
                                }
                            }
                            .padding(.vertical, AppLayout.Spacing.small)
                        }
                    }
                }

                // 图片背景选项
                if selectedBackgroundType == .image && backgroundImage == nil {
                    Button(action: {
                        showImagePicker = true
                        hapticFeedback(style: .light)
                    }) {
                        HStack {
                            Image(systemName: "photo")
                                .font(.system(size: AppLayout.IconSize.medium))

                            Text("选择背景图片")
                                .font(theme.fonts.bodyMedium)
                        }
                        .foregroundColor(.white)
                        .padding(.horizontal, AppLayout.Spacing.medium)
                        .padding(.vertical, AppLayout.Spacing.small)
                        .background(
                            Capsule()
                                .fill(theme.colors.accent)
                        )
                        .shadow(color: theme.colors.accent.opacity(0.3), radius: 4, x: 0, y: 2)
                    }
                    .padding(.top, AppLayout.Spacing.small)
                }
            }
        }
        .padding(AppLayout.Spacing.medium)
        .background(
            RoundedRectangle(cornerRadius: AppLayout.CornerRadius.large)
                .fill(theme.colors.surface)
                .shadow(color: theme.colors.shadow.opacity(0.1), radius: 8, x: 0, y: 4)
        )
    }

    // 保存按钮
    private var saveButton: some View {
        Button(action: {
            saveQRWidget()
        }) {
            HStack {
                Spacer()

                Text("保存并应用")
                    .font(theme.fonts.bodyLarge)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)

                Spacer()
            }
            .padding()
            .background(
                Capsule()
                    .fill(theme.colors.accent)
                    .shadow(color: theme.colors.accent.opacity(0.3), radius: 8, x: 0, y: 4)
            )
        }
        .padding(.top, AppLayout.Spacing.medium)
    }

    // 颜色选择器弹窗
    private var colorPickerSheet: some View {
        NavigationView {
            VStack {
                ColorPicker(
                    activeColorPicker == .foreground ? "选择二维码颜色" : "选择背景颜色",
                    selection: activeColorPicker == .foreground ? $foregroundColor : $backgroundColor,
                    supportsOpacity: true
                )
                .padding()

                Spacer()
            }
            .navigationBarTitle(activeColorPicker == .foreground ? "二维码颜色" : "背景颜色", displayMode: .inline)
            .navigationBarItems(
                trailing: Button(action: {
                    showColorPicker = false
                }) {
                    Text("完成")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.colors.accent)
                }
            )
        }
    }

    // MARK: - 辅助方法

    // 加载现有数据
    private func loadExistingData() {
        if let config = AppGroupDataManager.shared.read(QRWidgetViewData.self, for: .qrImage, property: .config) {
            qrWidgetData = config
            foregroundColor = config.foregroundColor.toColor()
            content = config.content

            if case let .color(widgetColor) = config.background {
                backgroundColor = widgetColor.toColor()
                selectedBackgroundType = .color
                useImageBackground = false
            } else if case let .imageFile(fileName) = config.background {
                selectedBackgroundType = .image
                useImageBackground = true

                // 尝试加载背景图片
                if let imageData = AppGroupDataManager.shared.readData(for: .qrImage, property: .backgroundImage) {
                    backgroundImage = UIImage(data: imageData)
                }
            } else if case let .packageImage(name) = config.background {
                // 加载内置背景图片
                selectedBackgroundType = .packageImage
                useImageBackground = true
            }
        } else {
            // 创建默认数据
            qrWidgetData = QRWidgetViewData(
                content: content,
                foreground: WidgetBackground.color(WidgetColor.fromColor(foregroundColor)),
                background: WidgetBackground.color(WidgetColor.fromColor(backgroundColor)),
                foregroundColor: WidgetColor.fromColor(.black)
            )

            // 生成默认二维码
            updateQRCode()
        }
    }

    // 更新二维码
    private func updateQRCode() {
        if let uiImageData = UIImage.generateQRCode(from: content, color: foregroundColor, backgroundColor: .clear)?.pngData() {
            qrWidgetData?.foreground = WidgetBackground.imageData(uiImageData)
            qrWidgetData?.foregroundColor = WidgetColor.fromColor(foregroundColor)
        }
    }

    // 保存二维码小组件
    private func saveQRWidget() {
        guard var qrWidgetData = qrWidgetData else { return }

        // 保存二维码图片
        if let uiImage = UIImage.generateQRCode(from: content, color: foregroundColor, backgroundColor: .clear),
           let pngData = uiImage.pngData() {
            AppGroupDataManager.shared.saveAuto(pngData, for: .qrImage, property: .image)
        }

        // 保存背景图片
        if selectedBackgroundType == .image, let pngData = backgroundImage?.compressForWidgetOptimized() {
            AppGroupDataManager.shared.saveAuto(pngData, for: .qrImage, property: .backgroundImage)
            qrWidgetData.background = .imageFile(AppGroupDataManager.shared.fileName(for: .qrImage, property: .backgroundImage))
        } else if selectedBackgroundType == .packageImage {
            // 使用内置背景图片
            // 注意：这里需要根据实际情况设置正确的内置背景图片名称
            qrWidgetData.background = .packageImage("background1")
        } else {
            qrWidgetData.background = .color(WidgetColor.fromColor(backgroundColor))
        }

        // 更新内容
        qrWidgetData.content = content

        // 保存配置
        AppGroupDataManager.shared.save(qrWidgetData, for: .qrImage, property: .config)

        // 刷新小组件
        WidgetCenter.shared.reloadAllTimelines()

        // 显示成功提示
        withAnimation {
            showSuccessToast = true
        }

        // 触觉反馈
        hapticFeedback(style: .medium)

        // 3秒后隐藏提示
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) {
            withAnimation {
                showSuccessToast = false
            }
        }
    }

    // 触觉反馈
    private func hapticFeedback(style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let generator = UIImpactFeedbackGenerator(style: style)
        generator.impactOccurred()
    }
}

// MARK: - 扩展

// 颜色亮度检测
extension Color {
    func isBright() -> Bool {
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0

        UIColor(self).getRed(&r, green: &g, blue: &b, alpha: &a)

        // 计算亮度 (0.299*R + 0.587*G + 0.114*B)
        let brightness = (0.299 * r + 0.587 * g + 0.114 * b)

        return brightness > 0.6
    }
}

#Preview {
    NavigationView {
        ModernQRWidgetConfigView()
            .environmentObject(ThemeManager.shared)
    }
}
