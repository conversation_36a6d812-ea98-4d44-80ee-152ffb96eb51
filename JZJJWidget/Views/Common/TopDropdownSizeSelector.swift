//
//  TopDropdownSizeSelector.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/25.
//

import SwiftUI
import WidgetKit
import MyWidgetKit

/// 从屏幕顶部下拉的尺寸选择器
struct TopDropdownSizeSelector: View {
    // MARK: - 属性

    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // 绑定的尺寸
    @Binding var selectedSize: WidgetFamily

    // 下拉菜单管理器
    @StateObject private var dropdownManager = DropdownMenuManager.shared

    // 状态变量
    private var dropdownMenuID: String

    // 支持的尺寸
    var supportedSizes: [WidgetFamily]

    // 下拉菜单Y轴位置
    var menuYPosition: CGFloat

    // 菜单宽度
    var menuWidth: CGFloat

    // MARK: - 初始化方法

    init(
        selectedSize: Binding<WidgetFamily>,
        supportedSizes: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge],
        menuYPosition: CGFloat = 60,
        menuWidth: CGFloat = 180,
        menuID: String? = nil
    ) {
        self._selectedSize = selectedSize
        self.supportedSizes = supportedSizes
        self.menuYPosition = menuYPosition
        self.menuWidth = menuWidth
        self.dropdownMenuID = menuID ?? "size-selector-\(UUID().uuidString)"
    }

    // MARK: - 视图主体

    var body: some View {
        ZStack {
            // 下拉菜单内容
            if dropdownManager.isMenuOpen(id: dropdownMenuID) {
                // 半透明背景遮罩，点击时关闭下拉菜单
                Color.black.opacity(0.1)
                    .ignoresSafeArea()
                    .onTapGesture {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            dropdownManager.closeMenu(id: dropdownMenuID)
                        }
                    }
                    .zIndex(900)

                // 下拉菜单内容
                VStack(spacing: 0) {
                    ForEach(supportedSizes, id: \.self) { size in
                        Button(action: {
                            selectedSize = size
                            // 触觉反馈
                            #if os(iOS)
                            let generator = UIImpactFeedbackGenerator(style: .light)
                            generator.impactOccurred()
                            #endif
                            // 关闭菜单
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                dropdownManager.closeMenu(id: dropdownMenuID)
                            }
                        }) {
                            HStack {
                                Image(systemName: sizeIcon(for: size))
                                    .font(.system(size: 16))
                                    .foregroundColor(theme.colors.accent)
                                    .frame(width: 24, height: 24)

                                Text(sizeName(for: size))
                                    .font(theme.fonts.bodyMedium)
                                    .foregroundColor(theme.colors.text)

                                Spacer()

                                if size == selectedSize {
                                    Image(systemName: "checkmark")
                                        .font(.system(size: 14, weight: .semibold))
                                        .foregroundColor(theme.colors.accent)
                                }
                            }
                            .padding(.horizontal, 16)
                            .padding(.vertical, 12)
                            .background(
                                size == selectedSize ?
                                    theme.colors.accent.opacity(0.1) :
                                    Color.clear
                            )
                            .contentShape(Rectangle())
                        }
                        .buttonStyle(PlainButtonStyle())

                        if size != supportedSizes.last {
                            Divider()
                                .padding(.horizontal, 8)
                        }
                    }
                }
                .background(theme.colors.surface)
                .cornerRadius(12)
                .shadow(color: theme.colors.shadow.opacity(0.15), radius: 10, x: 0, y: 5)
                .frame(width: menuWidth)
                .position(x: getScreenWidth() / 2, y: menuYPosition)
                .transition(.opacity.combined(with: .move(edge: .top)))
                .zIndex(1000)
            }
        }
        .onAppear {
            // 确保初始状态为关闭
            dropdownManager.closeMenu(id: dropdownMenuID)
        }
    }

    // MARK: - 视图修饰符

    /// 允许父视图获取对此视图的引用
    func onAppear(_ action: @escaping (Self) -> Void) -> some View {
        var copy = self
        return self.onAppear { action(copy) }
    }

    // MARK: - 公共方法

    /// 显示下拉菜单
    func showMenu() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            dropdownManager.openMenu(id: dropdownMenuID)
        }
    }

    /// 隐藏下拉菜单
    func hideMenu() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            dropdownManager.closeMenu(id: dropdownMenuID)
        }
    }

    /// 切换下拉菜单显示状态
    func toggleMenu() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            dropdownManager.toggleMenu(id: dropdownMenuID)
        }
    }

    /// 检查下拉菜单是否显示
    func isMenuOpen() -> Bool {
        return dropdownManager.isMenuOpen(id: dropdownMenuID)
    }

    // MARK: - 辅助方法

    /// 获取尺寸对应的图标
    private func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        case .systemExtraLarge:
            return "square.grid.2x2"
        @unknown default:
            return "square"
        }
    }

    /// 获取尺寸对应的名称
    private func sizeName(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }

    /// 获取屏幕宽度
    private func getScreenWidth() -> CGFloat {
        #if os(iOS)
        return UIScreen.main.bounds.width
        #else
        return 390.0 // 默认宽度
        #endif
    }
}

// MARK: - 预览
#Preview {
    ZStack {
        Color.gray.opacity(0.2).ignoresSafeArea()

        VStack {
            Text("测试下拉菜单")
                .padding()

            Button("显示菜单") {
                // 在预览中无法直接调用方法
            }
        }

        TopDropdownSizeSelector(selectedSize: .constant(.systemMedium))
    }
    .environmentObject(ThemeManager.shared)
}
