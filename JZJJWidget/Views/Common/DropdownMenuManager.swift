import SwiftUI

/// 下拉菜单管理器 - 用于管理全局下拉菜单状态
class DropdownMenuManager: ObservableObject {
    /// 单例
    static let shared = DropdownMenuManager()

    /// 当前活跃的下拉菜单 ID
    @Published var activeMenuID: String? = nil

    /// 关闭所有下拉菜单
    func closeAllMenus() {
        activeMenuID = nil
    }

    /// 切换指定 ID 的下拉菜单状态
    func toggleMenu(id: String) {
        if activeMenuID == id {
            activeMenuID = nil
        } else {
            activeMenuID = id
        }
    }

    /// 打开指定 ID 的下拉菜单
    func openMenu(id: String) {
        activeMenuID = id
    }

    /// 关闭指定 ID 的下拉菜单
    func closeMenu(id: String) {
        if activeMenuID == id {
            activeMenuID = nil
        }
    }

    /// 检查指定 ID 的下拉菜单是否打开
    func isMenuOpen(id: String) -> Bool {
        return activeMenuID == id
    }
}

/// 下拉菜单环境键
private struct DropdownMenuManagerKey: EnvironmentKey {
    static let defaultValue = DropdownMenuManager.shared
}

/// 下拉菜单环境值扩展
extension EnvironmentValues {
    var dropdownMenuManager: DropdownMenuManager {
        get { self[DropdownMenuManagerKey.self] }
        set { self[DropdownMenuManagerKey.self] = newValue }
    }
}

/// 下拉菜单修饰符 - 用于添加全局点击手势关闭下拉菜单
struct DropdownMenuModifier: ViewModifier {
    // 使用 StateObject 而不是 EnvironmentObject，确保即使没有祖先提供也能创建
    @StateObject private var localDropdownManager = DropdownMenuManager.shared

    func body(content: Content) -> some View {
        ZStack {
            // 如果有活跃的菜单，添加一个透明的全屏覆盖层来捕获点击事件
            if localDropdownManager.activeMenuID != nil {
                Color.clear
                    .contentShape(Rectangle())
                    .onTapGesture {
                        // 点击任何地方都会关闭所有下拉菜单
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                            localDropdownManager.closeAllMenus()
                        }
                    }
                    .zIndex(900) // 确保覆盖层在内容之上，但在下拉菜单之下
            }

            content
        }
        .environmentObject(localDropdownManager) // 提供环境对象给子视图
    }
}

/// 视图扩展 - 添加下拉菜单修饰符
extension View {
    func withDropdownMenuSupport() -> some View {
        modifier(DropdownMenuModifier())
    }
}
