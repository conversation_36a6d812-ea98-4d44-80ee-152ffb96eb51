//
//  SizeSelectorPopoverView.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/20.
//

import SwiftUI
import WidgetKit
import MyWidgetKit

/// 尺寸选择器 Popover 视图
struct SizeSelectorPopoverView: View {
    // MARK: - 属性

    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // 绑定的尺寸
    @Binding var selectedSize: WidgetFamily

    // 关闭回调
    var onDismiss: (() -> Void)?

    // 支持的尺寸
    let supportedSizes: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]

    // MARK: - 视图主体

    var body: some View {
        VStack(spacing: 0) {
            ForEach(supportedSizes, id: \.self) { size in
                Button(action: {
                    selectedSize = size
                    onDismiss?()
                }) {
                    HStack(spacing: 12) {
                        // 图标
                        Image(systemName: sizeIcon(for: size))
                            .font(.system(size: 16))
                            .foregroundColor(theme.colors.accent)
                            .frame(width: 24, height: 24)

                        // 文本
                        Text(sizeName(for: size))
                            .font(theme.fonts.bodyMedium)
                            .foregroundColor(theme.colors.text)

                        Spacer()

                        // 选中标记
                        if size == selectedSize {
                            Image(systemName: "checkmark")
                                .font(.system(size: 14, weight: .semibold))
                                .foregroundColor(theme.colors.accent)
                        }
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
                .background(
                    size == selectedSize ?
                        theme.colors.accent.opacity(0.1) :
                        Color.clear
                )

                // 分隔线
                if size != supportedSizes.last {
                    Divider()
                        .padding(.horizontal, 8)
                }
            }
        }
        .background(theme.colors.surface)
        .cornerRadius(12)
        .shadow(color: theme.colors.shadow.opacity(0.15), radius: 10, x: 0, y: 5)
        .frame(width: 180)
    }

    // MARK: - 辅助方法

    /// 获取尺寸对应的图标
    private func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        case .systemExtraLarge:
            return "square.grid.2x2"
        @unknown default:
            return "square"
        }
    }

    /// 获取尺寸对应的名称
    private func sizeName(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }
}

// MARK: - 预览

#Preview {
    SizeSelectorPopoverView(selectedSize: .constant(.systemMedium))
        .environmentObject(ThemeManager.shared)
        .frame(width: 180, height: 150)
        .previewLayout(.sizeThatFits)
}
