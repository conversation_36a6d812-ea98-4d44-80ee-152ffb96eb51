# 尺寸选择器组件使用指南

本文档介绍如何使用从屏幕顶部下拉的尺寸选择器组件。

## 组件概述

该组件包含两个主要部分：

1. `TopDropdownSizeSelector` - 从屏幕顶部下拉的尺寸选择菜单
2. `SizeSelectorButton` - 用于在导航栏中显示的按钮，点击后触发下拉菜单

这两个组件配合使用，可以实现符合iOS设计规范的尺寸选择功能。

## 使用方法

### 基本用法

```swift
import SwiftUI
import WidgetKit
import MyWidgetKit

struct YourConfigView: View {
    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    
    // 状态变量
    @State private var previewSize: WidgetFamily = .systemSmall
    @State private var dropdownMenuID = "your-unique-menu-id"
    
    // 下拉菜单引用
    @State private var sizeSelector: TopDropdownSizeSelector?
    
    var body: some View {
        ZStack {
            // 主内容
            VStack {
                // 你的视图内容
            }
            
            // 尺寸选择器下拉菜单
            TopDropdownSizeSelector(
                selectedSize: $previewSize,
                supportedSizes: [.systemSmall, .systemMedium, .systemLarge],
                menuYPosition: 60,
                menuID: dropdownMenuID
            )
            .onAppear { sizeSelector = $0 }
        }
        .navigationTitle("")
        .toolbar {
            ToolbarItem(placement: .principal) {
                // 导航栏中的尺寸选择按钮
                SizeSelectorButton(
                    selectedSize: previewSize,
                    onTap: {
                        sizeSelector?.toggleMenu()
                    },
                    menuID: dropdownMenuID
                )
            }
        }
    }
}
```

### 参数说明

#### TopDropdownSizeSelector

| 参数 | 类型 | 说明 |
|------|------|------|
| selectedSize | Binding<WidgetFamily> | 当前选中的尺寸（绑定值） |
| supportedSizes | [WidgetFamily] | 支持的尺寸列表，默认为 [.systemSmall, .systemMedium, .systemLarge] |
| menuYPosition | CGFloat | 菜单在Y轴上的位置，默认为60 |
| menuWidth | CGFloat | 菜单宽度，默认为180 |
| menuID | String? | 菜单唯一标识符，如果不提供则自动生成 |

#### SizeSelectorButton

| 参数 | 类型 | 说明 |
|------|------|------|
| selectedSize | WidgetFamily | 当前选中的尺寸 |
| onTap | () -> Void | 点击按钮时的回调 |
| menuID | String | 下拉菜单的唯一标识符，用于状态管理 |

### 公共方法

TopDropdownSizeSelector 提供以下方法用于控制下拉菜单：

```swift
// 显示下拉菜单
sizeSelector?.showMenu()

// 隐藏下拉菜单
sizeSelector?.hideMenu()

// 切换下拉菜单显示状态
sizeSelector?.toggleMenu()

// 检查下拉菜单是否显示
let isOpen = sizeSelector?.isMenuOpen() ?? false
```

## 示例

### 在配置视图中使用

```swift
struct ConfigView: View {
    @EnvironmentObject private var themeManager: ThemeManager
    @State private var previewSize: WidgetFamily = .systemMedium
    @State private var dropdownMenuID = "config-size-selector"
    @State private var sizeSelector: TopDropdownSizeSelector?
    
    var body: some View {
        ZStack {
            // 主内容
            ScrollView {
                VStack(spacing: 20) {
                    // 预览区域
                    PreviewSection(size: previewSize)
                    
                    // 配置选项
                    ConfigOptions()
                }
                .padding()
            }
            
            // 尺寸选择器
            TopDropdownSizeSelector(
                selectedSize: $previewSize,
                menuID: dropdownMenuID
            )
            .onAppear { sizeSelector = $0 }
        }
        .navigationTitle("")
        .toolbar {
            ToolbarItem(placement: .principal) {
                SizeSelectorButton(
                    selectedSize: previewSize,
                    onTap: { sizeSelector?.toggleMenu() },
                    menuID: dropdownMenuID
                )
            }
            
            ToolbarItem(placement: .navigationBarTrailing) {
                Button("保存") {
                    // 保存操作
                }
            }
        }
    }
}
```

## 注意事项

1. 确保在使用这些组件的视图中提供 `ThemeManager` 环境对象
2. 为每个下拉菜单指定唯一的 `menuID`，避免冲突
3. 使用 `onAppear { sizeSelector = $0 }` 获取对下拉菜单的引用，以便控制其显示和隐藏
