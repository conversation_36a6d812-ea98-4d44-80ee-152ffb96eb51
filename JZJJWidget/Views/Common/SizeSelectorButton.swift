//
//  SizeSelectorButton.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/25.
//

import SwiftUI
import WidgetKit
import MyWidgetKit

/// 尺寸选择器按钮 - 用于导航栏标题位置
struct SizeSelectorButton: View {
    // MARK: - 属性

    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    @StateObject private var dropdownManager = DropdownMenuManager.shared

    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }

    // 当前选中的尺寸
    var selectedSize: WidgetFamily

    // 点击回调
    var onTap: () -> Void

    // 下拉菜单ID
    var menuID: String

    // 计算属性：是否展开
    private var isExpanded: Bool {
        dropdownManager.isMenuOpen(id: menuID)
    }

    // MARK: - 视图主体

    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 4) {
                Image(systemName: sizeIcon(for: selectedSize))
                    .font(.system(size: 14))
                Text(sizeText(for: selectedSize))
                    .font(theme.fonts.headlineSmall)
                Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                    .font(.system(size: 12))
                    .foregroundColor(theme.colors.accent.opacity(0.8))
            }
            .foregroundColor(theme.colors.text)
            .padding(.vertical, 6)
            .padding(.horizontal, 10)
        }
    }

    // MARK: - 辅助方法

    // 获取尺寸文本
    private func sizeText(for size: WidgetFamily) -> String {
        switch size {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大尺寸"
        @unknown default:
            return "未知尺寸"
        }
    }

    // 获取尺寸图标
    private func sizeIcon(for size: WidgetFamily) -> String {
        switch size {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        case .systemExtraLarge:
            return "square.grid.2x2"
        @unknown default:
            return "square"
        }
    }
}

// MARK: - 预览
#Preview {
    VStack(spacing: 20) {
        SizeSelectorButton(
            selectedSize: .systemSmall,
            onTap: {},
            menuID: "test-menu-1"
        )

        SizeSelectorButton(
            selectedSize: .systemMedium,
            onTap: {
                DropdownMenuManager.shared.openMenu(id: "test-menu-2")
            },
            menuID: "test-menu-2"
        )
    }
    .padding()
    .environmentObject(ThemeManager.shared)
}
