import SwiftUI
import MyWidgetKit

/// 字体选择视图
struct FontSelectionView: View {
    /// 字体选择绑定
    @Binding var fontSelection: FontSelection
    
    /// 主题
    let theme: AppTheme
    
    /// 字体变更回调
    var onFontChange: (() -> Void)?
    
    /// 支持的字体列表
    private let fontOptions: [String] = [
        "SF Pro", "Arial", "Helvetica", "Times New Roman", 
        "Georgia", "Courier New", "Verdana"
    ]
    
    /// 支持的字号列表
    private let fontSizes: [CGFloat] = [12, 14, 16, 18, 20, 24, 28, 32]
    
    /// 支持的颜色列表
    private let fontColors: [Color] = [
        .black, .white, .gray, .red, .blue, .green, .orange, .purple
    ]
    
    /// 当前选中的字体索引
    @State private var selectedFontIndex: Int = 0
    
    /// 当前选中的字号索引
    @State private var selectedFontSizeIndex: Int = 1
    
    /// 当前选中的颜色
    @State private var selectedColor: Color = .black
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("字体设置")
                .font(theme.fonts.headlineSmall)
                .foregroundColor(theme.colors.text)
            
            // 字体选择
            VStack(alignment: .leading, spacing: 8) {
                Text("字体")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
                
                Picker("选择字体", selection: $selectedFontIndex) {
                    ForEach(0..<fontOptions.count, id: \.self) { index in
                        Text(fontOptions[index])
                            .tag(index)
                    }
                }
                .pickerStyle(MenuPickerStyle())
                .onChange(of: selectedFontIndex) { _ in
                    updateFontSelection()
                }
            }
            
            // 字号选择
            VStack(alignment: .leading, spacing: 8) {
                Text("字号")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
                
                Picker("选择字号", selection: $selectedFontSizeIndex) {
                    ForEach(0..<fontSizes.count, id: \.self) { index in
                        Text("\(Int(fontSizes[index]))")
                            .tag(index)
                    }
                }
                .pickerStyle(SegmentedPickerStyle())
                .onChange(of: selectedFontSizeIndex) { _ in
                    updateFontSelection()
                }
            }
            
            // 颜色选择
            VStack(alignment: .leading, spacing: 8) {
                Text("颜色")
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(theme.colors.text)
                
                ColorPicker("选择颜色", selection: $selectedColor)
                    .onChange(of: selectedColor) { _ in
                        updateFontSelection()
                    }
            }
        }
        .padding()
        .background(theme.colors.surface)
        .cornerRadius(12)
        .onAppear {
            // 初始化选择状态
            initializeSelectionState()
        }
    }
    
    /// 初始化选择状态
    private func initializeSelectionState() {
        // 尝试找到匹配的字体
        if let fontIndex = fontOptions.firstIndex(where: { $0 == fontSelection.fontName }) {
            selectedFontIndex = fontIndex
        }
        
        // 尝试找到匹配的字号
        if let sizeIndex = fontSizes.firstIndex(where: { $0 == fontSelection.fontSize }) {
            selectedFontSizeIndex = sizeIndex
        } else {
            // 找到最接近的字号
            let closest = fontSizes.enumerated().min(by: { abs($0.1 - fontSelection.fontSize) < abs($1.1 - fontSelection.fontSize) })
            if let index = closest?.offset {
                selectedFontSizeIndex = index
            }
        }
        
        // 设置颜色
        selectedColor = fontSelection.fontColor
    }
    
    /// 更新字体选择
    private func updateFontSelection() {
        let fontName = fontOptions[selectedFontIndex]
        let fontSize = fontSizes[selectedFontSizeIndex]
        let font = Font.custom(fontName, size: fontSize)
        
        // 创建新的 FontSelection 实例
        fontSelection = FontSelection(
            font: font,
            fontSize: fontSize,
            fontColor: selectedColor
        )
        
        // 调用回调
        onFontChange?()
    }
}

#Preview {
    FontSelectionView(
        fontSelection: .constant(FontSelection(
            font: .system(size: 16),
            fontSize: 16,
            fontColor: .black
        )),
        theme: ThemeManager.shared.currentTheme
    )
}
