//
//  PopoverButton.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/20.
//

import SwiftUI
import UIKit
import MyWidgetKit

/// 用于在 SwiftUI 中显示 popover 的按钮
struct PopoverButton<Content: View>: View {
    // MARK: - 属性
    
    // 环境对象
    @EnvironmentObject private var themeManager: ThemeManager
    
    // 获取当前主题
    private var theme: AppTheme {
        themeManager.currentTheme
    }
    
    // 按钮标题
    var title: String
    
    // 按钮图标
    var icon: String?
    
    // 是否显示下拉箭头
    var showArrow: Bool = true
    
    // popover 内容
    var content: () -> Content
    
    // popover 尺寸
    var contentSize: CGSize
    
    // 状态变量
    @State private var showPopover = false
    @State private var buttonRect: CGRect = .zero
    
    // MARK: - 视图主体
    
    var body: some View {
        Button(action: {
            showPopover = true
        }) {
            HStack(spacing: 4) {
                // 图标
                if let icon = icon {
                    Image(systemName: icon)
                        .font(.system(size: 14))
                }
                
                // 标题
                Text(title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                // 下拉箭头
                if showArrow {
                    Image(systemName: "chevron.down")
                        .font(.system(size: 12))
                        .foregroundColor(theme.colors.accent.opacity(0.8))
                }
            }
            .foregroundColor(theme.colors.text)
            .padding(.vertical, 6)
            .padding(.horizontal, 10)
            .background(
                GeometryReader { geo -> Color in
                    DispatchQueue.main.async {
                        // 获取按钮在屏幕上的位置
                        buttonRect = geo.frame(in: .global)
                    }
                    return Color.clear
                }
            )
        }
        .background(
            PopoverPresenter(
                isPresented: $showPopover,
                content: content,
                contentSize: contentSize,
                sourceRect: buttonRect
            )
        )
    }
}

/// 用于在 SwiftUI 中显示 popover 的 UIViewRepresentable
struct PopoverPresenter<Content: View>: UIViewRepresentable {
    // MARK: - 属性
    
    // 是否显示 popover
    @Binding var isPresented: Bool
    
    // popover 内容
    var content: () -> Content
    
    // popover 尺寸
    var contentSize: CGSize
    
    // 源视图矩形
    var sourceRect: CGRect
    
    // MARK: - UIViewRepresentable
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        // 当 isPresented 变为 true 时显示 popover
        if isPresented, !context.coordinator.isPresenting {
            context.coordinator.isPresenting = true
            
            // 创建 popover 内容视图
            let hostingController = UIHostingController(rootView: content())
            hostingController.preferredContentSize = contentSize
            hostingController.modalPresentationStyle = .popover
            
            // 配置 popover
            if let popover = hostingController.popoverPresentationController {
                popover.sourceView = uiView
                popover.sourceRect = CGRect(
                    x: sourceRect.midX - uiView.frame.origin.x,
                    y: sourceRect.maxY - uiView.frame.origin.y,
                    width: 0,
                    height: 0
                )
                popover.permittedArrowDirections = [.up, .down]
                popover.delegate = context.coordinator
            }
            
            // 获取当前视图控制器并显示 popover
            if let viewController = uiView.findViewController() {
                viewController.present(hostingController, animated: true)
            }
        }
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(parent: self)
    }
    
    // MARK: - Coordinator
    
    class Coordinator: NSObject, UIPopoverPresentationControllerDelegate {
        var parent: PopoverPresenter
        var isPresenting = false
        
        init(parent: PopoverPresenter) {
            self.parent = parent
        }
        
        // 强制使用 popover 样式
        func adaptivePresentationStyle(for controller: UIPresentationController) -> UIModalPresentationStyle {
            return .none
        }
        
        // popover 关闭时更新状态
        func presentationControllerDidDismiss(_ presentationController: UIPresentationController) {
            parent.isPresented = false
            isPresenting = false
        }
    }
}

// MARK: - UIView 扩展，用于查找视图控制器

extension UIView {
    func findViewController() -> UIViewController? {
        if let nextResponder = self.next as? UIViewController {
            return nextResponder
        } else if let nextResponder = self.next as? UIView {
            return nextResponder.findViewController()
        } else {
            return nil
        }
    }
}
