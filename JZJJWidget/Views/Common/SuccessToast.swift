import SwiftUI
import MyWidgetKit

/// 成功提示视图
struct SuccessToast: View {
    /// 提示消息
    let message: String
    
    /// 主题
    let theme: AppTheme
    
    var body: some View {
        VStack {
            Spacer()
            
            HStack(spacing: 12) {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.white)
                
                Text(message)
                    .font(theme.fonts.bodyMedium)
                    .foregroundColor(.white)
            }
            .padding()
            .background(
                Capsule()
                    .fill(theme.colors.success)
                    .shadow(color: theme.colors.shadow, radius: 8, x: 0, y: 4)
            )
            .padding(.bottom, 50)
            .transition(.move(edge: .bottom).combined(with: .opacity))
        }
        .zIndex(100)
    }
}

#Preview {
    SuccessToast(message: "设置已保存", theme: ThemeManager.shared.currentTheme)
}
