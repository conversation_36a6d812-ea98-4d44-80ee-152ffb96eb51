import SwiftUI
import WidgetKit

/// 下拉式尺寸选择器 - 用于导航栏标题位置
struct DropdownSizeSelector: View {
    // MARK: - 属性

    // 绑定的尺寸
    @Binding var selectedSize: WidgetFamily

    // 主题颜色
    var accentColor: Color
    var textColor: Color
    var backgroundColor: Color

    // 下拉菜单管理器 - 使用 StateObject 确保即使没有环境对象也能工作
    @StateObject private var localDropdownManager = DropdownMenuManager.shared

    // 唯一标识符
    private let menuID = "size-selector-\(UUID().uuidString)"

    // 计算属性：是否展开
    private var isExpanded: Bool {
        localDropdownManager.isMenuOpen(id: menuID)
    }

    // 支持的尺寸
    let supportedSizes: [WidgetFamily] = [.systemSmall, .systemMedium, .systemLarge]

    // MARK: - 初始化方法

    init(
        selectedSize: Binding<WidgetFamily>,
        accentColor: Color = .blue,
        textColor: Color = .primary,
        backgroundColor: Color = Color(.systemBackground)
    ) {
        self._selectedSize = selectedSize
        self.accentColor = accentColor
        self.textColor = textColor
        self.backgroundColor = backgroundColor
    }

    // MARK: - 视图主体

    var body: some View {
        // 使用 GeometryReader 获取视图的位置和尺寸
        GeometryReader { geometry in
            // 当前选中的尺寸按钮
            Button(action: {
                withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                    localDropdownManager.toggleMenu(id: menuID)
                }
            }) {
                HStack(spacing: 4) {
                    // 尺寸图标
                    Image(systemName: sizeIcon(for: selectedSize))
                        .font(.system(size: 14, weight: .medium))

                    // 尺寸名称
                    Text(sizeName(for: selectedSize))
                        .font(.subheadline)
                        .fontWeight(.medium)

                    // 下拉箭头
                    Image(systemName: isExpanded ? "chevron.up" : "chevron.down")
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(accentColor.opacity(0.8))
                }
                .foregroundColor(textColor)
                .padding(.vertical, 6)
                .padding(.horizontal, 10)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(backgroundColor.opacity(0.01))
                )
            }
            .frame(width: geometry.size.width, height: geometry.size.height)
            .overlay {
                // 下拉菜单作为覆盖层，这样可以突破导航栏的布局限制
                if isExpanded {
                    VStack(spacing: 0) {
                        // 下拉选项
                        VStack(spacing: 0) {
                            ForEach(supportedSizes, id: \.self) { size in
                                // 不显示当前选中的尺寸
                                if size != selectedSize {
                                    Button(action: {
                                        selectedSize = size
                                        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                            localDropdownManager.closeMenu(id: menuID)
                                        }
                                        // 提供触觉反馈
                                        UIImpactFeedbackGenerator(style: .light).impactOccurred()
                                    }) {
                                        HStack(spacing: 6) {
                                            Image(systemName: sizeIcon(for: size))
                                                .font(.system(size: 14, weight: .medium))

                                            Text(sizeName(for: size))
                                                .font(.subheadline)
                                        }
                                        .foregroundColor(textColor)
                                        .frame(maxWidth: .infinity, alignment: .center)
                                        .padding(.vertical, 10)
                                        .contentShape(Rectangle())
                                    }
                                    .buttonStyle(PlainButtonStyle())

                                    // 分隔线
                                    if size != supportedSizes.last && size != selectedSize {
                                        Divider()
                                            .padding(.horizontal, 8)
                                    }
                                }
                            }
                        }
                        .padding(.vertical, 6)
                        .background(
                            RoundedRectangle(cornerRadius: 10)
                                .fill(backgroundColor)
                                .shadow(color: Color.black.opacity(0.15), radius: 8, x: 0, y: 4)
                        )
                        .clipShape(RoundedRectangle(cornerRadius: 10))
                    }
                    .frame(width: geometry.size.width + 20)
                    .offset(y: geometry.size.height + 4)
                    .transition(.opacity.combined(with: .scale(scale: 0.95, anchor: .top)))
                    .zIndex(999) // 增加 zIndex 确保在最上层
                    .fixedSize() // 使用固定尺寸，避免被约束
                    .allowsHitTesting(true) // 确保可以接收点击事件
                }
            }
        }
        .frame(width: 110, height: 36)
        .onAppear {
            // 确保初始状态为关闭
            localDropdownManager.closeMenu(id: menuID)
        }
        // 添加点击手势识别器到全局视图，用于关闭下拉菜单
        .onTapGesture {
            // 这个手势不会触发，因为按钮的点击事件会先触发
        }
        // 使用 preference key 来获取视图的全局位置
        .overlay(
            GeometryReader { geo in
                Color.clear
                    .preference(key: ViewPositionKey.self, value: geo.frame(in: .global))
            }
        )
    }

    // 用于获取视图位置的 PreferenceKey
    private struct ViewPositionKey: PreferenceKey {
        static var defaultValue: CGRect = .zero
        static func reduce(value: inout CGRect, nextValue: () -> CGRect) {
            value = nextValue()
        }
    }

    // MARK: - 辅助方法

    /// 获取尺寸对应的图标
    private func sizeIcon(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "square"
        case .systemMedium:
            return "rectangle"
        case .systemLarge:
            return "rectangle.portrait"
        case .systemExtraLarge:
            return "square.grid.2x2"
        @unknown default:
            return "square"
        }
    }

    /// 获取尺寸对应的名称
    private func sizeName(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        case .systemExtraLarge:
            return "超大"
        @unknown default:
            return "未知"
        }
    }
}

// MARK: - 预览
struct DropdownSizeSelector_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            DropdownSizeSelector(
                selectedSize: .constant(.systemMedium),
                accentColor: .blue,
                textColor: .primary,
                backgroundColor: Color(.systemBackground)
            )
            .padding()
            .previewLayout(.sizeThatFits)
            .previewDisplayName("Light Mode")

            DropdownSizeSelector(
                selectedSize: .constant(.systemSmall),
                accentColor: .blue,
                textColor: .white,
                backgroundColor: Color(.systemGray6)
            )
            .padding()
            .background(Color.black)
            .previewLayout(.sizeThatFits)
            .previewDisplayName("Dark Mode")
        }
    }
}
