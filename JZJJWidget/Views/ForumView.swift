import SwiftUI

struct ForumView: View {
    var body: some View {
        NavigationView {
            List {
                Section(header: Text("每日精选")) {
                    ForEach(0..<3) { _ in
                        VStack(alignment: .leading) {
                            Text("精选帖子标题")
                                .font(.headline)
                            Text("作者: User123")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                        }
                    }
                }
                
                Section(header: Text("最新发布")) {
                    ForEach(0..<5) { _ in
                        VStack(alignment: .leading) {
                            Text("帖子标题")
                                .font(.headline)
                            Text("作者: User456")
                                .font(.subheadline)
                                .foregroundColor(.gray)
                        }
                    }
                }
            }
            .navigationTitle("论坛")
        }
    }
}