import SwiftUI
import MyWidgetKit

/// 日历选择器视图 - 在单独的 sheet 中显示，避免嵌套视图层次结构问题
struct CalendarPickerView: View {
    // 绑定的选中日期
    @Binding var selectedDate: Date
    
    // 主题管理器
    let theme: ThemeManager
    
    // 环境变量
    @Environment(\.presentationMode) var presentationMode
    
    // 状态变量
    @State private var tempDate: Date
    @State private var showConfirmation: Bool = false
    
    // 初始化
    init(selectedDate: Binding<Date>, theme: ThemeManager) {
        self._selectedDate = selectedDate
        self.theme = theme
        self._tempDate = State(initialValue: selectedDate.wrappedValue)
    }
    
    // 日期格式化器
    private var dateFormatter: DateFormatter {
        let formatter = DateFormatter()
        formatter.dateStyle = .long
        formatter.timeStyle = .none
        formatter.locale = Locale(identifier: "zh_CN")
        return formatter
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                // 当前选择的日期显示
                VStack(alignment: .center, spacing: 8) {
                    Text("已选择日期")
                        .font(.headline)
                        .foregroundColor(theme.currentTheme.colors.subtext)
                    
                    Text(dateFormatter.string(from: tempDate))
                        .font(.title2)
                        .fontWeight(.semibold)
                        .foregroundColor(theme.currentTheme.colors.accent)
                }
                .padding()
                .frame(maxWidth: .infinity)
                .background(theme.currentTheme.colors.surfaceVariant.opacity(0.5))
                .cornerRadius(12)
                .padding(.horizontal)
                
                // 日期选择器 - 在单独的视图中，没有复杂的嵌套
                DatePicker(
                    "选择日期",
                    selection: $tempDate,
                    displayedComponents: [.date]
                )
                .datePickerStyle(GraphicalDatePickerStyle())
                .labelsHidden()
                .padding()
                .onChange(of: tempDate) { _ in
                    // 日期变更时显示确认按钮
                    showConfirmation = true
                }
                
                // 快捷日期选择
                VStack(alignment: .leading, spacing: 12) {
                    Text("快速选择")
                        .font(.headline)
                        .foregroundColor(theme.currentTheme.colors.text)
                    
                    ScrollView(.horizontal, showsIndicators: false) {
                        HStack(spacing: 12) {
                            quickDateButton("今天", date: Date())
                            quickDateButton("明天", date: Calendar.current.date(byAdding: .day, value: 1, to: Date())!)
                            quickDateButton("下周", date: Calendar.current.date(byAdding: .day, value: 7, to: Date())!)
                            quickDateButton("下个月", date: Calendar.current.date(byAdding: .month, value: 1, to: Date())!)
                            quickDateButton("3个月后", date: Calendar.current.date(byAdding: .month, value: 3, to: Date())!)
                            quickDateButton("半年后", date: Calendar.current.date(byAdding: .month, value: 6, to: Date())!)
                            quickDateButton("一年后", date: Calendar.current.date(byAdding: .year, value: 1, to: Date())!)
                        }
                        .padding(.horizontal)
                    }
                }
                .padding()
                
                // 确认按钮
                if showConfirmation {
                    Button(action: {
                        // 更新选中的日期并关闭 sheet
                        selectedDate = tempDate
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Text("确认选择")
                            .font(.headline)
                            .foregroundColor(.white)
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(theme.currentTheme.colors.accent)
                            .cornerRadius(12)
                    }
                    .padding(.horizontal)
                    .transition(.opacity)
                    .animation(.easeInOut, value: showConfirmation)
                }
                
                Spacer()
            }
            .navigationBarTitle("选择日期", displayMode: .inline)
            .navigationBarItems(
                leading: Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(theme.currentTheme.colors.accent)
                },
                trailing: Button(action: {
                    // 更新选中的日期并关闭 sheet
                    selectedDate = tempDate
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Text("确定")
                        .fontWeight(.semibold)
                        .foregroundColor(theme.currentTheme.colors.accent)
                }
            )
        }
    }
    
    // 快捷日期按钮
    private func quickDateButton(_ title: String, date: Date) -> some View {
        Button(action: {
            withAnimation {
                tempDate = date
                showConfirmation = true
            }
        }) {
            Text(title)
                .font(.footnote)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(Calendar.current.isDate(tempDate, inSameDayAs: date) 
                              ? theme.currentTheme.colors.accent 
                              : theme.currentTheme.colors.surfaceVariant)
                )
                .foregroundColor(Calendar.current.isDate(tempDate, inSameDayAs: date) 
                                 ? .white 
                                 : theme.currentTheme.colors.text)
        }
    }
}

// 预览
struct CalendarPickerView_Previews: PreviewProvider {
    static var previews: some View {
        CalendarPickerView(
            selectedDate: .constant(Date()),
            theme: ThemeManager.shared
        )
    }
}
