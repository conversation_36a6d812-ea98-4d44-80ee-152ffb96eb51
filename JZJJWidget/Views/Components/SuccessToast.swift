//
//  SuccessToast.swift
//  JZJJWidget
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/23.
//

import SwiftUI
import MyWidgetKit

/// 成功提示Toast组件
struct SuccessToast: View {
    let message: String
    @Binding var isShowing: Bool

    var body: some View {
        VStack {
            Spacer()

            if isShowing {
                HStack {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.white)
                        .font(.system(size: 20))

                    Text(message)
                        .foregroundColor(.white)
                        .font(.system(size: 16, weight: .medium))
                }
                .padding(.horizontal, 20)
                .padding(.vertical, 12)
                .background(Color.green)
                .cornerRadius(25)
                .shadow(color: .black.opacity(0.2), radius: 10, x: 0, y: 5)
                .transition(.move(edge: .bottom).combined(with: .opacity))
                .onAppear {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
                        withAnimation(.easeInOut(duration: 0.3)) {
                            isShowing = false
                        }
                    }
                }
            }
        }
        .padding(.bottom, 100)
        .animation(.easeInOut(duration: 0.3), value: isShowing)
    }
}

// MARK: - 预览
struct SuccessToast_Previews: PreviewProvider {
    static var previews: some View {
        SuccessToast(message: "主题已应用", isShowing: .constant(true))
    }
}
