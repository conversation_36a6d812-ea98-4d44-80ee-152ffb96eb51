//
//  EnhancedWidgetCard.swift
//  JZJJWidget
//
//  Created by y<PERSON><PERSON><PERSON> on 2025/5/15.
//

import SwiftUI
import MyWidgetKit
import WidgetKit

/// 增强型小组件卡片视图
struct EnhancedWidgetCard: View {
    // MARK: - 属性

    // 卡片内容
    let title: String
    let description: String
    let iconName: String
    let widgetFamily: WidgetFamily
    let theme: AppTheme

    // 点击回调
    var onTap: (() -> Void)?

    // 状态
    @State private var isPressed: Bool = false
    @State private var showPreview: Bool = false

    // 动画持续时间
    private let animationDuration: Double = 0.2

    // MARK: - 视图主体

    var body: some View {
        cardContent
            .scaleEffect(isPressed ? 0.98 : 1.0)
            .animation(.easeInOut(duration: animationDuration), value: isPressed)
            .onTapGesture {
                // 触觉反馈
                let generator = UIImpactFeedbackGenerator(style: .light)
                generator.impactOccurred()

                // 按下动画
                withAnimation(.easeInOut(duration: animationDuration)) {
                    isPressed = true
                }

                // 延迟后恢复并执行回调
                DispatchQueue.main.asyncAfter(deadline: .now() + animationDuration) {
                    withAnimation(.easeInOut(duration: animationDuration)) {
                        isPressed = false
                    }

                    // 执行点击回调
                    if let onTap = onTap {
                        onTap()
                    }
                }
            }
    }

    // 卡片内容
    private var cardContent: some View {
        VStack(alignment: .leading, spacing: 16) {
            // 顶部区域：图标和标题
            HStack(alignment: .center, spacing: 12) {
                // 图标
                Image(systemName: iconName)
                    .font(.system(size: 24))
                    .foregroundColor(.white)
                    .frame(width: 48, height: 48)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(theme.colors.accent)
                    )

                // 标题和尺寸
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(theme.fonts.titleMedium)
                        .foregroundColor(theme.colors.text)

                    Text(getSizeText(for: widgetFamily))
                        .font(theme.fonts.bodySmall)
                        .foregroundColor(theme.colors.subtext)
                }

                Spacer()

                // 预览按钮
                Button(action: {
                    showPreview.toggle()
                }) {
                    Image(systemName: "eye")
                        .font(.system(size: 16))
                        .foregroundColor(theme.colors.accent)
                        .padding(8)
                        .background(
                            Circle()
                                .fill(theme.colors.surfaceVariant)
                        )
                }
                .buttonStyle(PlainButtonStyle())
                .sheet(isPresented: $showPreview) {
                    previewView
                }
            }

            // 描述
            Text(description)
                .font(theme.fonts.bodyMedium)
                .foregroundColor(theme.colors.subtext)
                .lineLimit(2)
                .multilineTextAlignment(.leading)

            // 底部区域：预览图和操作按钮
            HStack(alignment: .bottom) {
                // 预览图
                previewThumbnail
                    .frame(width: 100, height: 100)
                    .clipShape(RoundedRectangle(cornerRadius: 12))
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(theme.colors.border, lineWidth: 1)
                    )

                Spacer()

                // 操作按钮
                VStack(alignment: .trailing, spacing: 8) {
                    Text("点击配置")
                        .font(theme.fonts.bodySmall)
                        .foregroundColor(theme.colors.subtext)

                    Image(systemName: "chevron.right")
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(theme.colors.accent)
                        .padding(8)
                        .background(
                            Circle()
                                .fill(theme.colors.accent.opacity(0.1))
                        )
                }
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(theme.colors.surface)
                .shadow(color: theme.colors.shadow.opacity(0.1), radius: 10, x: 0, y: 4)
        )
    }

    // 预览缩略图
    private var previewThumbnail: some View {
        ZStack {
            // 背景
            theme.colors.surfaceVariant

            // 图标
            VStack(spacing: 8) {
                Image(systemName: iconName)
                    .font(.system(size: 24))
                    .foregroundColor(theme.colors.accent)

                Text(title)
                    .font(.caption)
                    .foregroundColor(theme.colors.text)
                    .lineLimit(1)
            }
            .padding(8)
        }
    }

    // 预览视图
    private var previewView: some View {
        NavigationView {
            VStack {
                // 预览内容
                getPreviewContent()
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .padding()

                Spacer()
            }
            .navigationTitle("\(title) 预览")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        showPreview = false
                    }
                }
            }
        }
    }

    // 获取预览内容
    @ViewBuilder
    private func getPreviewContent() -> some View {
        switch title {
        case "每日灵感":
            DailyQuoteWidgetView(
                data: DailyQuoteWidgetViewData(
                    quote: "生活不是等待风暴过去，而是学会在雨中跳舞。",
                    background: .imageURL("https://fastly.picsum.photos/id/411/300/300.jpg?blur=5&hmac=QJHyalA8gCgzcn6czrEuqvsYhakOXRTKEvh3nczlUFs"),
                    date: Date(),
                    config: nil
                ),
                fontName: "PingFangSC",
                fontSize: 16,
                fontColor: .black
            )
            .frame(width: getPreviewWidth(), height: getPreviewHeight())
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)

        case "二维码小组件":
            QRWidgetView(
                data: QRWidgetViewData(
                    content: "https://example.com",
                    foreground: .color(WidgetColor.fromColor(.black)),
                    background: .color(WidgetColor.fromColor(.white)),
                    foregroundColor: WidgetColor.fromColor(.black)
                )
            )
            .frame(width: getPreviewWidth(), height: getPreviewHeight())
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)

        case "任务清单":
            TodoWidgetView(
                data: TodoWidgetData(
                    category: nil,
                    showCompleted: false,
                    maxTaskCount: 5,
                    priorityFilter: .all,
                    background: .color(WidgetColor.fromColor(.white)),
                    fontName: "SF Pro",
                    fontSize: 14,
                    fontColor: .black
                ),
                family: widgetFamily
            )
            .frame(width: getPreviewWidth(), height: getPreviewHeight())
            .clipShape(RoundedRectangle(cornerRadius: 20))
            .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)

        default:
            // 默认预览
            PlaceholderWidgetView()
                .frame(width: getPreviewWidth(), height: getPreviewHeight())
                .clipShape(RoundedRectangle(cornerRadius: 20))
                .shadow(color: Color.black.opacity(0.1), radius: 10, x: 0, y: 5)
        }
    }

    // 获取尺寸文本
    private func getSizeText(for family: WidgetFamily) -> String {
        switch family {
        case .systemSmall:
            return "小尺寸"
        case .systemMedium:
            return "中尺寸"
        case .systemLarge:
            return "大尺寸"
        default:
            return "小尺寸"
        }
    }

    // 获取预览宽度
    private func getPreviewWidth() -> CGFloat {
        switch widgetFamily {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 360
        case .systemLarge:
            return 360
        default:
            return 170
        }
    }

    // 获取预览高度
    private func getPreviewHeight() -> CGFloat {
        switch widgetFamily {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 170
        case .systemLarge:
            return 380
        default:
            return 170
        }
    }
}

// MARK: - 预览
#Preview {
    EnhancedWidgetCard(
        title: "每日灵感",
        description: "精选名言、诗词、段子，每日更新，启发思考",
        iconName: "text.quote",
        widgetFamily: .systemSmall,
        theme: AppTheme.iosLight,
        onTap: {
            print("Card tapped")
        }
    )
    .padding()
    .previewLayout(.sizeThatFits)
}

#Preview {
    EnhancedWidgetCard(
        title: "时光提醒",
        description: "定制专属倒计时，记录生活中的重要时刻",
        iconName: "calendar",
        widgetFamily: .systemMedium,
        theme: AppTheme.iosDark,
        onTap: {
            print("Card tapped")
        }
    )
    .padding()
    .previewLayout(.sizeThatFits)
}
