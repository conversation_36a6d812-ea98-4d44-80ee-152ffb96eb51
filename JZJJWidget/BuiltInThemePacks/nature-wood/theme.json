{"schema_version": "1.0.0", "theme_info": {"id": "nature-wood", "name": "自然木纹", "description": "温暖的木质纹理主题"}, "base_theme": {"id": "nature-wood-base", "name": "自然木纹基础", "type": "custom", "key_style": "rounded", "colors": {"background": {"red": 0.8, "green": 0.6, "blue": 0.4, "alpha": 1.0}, "key_background": {"red": 0.9, "green": 0.7, "blue": 0.5, "alpha": 0.9}, "key_pressed": {"red": 0.7, "green": 0.5, "blue": 0.3, "alpha": 1.0}, "text": {"red": 0.3, "green": 0.2, "blue": 0.1, "alpha": 1.0}, "special_key": {"red": 0.6, "green": 0.4, "blue": 0.2, "alpha": 1.0}, "border": {"red": 0.5, "green": 0.3, "blue": 0.1, "alpha": 0.8}}, "images": {"has_background_image": true, "has_key_image": true, "background_image_path": "resources/backgrounds/wood-bg.jpg", "key_image_path": "resources/keys/wood-key.png", "is_built_in_image_theme": true, "image_opacity": 0.8, "image_blend_mode": "multiply"}, "typography": {"font_name": "SF Pro", "font_size": 16, "font_weight": "semibold"}, "layout": {"key_spacing": 6, "key_height": 44, "show_border": true, "border_width": 1.5}, "effects": {"enable_shadow": true, "shadow_color": {"red": 0.2, "green": 0.1, "blue": 0.0, "alpha": 0.4}, "shadow_radius": 3, "enable_haptic": true, "enable_sound": true}}, "advanced_config": {"global_settings": {"key_spacing": 6, "key_height": 44, "enable_haptic_feedback": true, "enable_sound_feedback": true, "enable_key_animations": false, "animation_duration": 0.1, "enable_gradient_effects": false, "enable_parallax_effect": false}, "key_type_configs": {"symbol": {"id": "symbol-config", "key_type": "symbol", "name": "符号键", "description": "@#$%等符号按键配置", "default_background_color": {"red": 0.8, "green": 0.6, "blue": 0.4, "alpha": 0.9}, "default_pressed_color": {"red": 0.7, "green": 0.5, "blue": 0.3, "alpha": 1.0}, "default_text_color": {"red": 0.4, "green": 0.2, "blue": 0.0, "alpha": 1.0}, "default_border_color": {"red": 0.5, "green": 0.3, "blue": 0.1, "alpha": 0.8}, "default_font_size": 16, "default_font_weight": "bold", "default_corner_radius": 8, "default_border_width": 1.5, "affected_keys": ["@", "#", "$", "%", "&", "*", "+", "="], "updatedAt": "2025-01-01T00:00:00Z"}, "punctuation": {"id": "punctuation-config", "key_type": "punctuation", "name": "标点键", "description": ".,?!等标点按键配置", "default_background_color": {"red": 0.85, "green": 0.65, "blue": 0.45, "alpha": 0.9}, "default_pressed_color": {"red": 0.7, "green": 0.5, "blue": 0.3, "alpha": 1.0}, "default_text_color": {"red": 0.3, "green": 0.15, "blue": 0.0, "alpha": 1.0}, "default_border_color": {"red": 0.5, "green": 0.3, "blue": 0.1, "alpha": 0.8}, "default_font_size": 16, "default_font_weight": "bold", "default_corner_radius": 8, "default_border_width": 1.5, "affected_keys": [".", ",", "?", "!", ";", ":", "'", "\""], "updatedAt": "2025-01-01T00:00:00Z"}}, "individual_key_configs": {}, "createdAt": "2025-01-01T00:00:00Z", "updatedAt": "2025-01-01T00:00:00Z"}, "validation": {"checksum": "ghi789jkl012", "file_count": 4, "total_size": 4194304}}