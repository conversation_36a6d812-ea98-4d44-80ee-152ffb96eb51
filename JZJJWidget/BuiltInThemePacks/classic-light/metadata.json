{"id": "classic-light", "name": "经典浅色", "description": "iOS系统风格的经典浅色主题，简洁优雅，适合日常使用", "version": "1.0.0", "category": "classic", "style": "light", "author": "JZJJWidget Team", "createdAt": "2025-01-01T00:00:00Z", "updatedAt": "2025-01-01T00:00:00Z", "compatibility": {"minIOSVersion": "13.0", "minAppVersion": "1.0.0", "supportedDevices": ["iPhone", "iPad"]}, "resources": {"previewImage": "preview.png", "backgroundImages": [], "keyImages": [], "totalSize": 1024768, "compressedSize": 512384}, "features": {"supportsDarkMode": false, "hasAnimations": true, "hasSounds": true, "hasHaptics": true, "customFonts": false}, "tags": ["经典", "浅色", "系统", "简洁"], "rating": 4.8, "downloadCount": 10000, "isPremium": false, "isBuiltIn": true}