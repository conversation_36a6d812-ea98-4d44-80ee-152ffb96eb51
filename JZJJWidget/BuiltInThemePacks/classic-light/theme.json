{"schema_version": "1.0.0", "theme_info": {"id": "classic-light", "name": "经典浅色", "description": "iOS系统风格的经典浅色主题"}, "base_theme": {"id": "classic-light-base", "name": "经典浅色基础", "type": "light", "key_style": "rounded", "colors": {"background": {"red": 1.0, "green": 1.0, "blue": 1.0, "alpha": 1.0}, "key_background": {"red": 0.95, "green": 0.95, "blue": 0.95, "alpha": 1.0}, "key_pressed": {"red": 0.0, "green": 0.48, "blue": 1.0, "alpha": 1.0}, "text": {"red": 0.0, "green": 0.0, "blue": 0.0, "alpha": 1.0}, "special_key": {"red": 0.68, "green": 0.68, "blue": 0.7, "alpha": 1.0}, "border": {"red": 0.78, "green": 0.78, "blue": 0.8, "alpha": 1.0}}, "images": {"has_background_image": false, "has_key_image": false, "background_image_path": null, "key_image_path": null, "is_built_in_image_theme": false, "image_opacity": 1.0, "image_blend_mode": "normal"}, "typography": {"font_name": "SF Pro", "font_size": 16, "font_weight": "medium"}, "layout": {"key_spacing": 6, "key_height": 44, "show_border": true, "border_width": 1}, "effects": {"enable_shadow": true, "shadow_color": {"red": 0.0, "green": 0.0, "blue": 0.0, "alpha": 0.1}, "shadow_radius": 2, "enable_haptic": true, "enable_sound": true}}, "advanced_config": {"global_settings": {"key_spacing": 6, "key_height": 44, "enable_haptic_feedback": true, "enable_sound_feedback": true, "enable_key_animations": true, "animation_duration": 0.1, "enable_gradient_effects": false, "enable_parallax_effect": false}, "key_type_configs": {"symbol": {"id": "symbol-config", "key_type": "symbol", "name": "符号键", "description": "@#$%等符号按键配置", "default_background_color": {"red": 0.9, "green": 0.9, "blue": 0.9, "alpha": 1.0}, "default_pressed_color": {"red": 0.0, "green": 0.48, "blue": 1.0, "alpha": 1.0}, "default_text_color": {"red": 0.0, "green": 0.0, "blue": 0.0, "alpha": 1.0}, "default_border_color": {"red": 0.78, "green": 0.78, "blue": 0.8, "alpha": 1.0}, "default_font_size": 16, "default_font_weight": "medium", "default_corner_radius": 8, "default_border_width": 1, "affected_keys": ["@", "#", "$", "%", "&", "*", "+", "="], "updatedAt": "2025-01-01T00:00:00Z"}, "punctuation": {"id": "punctuation-config", "key_type": "punctuation", "name": "标点键", "description": ".,?!等标点按键配置", "default_background_color": {"red": 0.9, "green": 0.9, "blue": 0.9, "alpha": 1.0}, "default_pressed_color": {"red": 0.0, "green": 0.48, "blue": 1.0, "alpha": 1.0}, "default_text_color": {"red": 0.0, "green": 0.0, "blue": 0.0, "alpha": 1.0}, "default_border_color": {"red": 0.78, "green": 0.78, "blue": 0.8, "alpha": 1.0}, "default_font_size": 16, "default_font_weight": "medium", "default_corner_radius": 8, "default_border_width": 1, "affected_keys": [".", ",", "?", "!", ";", ":", "'", "\""], "updatedAt": "2025-01-01T00:00:00Z"}}, "individual_key_configs": {}, "createdAt": "2025-01-01T00:00:00Z", "updatedAt": "2025-01-01T00:00:00Z"}, "validation": {"checksum": "abc123def456", "file_count": 2, "total_size": 1024768}}