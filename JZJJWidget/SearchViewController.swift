//
//  SearchViewController.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/15.
//

import UIKit

class SearchViewController: UIViewController {
    // MARK: - 属性

    private let searchController = UISearchController(searchResultsController: nil)
    private var searchHistoryTableView: UITableView!
    private var searchResultsTableView: UITableView!
    private var emptyStateView: UIView!

    private var searchHistory: [String] = []
    private var searchResults: [String] = []
    private var isSearching: Bool = false

    // MARK: - 生命周期方法

    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        loadSearchHistory()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // 自动弹出键盘
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            self.searchController.searchBar.becomeFirstResponder()
        }
    }

    // MARK: - UI 设置

    private func setupUI() {
        view.backgroundColor = .systemBackground

        // 设置导航栏
        title = "搜索"
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            title: "取消",
            style: .plain,
            target: self,
            action: #selector(cancelButtonTapped)
        )

        // 设置搜索控制器
        searchController.searchResultsUpdater = self
        searchController.obscuresBackgroundDuringPresentation = false
        searchController.searchBar.placeholder = "搜索组件、壁纸等"
        searchController.searchBar.delegate = self
        navigationItem.searchController = searchController
        navigationItem.hidesSearchBarWhenScrolling = false
        definesPresentationContext = true

        // 设置历史搜索表格视图
        setupHistoryTableView()

        // 设置搜索结果表格视图
        setupResultsTableView()

        // 设置空状态视图
        setupEmptyStateView()

        // 初始状态显示历史记录
        updateVisibleViews()
    }

    private func setupHistoryTableView() {
        searchHistoryTableView = UITableView(frame: .zero, style: .grouped)
        searchHistoryTableView.delegate = self
        searchHistoryTableView.dataSource = self
        searchHistoryTableView.register(UITableViewCell.self, forCellReuseIdentifier: "HistoryCell")
        searchHistoryTableView.tableFooterView = createClearHistoryButton()
        view.addSubview(searchHistoryTableView)

        searchHistoryTableView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            searchHistoryTableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            searchHistoryTableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            searchHistoryTableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            searchHistoryTableView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
        ])
    }

    private func setupResultsTableView() {
        searchResultsTableView = UITableView(frame: .zero, style: .plain)
        searchResultsTableView.delegate = self
        searchResultsTableView.dataSource = self
        searchResultsTableView.register(UITableViewCell.self, forCellReuseIdentifier: "ResultCell")
        searchResultsTableView.isHidden = true
        view.addSubview(searchResultsTableView)

        searchResultsTableView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            searchResultsTableView.topAnchor.constraint(equalTo: view.safeAreaLayoutGuide.topAnchor),
            searchResultsTableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            searchResultsTableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            searchResultsTableView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
        ])
    }

    private func setupEmptyStateView() {
        emptyStateView = UIView()
        emptyStateView.isHidden = true
        view.addSubview(emptyStateView)

        // 创建空状态图标
        let imageView = UIImageView(image: UIImage(systemName: "magnifyingglass"))
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit

        // 创建空状态文本
        let label = UILabel()
        label.text = "无搜索结果"
        label.textAlignment = .center
        label.textColor = .systemGray
        label.font = .systemFont(ofSize: 16, weight: .medium)

        // 添加到空状态视图
        emptyStateView.addSubview(imageView)
        emptyStateView.addSubview(label)

        // 设置约束
        emptyStateView.translatesAutoresizingMaskIntoConstraints = false
        imageView.translatesAutoresizingMaskIntoConstraints = false
        label.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            emptyStateView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            emptyStateView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            emptyStateView.widthAnchor.constraint(equalToConstant: 200),
            emptyStateView.heightAnchor.constraint(equalToConstant: 200),

            imageView.topAnchor.constraint(equalTo: emptyStateView.topAnchor),
            imageView.centerXAnchor.constraint(equalTo: emptyStateView.centerXAnchor),
            imageView.widthAnchor.constraint(equalToConstant: 60),
            imageView.heightAnchor.constraint(equalToConstant: 60),

            label.topAnchor.constraint(equalTo: imageView.bottomAnchor, constant: 16),
            label.centerXAnchor.constraint(equalTo: emptyStateView.centerXAnchor),
            label.leadingAnchor.constraint(equalTo: emptyStateView.leadingAnchor),
            label.trailingAnchor.constraint(equalTo: emptyStateView.trailingAnchor),
        ])
    }

    private func createClearHistoryButton() -> UIView {
        let footerView = UIView(frame: CGRect(x: 0, y: 0, width: view.frame.width, height: 60))

        let button = UIButton(type: .system)
        button.setTitle("清除搜索历史", for: .normal)
        button.setTitleColor(.systemRed, for: .normal)
        button.addTarget(self, action: #selector(clearSearchHistory), for: .touchUpInside)

        footerView.addSubview(button)
        button.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            button.centerXAnchor.constraint(equalTo: footerView.centerXAnchor),
            button.centerYAnchor.constraint(equalTo: footerView.centerYAnchor),
            button.heightAnchor.constraint(equalToConstant: 44),
        ])

        return footerView
    }

    // MARK: - 视图更新

    private func updateVisibleViews() {
        if isSearching {
            // 搜索中
            searchHistoryTableView.isHidden = true

            if searchResults.isEmpty {
                // 无搜索结果
                searchResultsTableView.isHidden = true
                emptyStateView.isHidden = false
            } else {
                // 有搜索结果
                searchResultsTableView.isHidden = false
                emptyStateView.isHidden = true
            }
        } else {
            // 未搜索，显示历史记录
            searchHistoryTableView.isHidden = false
            searchResultsTableView.isHidden = true
            emptyStateView.isHidden = true
        }
    }

    // MARK: - 搜索历史管理

    private func loadSearchHistory() {
        if let history = UserDefaults.standard.array(forKey: "searchHistory") as? [String] {
            searchHistory = history
            searchHistoryTableView.reloadData()
        }
    }

    private func saveSearchHistory() {
        UserDefaults.standard.set(searchHistory, forKey: "searchHistory")
    }

    private func addToSearchHistory(_ query: String) {
        // 如果已存在，先移除旧的
        if let index = searchHistory.firstIndex(of: query) {
            searchHistory.remove(at: index)
        }

        // 添加到最前面
        searchHistory.insert(query, at: 0)

        // 限制历史记录数量
        if searchHistory.count > 10 {
            searchHistory = Array(searchHistory.prefix(10))
        }

        saveSearchHistory()
        searchHistoryTableView.reloadData()
    }

    @objc private func clearSearchHistory() {
        searchHistory.removeAll()
        saveSearchHistory()
        searchHistoryTableView.reloadData()
    }

    // MARK: - 搜索功能

    private func performSearch(with query: String) {
        guard !query.isEmpty else {
            isSearching = false
            searchResults.removeAll()
            updateVisibleViews()
            return
        }

        isSearching = true

        // 模拟搜索结果
        // 在实际应用中，这里应该调用真实的搜索API
        DispatchQueue.global().async {
            // 模拟网络延迟
            Thread.sleep(forTimeInterval: 0.5)

            // 模拟搜索结果
            let mockResults = [
                "时光提醒小组件",
                "日历小组件",
                "照片小组件",
                "音乐小组件",
                "心情日记小组件",
            ]

            let filteredResults = mockResults.filter { $0.contains(query) }

            DispatchQueue.main.async {
                self.searchResults = filteredResults
                self.updateVisibleViews()
                self.searchResultsTableView.reloadData()
            }
        }
    }

    // MARK: - 动作处理

    @objc private func cancelButtonTapped() {
        dismiss(animated: true)
    }
}

// MARK: - UITableViewDelegate, UITableViewDataSource

extension SearchViewController: UITableViewDelegate, UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        if tableView == searchHistoryTableView {
            return 1
        }
        return 1
    }

    func tableView(_ tableView: UITableView, numberOfRowsInSection _: Int) -> Int {
        if tableView == searchHistoryTableView {
            return searchHistory.count
        } else {
            return searchResults.count
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if tableView == searchHistoryTableView {
            let cell = tableView.dequeueReusableCell(withIdentifier: "HistoryCell", for: indexPath)
            cell.textLabel?.text = searchHistory[indexPath.row]
            cell.accessoryType = .disclosureIndicator
            return cell
        } else {
            let cell = tableView.dequeueReusableCell(withIdentifier: "ResultCell", for: indexPath)
            cell.textLabel?.text = searchResults[indexPath.row]
            cell.accessoryType = .disclosureIndicator
            return cell
        }
    }

    func tableView(_ tableView: UITableView, titleForHeaderInSection _: Int) -> String? {
        if tableView == searchHistoryTableView && !searchHistory.isEmpty {
            return "搜索历史"
        }
        return nil
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        if tableView == searchHistoryTableView {
            let query = searchHistory[indexPath.row]
            searchController.searchBar.text = query
            performSearch(with: query)
        } else {
            // 处理搜索结果的点击
            let selectedItem = searchResults[indexPath.row]
            print("Selected: \(selectedItem)")
            // 这里可以添加跳转到详情页的逻辑
        }
    }
}

// MARK: - UISearchResultsUpdating, UISearchBarDelegate

extension SearchViewController: UISearchResultsUpdating, UISearchBarDelegate {
    func updateSearchResults(for searchController: UISearchController) {
        guard let query = searchController.searchBar.text else { return }
        performSearch(with: query)
    }

    func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
        guard let query = searchBar.text, !query.isEmpty else { return }
        addToSearchHistory(query)
        performSearch(with: query)
    }

    func searchBarCancelButtonClicked(_: UISearchBar) {
        isSearching = false
        updateVisibleViews()
    }
}
