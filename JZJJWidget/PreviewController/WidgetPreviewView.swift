import SwiftUI
import MyWidgetKit
import WidgetKit

/// 小组件预览视图
struct WidgetPreviewView: View {
    // MARK: - 属性

    // 小组件类型
    var widgetType: String

    // 小组件ID
    var itemId: String?

    // 小组件尺寸
    @State private var selectedFamily: WidgetFamily = .systemMedium

    // 主题管理器
    @EnvironmentObject private var themeManager: ThemeManager

    // MARK: - 视图主体

    var body: some View {
        VStack(spacing: 16) {
            // 预览标题
            Text("小组件预览")
                .font(.headline)
                .padding(.top)

            // 尺寸选择器
            HStack {
                Text("尺寸:")
                    .font(.subheadline)

                Picker("尺寸", selection: $selectedFamily) {
                    Text("小").tag(WidgetFamily.systemSmall)
                    Text("中").tag(WidgetFamily.systemMedium)
                    Text("大").tag(WidgetFamily.systemLarge)
                }
                .pickerStyle(SegmentedPickerStyle())
            }
            .padding(.horizontal)

            // 预览容器
            ZStack {
                // 背景
                RoundedRectangle(cornerRadius: 16)
                    .fill(Color(UIColor.secondarySystemBackground))
                    .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)

                // 小组件预览
                widgetPreview
                    .padding(8)
            }
            .frame(width: previewWidth, height: previewHeight)
            .padding()

            Spacer()
        }
        .padding()
        .background(Color(uiColor: UIColor(themeManager.currentTheme.colors.background)))
    }

    // MARK: - 小组件预览

    @ViewBuilder
    private var widgetPreview: some View {
        switch widgetType {
        case "appLauncher":
            appLauncherPreview
        case "dailyQuote":
            dailyQuotePreview
        case "waterIntake":
            waterIntakePreview
        case "timeWidget":
            timeWidgetPreview
        case "todoList":
            todoListPreview
        case "passwordGenerator":
            passwordGeneratorPreview
        case "qrCode":
            qrCodePreview
        default:
            Text("不支持的小组件类型")
                .foregroundColor(.secondary)
        }
    }

    // MARK: - 应用启动器预览

    private var appLauncherPreview: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(AppLauncherWidgetData.self, for: .appLauncher, property: .config) {
                // 如果有指定的项目ID，尝试高亮显示该项目
                let highlightedConfig = highlightItem(in: config)
                AppLauncherWidgetView(data: highlightedConfig, family: selectedFamily)
            } else {
                Text("未找到应用启动器配置")
                    .foregroundColor(.secondary)
            }
        }
    }

    // 高亮显示指定的项目
    private func highlightItem(in config: AppLauncherWidgetData) -> AppLauncherWidgetData {
        guard let itemId = itemId else { return config }

        var newConfig = config

        // 查找匹配的项目并高亮显示
        if let index = newConfig.items.firstIndex(where: { $0.id == itemId }) {
            // 创建高亮版本的项目
            var highlightedItem = newConfig.items[index]

            // 修改颜色以高亮显示
            let uiColor = UIColor(highlightedItem.color)
            var h: CGFloat = 0, s: CGFloat = 0, b: CGFloat = 0, a: CGFloat = 0
            uiColor.getHue(&h, saturation: &s, brightness: &b, alpha: &a)

            // 增加亮度和饱和度
            let highlightedColor = UIColor(hue: h, saturation: min(s + 0.2, 1.0), brightness: min(b + 0.2, 1.0), alpha: a)
            highlightedItem.color = Color(highlightedColor)

            // 更新项目
            newConfig.items[index] = highlightedItem
        }

        return newConfig
    }

    // MARK: - 每日一言预览

    private var dailyQuotePreview: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(DailyQuoteWidgetConfig.self, for: .dailyQuote, property: .config) {
                // 创建 DailyQuoteWidgetViewData
                let viewData = config.toViewData(date: Date())

                // 使用正确的初始化方法
                DailyQuoteWidgetView(
                    data: viewData,
                    fontName: config.fontName,
                    fontSize: CGFloat(config.fontSize),
                    fontColor: config.fontColor,
                    isPreviewMode: true
                )
            } else {
                Text("未找到每日一言配置")
                    .foregroundColor(.secondary)
            }
        }
    }

    // MARK: - 水分摄入追踪器预览

    private var waterIntakePreview: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(WaterIntakeWidgetData.self, for: .waterIntake, property: .config) {
                WaterIntakeWidgetView(data: config, family: selectedFamily)
            } else {
                Text("未找到水分摄入追踪器配置")
                    .foregroundColor(.secondary)
            }
        }
    }

    // MARK: - 时间小组件预览

    private var timeWidgetPreview: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(TimeWidgetData.self, for: .timeWidget, property: .config) {
                // TimeWidgetView 不接受 family 参数，只接受 data 参数
                TimeWidgetView(data: config)
            } else {
                Text("未找到时间小组件配置")
                    .foregroundColor(.secondary)
            }
        }
    }

    // MARK: - 任务清单预览

    private var todoListPreview: some View {
        Group {
            // 由于 TodoListWidgetData 和 TodoListWidgetView 可能尚未实现
            // 显示一个占位符
            Text("任务清单小组件预览")
                .foregroundColor(.secondary)
        }
    }

    // MARK: - 密码生成器预览

    private var passwordGeneratorPreview: some View {
        Group {
            // 由于 PasswordGeneratorData 和 PasswordGeneratorWidgetView 可能尚未实现
            // 显示一个占位符
            Text("密码生成器小组件预览")
                .foregroundColor(.secondary)
        }
    }

    // MARK: - 二维码预览

    private var qrCodePreview: some View {
        Group {
            // 由于 QRCodeWidgetData 和 QRCodeWidgetView 可能尚未实现
            // 显示一个占位符
            Text("二维码小组件预览")
                .foregroundColor(.secondary)
        }
    }

    // MARK: - 辅助计算属性

    // 预览宽度
    private var previewWidth: CGFloat {
        switch selectedFamily {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 360
        case .systemLarge:
            return 360
        default:
            return 170
        }
    }

    // 预览高度
    private var previewHeight: CGFloat {
        switch selectedFamily {
        case .systemSmall:
            return 170
        case .systemMedium:
            return 170
        case .systemLarge:
            return 380
        default:
            return 170
        }
    }
}
