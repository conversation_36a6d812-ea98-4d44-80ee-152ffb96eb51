import UIKit
import SwiftUI
import MyWidgetKit
import WidgetKit

/// 项目预览控制器
class ItemPreviewController: UIViewController {
    // MARK: - 属性

    // 项目信息
    private var itemId: String?
    private var itemName: String?
    private var widgetType: String?

    // 项目数据
    private var item: AppLauncherItem?

    // 小组件预览
    private var widgetPreviewHostingController: UIHostingController<AnyView>?

    // UI元素
    private let containerView = UIView()
    private let headerView = UIView()
    private let contentView = UIView()
    private let footerView = UIView()

    private let titleLabel = UILabel()
    private let closeButton = UIButton(type: .system)
    private let iconImageView = UIImageView()
    private let nameLabel = UILabel()
    private let idLabel = UILabel()
    private let actionButton = UIButton(type: .system)

    // 预览切换按钮
    private let previewToggleButton = UIButton(type: .system)

    // 手势识别器
    private var panGesture: UIPanGestureRecognizer!

    // 动画相关
    private var initialCenter: CGPoint = .zero
    private var dismissalThreshold: CGFloat = 100

    // MARK: - 初始化

    init(itemId: String?, itemName: String?, widgetType: String?) {
        self.itemId = itemId
        self.itemName = itemName
        self.widgetType = widgetType
        super.init(nibName: nil, bundle: nil)
        modalPresentationStyle = .overFullScreen
        modalTransitionStyle = .crossDissolve
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 生命周期

    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadItemData()
        setupGestures()
    }

    // MARK: - UI设置

    private func setupUI() {
        // 设置背景
        view.backgroundColor = UIColor.black.withAlphaComponent(0.5)

        // 设置容器视图
        containerView.backgroundColor = .systemBackground
        containerView.layer.cornerRadius = 16
        containerView.clipsToBounds = true
        containerView.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(containerView)

        // 设置容器视图约束
        NSLayoutConstraint.activate([
            containerView.centerXAnchor.constraint(equalTo: view.centerXAnchor),
            containerView.centerYAnchor.constraint(equalTo: view.centerYAnchor),
            containerView.widthAnchor.constraint(equalTo: view.widthAnchor, multiplier: 0.85),
            containerView.heightAnchor.constraint(lessThanOrEqualTo: view.heightAnchor, multiplier: 0.7)
        ])

        // 创建小组件预览视图
        setupWidgetPreview()

        // 设置头部视图
        headerView.backgroundColor = .systemBackground
        headerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(headerView)

        // 设置标题标签
        titleLabel.text = "应用详情"
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .semibold)
        titleLabel.textAlignment = .center
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(titleLabel)

        // 设置关闭按钮
        closeButton.setImage(UIImage(systemName: "xmark.circle.fill"), for: .normal)
        closeButton.tintColor = .systemGray
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        headerView.addSubview(closeButton)

        // 设置内容视图
        contentView.backgroundColor = .systemBackground
        contentView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(contentView)

        // 设置图标图像视图
        iconImageView.contentMode = .scaleAspectFit
        iconImageView.backgroundColor = .systemBlue
        iconImageView.layer.cornerRadius = 30
        iconImageView.clipsToBounds = true
        iconImageView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(iconImageView)

        // 设置名称标签
        nameLabel.font = UIFont.systemFont(ofSize: 20, weight: .bold)
        nameLabel.textAlignment = .center
        nameLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(nameLabel)

        // 设置ID标签
        idLabel.font = UIFont.systemFont(ofSize: 14)
        idLabel.textColor = .systemGray
        idLabel.textAlignment = .center
        idLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(idLabel)

        // 设置底部视图
        footerView.backgroundColor = .systemBackground
        footerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(footerView)

        // 设置预览切换按钮
        previewToggleButton.setTitle("查看小组件", for: .normal)
        previewToggleButton.setImage(UIImage(systemName: "square.grid.2x2"), for: .normal)
        previewToggleButton.backgroundColor = .systemGray5
        previewToggleButton.setTitleColor(.systemBlue, for: .normal)
        previewToggleButton.layer.cornerRadius = 12
        previewToggleButton.addTarget(self, action: #selector(previewToggleButtonTapped), for: .touchUpInside)
        previewToggleButton.translatesAutoresizingMaskIntoConstraints = false
        footerView.addSubview(previewToggleButton)

        // 设置操作按钮
        actionButton.setTitle("打开应用", for: .normal)
        actionButton.backgroundColor = .systemBlue
        actionButton.setTitleColor(.white, for: .normal)
        actionButton.layer.cornerRadius = 12
        actionButton.addTarget(self, action: #selector(actionButtonTapped), for: .touchUpInside)
        actionButton.translatesAutoresizingMaskIntoConstraints = false
        footerView.addSubview(actionButton)

        // 设置约束
        NSLayoutConstraint.activate([
            // 头部视图约束
            headerView.topAnchor.constraint(equalTo: containerView.topAnchor),
            headerView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            headerView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            headerView.heightAnchor.constraint(equalToConstant: 60),

            // 标题标签约束
            titleLabel.centerXAnchor.constraint(equalTo: headerView.centerXAnchor),
            titleLabel.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),

            // 关闭按钮约束
            closeButton.trailingAnchor.constraint(equalTo: headerView.trailingAnchor, constant: -16),
            closeButton.centerYAnchor.constraint(equalTo: headerView.centerYAnchor),
            closeButton.widthAnchor.constraint(equalToConstant: 30),
            closeButton.heightAnchor.constraint(equalToConstant: 30),

            // 内容视图约束
            contentView.topAnchor.constraint(equalTo: headerView.bottomAnchor),
            contentView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            contentView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),

            // 图标图像视图约束
            iconImageView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 20),
            iconImageView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            iconImageView.widthAnchor.constraint(equalToConstant: 60),
            iconImageView.heightAnchor.constraint(equalToConstant: 60),

            // 名称标签约束
            nameLabel.topAnchor.constraint(equalTo: iconImageView.bottomAnchor, constant: 16),
            nameLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            nameLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),

            // ID标签约束
            idLabel.topAnchor.constraint(equalTo: nameLabel.bottomAnchor, constant: 8),
            idLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            idLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            idLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -20),

            // 底部视图约束
            footerView.topAnchor.constraint(equalTo: contentView.bottomAnchor),
            footerView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            footerView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            footerView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            footerView.heightAnchor.constraint(equalToConstant: 80),

            // 预览切换按钮约束
            previewToggleButton.leadingAnchor.constraint(equalTo: footerView.leadingAnchor, constant: 16),
            previewToggleButton.centerYAnchor.constraint(equalTo: footerView.centerYAnchor),
            previewToggleButton.widthAnchor.constraint(equalToConstant: 120),
            previewToggleButton.heightAnchor.constraint(equalToConstant: 40),

            // 操作按钮约束
            actionButton.trailingAnchor.constraint(equalTo: footerView.trailingAnchor, constant: -16),
            actionButton.centerYAnchor.constraint(equalTo: footerView.centerYAnchor),
            actionButton.widthAnchor.constraint(equalToConstant: 120),
            actionButton.heightAnchor.constraint(equalToConstant: 40)
        ])
    }

    // MARK: - 手势设置

    private func setupGestures() {
        // 添加点击手势，用于关闭预览
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTapGesture(_:)))
        tapGesture.delegate = self
        view.addGestureRecognizer(tapGesture)

        // 添加平移手势，用于拖动关闭
        panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePanGesture(_:)))
        containerView.addGestureRecognizer(panGesture)
    }

    // MARK: - 数据加载

    private func loadItemData() {
        // 设置名称和ID
        nameLabel.text = itemName ?? "未知应用"
        idLabel.text = "ID: \(itemId ?? "未知")"

        // 如果是应用启动器小组件，尝试加载应用数据
        if widgetType == "appLauncher", let itemId = itemId {
            // 从AppGroupDataManager加载数据
            if let config = AppGroupDataManager.shared.read(AppLauncherWidgetData.self, for: .appLauncher, property: .config) {
                // 查找匹配的项目
                if let matchedItem = config.items.first(where: { $0.id == itemId }) {
                    // 更新UI
                    updateUI(with: matchedItem)
                }
            }
        }
    }

    private func updateUI(with item: AppLauncherItem) {
        self.item = item

        // 更新名称
        nameLabel.text = item.name

        // 更新ID
        idLabel.text = "ID: \(item.id)"

        // 更新图标
        iconImageView.backgroundColor = UIColor(item.color)

        // 设置图标
        if let image = UIImage(systemName: item.iconName) {
            iconImageView.image = image.withRenderingMode(.alwaysTemplate)
            iconImageView.tintColor = .white
        }

        // 更新操作按钮
        if !item.urlScheme.isEmpty {
            actionButton.setTitle("打开 \(item.name)", for: .normal)
        } else {
            actionButton.setTitle("关闭", for: .normal)
        }
    }

    // MARK: - 小组件预览

    private func setupWidgetPreview() {
        // 创建小组件预览视图
        if let widgetType = widgetType {
            // 确保在设置约束之前，所有相关视图都已添加到视图层次结构中
            // 这是为了确保它们有共同的祖先视图

            // 创建预览视图
            let previewView = WidgetPreviewView(widgetType: widgetType, itemId: itemId)
                .environmentObject(ThemeManager.shared)

            // 创建托管控制器
            widgetPreviewHostingController = UIHostingController(rootView: AnyView(previewView))

            if let previewVC = widgetPreviewHostingController {
                // 配置预览控制器
                previewVC.view.backgroundColor = .clear
                previewVC.view.translatesAutoresizingMaskIntoConstraints = false

                // 初始时隐藏预览
                previewVC.view.isHidden = true

                // 重要：确保在设置约束之前，所有相关视图都已添加到视图层次结构中
                // 先将预览视图添加为子视图
                addChild(previewVC)
                containerView.addSubview(previewVC.view)
                previewVC.didMove(toParent: self)
            
                // 确保headerView和footerView已经添加到containerView
                // 这些视图应该在setupUI方法中已经添加

                // 使用安全的方式设置约束
                if previewVC.view.superview == containerView &&
                   headerView.superview == containerView &&
                   footerView.superview == containerView {
                    // 设置约束
                    NSLayoutConstraint.activate([
                        previewVC.view.topAnchor.constraint(equalTo: headerView.bottomAnchor),
                        previewVC.view.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                        previewVC.view.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
                        previewVC.view.bottomAnchor.constraint(equalTo: footerView.topAnchor)
                    ])
                } else {
                    print("错误：无法设置约束，视图层次结构不正确")
                    // 使用安全的替代约束
                    NSLayoutConstraint.activate([
                        previewVC.view.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 60),
                        previewVC.view.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
                        previewVC.view.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
                        previewVC.view.heightAnchor.constraint(equalToConstant: 200)
                    ])
                }
            }
        }
    }

    // MARK: - 操作处理

    @objc private func closeButtonTapped() {
        dismiss(animated: true)
    }

    @objc private func previewToggleButtonTapped() {
        // 切换预览视图的可见性
        if let previewView = widgetPreviewHostingController?.view {
            // 切换可见性
            let isShowingPreview = !previewView.isHidden

            // 更新UI
            UIView.animate(withDuration: 0.3) {
                previewView.isHidden = isShowingPreview
                self.contentView.isHidden = !isShowingPreview

                // 更新按钮标题和图标
                if isShowingPreview {
                    self.previewToggleButton.setTitle("查看小组件", for: .normal)
                    self.previewToggleButton.setImage(UIImage(systemName: "square.grid.2x2"), for: .normal)
                } else {
                    self.previewToggleButton.setTitle("查看详情", for: .normal)
                    self.previewToggleButton.setImage(UIImage(systemName: "info.circle"), for: .normal)
                }
            }
        }
    }

    @objc private func actionButtonTapped() {
        if let item = item, !item.urlScheme.isEmpty {
            // 尝试打开URL
            if let url = URL(string: item.urlScheme) {
                // 首先检查是否可以打开URL
                if UIApplication.shared.canOpenURL(url) {
                    // 可以打开URL，尝试打开
                    UIApplication.shared.open(url, options: [:]) { [weak self] success in
                        guard let self = self else { return }

                        if success {
                            // 成功打开URL后关闭预览
                            self.dismiss(animated: true)
                        } else {
                            // 显示错误提示
                            self.showAlert(title: "无法打开应用", message: "无法打开URL: \(item.urlScheme)")
                        }
                    }
                } else {
                    // 无法打开URL，显示错误提示
                    showAlert(title: "应用未安装", message: "未安装应用或无法打开URL: \(item.urlScheme)")
                }
            } else {
                // URL格式无效，显示错误提示
                showAlert(title: "无效的URL", message: "URL格式无效: \(item.urlScheme)")
            }
        } else {
            // 如果没有URL，直接关闭
            dismiss(animated: true)
        }
    }

    private func showAlert(title: String, message: String) {
        // 创建警告控制器
        let alertController = UIAlertController(title: title, message: message, preferredStyle: .alert)

        // 添加确定按钮，点击后关闭预览
        alertController.addAction(UIAlertAction(title: "确定", style: .default) { [weak self] _ in
            // 安全地关闭预览
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                self?.dismiss(animated: true)
            }
        })

        // 安全地显示警告
        let keyWindow = UIApplication.shared.windows.first(where: { $0.isKeyWindow }) ?? UIApplication.shared.windows.first
        if let rootVC = keyWindow?.rootViewController {
            // 如果当前控制器已经被关闭，使用根视图控制器显示警告
            if self.presentingViewController == nil {
                rootVC.present(alertController, animated: true)
            } else {
                // 否则使用当前控制器显示警告
                present(alertController, animated: true)
            }
        } else {
            // 如果无法获取根视图控制器，尝试直接显示
            present(alertController, animated: true)
        }
    }

    // MARK: - 手势处理

    @objc private func handleTapGesture(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: view)
        if !containerView.frame.contains(location) {
            dismiss(animated: true)
        }
    }

    @objc private func handlePanGesture(_ gesture: UIPanGestureRecognizer) {
        let translation = gesture.translation(in: view)

        switch gesture.state {
        case .began:
            initialCenter = containerView.center
        case .changed:
            containerView.center = CGPoint(x: initialCenter.x, y: initialCenter.y + translation.y)

            // 根据拖动距离调整透明度
            let distance = abs(containerView.center.y - initialCenter.y)
            let alpha = 1.0 - min(distance / dismissalThreshold, 0.6)
            view.backgroundColor = UIColor.black.withAlphaComponent(0.5 * alpha)

        case .ended, .cancelled:
            let velocity = gesture.velocity(in: view)
            let distance = abs(containerView.center.y - initialCenter.y)

            // 如果拖动距离超过阈值或速度足够快，则关闭预览
            if distance > dismissalThreshold || abs(velocity.y) > 1000 {
                dismiss(animated: true)
            } else {
                // 否则恢复原位
                UIView.animate(withDuration: 0.3) {
                    self.containerView.center = self.initialCenter
                    self.view.backgroundColor = UIColor.black.withAlphaComponent(0.5)
                }
            }

        default:
            break
        }
    }
}

// MARK: - UIGestureRecognizerDelegate

extension ItemPreviewController: UIGestureRecognizerDelegate {
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        // 确保点击容器视图外部才触发手势
        let location = touch.location(in: view)
        return !containerView.frame.contains(location)
    }
}
