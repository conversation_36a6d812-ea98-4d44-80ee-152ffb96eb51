//
//  UIHostingController+Popover.swift
//  JZJJWidget
//
//  Created by yjzheng on 2025/5/20.
//

import SwiftUI
import UIKit

// MARK: - UIHostingController 扩展，用于在 SwiftUI 中显示 popover

extension UIHostingController {
    /// 显示 popover
    /// - Parameters:
    ///   - sourceView: 源视图
    ///   - sourceRect: 源视图中的矩形区域
    ///   - permittedArrowDirections: 允许的箭头方向
    ///   - delegate: popover 代理
    func showPopover(
        from sourceView: UIView,
        sourceRect: CGRect,
        permittedArrowDirections: UIPopoverArrowDirection = [.up, .down],
        delegate: UIPopoverPresentationControllerDelegate? = nil
    ) {
        modalPresentationStyle = .popover
        
        if let popoverController = popoverPresentationController {
            popoverController.sourceView = sourceView
            popoverController.sourceRect = sourceRect
            popoverController.permittedArrowDirections = permittedArrowDirections
            popoverController.delegate = delegate
        }
        
        sourceView.window?.rootViewController?.present(self, animated: true)
    }
}

// MARK: - SwiftUI 扩展，用于在 SwiftUI 中显示 popover

extension View {
    /// 显示 popover
    /// - Parameters:
    ///   - isPresented: 绑定值，控制 popover 的显示和隐藏
    ///   - content: popover 内容
    ///   - sourceView: 源视图
    ///   - sourceRect: 源视图中的矩形区域
    ///   - permittedArrowDirections: 允许的箭头方向
    ///   - delegate: popover 代理
    func showPopover<Content: View>(
        isPresented: Binding<Bool>,
        content: @escaping () -> Content,
        from sourceView: UIView,
        sourceRect: CGRect,
        permittedArrowDirections: UIPopoverArrowDirection = [.up, .down],
        delegate: UIPopoverPresentationControllerDelegate? = nil
    ) -> some View {
        self.onChange(of: isPresented.wrappedValue) { newValue in
            if newValue {
                let hostingController = UIHostingController(rootView: content())
                hostingController.showPopover(
                    from: sourceView,
                    sourceRect: sourceRect,
                    permittedArrowDirections: permittedArrowDirections,
                    delegate: delegate
                )
            }
        }
    }
}

// MARK: - UIPopoverPresentationControllerDelegate 扩展，用于强制使用 popover 样式

class PopoverDelegate: NSObject, UIPopoverPresentationControllerDelegate {
    func adaptivePresentationStyle(for controller: UIPresentationController) -> UIModalPresentationStyle {
        return .none // 强制使用 popover 样式，不适应为其他样式
    }
}
