<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>INEnums</key>
	<array>
		<dict>
			<key>INEnumDisplayName</key>
			<string>Static Config Btn Type</string>
			<key>INEnumDisplayNameID</key>
			<string>EyQRTp</string>
			<key>INEnumGeneratesHeader</key>
			<true/>
			<key>INEnumName</key>
			<string>StaticConfigBtnType</string>
			<key>INEnumType</key>
			<string>Regular</string>
			<key>INEnumValues</key>
			<array>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>unknown</string>
					<key>INEnumValueDisplayNameID</key>
					<string>4kiCGj</string>
					<key>INEnumValueName</key>
					<string>unknown</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>daily quote</string>
					<key>INEnumValueDisplayNameID</key>
					<string>25z1q2</string>
					<key>INEnumValueIndex</key>
					<integer>1</integer>
					<key>INEnumValueName</key>
					<string>dailyQuote</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>scan</string>
					<key>INEnumValueDisplayNameID</key>
					<string>qxP70q</string>
					<key>INEnumValueIndex</key>
					<integer>2</integer>
					<key>INEnumValueName</key>
					<string>Scan</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>todo list</string>
					<key>INEnumValueDisplayNameID</key>
					<string>vCMUNb</string>
					<key>INEnumValueIndex</key>
					<integer>3</integer>
					<key>INEnumValueName</key>
					<string>todoList</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>time clock</string>
					<key>INEnumValueDisplayNameID</key>
					<string>5QJ2sT</string>
					<key>INEnumValueIndex</key>
					<integer>5</integer>
					<key>INEnumValueName</key>
					<string>timeClock</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>water intake</string>
					<key>INEnumValueDisplayNameID</key>
					<string>H2dGqu</string>
					<key>INEnumValueIndex</key>
					<integer>6</integer>
					<key>INEnumValueName</key>
					<string>waterIntake</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>password generator</string>
					<key>INEnumValueDisplayNameID</key>
					<string>GDOn1Z</string>
					<key>INEnumValueIndex</key>
					<integer>7</integer>
					<key>INEnumValueName</key>
					<string>passwordGenerator</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>pomodoro</string>
					<key>INEnumValueDisplayNameID</key>
					<string>rDTU38</string>
					<key>INEnumValueIndex</key>
					<integer>8</integer>
					<key>INEnumValueName</key>
					<string>pomodoro</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>note</string>
					<key>INEnumValueDisplayNameID</key>
					<string>KoVD2G</string>
					<key>INEnumValueIndex</key>
					<integer>9</integer>
					<key>INEnumValueName</key>
					<string>note</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>moon phase</string>
					<key>INEnumValueDisplayNameID</key>
					<string>pOWHat</string>
					<key>INEnumValueIndex</key>
					<integer>10</integer>
					<key>INEnumValueName</key>
					<string>moonPhase</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>app launcher</string>
					<key>INEnumValueDisplayNameID</key>
					<string>tRLnBD</string>
					<key>INEnumValueIndex</key>
					<integer>11</integer>
					<key>INEnumValueName</key>
					<string>appLauncher</string>
				</dict>
				<dict>
					<key>INEnumValueDisplayName</key>
					<string>device info</string>
					<key>INEnumValueDisplayNameID</key>
					<string>Ymazyc</string>
					<key>INEnumValueIndex</key>
					<integer>12</integer>
					<key>INEnumValueName</key>
					<string>deviceInfo</string>
				</dict>
			</array>
		</dict>
	</array>
	<key>INIntentDefinitionModelVersion</key>
	<string>1.2</string>
	<key>INIntentDefinitionNamespace</key>
	<string>5OLjI4</string>
	<key>INIntentDefinitionSystemVersion</key>
	<string>24F74</string>
	<key>INIntentDefinitionToolsBuildVersion</key>
	<string>16E140</string>
	<key>INIntentDefinitionToolsVersion</key>
	<string>16.3</string>
	<key>INIntents</key>
	<array>
		<dict>
			<key>INIntentCategory</key>
			<string>information</string>
			<key>INIntentDescription</key>
			<string>StaticConfig</string>
			<key>INIntentDescriptionID</key>
			<string>rrKEx5</string>
			<key>INIntentEligibleForWidgets</key>
			<true/>
			<key>INIntentIneligibleForSuggestions</key>
			<true/>
			<key>INIntentLastParameterTag</key>
			<integer>2</integer>
			<key>INIntentName</key>
			<string>StaticConfig</string>
			<key>INIntentParameters</key>
			<array>
				<dict>
					<key>INIntentParameterConfigurable</key>
					<true/>
					<key>INIntentParameterDisplayName</key>
					<string>Btn Type</string>
					<key>INIntentParameterDisplayNameID</key>
					<string>kp8kEu</string>
					<key>INIntentParameterDisplayPriority</key>
					<integer>1</integer>
					<key>INIntentParameterEnumType</key>
					<string>StaticConfigBtnType</string>
					<key>INIntentParameterEnumTypeNamespace</key>
					<string>5OLjI4</string>
					<key>INIntentParameterName</key>
					<string>btnType</string>
					<key>INIntentParameterPromptDialogs</key>
					<array>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogType</key>
							<string>Configuration</string>
						</dict>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogType</key>
							<string>Primary</string>
						</dict>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogFormatString</key>
							<string>There are ${count} options matching ‘${btnType}’.</string>
							<key>INIntentParameterPromptDialogFormatStringID</key>
							<string>tdqNlz</string>
							<key>INIntentParameterPromptDialogType</key>
							<string>DisambiguationIntroduction</string>
						</dict>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogFormatString</key>
							<string>Just to confirm, you wanted ‘${btnType}’?</string>
							<key>INIntentParameterPromptDialogFormatStringID</key>
							<string>lUZpdl</string>
							<key>INIntentParameterPromptDialogType</key>
							<string>Confirmation</string>
						</dict>
					</array>
					<key>INIntentParameterTag</key>
					<integer>2</integer>
					<key>INIntentParameterType</key>
					<string>Integer</string>
				</dict>
			</array>
			<key>INIntentResponse</key>
			<dict>
				<key>INIntentResponseCodes</key>
				<array>
					<dict>
						<key>INIntentResponseCodeName</key>
						<string>success</string>
						<key>INIntentResponseCodeSuccess</key>
						<true/>
					</dict>
					<dict>
						<key>INIntentResponseCodeName</key>
						<string>failure</string>
					</dict>
				</array>
			</dict>
			<key>INIntentTitle</key>
			<string>StaticConfig</string>
			<key>INIntentTitleID</key>
			<string>iEBixT</string>
			<key>INIntentType</key>
			<string>Custom</string>
			<key>INIntentVerb</key>
			<string>View</string>
		</dict>
		<dict>
			<key>INIntentCategory</key>
			<string>information</string>
			<key>INIntentDescription</key>
			<string>请先到软件内添加组件后选择</string>
			<key>INIntentDescriptionID</key>
			<string>TYqP9A</string>
			<key>INIntentEligibleForWidgets</key>
			<true/>
			<key>INIntentIneligibleForSuggestions</key>
			<true/>
			<key>INIntentLastParameterTag</key>
			<integer>10</integer>
			<key>INIntentName</key>
			<string>DynamicConfig</string>
			<key>INIntentParameters</key>
			<array>
				<dict>
					<key>INIntentParameterConfigurable</key>
					<true/>
					<key>INIntentParameterDisplayName</key>
					<string>选择组件</string>
					<key>INIntentParameterDisplayNameID</key>
					<string>crY2KA</string>
					<key>INIntentParameterDisplayPriority</key>
					<integer>1</integer>
					<key>INIntentParameterName</key>
					<string>selectButton</string>
					<key>INIntentParameterObjectType</key>
					<string>CustomButtonItem</string>
					<key>INIntentParameterObjectTypeNamespace</key>
					<string>5OLjI4</string>
					<key>INIntentParameterPromptDialogs</key>
					<array>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogFormatString</key>
							<string>请先到软件内添加组件后选择</string>
							<key>INIntentParameterPromptDialogFormatStringID</key>
							<string>Me2V8Y</string>
							<key>INIntentParameterPromptDialogType</key>
							<string>Configuration</string>
						</dict>
						<dict>
							<key>INIntentParameterPromptDialogCustom</key>
							<true/>
							<key>INIntentParameterPromptDialogType</key>
							<string>Primary</string>
						</dict>
					</array>
					<key>INIntentParameterSupportsDynamicEnumeration</key>
					<true/>
					<key>INIntentParameterSupportsSearch</key>
					<true/>
					<key>INIntentParameterTag</key>
					<integer>10</integer>
					<key>INIntentParameterType</key>
					<string>Object</string>
				</dict>
			</array>
			<key>INIntentResponse</key>
			<dict>
				<key>INIntentResponseCodes</key>
				<array>
					<dict>
						<key>INIntentResponseCodeName</key>
						<string>success</string>
						<key>INIntentResponseCodeSuccess</key>
						<true/>
					</dict>
					<dict>
						<key>INIntentResponseCodeName</key>
						<string>failure</string>
					</dict>
				</array>
			</dict>
			<key>INIntentTitle</key>
			<string>选择组件</string>
			<key>INIntentTitleID</key>
			<string>UmpY3N</string>
			<key>INIntentType</key>
			<string>Custom</string>
			<key>INIntentVerb</key>
			<string>View</string>
		</dict>
	</array>
	<key>INTypes</key>
	<array>
		<dict>
			<key>INTypeDisplayName</key>
			<string>Custom Button Item</string>
			<key>INTypeDisplayNameID</key>
			<string>D78TrN</string>
			<key>INTypeLastPropertyTag</key>
			<integer>104</integer>
			<key>INTypeName</key>
			<string>CustomButtonItem</string>
			<key>INTypeProperties</key>
			<array>
				<dict>
					<key>INTypePropertyDefault</key>
					<true/>
					<key>INTypePropertyDisplayPriority</key>
					<integer>1</integer>
					<key>INTypePropertyName</key>
					<string>identifier</string>
					<key>INTypePropertyTag</key>
					<integer>1</integer>
					<key>INTypePropertyType</key>
					<string>String</string>
				</dict>
				<dict>
					<key>INTypePropertyDefault</key>
					<true/>
					<key>INTypePropertyDisplayPriority</key>
					<integer>2</integer>
					<key>INTypePropertyName</key>
					<string>displayString</string>
					<key>INTypePropertyTag</key>
					<integer>2</integer>
					<key>INTypePropertyType</key>
					<string>String</string>
				</dict>
				<dict>
					<key>INTypePropertyDefault</key>
					<true/>
					<key>INTypePropertyDisplayPriority</key>
					<integer>3</integer>
					<key>INTypePropertyName</key>
					<string>pronunciationHint</string>
					<key>INTypePropertyTag</key>
					<integer>3</integer>
					<key>INTypePropertyType</key>
					<string>String</string>
				</dict>
				<dict>
					<key>INTypePropertyDefault</key>
					<true/>
					<key>INTypePropertyDisplayPriority</key>
					<integer>4</integer>
					<key>INTypePropertyName</key>
					<string>alternativeSpeakableMatches</string>
					<key>INTypePropertySupportsMultipleValues</key>
					<true/>
					<key>INTypePropertyTag</key>
					<integer>4</integer>
					<key>INTypePropertyType</key>
					<string>SpeakableString</string>
				</dict>
				<dict>
					<key>INTypePropertyDisplayName</key>
					<string>Url Str</string>
					<key>INTypePropertyDisplayNameID</key>
					<string>xLIuwg</string>
					<key>INTypePropertyDisplayPriority</key>
					<integer>5</integer>
					<key>INTypePropertyName</key>
					<string>urlStr</string>
					<key>INTypePropertyTag</key>
					<integer>100</integer>
					<key>INTypePropertyType</key>
					<string>String</string>
				</dict>
				<dict>
					<key>INTypePropertyDisplayName</key>
					<string>Image Name</string>
					<key>INTypePropertyDisplayNameID</key>
					<string>dT1HXv</string>
					<key>INTypePropertyDisplayPriority</key>
					<integer>6</integer>
					<key>INTypePropertyName</key>
					<string>imageName</string>
					<key>INTypePropertyTag</key>
					<integer>101</integer>
					<key>INTypePropertyType</key>
					<string>String</string>
				</dict>
				<dict>
					<key>INTypePropertyDisplayName</key>
					<string>Button Type</string>
					<key>INTypePropertyDisplayNameID</key>
					<string>IwGXsL</string>
					<key>INTypePropertyDisplayPriority</key>
					<integer>7</integer>
					<key>INTypePropertyEnumType</key>
					<string>StaticConfigBtnType</string>
					<key>INTypePropertyEnumTypeNamespace</key>
					<string>5OLjI4</string>
					<key>INTypePropertyName</key>
					<string>buttonType</string>
					<key>INTypePropertyTag</key>
					<integer>104</integer>
					<key>INTypePropertyType</key>
					<string>Integer</string>
				</dict>
			</array>
		</dict>
	</array>
</dict>
</plist>
