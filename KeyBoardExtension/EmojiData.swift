//
//  EmojiData.swift
//  KeyBoardExtension
//
//  Created by JZJJWidget
//

import Foundation

/// 表情符号分类
enum EmojiCategory: String, CaseIterable {
    case smileys = "😀"     // 笑脸与人物
    case hearts = "❤️"      // 心形与爱情
    case animals = "🐶"     // 动物与自然
    case food = "🍎"        // 食物与饮料
    case sports = "⚽"      // 运动与活动
    case travel = "🚗"      // 交通与地点
    case objects = "💡"     // 物品与符号
    case flags = "🏳️"       // 旗帜
    
    var title: String {
        switch self {
        case .smileys: return "笑脸"
        case .hearts: return "心形"
        case .animals: return "动物"
        case .food: return "食物"
        case .sports: return "运动"
        case .travel: return "交通"
        case .objects: return "物品"
        case .flags: return "旗帜"
        }
    }
}

/// 表情符号数据管理器
struct EmojiDataManager {
    
    /// 获取指定分类的表情符号
    static func getEmojis(for category: EmojiCategory) -> [String] {
        switch category {
        case .smileys:
            return [
                "😀", "😃", "😄", "😁", "😆", "😅", "🤣", "😂", "🙂", "🙃",
                "😉", "😊", "😇", "🥰", "😍", "🤩", "😘", "😗", "😚", "😙",
                "😋", "😛", "😜", "🤪", "😝", "🤑", "🤗", "🤭", "🤫", "🤔",
                "🤐", "🤨", "😐", "😑", "😶", "😏", "😒", "🙄", "😬", "🤥",
                "😔", "😪", "🤤", "😴", "😷", "🤒", "🤕", "🤢", "🤮", "🤧"
            ]
        case .hearts:
            return [
                "❤️", "🧡", "💛", "💚", "💙", "💜", "🖤", "🤍", "🤎", "💔",
                "❣️", "💕", "💞", "💓", "💗", "💖", "💘", "💝", "💟", "♥️",
                "💋", "💌", "💐", "🌹", "🌷", "🌺", "🌸", "🌻", "🌼", "💒"
            ]
        case .animals:
            return [
                "🐶", "🐱", "🐭", "🐹", "🐰", "🦊", "🐻", "🐼", "🐨", "🐯",
                "🦁", "🐮", "🐷", "🐽", "🐸", "🐵", "🙈", "🙉", "🙊", "🐒",
                "🐔", "🐧", "🐦", "🐤", "🐣", "🐥", "🦆", "🦅", "🦉", "🦇",
                "🐺", "🐗", "🐴", "🦄", "🐝", "🐛", "🦋", "🐌", "🐞", "🐜"
            ]
        case .food:
            return [
                "🍎", "🍐", "🍊", "🍋", "🍌", "🍉", "🍇", "🍓", "🍈", "🍒",
                "🍑", "🥭", "🍍", "🥥", "🥝", "🍅", "🍆", "🥑", "🥦", "🥬",
                "🥒", "🌶️", "🌽", "🥕", "🧄", "🧅", "🥔", "🍠", "🥐", "🍞",
                "🥖", "🥨", "🧀", "🥚", "🍳", "🧈", "🥞", "🧇", "🥓", "🥩"
            ]
        case .sports:
            return [
                "⚽", "🏀", "🏈", "⚾", "🥎", "🎾", "🏐", "🏉", "🥏", "🎱",
                "🪀", "🏓", "🏸", "🏒", "🏑", "🥍", "🏏", "🪃", "🥅", "⛳",
                "🪁", "🏹", "🎣", "🤿", "🥊", "🥋", "🎽", "🛹", "🛷", "⛸️",
                "🥌", "🎿", "⛷️", "🏂", "🪂", "🏋️", "🤼", "🤸", "⛹️", "🤺"
            ]
        case .travel:
            return [
                "🚗", "🚕", "🚙", "🚌", "🚎", "🏎️", "🚓", "🚑", "🚒", "🚐",
                "🛻", "🚚", "🚛", "🚜", "🏍️", "🛵", "🚲", "🛴", "🛺", "🚨",
                "🚔", "🚍", "🚘", "🚖", "🚡", "🚠", "🚟", "🚃", "🚋", "🚞",
                "🚝", "🚄", "🚅", "🚈", "🚂", "🚆", "🚇", "🚊", "🚉", "✈️"
            ]
        case .objects:
            return [
                "💡", "🔦", "🕯️", "🪔", "🧯", "🛢️", "💸", "💵", "💴", "💶",
                "💷", "🪙", "💰", "💳", "💎", "⚖️", "🪜", "🧰", "🔧", "🔨",
                "⚒️", "🛠️", "⛏️", "🔩", "⚙️", "🪚", "🔫", "💣", "🧨", "🪓",
                "🔪", "🗡️", "⚔️", "🛡️", "🚬", "⚰️", "🪦", "⚱️", "🏺", "🔮"
            ]
        case .flags:
            return [
                "🏳️", "🏴", "🏁", "🚩", "🏳️‍🌈", "🏳️‍⚧️", "🇺🇳", "🇨🇳", "🇺🇸", "🇯🇵",
                "🇰🇷", "🇬🇧", "🇫🇷", "🇩🇪", "🇮🇹", "🇪🇸", "🇷🇺", "🇨🇦", "🇦🇺", "🇧🇷",
                "🇮🇳", "🇲🇽", "🇿🇦", "🇸🇬", "🇹🇭", "🇻🇳", "🇵🇭", "🇲🇾", "🇮🇩", "🇳🇱"
            ]
        }
    }
    
    /// 获取常用表情符号
    static func getFrequentlyUsedEmojis() -> [String] {
        return [
            "😀", "😂", "🥰", "😍", "😘", "😊", "😉", "😎", "🤔", "😅",
            "❤️", "💕", "💖", "💯", "👍", "👌", "✌️", "🤝", "🙏", "💪",
            "🎉", "🎊", "🔥", "⭐", "✨", "💫", "🌟", "🎈", "🎁", "🎂"
        ]
    }
}
