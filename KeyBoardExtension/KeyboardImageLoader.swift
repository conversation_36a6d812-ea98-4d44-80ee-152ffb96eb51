//
//  KeyboardImageLoader.swift
//  KeyBoardExtension
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import UIKit
import Foundation

/// 图片类型（键盘扩展版本）
enum ImageType: String {
    case background = "background"
    case key = "key"
}

/// 按键状态
enum KeyState: String {
    case normal = "normal"
    case pressed = "pressed"
    case hover = "hover"
}

/// 线程安全的图片缓存（键盘扩展版本）
private class ExtensionImageCache: @unchecked Sendable {
    private var cache: [String: UIImage] = [:]
    private let queue = DispatchQueue(label: "extension.image.cache.queue", attributes: .concurrent)

    func get(_ key: String) -> UIImage? {
        return queue.sync {
            return cache[key]
        }
    }

    func set(_ key: String, _ image: UIImage) {
        queue.async(flags: .barrier) {
            self.cache[key] = image
        }
    }

    func remove(_ key: String) {
        queue.async(flags: .barrier) {
            self.cache.removeValue(forKey: key)
        }
    }

    func removeAll() {
        queue.async(flags: .barrier) {
            self.cache.removeAll()
        }
    }

    var count: Int {
        return queue.sync {
            return cache.count
        }
    }
}

/// 键盘扩展图片加载器
class KeyboardImageLoader {

    // MARK: - 单例
    static let shared = KeyboardImageLoader()

    // MARK: - 属性
    private let fileManager = FileManager.default
    private let imageCache = ExtensionImageCache()
    private let cacheQueue = DispatchQueue(label: "keyboard.extension.image.cache", qos: .userInitiated)

    // App Groups 目录
    private var appGroupContainer: URL? {
        return fileManager.containerURL(forSecurityApplicationGroupIdentifier: "group.com.ort.JZJJWidgetAPP.group")
    }

    // 主题包源目录（直接从主题包加载）
    private var themePacksDirectory: URL? {
        return appGroupContainer?.appendingPathComponent("theme-packs")
    }

    private var builtInThemePacksDirectory: URL? {
        return themePacksDirectory?.appendingPathComponent("built-in")
    }

    private var customThemePacksDirectory: URL? {
        return themePacksDirectory?.appendingPathComponent("custom")
    }

    // 缓存目录（保留作为备用）
    private var cacheDirectory: URL? {
        return appGroupContainer?.appendingPathComponent("keyboard_themes")
    }

    private var builtInCacheDirectory: URL? {
        return cacheDirectory?.appendingPathComponent("built-in")
    }

    private var customCacheDirectory: URL? {
        return cacheDirectory?.appendingPathComponent("custom")
    }

    // MARK: - 初始化
    private init() {
        // 监听内存警告
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleMemoryWarning),
            name: UIApplication.didReceiveMemoryWarningNotification,
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - 图片加载
    /// 加载主题背景图片
    func loadBackgroundImage(for theme: KeyboardTheme) -> UIImage? {
        guard theme.hasBackgroundImage,
              let imagePath = theme.backgroundImagePath else {
            return nil
        }

        return loadImage(
            for: imagePath,
            type: .background,
            isBuiltIn: theme.isBuiltInImageTheme
        )
    }

    /// 加载主题按键图片
    func loadKeyImage(for theme: KeyboardTheme) -> UIImage? {
        guard theme.hasKeyImage,
              let imagePath = theme.keyImagePath else {
            return nil
        }

        return loadImage(
            for: imagePath,
            type: .key,
            isBuiltIn: theme.isBuiltInImageTheme
        )
    }

    /// 加载个性化按键图片
    func loadIndividualKeyImage(
        for keyValue: String,
        keyConfig: KeyConfig,
        themeId: String,
        state: KeyState = .normal,
        isBuiltIn: Bool = true
    ) -> UIImage? {
        guard keyConfig.hasCustomImage else {
            return nil
        }

        // 根据状态选择图片路径
        let imagePath: String?
        switch state {
        case .normal:
            imagePath = keyConfig.normalImagePath
        case .pressed:
            imagePath = keyConfig.pressedImagePath
        case .hover:
            imagePath = keyConfig.hoverImagePath
        }

        guard let path = imagePath else {
            return nil
        }

        return loadIndividualKeyImageFromPath(
            path: path,
            themeId: themeId,
            keyValue: keyValue,
            state: state,
            isBuiltIn: isBuiltIn
        )
    }

    /// 从指定路径加载个性化按键图片
    private func loadIndividualKeyImageFromPath(
        path: String,
        themeId: String,
        keyValue: String,
        state: KeyState,
        isBuiltIn: Bool
    ) -> UIImage? {
        let cacheKey = "\(themeId)_\(keyValue)_\(state.rawValue)_individual"

        // 先检查缓存
        if let cachedImage = imageCache.get(cacheKey) {
            return cachedImage
        }

        // 从键盘扩展目录加载（个性化图片已经被复制到这里）
        guard let keyboardThemesDir = appGroupContainer?.appendingPathComponent("keyboard_themes") else {
            print("❌ 无法获取键盘主题目录")
            return nil
        }

        let targetSubDir = isBuiltIn ? "built-in" : "custom"
        let targetThemeDir = keyboardThemesDir.appendingPathComponent(targetSubDir)

        // 构建个性化图片的目标文件名
        // 例如：A-key.png -> colorful-vibrant_custom_A-key.png
        let fileName = (path as NSString).lastPathComponent
        let targetFileName = "\(themeId)_custom_\(fileName)"
        let imagePath = targetThemeDir.appendingPathComponent(targetFileName)

        print("🔍 个性化图片加载详情:")
        print("  - 键值: \(keyValue)")
        print("  - 状态: \(state.rawValue)")
        print("  - 原始路径: \(path)")
        print("  - 文件名: \(fileName)")
        print("  - 目标文件名: \(targetFileName)")
        print("  - 完整路径: \(imagePath.path)")
        print("  - 文件存在: \(fileManager.fileExists(atPath: imagePath.path))")

        if let image = loadImageFromFile(at: imagePath, description: "个性化按键(\(keyValue)-\(state.rawValue))") {
            imageCache.set(cacheKey, image)
            print("✅ 个性化图片加载成功: \(targetFileName)")
            return image
        }

        print("❌ 无法加载个性化按键图片: \(path)")
        print("  期望文件: \(imagePath.path)")

        // 列出目标目录中的所有文件以便调试
        do {
            let files = try fileManager.contentsOfDirectory(atPath: targetThemeDir.path)
            let customFiles = files.filter { $0.contains("_custom_") }
            print("  目录中的个性化文件: \(customFiles.joined(separator: ", "))")
        } catch {
            print("  无法列出目录内容: \(error)")
        }

        return nil
    }

    /// 通用图片加载方法（直接从主题包源目录加载）
    private func loadImage(for themeId: String, type: ImageType, isBuiltIn: Bool) -> UIImage? {
        let cacheKey = "\(themeId)_\(type.rawValue)_\(isBuiltIn)"

        // 先检查缓存
        if let cachedImage = imageCache.get(cacheKey) {
            return cachedImage
        }

        // 尝试从主题包源目录加载
        if let image = loadImageFromThemePack(themeId: themeId, type: type, isBuiltIn: isBuiltIn) {
            imageCache.set(cacheKey, image)
            return image
        }

        // 回退到缓存目录加载（兼容性）
        if let image = loadImageFromCache(themeId: themeId, type: type, isBuiltIn: isBuiltIn) {
            imageCache.set(cacheKey, image)
            return image
        }

        print("❌ 无法从任何位置加载图片: \(themeId)_\(type.rawValue)")
        return createFallbackImage(for: type)
    }

    /// 从主题包源目录加载图片
    private func loadImageFromThemePack(themeId: String, type: ImageType, isBuiltIn: Bool) -> UIImage? {
        guard let baseDirectory = isBuiltIn ? builtInThemePacksDirectory : customThemePacksDirectory else {
            print("❌ 无法获取主题包目录: isBuiltIn=\(isBuiltIn)")
            return nil
        }

        let themePackDirectory = baseDirectory.appendingPathComponent(themeId)
        let resourcesDirectory = themePackDirectory.appendingPathComponent("resources")

        let imagePath: URL
        switch type {
        case .background:
            imagePath = resourcesDirectory.appendingPathComponent("backgrounds/keyboard-bg.png")
        case .key:
            // 使用字母键作为通用按键图片
            imagePath = resourcesDirectory.appendingPathComponent("keys/letter-key.png")
        }

        return loadImageFromFile(at: imagePath, description: "主题包(\(themeId))")
    }

    /// 从缓存目录加载图片（兼容性回退）
    private func loadImageFromCache(themeId: String, type: ImageType, isBuiltIn: Bool) -> UIImage? {
        guard let directory = isBuiltIn ? builtInCacheDirectory : customCacheDirectory else {
            print("❌ 无法获取缓存目录: isBuiltIn=\(isBuiltIn)")
            return nil
        }

        let filename = "\(themeId)_\(type.rawValue).png"
        let url = directory.appendingPathComponent(filename)

        return loadImageFromFile(at: url, description: "缓存(\(themeId))")
    }

    /// 从指定文件路径加载图片
    private func loadImageFromFile(at url: URL, description: String) -> UIImage? {
        // 检查文件是否存在
        guard fileManager.fileExists(atPath: url.path) else {
            print("❌ 图片文件不存在: \(url.path)")
            print("   父目录内容: \(listDirectoryContents(url.deletingLastPathComponent()))")
            return nil
        }

        do {
            let data = try Data(contentsOf: url)
            guard !data.isEmpty else {
                print("❌ 图片文件为空: \(url.lastPathComponent)")
                return nil
            }

            guard let image = UIImage(data: data) else {
                print("❌ 无法解析图片数据: \(url.lastPathComponent), 数据大小: \(data.count) bytes")
                return nil
            }

            // 验证图片尺寸
            guard image.size.width > 0 && image.size.height > 0 else {
                print("❌ 图片尺寸无效: \(url.lastPathComponent), 尺寸: \(image.size)")
                return nil
            }

            print("✅ 成功从\(description)加载图片: \(url.lastPathComponent), 尺寸: \(image.size)")
            return image
        } catch {
            print("❌ 读取图片文件失败: \(url.lastPathComponent), 错误: \(error.localizedDescription)")
            return nil
        }
    }

    // MARK: - 图片处理
    /// 创建带透明度的图片
    func createImageWithOpacity(_ image: UIImage, opacity: Double) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
        defer { UIGraphicsEndImageContext() }

        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        let rect = CGRect(origin: .zero, size: image.size)
        context.scaleBy(x: 1.0, y: -1.0)
        context.translateBy(x: 0.0, y: -rect.size.height)
        context.setAlpha(CGFloat(opacity))
        context.draw(image.cgImage!, in: rect)

        return UIGraphicsGetImageFromCurrentImageContext()
    }

    /// 创建平铺背景图片
    func createTiledBackgroundImage(_ image: UIImage, size: CGSize) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        defer { UIGraphicsEndImageContext() }

        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        // 设置平铺模式
        context.setFillColor(UIColor.clear.cgColor)
        context.fill(CGRect(origin: .zero, size: size))

        // 计算平铺次数
        let imageSize = image.size
        let tilesX = Int(ceil(size.width / imageSize.width))
        let tilesY = Int(ceil(size.height / imageSize.height))

        // 绘制平铺图片
        for x in 0..<tilesX {
            for y in 0..<tilesY {
                let rect = CGRect(
                    x: CGFloat(x) * imageSize.width,
                    y: CGFloat(y) * imageSize.height,
                    width: imageSize.width,
                    height: imageSize.height
                )
                image.draw(in: rect)
            }
        }

        return UIGraphicsGetImageFromCurrentImageContext()
    }

    /// 创建圆角图片
    func createRoundedImage(_ image: UIImage, cornerRadius: CGFloat) -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(image.size, false, image.scale)
        defer { UIGraphicsEndImageContext() }

        guard let context = UIGraphicsGetCurrentContext() else { return nil }

        let rect = CGRect(origin: .zero, size: image.size)
        let path = UIBezierPath(roundedRect: rect, cornerRadius: cornerRadius)

        context.addPath(path.cgPath)
        context.clip()

        image.draw(in: rect)

        return UIGraphicsGetImageFromCurrentImageContext()
    }

    // MARK: - 缓存管理
    /// 清除图片缓存
    func clearImageCache() {
        imageCache.removeAll()
    }

    /// 获取缓存大小
    func getCacheSize() -> Int {
        return imageCache.count
    }

    /// 预加载主题图片
    func preloadThemeImages(_ theme: KeyboardTheme) {
        cacheQueue.async {
            // 预加载背景图片
            if theme.hasBackgroundImage {
                _ = self.loadBackgroundImage(for: theme)
            }

            // 预加载按键图片
            if theme.hasKeyImage {
                _ = self.loadKeyImage(for: theme)
            }
        }
    }

    // MARK: - 内存管理
    @objc private func handleMemoryWarning() {
        print("⚠️ 键盘扩展收到内存警告，清除图片缓存")
        clearImageCache()
    }

    /// 检查图片是否存在
    func imageExists(for themeId: String, type: ImageType, isBuiltIn: Bool) -> Bool {
        // 首先检查主题包源目录
        if imageExistsInThemePack(themeId: themeId, type: type, isBuiltIn: isBuiltIn) {
            return true
        }

        // 回退检查缓存目录
        return imageExistsInCache(themeId: themeId, type: type, isBuiltIn: isBuiltIn)
    }

    /// 检查主题包源目录中的图片是否存在
    private func imageExistsInThemePack(themeId: String, type: ImageType, isBuiltIn: Bool) -> Bool {
        guard let baseDirectory = isBuiltIn ? builtInThemePacksDirectory : customThemePacksDirectory else {
            return false
        }

        let themePackDirectory = baseDirectory.appendingPathComponent(themeId)
        let resourcesDirectory = themePackDirectory.appendingPathComponent("resources")

        let imagePath: URL
        switch type {
        case .background:
            imagePath = resourcesDirectory.appendingPathComponent("backgrounds/keyboard-bg.png")
        case .key:
            imagePath = resourcesDirectory.appendingPathComponent("keys/letter-key.png")
        }

        return fileManager.fileExists(atPath: imagePath.path)
    }

    /// 检查缓存目录中的图片是否存在
    private func imageExistsInCache(themeId: String, type: ImageType, isBuiltIn: Bool) -> Bool {
        guard let directory = isBuiltIn ? builtInCacheDirectory : customCacheDirectory else {
            return false
        }

        let filename = "\(themeId)_\(type.rawValue).png"
        let url = directory.appendingPathComponent(filename)

        return fileManager.fileExists(atPath: url.path)
    }

    /// 创建降级图片
    private func createFallbackImage(for type: ImageType) -> UIImage? {
        let size: CGSize
        let color: UIColor

        switch type {
        case .background:
            size = CGSize(width: 1024, height: 512)
            color = UIColor.systemGray6
        case .key:
            size = CGSize(width: 64, height: 64)
            color = UIColor.systemGray5
        }

        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { context in
            color.setFill()
            context.fill(CGRect(origin: .zero, size: size))

            // 添加错误标识
            UIColor.systemRed.setStroke()
            context.cgContext.setLineWidth(2.0)
            context.cgContext.strokeEllipse(in: CGRect(
                x: size.width * 0.25,
                y: size.height * 0.25,
                width: size.width * 0.5,
                height: size.height * 0.5
            ))
        }
    }

    /// 列出目录内容（用于调试）
    private func listDirectoryContents(_ directory: URL) -> String {
        do {
            let contents = try fileManager.contentsOfDirectory(atPath: directory.path)
            return contents.isEmpty ? "目录为空" : contents.joined(separator: ", ")
        } catch {
            return "无法读取目录: \(error.localizedDescription)"
        }
    }

    /// 获取图片文件大小
    func getImageFileSize(for themeId: String, type: ImageType, isBuiltIn: Bool) -> Int64? {
        // 首先尝试从主题包源目录获取
        if let size = getImageFileSizeFromThemePack(themeId: themeId, type: type, isBuiltIn: isBuiltIn) {
            return size
        }

        // 回退到缓存目录
        return getImageFileSizeFromCache(themeId: themeId, type: type, isBuiltIn: isBuiltIn)
    }

    /// 从主题包源目录获取图片文件大小
    private func getImageFileSizeFromThemePack(themeId: String, type: ImageType, isBuiltIn: Bool) -> Int64? {
        guard let baseDirectory = isBuiltIn ? builtInThemePacksDirectory : customThemePacksDirectory else {
            return nil
        }

        let themePackDirectory = baseDirectory.appendingPathComponent(themeId)
        let resourcesDirectory = themePackDirectory.appendingPathComponent("resources")

        let imagePath: URL
        switch type {
        case .background:
            imagePath = resourcesDirectory.appendingPathComponent("backgrounds/keyboard-bg.png")
        case .key:
            imagePath = resourcesDirectory.appendingPathComponent("keys/letter-key.png")
        }

        do {
            let attributes = try fileManager.attributesOfItem(atPath: imagePath.path)
            return attributes[FileAttributeKey.size] as? Int64
        } catch {
            return nil
        }
    }

    /// 从缓存目录获取图片文件大小
    private func getImageFileSizeFromCache(themeId: String, type: ImageType, isBuiltIn: Bool) -> Int64? {
        guard let directory = isBuiltIn ? builtInCacheDirectory : customCacheDirectory else {
            return nil
        }

        let filename = "\(themeId)_\(type.rawValue).png"
        let url = directory.appendingPathComponent(filename)

        do {
            let attributes = try fileManager.attributesOfItem(atPath: url.path)
            return attributes[FileAttributeKey.size] as? Int64
        } catch {
            return nil
        }
    }
}
