//
//  EmojiPickerView.swift
//  KeyBoardExtension
//
//  Created by JZJJWidget
//

import UIKit

/// 表情符号选择器代理
protocol EmojiPickerDelegate: AnyObject {
    func emojiPickerDidSelectEmoji(_ emoji: String)
    func emojiPickerDidClose()

    // 键盘功能代理方法
    func emojiPickerDidTapSpace()
    func emojiPickerDidTapEnter()
    func emojiPickerDidTapDelete()
    func emojiPickerDidSwitchToNumbers()
    func emojiPickerDidSwitchToLetters()
    func emojiPickerDidSwitchToSymbols()
}

/// 表情符号选择器视图
class EmojiPickerView: UIView {

    // MARK: - 属性
    weak var delegate: EmojiPickerDelegate?
    private var theme: KeyboardTheme
    private var advancedTheme: AdvancedKeyboardTheme?

    // UI组件
    private var containerView: UIView!
    private var categoryTabsStackView: UIStackView!
    private var emojiCollectionView: UICollectionView!
    private var closeButton: UIButton!
    private var bottomToolbarView: UIView!
    private var bottomToolbarStackView: UIStackView!

    // 数据
    private var currentCategory: EmojiCategory = .smileys
    private var currentEmojis: [String] = []
    private var currentKeyboardState: KeyboardState = .letters

    // MARK: - 初始化
    init(theme: KeyboardTheme, advancedTheme: AdvancedKeyboardTheme? = nil) {
        self.theme = theme
        self.advancedTheme = advancedTheme
        super.init(frame: .zero)
        setupUI()
        loadEmojis(for: .smileys)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - UI设置
    private func setupUI() {
        backgroundColor = UIColor.black.withAlphaComponent(0.3)

        // 创建容器视图
        containerView = UIView()
        containerView.backgroundColor = theme.backgroundColor.uiColor
        containerView.layer.cornerRadius = 12
        containerView.layer.masksToBounds = true
        containerView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(containerView)

        // 设置容器约束 - 确保顶部有足够的安全距离
        NSLayoutConstraint.activate([
            containerView.topAnchor.constraint(equalTo: safeAreaLayoutGuide.topAnchor, constant: 20),
            containerView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 8),
            containerView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -8),
            containerView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -8)
        ])

        setupCategoryTabs()
        setupEmojiCollectionView()
        setupBottomToolbar()
        setupCloseButton()
    }

    private func setupCategoryTabs() {
        categoryTabsStackView = UIStackView()
        categoryTabsStackView.axis = .horizontal
        categoryTabsStackView.distribution = .fillEqually
        categoryTabsStackView.spacing = 4
        categoryTabsStackView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(categoryTabsStackView)

        // 创建分类标签按钮
        for category in EmojiCategory.allCases {
            let button = createCategoryButton(for: category)
            categoryTabsStackView.addArrangedSubview(button)
        }

        // 设置约束 - 增加顶部间距确保完全可见
        NSLayoutConstraint.activate([
            categoryTabsStackView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 16),
            categoryTabsStackView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 8),
            categoryTabsStackView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -8),
            categoryTabsStackView.heightAnchor.constraint(equalToConstant: 44)
        ])
    }

    private func createCategoryButton(for category: EmojiCategory) -> UIButton {
        let button = UIButton(type: .system)
        button.setTitle(category.rawValue, for: .normal)
        button.titleLabel?.font = UIFont.systemFont(ofSize: 20)
        button.backgroundColor = category == currentCategory ?
            theme.specialKeyColor.uiColor : theme.keyBackgroundColor.uiColor
        button.layer.cornerRadius = 8
        button.tag = EmojiCategory.allCases.firstIndex(of: category) ?? 0
        button.addTarget(self, action: #selector(categoryButtonTapped(_:)), for: .touchUpInside)
        return button
    }

    private func setupEmojiCollectionView() {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 4
        layout.minimumLineSpacing = 4
        layout.sectionInset = UIEdgeInsets(top: 8, left: 8, bottom: 8, right: 8)

        emojiCollectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        emojiCollectionView.backgroundColor = .clear
        emojiCollectionView.delegate = self
        emojiCollectionView.dataSource = self
        emojiCollectionView.register(EmojiCell.self, forCellWithReuseIdentifier: "EmojiCell")
        emojiCollectionView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(emojiCollectionView)

        NSLayoutConstraint.activate([
            emojiCollectionView.topAnchor.constraint(equalTo: categoryTabsStackView.bottomAnchor, constant: 12),
            emojiCollectionView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            emojiCollectionView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            emojiCollectionView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -52) // 为底部工具栏留出空间
        ])
    }

    private func setupBottomToolbar() {
        // 创建底部工具栏容器
        bottomToolbarView = UIView()
        bottomToolbarView.backgroundColor = theme.backgroundColor.uiColor
        bottomToolbarView.isUserInteractionEnabled = true
        bottomToolbarView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(bottomToolbarView)

        // 创建工具栏按钮堆栈视图
        bottomToolbarStackView = UIStackView()
        bottomToolbarStackView.axis = .horizontal
        bottomToolbarStackView.spacing = CGFloat(theme.keySpacing)
        bottomToolbarStackView.distribution = .fill
        bottomToolbarStackView.isUserInteractionEnabled = true
        bottomToolbarStackView.translatesAutoresizingMaskIntoConstraints = false
        bottomToolbarView.addSubview(bottomToolbarStackView)

        // 设置工具栏约束
        NSLayoutConstraint.activate([
            bottomToolbarView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            bottomToolbarView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            bottomToolbarView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),
            bottomToolbarView.heightAnchor.constraint(equalToConstant: 44),

            bottomToolbarStackView.topAnchor.constraint(equalTo: bottomToolbarView.topAnchor, constant: 4),
            bottomToolbarStackView.leadingAnchor.constraint(equalTo: bottomToolbarView.leadingAnchor, constant: 8),
            bottomToolbarStackView.trailingAnchor.constraint(equalTo: bottomToolbarView.trailingAnchor, constant: -8),
            bottomToolbarStackView.bottomAnchor.constraint(equalTo: bottomToolbarView.bottomAnchor, constant: -4)
        ])

        setupToolbarButtons()
    }

    private func setupToolbarButtons() {
        // 清除现有按钮
        bottomToolbarStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }

        // 根据当前状态决定左侧切换键
        let switchKey: KeyboardKeyView
        switch currentKeyboardState {
        case .letters:
            switchKey = KeyboardKeyView(keyType: .switchToNumbers, theme: theme, advancedTheme: advancedTheme)
            switchKey.addTarget(self, action: #selector(switchToNumbersTapped(_:)), for: .touchUpInside)
            print("🔧 创建123切换按钮，当前状态: \(currentKeyboardState)")
        case .numbers, .symbols:
            switchKey = KeyboardKeyView(keyType: .switchToLetters, theme: theme, advancedTheme: advancedTheme)
            switchKey.addTarget(self, action: #selector(switchToLettersTapped(_:)), for: .touchUpInside)
            print("🔧 创建ABC切换按钮，当前状态: \(currentKeyboardState)")
        }

        // 确保按钮可以接收用户交互
        switchKey.isUserInteractionEnabled = true
        bottomToolbarStackView.addArrangedSubview(switchKey)
        print("🔧 切换按钮已添加到工具栏，按钮标题: \(switchKey.titleLabel?.text ?? "无")")

        // 空格键
        let spaceKey = KeyboardKeyView(keyType: .space, theme: theme, advancedTheme: advancedTheme)
        spaceKey.addTarget(self, action: #selector(spaceTapped(_:)), for: .touchUpInside)
        spaceKey.isUserInteractionEnabled = true
        bottomToolbarStackView.addArrangedSubview(spaceKey)

        // 删除键
        let deleteKey = KeyboardKeyView(keyType: .delete, theme: theme, advancedTheme: advancedTheme)
        deleteKey.addTarget(self, action: #selector(deleteTapped(_:)), for: .touchUpInside)
        deleteKey.isUserInteractionEnabled = true
        bottomToolbarStackView.addArrangedSubview(deleteKey)

        // 回车键
        let enterKey = KeyboardKeyView(keyType: .enter, theme: theme, advancedTheme: advancedTheme)
        enterKey.addTarget(self, action: #selector(enterTapped(_:)), for: .touchUpInside)
        enterKey.isUserInteractionEnabled = true
        bottomToolbarStackView.addArrangedSubview(enterKey)

        // 设置按键宽度约束
        switchKey.widthAnchor.constraint(equalToConstant: 50).isActive = true
        deleteKey.widthAnchor.constraint(equalToConstant: 50).isActive = true
        enterKey.widthAnchor.constraint(equalToConstant: 60).isActive = true

        // 设置空格键占用更多空间
        spaceKey.setContentHuggingPriority(.defaultLow, for: .horizontal)
        switchKey.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        deleteKey.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        enterKey.setContentHuggingPriority(.defaultHigh, for: .horizontal)
    }

    private func setupCloseButton() {
        closeButton = UIButton(type: .system)
        closeButton.setTitle("×", for: .normal)
        closeButton.titleLabel?.font = UIFont.systemFont(ofSize: 24, weight: .medium)
        closeButton.setTitleColor(theme.textColor.uiColor, for: .normal)
        closeButton.backgroundColor = theme.specialKeyColor.uiColor
        closeButton.layer.cornerRadius = 15
        closeButton.translatesAutoresizingMaskIntoConstraints = false
        closeButton.addTarget(self, action: #selector(closeButtonTapped), for: .touchUpInside)
        addSubview(closeButton)

        NSLayoutConstraint.activate([
            closeButton.topAnchor.constraint(equalTo: containerView.topAnchor, constant: -10),
            closeButton.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -8),
            closeButton.widthAnchor.constraint(equalToConstant: 30),
            closeButton.heightAnchor.constraint(equalToConstant: 30)
        ])
    }

    // MARK: - 数据加载
    private func loadEmojis(for category: EmojiCategory) {
        currentCategory = category
        currentEmojis = EmojiDataManager.getEmojis(for: category)
        emojiCollectionView.reloadData()
        updateCategoryButtons()
    }

    private func updateCategoryButtons() {
        for (index, button) in categoryTabsStackView.arrangedSubviews.enumerated() {
            if let button = button as? UIButton {
                let category = EmojiCategory.allCases[index]
                button.backgroundColor = category == currentCategory ?
                    theme.specialKeyColor.uiColor : theme.keyBackgroundColor.uiColor
            }
        }
    }

    // MARK: - 事件处理
    @objc private func categoryButtonTapped(_ sender: UIButton) {
        let category = EmojiCategory.allCases[sender.tag]
        loadEmojis(for: category)
    }

    @objc private func closeButtonTapped() {
        delegate?.emojiPickerDidClose()
    }

    // MARK: - 工具栏按钮事件处理
    @objc private func spaceTapped(_ sender: KeyboardKeyView) {
        delegate?.emojiPickerDidTapSpace()
    }

    @objc private func enterTapped(_ sender: KeyboardKeyView) {
        delegate?.emojiPickerDidTapEnter()
    }

    @objc private func deleteTapped(_ sender: KeyboardKeyView) {
        delegate?.emojiPickerDidTapDelete()
    }

    @objc private func switchToNumbersTapped(_ sender: KeyboardKeyView) {
        print("🎯 表情键盘：123按钮被点击")
        currentKeyboardState = .numbers
        setupToolbarButtons()
        delegate?.emojiPickerDidSwitchToNumbers()
        print("🎯 表情键盘：已调用代理方法 emojiPickerDidSwitchToNumbers")

        // 关闭表情键盘，显示切换后的主键盘
        delegate?.emojiPickerDidClose()
        print("🎯 表情键盘：切换后关闭表情选择器")
    }

    @objc private func switchToLettersTapped(_ sender: KeyboardKeyView) {
        print("🎯 表情键盘：ABC按钮被点击")
        currentKeyboardState = .letters
        setupToolbarButtons()
        delegate?.emojiPickerDidSwitchToLetters()
        print("🎯 表情键盘：已调用代理方法 emojiPickerDidSwitchToLetters")

        // 关闭表情键盘，显示切换后的主键盘
        delegate?.emojiPickerDidClose()
        print("🎯 表情键盘：切换后关闭表情选择器")
    }

    @objc private func switchToSymbolsTapped(_ sender: KeyboardKeyView) {
        print("🎯 表情键盘：#+=按钮被点击")
        currentKeyboardState = .symbols
        setupToolbarButtons()
        delegate?.emojiPickerDidSwitchToSymbols()
        print("🎯 表情键盘：已调用代理方法 emojiPickerDidSwitchToSymbols")

        // 关闭表情键盘，显示切换后的主键盘
        delegate?.emojiPickerDidClose()
        print("🎯 表情键盘：切换后关闭表情选择器")
    }

    // MARK: - 公共方法
    func updateTheme(_ newTheme: KeyboardTheme, advancedTheme: AdvancedKeyboardTheme? = nil) {
        theme = newTheme
        self.advancedTheme = advancedTheme

        containerView.backgroundColor = theme.backgroundColor.uiColor
        closeButton.setTitleColor(theme.textColor.uiColor, for: .normal)
        closeButton.backgroundColor = theme.specialKeyColor.uiColor
        bottomToolbarView.backgroundColor = theme.backgroundColor.uiColor

        updateCategoryButtons()
        setupToolbarButtons() // 重新设置工具栏按钮以应用新主题
        emojiCollectionView.reloadData()
    }

    // 公共方法：设置键盘状态
    func setKeyboardState(_ state: KeyboardState) {
        currentKeyboardState = state
        setupToolbarButtons()
    }
}

// MARK: - UICollectionViewDataSource
extension EmojiPickerView: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return currentEmojis.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "EmojiCell", for: indexPath) as! EmojiCell
        cell.configure(with: currentEmojis[indexPath.item], theme: theme)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension EmojiPickerView: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let emoji = currentEmojis[indexPath.item]
        delegate?.emojiPickerDidSelectEmoji(emoji)
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension EmojiPickerView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = (collectionView.frame.width - 64) / 8 // 8列，考虑间距
        return CGSize(width: width, height: width)
    }
}

// MARK: - 表情符号单元格
class EmojiCell: UICollectionViewCell {
    private let emojiLabel = UILabel()

    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupUI() {
        emojiLabel.textAlignment = .center
        emojiLabel.font = UIFont.systemFont(ofSize: 24)
        emojiLabel.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(emojiLabel)

        NSLayoutConstraint.activate([
            emojiLabel.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            emojiLabel.centerYAnchor.constraint(equalTo: contentView.centerYAnchor)
        ])

        layer.cornerRadius = 8
    }

    func configure(with emoji: String, theme: KeyboardTheme) {
        emojiLabel.text = emoji
        backgroundColor = theme.keyBackgroundColor.uiColor
    }

    override var isHighlighted: Bool {
        didSet {
            alpha = isHighlighted ? 0.7 : 1.0
        }
    }
}
