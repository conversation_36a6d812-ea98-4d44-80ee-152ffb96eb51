//
//  KeyboardThemeBridge.swift
//  KeyBoardExtension
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import UIKit
import Foundation

// MARK: - 高级主题相关类型定义（键盘扩展本地版本）

/// 按键类型枚举
enum KeyType: String, CaseIterable, Codable {
    case letter = "letter"
    case number = "number"
    case function = "function"
    case space = "space"
    case shift = "shift"
    case symbol = "symbol"
    case punctuation = "punctuation"
}

/// 字体权重枚举
enum FontWeight: String, Codable {
    case ultraLight = "ultraLight"
    case thin = "thin"
    case light = "light"
    case regular = "regular"
    case medium = "medium"
    case semibold = "semibold"
    case bold = "bold"
    case heavy = "heavy"
    case black = "black"

    var uiKitFontWeight: UIFont.Weight {
        switch self {
        case .ultraLight: return .ultraLight
        case .thin: return .thin
        case .light: return .light
        case .regular: return .regular
        case .medium: return .medium
        case .semibold: return .semibold
        case .bold: return .bold
        case .heavy: return .heavy
        case .black: return .black
        }
    }
}

/// 颜色数据结构（扩展版本）
struct WidgetColor: Codable {
    let red: Double
    let green: Double
    let blue: Double
    let alpha: Double

    var uiColor: UIColor {
        return UIColor(red: red, green: green, blue: blue, alpha: alpha)
    }

    var color: UIColor {
        return uiColor
    }
}

/// 按键配置结构
struct KeyConfig: Codable, Identifiable {
    let id: String
    var keyType: KeyType
    var keyValue: String

    // 颜色配置
    var backgroundColor: WidgetColor
    var pressedColor: WidgetColor
    var textColor: WidgetColor
    var borderColor: WidgetColor

    // 字体配置
    var fontName: String
    var fontSize: Double
    var fontWeight: FontWeight

    // 视觉效果配置
    var cornerRadius: Double
    var borderWidth: Double
    var shadowEnabled: Bool
    var shadowColor: WidgetColor
    var shadowRadius: Double
    var shadowOffset: CGSize

    // 按键尺寸配置
    var widthMultiplier: Double
    var heightMultiplier: Double

    // 图片配置
    var hasCustomImage: Bool // 是否使用自定义图片
    var normalImagePath: String? // 正常状态图片路径
    var pressedImagePath: String? // 按下状态图片路径
    var hoverImagePath: String? // 悬停状态图片路径（iPad支持）
    var imageOpacity: Double // 图片透明度
    var imageBlendMode: String // 图片混合模式
    var hideTextWhenImageExists: Bool // 当有自定义图片时是否隐藏文字

    // 创建和更新时间
    var createdAt: Date
    var updatedAt: Date

    // 自定义编码键（与主应用保持一致）
    private enum CodingKeys: String, CodingKey {
        case id
        case keyType = "key_type"
        case keyValue = "key_value"
        case backgroundColor = "background_color"
        case pressedColor = "pressed_color"
        case textColor = "text_color"
        case borderColor = "border_color"
        case fontName = "font_name"
        case fontSize = "font_size"
        case fontWeight = "font_weight"
        case cornerRadius = "corner_radius"
        case borderWidth = "border_width"
        case shadowEnabled = "shadow_enabled"
        case shadowColor = "shadow_color"
        case shadowRadius = "shadow_radius"
        case shadowOffset = "shadow_offset"
        case widthMultiplier = "width_multiplier"
        case heightMultiplier = "height_multiplier"
        case hasCustomImage = "has_custom_image"
        case normalImagePath = "normal_image_path"
        case pressedImagePath = "pressed_image_path"
        case hoverImagePath = "hover_image_path"
        case imageOpacity = "image_opacity"
        case imageBlendMode = "image_blend_mode"
        case hideTextWhenImageExists = "hide_text_when_image_exists"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }

    init(
        keyType: KeyType,
        keyValue: String,
        backgroundColor: WidgetColor = WidgetColor(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
        pressedColor: WidgetColor = WidgetColor(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
        textColor: WidgetColor = WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
        borderColor: WidgetColor = WidgetColor(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0),
        fontName: String = "SF Pro",
        fontSize: Double = 16,
        fontWeight: FontWeight = .medium,
        cornerRadius: Double = 8,
        borderWidth: Double = 1,
        shadowEnabled: Bool = true,
        shadowColor: WidgetColor = WidgetColor(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.1),
        shadowRadius: Double = 2,
        shadowOffset: CGSize = CGSize(width: 0, height: 1),
        widthMultiplier: Double = 1.0,
        heightMultiplier: Double = 1.0,
        hasCustomImage: Bool = false,
        normalImagePath: String? = nil,
        pressedImagePath: String? = nil,
        hoverImagePath: String? = nil,
        imageOpacity: Double = 1.0,
        imageBlendMode: String = "normal",
        hideTextWhenImageExists: Bool = false
    ) {
        self.id = UUID().uuidString
        self.keyType = keyType
        self.keyValue = keyValue
        self.backgroundColor = backgroundColor
        self.pressedColor = pressedColor
        self.textColor = textColor
        self.borderColor = borderColor
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontWeight = fontWeight
        self.cornerRadius = cornerRadius
        self.borderWidth = borderWidth
        self.shadowEnabled = shadowEnabled
        self.shadowColor = shadowColor
        self.shadowRadius = shadowRadius
        self.shadowOffset = shadowOffset
        self.widthMultiplier = widthMultiplier
        self.heightMultiplier = heightMultiplier
        self.hasCustomImage = hasCustomImage
        self.normalImagePath = normalImagePath
        self.pressedImagePath = pressedImagePath
        self.hoverImagePath = hoverImagePath
        self.imageOpacity = imageOpacity
        self.imageBlendMode = imageBlendMode
        self.hideTextWhenImageExists = hideTextWhenImageExists
        self.createdAt = Date()
        self.updatedAt = Date()
    }

    // MARK: - Custom Codable Implementation

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        keyType = try container.decode(KeyType.self, forKey: .keyType)
        keyValue = try container.decode(String.self, forKey: .keyValue)
        backgroundColor = try container.decode(WidgetColor.self, forKey: .backgroundColor)
        pressedColor = try container.decode(WidgetColor.self, forKey: .pressedColor)
        textColor = try container.decode(WidgetColor.self, forKey: .textColor)
        borderColor = try container.decode(WidgetColor.self, forKey: .borderColor)
        fontName = try container.decode(String.self, forKey: .fontName)
        fontSize = try container.decode(Double.self, forKey: .fontSize)
        fontWeight = try container.decode(FontWeight.self, forKey: .fontWeight)
        cornerRadius = try container.decode(Double.self, forKey: .cornerRadius)
        borderWidth = try container.decode(Double.self, forKey: .borderWidth)
        shadowEnabled = try container.decode(Bool.self, forKey: .shadowEnabled)
        shadowColor = try container.decode(WidgetColor.self, forKey: .shadowColor)
        shadowRadius = try container.decode(Double.self, forKey: .shadowRadius)
        shadowOffset = try container.decode(CGSize.self, forKey: .shadowOffset)
        widthMultiplier = try container.decode(Double.self, forKey: .widthMultiplier)
        heightMultiplier = try container.decode(Double.self, forKey: .heightMultiplier)
        hasCustomImage = try container.decode(Bool.self, forKey: .hasCustomImage)
        normalImagePath = try container.decodeIfPresent(String.self, forKey: .normalImagePath)
        pressedImagePath = try container.decodeIfPresent(String.self, forKey: .pressedImagePath)
        hoverImagePath = try container.decodeIfPresent(String.self, forKey: .hoverImagePath)
        imageOpacity = try container.decode(Double.self, forKey: .imageOpacity)
        imageBlendMode = try container.decode(String.self, forKey: .imageBlendMode)
        hideTextWhenImageExists = try container.decodeIfPresent(Bool.self, forKey: .hideTextWhenImageExists) ?? false
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        updatedAt = try container.decode(Date.self, forKey: .updatedAt)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(keyType, forKey: .keyType)
        try container.encode(keyValue, forKey: .keyValue)
        try container.encode(backgroundColor, forKey: .backgroundColor)
        try container.encode(pressedColor, forKey: .pressedColor)
        try container.encode(textColor, forKey: .textColor)
        try container.encode(borderColor, forKey: .borderColor)
        try container.encode(fontName, forKey: .fontName)
        try container.encode(fontSize, forKey: .fontSize)
        try container.encode(fontWeight, forKey: .fontWeight)
        try container.encode(cornerRadius, forKey: .cornerRadius)
        try container.encode(borderWidth, forKey: .borderWidth)
        try container.encode(shadowEnabled, forKey: .shadowEnabled)
        try container.encode(shadowColor, forKey: .shadowColor)
        try container.encode(shadowRadius, forKey: .shadowRadius)
        try container.encode(shadowOffset, forKey: .shadowOffset)
        try container.encode(widthMultiplier, forKey: .widthMultiplier)
        try container.encode(heightMultiplier, forKey: .heightMultiplier)
        try container.encode(hasCustomImage, forKey: .hasCustomImage)
        try container.encodeIfPresent(normalImagePath, forKey: .normalImagePath)
        try container.encodeIfPresent(pressedImagePath, forKey: .pressedImagePath)
        try container.encodeIfPresent(hoverImagePath, forKey: .hoverImagePath)
        try container.encode(imageOpacity, forKey: .imageOpacity)
        try container.encode(imageBlendMode, forKey: .imageBlendMode)
        try container.encode(hideTextWhenImageExists, forKey: .hideTextWhenImageExists)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
    }
}

/// 按键类型配置结构
struct KeyTypeConfig: Codable, Identifiable {
    let id: String
    var keyType: KeyType
    var name: String
    var description: String

    // 默认配置
    var defaultBackgroundColor: WidgetColor
    var defaultPressedColor: WidgetColor
    var defaultTextColor: WidgetColor
    var defaultBorderColor: WidgetColor
    var defaultFontSize: Double
    var defaultFontWeight: FontWeight
    var defaultCornerRadius: Double
    var defaultBorderWidth: Double

    // 应用到的按键列表
    var affectedKeys: [String]

    var updatedAt: Date

    // MARK: - Custom CodingKeys for snake_case compatibility
    private enum CodingKeys: String, CodingKey {
        case id
        case keyType = "key_type"
        case name
        case description
        case defaultBackgroundColor = "default_background_color"
        case defaultPressedColor = "default_pressed_color"
        case defaultTextColor = "default_text_color"
        case defaultBorderColor = "default_border_color"
        case defaultFontSize = "default_font_size"
        case defaultFontWeight = "default_font_weight"
        case defaultCornerRadius = "default_corner_radius"
        case defaultBorderWidth = "default_border_width"
        case affectedKeys = "affected_keys"
        case updatedAt = "updated_at"
    }
}

/// 高级键盘主题结构
struct AdvancedKeyboardTheme: Codable, Identifiable {
    let id: String
    var name: String
    var description: String

    // 按键类型配置
    var keyTypeConfigs: [KeyType: KeyTypeConfig]

    // 单个按键配置
    var individualKeyConfigs: [String: KeyConfig]

    // 全局设置
    var globalKeySpacing: Double
    var globalKeyHeight: Double
    var enableHapticFeedback: Bool
    var enableSoundFeedback: Bool

    // 高级效果
    var enableKeyAnimations: Bool
    var animationDuration: Double
    var enableGradientEffects: Bool
    var enableParallaxEffect: Bool

    // 创建和更新时间
    var createdAt: Date
    var updatedAt: Date

    // MARK: - Custom Codable Implementation

    private enum CodingKeys: String, CodingKey {
        case id, name, description
        case keyTypeConfigs, individualKeyConfigs
        case globalKeySpacing, globalKeyHeight
        case enableHapticFeedback, enableSoundFeedback
        case enableKeyAnimations, animationDuration
        case enableGradientEffects, enableParallaxEffect
        case createdAt, updatedAt
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)

        id = try container.decode(String.self, forKey: .id)
        name = try container.decode(String.self, forKey: .name)
        description = try container.decode(String.self, forKey: .description)

        // 解码 keyTypeConfigs 字典
        let keyTypeConfigsDict = try container.decode([String: KeyTypeConfig].self, forKey: .keyTypeConfigs)
        var configs: [KeyType: KeyTypeConfig] = [:]
        for (key, config) in keyTypeConfigsDict {
            if let keyType = KeyType(rawValue: key) {
                configs[keyType] = config
            }
        }
        keyTypeConfigs = configs

        individualKeyConfigs = try container.decode([String: KeyConfig].self, forKey: .individualKeyConfigs)
        globalKeySpacing = try container.decode(Double.self, forKey: .globalKeySpacing)
        globalKeyHeight = try container.decode(Double.self, forKey: .globalKeyHeight)
        enableHapticFeedback = try container.decode(Bool.self, forKey: .enableHapticFeedback)
        enableSoundFeedback = try container.decode(Bool.self, forKey: .enableSoundFeedback)
        enableKeyAnimations = try container.decode(Bool.self, forKey: .enableKeyAnimations)
        animationDuration = try container.decode(Double.self, forKey: .animationDuration)
        enableGradientEffects = try container.decode(Bool.self, forKey: .enableGradientEffects)
        enableParallaxEffect = try container.decode(Bool.self, forKey: .enableParallaxEffect)
        createdAt = try container.decode(Date.self, forKey: .createdAt)
        updatedAt = try container.decode(Date.self, forKey: .updatedAt)
    }

    func encode(to encoder: Encoder) throws {
        var container = encoder.container(keyedBy: CodingKeys.self)

        try container.encode(id, forKey: .id)
        try container.encode(name, forKey: .name)
        try container.encode(description, forKey: .description)

        // 编码 keyTypeConfigs 为字典格式
        var keyTypeConfigsDict: [String: KeyTypeConfig] = [:]
        for (keyType, config) in keyTypeConfigs {
            keyTypeConfigsDict[keyType.rawValue] = config
        }
        try container.encode(keyTypeConfigsDict, forKey: .keyTypeConfigs)

        try container.encode(individualKeyConfigs, forKey: .individualKeyConfigs)
        try container.encode(globalKeySpacing, forKey: .globalKeySpacing)
        try container.encode(globalKeyHeight, forKey: .globalKeyHeight)
        try container.encode(enableHapticFeedback, forKey: .enableHapticFeedback)
        try container.encode(enableSoundFeedback, forKey: .enableSoundFeedback)
        try container.encode(enableKeyAnimations, forKey: .enableKeyAnimations)
        try container.encode(animationDuration, forKey: .animationDuration)
        try container.encode(enableGradientEffects, forKey: .enableGradientEffects)
        try container.encode(enableParallaxEffect, forKey: .enableParallaxEffect)
        try container.encode(createdAt, forKey: .createdAt)
        try container.encode(updatedAt, forKey: .updatedAt)
    }
}

// MARK: - 主题包相关类型定义（键盘扩展本地版本）

/// 已安装主题包记录
struct InstalledPacksRecord: Codable {
    let packs: [InstalledPackInfo]
    let lastUpdated: Date

    init(packs: [InstalledPackInfo], lastUpdated: Date) {
        self.packs = packs
        self.lastUpdated = lastUpdated
    }
}

/// 已安装主题包信息
struct InstalledPackInfo: Codable {
    let id: String
    let version: String
    let installedAt: Date
    let isBuiltIn: Bool

    init(id: String, version: String, installedAt: Date, isBuiltIn: Bool) {
        self.id = id
        self.version = version
        self.installedAt = installedAt
        self.isBuiltIn = isBuiltIn
    }
}

// MARK: - 简化的键盘主题结构（用于键盘扩展）
struct KeyboardTheme: Codable {
    let id: String
    var name: String
    var type: String
    var keyStyle: String

    // 颜色配置（使用RGB值存储）
    var backgroundColor: ColorData
    var keyBackgroundColor: ColorData
    var keyPressedColor: ColorData
    var textColor: ColorData
    var specialKeyColor: ColorData
    var borderColor: ColorData

    // 图片配置
    var hasBackgroundImage: Bool
    var hasKeyImage: Bool
    var backgroundImagePath: String?
    var keyImagePath: String?
    var isBuiltInImageTheme: Bool
    var imageOpacity: Double
    var imageBlendMode: String

    // 字体配置
    var fontName: String
    var fontSize: Double
    var fontWeight: String

    // 布局配置
    var keySpacing: Double
    var keyHeight: Double
    var showBorder: Bool
    var borderWidth: Double

    // 特效配置
    var enableShadow: Bool
    var shadowColor: ColorData
    var shadowRadius: Double
    var enableHaptic: Bool
    var enableSound: Bool

    // 创建时间
    var createdAt: Date
    var updatedAt: Date

    init(
        id: String = UUID().uuidString,
        name: String,
        type: String = "light",
        keyStyle: String = "rounded",
        backgroundColor: ColorData = ColorData(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
        keyBackgroundColor: ColorData = ColorData(red: 0.95, green: 0.95, blue: 0.95, alpha: 1.0),
        keyPressedColor: ColorData = ColorData(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
        textColor: ColorData = ColorData(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
        specialKeyColor: ColorData = ColorData(red: 0.68, green: 0.68, blue: 0.7, alpha: 1.0),
        borderColor: ColorData = ColorData(red: 0.78, green: 0.78, blue: 0.8, alpha: 1.0),
        hasBackgroundImage: Bool = false,
        hasKeyImage: Bool = false,
        backgroundImagePath: String? = nil,
        keyImagePath: String? = nil,
        isBuiltInImageTheme: Bool = false,
        imageOpacity: Double = 1.0,
        imageBlendMode: String = "normal",
        fontName: String = "SF Pro",
        fontSize: Double = 16,
        fontWeight: String = "medium",
        keySpacing: Double = 6,
        keyHeight: Double = 44,
        showBorder: Bool = true,
        borderWidth: Double = 1,
        enableShadow: Bool = true,
        shadowColor: ColorData = ColorData(red: 0.0, green: 0.0, blue: 0.0, alpha: 0.1),
        shadowRadius: Double = 2,
        enableHaptic: Bool = true,
        enableSound: Bool = true
    ) {
        self.id = id
        self.name = name
        self.type = type
        self.keyStyle = keyStyle
        self.backgroundColor = backgroundColor
        self.keyBackgroundColor = keyBackgroundColor
        self.keyPressedColor = keyPressedColor
        self.textColor = textColor
        self.specialKeyColor = specialKeyColor
        self.borderColor = borderColor
        self.hasBackgroundImage = hasBackgroundImage
        self.hasKeyImage = hasKeyImage
        self.backgroundImagePath = backgroundImagePath
        self.keyImagePath = keyImagePath
        self.isBuiltInImageTheme = isBuiltInImageTheme
        self.imageOpacity = imageOpacity
        self.imageBlendMode = imageBlendMode
        self.fontName = fontName
        self.fontSize = fontSize
        self.fontWeight = fontWeight
        self.keySpacing = keySpacing
        self.keyHeight = keyHeight
        self.showBorder = showBorder
        self.borderWidth = borderWidth
        self.enableShadow = enableShadow
        self.shadowColor = shadowColor
        self.shadowRadius = shadowRadius
        self.enableHaptic = enableHaptic
        self.enableSound = enableSound
        self.createdAt = Date()
        self.updatedAt = Date()
    }
}

// MARK: - 颜色数据结构
struct ColorData: Codable {
    let red: Double
    let green: Double
    let blue: Double
    let alpha: Double

    var uiColor: UIColor {
        return UIColor(red: red, green: green, blue: blue, alpha: alpha)
    }

    init(red: Double, green: Double, blue: Double, alpha: Double) {
        self.red = red
        self.green = green
        self.blue = blue
        self.alpha = alpha
    }

    init(uiColor: UIColor) {
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        uiColor.getRed(&r, green: &g, blue: &b, alpha: &a)

        self.red = Double(r)
        self.green = Double(g)
        self.blue = Double(b)
        self.alpha = Double(a)
    }
}

// MARK: - 键盘样式枚举
extension KeyboardTheme {
    var cornerRadius: CGFloat {
        switch keyStyle {
        case "standard":
            return 6
        case "rounded":
            return 12
        case "circular":
            return 20
        case "flat":
            return 0
        default:
            return 6
        }
    }

    var uiKitFontWeight: UIFont.Weight {
        switch fontWeight {
        case "ultraLight": return .ultraLight
        case "thin": return .thin
        case "light": return .light
        case "regular": return .regular
        case "medium": return .medium
        case "semibold": return .semibold
        case "bold": return .bold
        case "heavy": return .heavy
        case "black": return .black
        default: return .medium
        }
    }
}

// MARK: - 字体权重辅助函数
extension String {
    var uiKitFontWeight: UIFont.Weight {
        switch self {
        case "ultraLight": return .ultraLight
        case "thin": return .thin
        case "light": return .light
        case "regular": return .regular
        case "medium": return .medium
        case "semibold": return .semibold
        case "bold": return .bold
        case "heavy": return .heavy
        case "black": return .black
        default: return .medium
        }
    }

    var cornerRadius: CGFloat {
        switch self {
        case "standard": return 6
        case "rounded": return 12
        case "circular": return 20
        case "flat": return 0
        default: return 6
        }
    }
}

// MARK: - 预设主题
extension KeyboardTheme {
    static var defaultThemes: [KeyboardTheme] {
        return [
            // 暖橙主题（默认主题）
            KeyboardTheme(
                name: "暖橙",
                type: "colorful",
                backgroundColor: ColorData(red: 1.0, green: 0.97, blue: 0.96, alpha: 1.0), // 暖橙背景
                keyBackgroundColor: ColorData(red: 1.0, green: 0.34, blue: 0.13, alpha: 1.0), // FF5722
                keyPressedColor: ColorData(red: 1.0, green: 0.54, blue: 0.40, alpha: 1.0), // FF8A65
                textColor: ColorData(red: 0.24, green: 0.15, blue: 0.14, alpha: 1.0), // 3E2723
                specialKeyColor: ColorData(red: 1.0, green: 0.80, blue: 0.74, alpha: 1.0), // FFCCBC
                borderColor: ColorData(red: 1.0, green: 0.80, blue: 0.74, alpha: 1.0) // FFCCBC
            ),

            // 浅色主题
            KeyboardTheme(
                name: "经典浅色",
                type: "light",
                backgroundColor: ColorData(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0),
                keyBackgroundColor: ColorData(red: 0.95, green: 0.95, blue: 0.95, alpha: 1.0),
                textColor: ColorData(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0)
            ),

            // 深色主题
            KeyboardTheme(
                name: "经典深色",
                type: "dark",
                backgroundColor: ColorData(red: 0.0, green: 0.0, blue: 0.0, alpha: 1.0),
                keyBackgroundColor: ColorData(red: 0.2, green: 0.2, blue: 0.2, alpha: 1.0),
                textColor: ColorData(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
            ),

            // 彩色主题
            KeyboardTheme(
                name: "活力彩色",
                type: "colorful",
                backgroundColor: ColorData(red: 0.0, green: 0.48, blue: 1.0, alpha: 0.1),
                keyBackgroundColor: ColorData(red: 0.0, green: 0.48, blue: 1.0, alpha: 1.0),
                textColor: ColorData(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
            ),

            // 渐变主题
            KeyboardTheme(
                name: "梦幻渐变",
                type: "gradient",
                backgroundColor: ColorData(red: 0.5, green: 0.0, blue: 0.5, alpha: 0.2),
                keyBackgroundColor: ColorData(red: 0.5, green: 0.0, blue: 0.5, alpha: 1.0),
                textColor: ColorData(red: 1.0, green: 1.0, blue: 1.0, alpha: 1.0)
            )
        ]
    }

    static var defaultTheme: KeyboardTheme {
        return defaultThemes[0]
    }
}

// MARK: - 键盘主题数据管理器
class KeyboardThemeDataManager {
    static let shared = KeyboardThemeDataManager()

    private let userDefaults: UserDefaults?
    private let simplifiedThemeKey = "keyboard_simplified_theme"
    private let advancedThemeKey = "current_advanced_keyboard_theme"

    private init() {
        userDefaults = UserDefaults(suiteName: "group.com.ort.JZJJWidgetAPP.group")
    }

    /// 加载键盘主题（从简化版本）
    func loadTheme() -> KeyboardTheme {
        guard let userDefaults = userDefaults,
              let data = userDefaults.data(forKey: simplifiedThemeKey) else {
            return KeyboardTheme.defaultTheme
        }

        do {
            let simplifiedTheme = try JSONDecoder().decode(SimplifiedKeyboardTheme.self, from: data)
            return convertToKeyboardTheme(simplifiedTheme)
        } catch {
            print("加载简化键盘主题失败: \(error)")
            return KeyboardTheme.defaultTheme
        }
    }

    /// 加载高级键盘主题
    func loadAdvancedTheme() -> AdvancedKeyboardTheme? {
        guard let userDefaults = userDefaults,
              let data = userDefaults.data(forKey: advancedThemeKey) else {
            return nil
        }

        do {
            return try JSONDecoder().decode(AdvancedKeyboardTheme.self, from: data)
        } catch {
            print("加载高级键盘主题失败: \(error)")
            return nil
        }
    }

    /// 获取按键的有效配置
    func getEffectiveKeyConfig(for keyValue: String, keyType: KeyType, advancedTheme: AdvancedKeyboardTheme?) -> KeyConfig? {
        guard let theme = advancedTheme else { return nil }

        // 优先使用单个按键配置
        if let individualConfig = theme.individualKeyConfigs[keyValue] {
            return individualConfig
        }

        // 使用按键类型配置
        if let typeConfig = theme.keyTypeConfigs[keyType] {
            return KeyConfig(
                keyType: keyType,
                keyValue: keyValue,
                backgroundColor: typeConfig.defaultBackgroundColor,
                pressedColor: typeConfig.defaultPressedColor,
                textColor: typeConfig.defaultTextColor,
                borderColor: typeConfig.defaultBorderColor,
                fontSize: typeConfig.defaultFontSize,
                fontWeight: typeConfig.defaultFontWeight,
                cornerRadius: typeConfig.defaultCornerRadius,
                borderWidth: typeConfig.defaultBorderWidth
            )
        }

        return nil
    }

    /// 检查主题包更新
    func checkThemePackUpdates() {
        // 获取App Groups共享目录
        guard let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: "group.com.ort.JZJJWidgetAPP.group") else {
            print("⚠️ 无法访问App Groups容器")
            return
        }

        // 构建主题包元数据路径
        let metadataDir = containerURL.appendingPathComponent("theme-packs/metadata")
        let metadataPath = metadataDir.appendingPathComponent("installed-packs.json")

        guard FileManager.default.fileExists(atPath: metadataPath.path) else {
            print("⚠️ 主题包安装记录文件不存在")
            return
        }

        // 读取已安装主题包信息，触发重新加载
        do {
            let data = try Data(contentsOf: metadataPath)
            let installedPacks: InstalledPacksRecord = try JSONDecoder().decode(InstalledPacksRecord.self, from: data)
            print("✅ 发现 \(installedPacks.packs.count) 个已安装主题包")

            // 可以在这里添加主题包变更检测逻辑
            for pack in installedPacks.packs {
                print("📦 主题包: \(pack.id) v\(pack.version) (\(pack.isBuiltIn ? "内置" : "自定义"))")
            }
        } catch {
            print("⚠️ 读取主题包安装记录失败: \(error)")
        }
    }

    /// 监听主题变化
    func observeThemeChanges(callback: @escaping (KeyboardTheme) -> Void) {
        NotificationCenter.default.addObserver(
            forName: NSNotification.Name("KeyboardThemeDidChange"),
            object: nil,
            queue: .main
        ) { _ in
            let theme = self.loadTheme()
            callback(theme)
        }
    }

    /// 转换简化主题为键盘主题
    private func convertToKeyboardTheme(_ simplified: SimplifiedKeyboardTheme) -> KeyboardTheme {
        return KeyboardTheme(
            id: simplified.id,
            name: simplified.name,
            type: simplified.type,
            keyStyle: simplified.keyStyle,
            backgroundColor: simplified.backgroundColor,
            keyBackgroundColor: simplified.keyBackgroundColor,
            keyPressedColor: simplified.keyPressedColor,
            textColor: simplified.textColor,
            specialKeyColor: simplified.specialKeyColor,
            borderColor: simplified.borderColor,
            hasBackgroundImage: simplified.hasBackgroundImage,
            hasKeyImage: simplified.hasKeyImage,
            backgroundImagePath: simplified.backgroundImagePath,
            keyImagePath: simplified.keyImagePath,
            isBuiltInImageTheme: simplified.isBuiltInImageTheme,
            imageOpacity: simplified.imageOpacity,
            imageBlendMode: simplified.imageBlendMode,
            fontName: simplified.fontName,
            fontSize: simplified.fontSize,
            fontWeight: simplified.fontWeight,
            keySpacing: simplified.keySpacing,
            keyHeight: simplified.keyHeight,
            showBorder: simplified.showBorder,
            borderWidth: simplified.borderWidth,
            enableShadow: simplified.enableShadow,
            shadowColor: simplified.shadowColor,
            shadowRadius: simplified.shadowRadius,
            enableHaptic: simplified.enableHaptic,
            enableSound: simplified.enableSound
        )
    }
}

// MARK: - 简化的键盘主题结构（与MyWidgetKit中的定义保持一致）
struct SimplifiedKeyboardTheme: Codable {
    let id: String
    var name: String
    var type: String
    var keyStyle: String

    // 颜色配置
    var backgroundColor: ColorData
    var keyBackgroundColor: ColorData
    var keyPressedColor: ColorData
    var textColor: ColorData
    var specialKeyColor: ColorData
    var borderColor: ColorData

    // 图片配置
    var hasBackgroundImage: Bool
    var hasKeyImage: Bool
    var backgroundImagePath: String?
    var keyImagePath: String?
    var isBuiltInImageTheme: Bool
    var imageOpacity: Double
    var imageBlendMode: String

    // 字体配置
    var fontName: String
    var fontSize: Double
    var fontWeight: String

    // 布局配置
    var keySpacing: Double
    var keyHeight: Double
    var showBorder: Bool
    var borderWidth: Double

    // 特效配置
    var enableShadow: Bool
    var shadowColor: ColorData
    var shadowRadius: Double
    var enableHaptic: Bool
    var enableSound: Bool

    // 创建时间
    var createdAt: Date
    var updatedAt: Date
}
