//
//  KeyboardViewController.swift
//  KeyBoardExtension
//
//  Created by yjzheng on 2025/5/23.
//

import UIKit

class KeyboardViewController: UIInputViewController {

    // MARK: - 属性
    private var keyboardLayoutView: KeyboardLayoutView!
    private var currentTheme: KeyboardTheme = KeyboardTheme.defaultTheme
    private var currentAdvancedTheme: AdvancedKeyboardTheme?
    private var emojiPickerView: EmojiPickerView?
    private let themeManager = KeyboardThemeDataManager.shared
    private var isShiftEnabled = false
    private var isCapsLockEnabled = false

    // MARK: - 生命周期
    override func viewDidLoad() {
        super.viewDidLoad()
        setupKeyboard()
        loadTheme()
        observeThemeChanges()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        // 每次显示时重新加载主题，确保获取最新设置
        loadTheme()
    }

    override func updateViewConstraints() {
        super.updateViewConstraints()

        // 设置键盘高度
        if let keyboardLayoutView = keyboardLayoutView {
            let keyboardHeight: CGFloat = 260 // 可以根据需要调整
            keyboardLayoutView.heightAnchor.constraint(equalToConstant: keyboardHeight).isActive = true
        }
    }

    // MARK: - 键盘设置
    private func setupKeyboard() {
        // 创建键盘布局视图
        keyboardLayoutView = KeyboardLayoutView(theme: currentTheme, advancedTheme: currentAdvancedTheme, delegate: self)
        keyboardLayoutView.translatesAutoresizingMaskIntoConstraints = false

        // 添加到视图
        view.addSubview(keyboardLayoutView)

        // 设置约束
        NSLayoutConstraint.activate([
            keyboardLayoutView.topAnchor.constraint(equalTo: view.topAnchor),
            keyboardLayoutView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            keyboardLayoutView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            keyboardLayoutView.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    // MARK: - 表情符号选择器
    private func showEmojiPicker() {
        // 如果已经显示，则隐藏
        if emojiPickerView != nil {
            hideEmojiPicker()
            return
        }

        // 创建表情符号选择器
        emojiPickerView = EmojiPickerView(theme: currentTheme, advancedTheme: currentAdvancedTheme)
        emojiPickerView?.delegate = self
        emojiPickerView?.translatesAutoresizingMaskIntoConstraints = false

        // 同步当前键盘状态到表情选择器
        if let currentState = getCurrentKeyboardState() {
            emojiPickerView?.setKeyboardState(currentState)
        }

        guard let emojiPicker = emojiPickerView else { return }

        view.addSubview(emojiPicker)

        // 设置约束
        NSLayoutConstraint.activate([
            emojiPicker.topAnchor.constraint(equalTo: view.topAnchor),
            emojiPicker.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            emojiPicker.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            emojiPicker.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])

        // 添加动画效果
        emojiPicker.alpha = 0
        UIView.animate(withDuration: 0.3) {
            emojiPicker.alpha = 1
        }
    }

    private func hideEmojiPicker() {
        guard let emojiPicker = emojiPickerView else { return }

        UIView.animate(withDuration: 0.3, animations: {
            emojiPicker.alpha = 0
        }) { _ in
            emojiPicker.removeFromSuperview()
            self.emojiPickerView = nil
        }
    }

    // MARK: - 键盘状态管理
    private func getCurrentKeyboardState() -> KeyboardState? {
        return keyboardLayoutView?.getCurrentKeyboardState()
    }

    // MARK: - 主题管理
    private func loadTheme() {
        let newTheme = themeManager.loadTheme()
        let newAdvancedTheme = themeManager.loadAdvancedTheme()

        var shouldUpdate = false

        if newTheme.id != currentTheme.id {
            currentTheme = newTheme
            shouldUpdate = true
        }

        if newAdvancedTheme?.id != currentAdvancedTheme?.id {
            currentAdvancedTheme = newAdvancedTheme
            shouldUpdate = true
        }

        if shouldUpdate {
            updateKeyboardTheme()
        }
    }

    private func updateKeyboardTheme() {
        keyboardLayoutView?.updateTheme(currentTheme, advancedTheme: currentAdvancedTheme)
        view.backgroundColor = currentTheme.backgroundColor.uiColor
    }

    private func observeThemeChanges() {
        themeManager.observeThemeChanges { [weak self] newTheme in
            DispatchQueue.main.async {
                self?.currentTheme = newTheme
                self?.updateKeyboardTheme()
            }
        }
    }

    // MARK: - 文本处理
    override func textWillChange(_ textInput: UITextInput?) {
        super.textWillChange(textInput)
    }

    override func textDidChange(_ textInput: UITextInput?) {
        super.textDidChange(textInput)

        // 根据系统外观调整主题（如果需要）
        if currentTheme.type == "auto" {
            let isDarkMode = traitCollection.userInterfaceStyle == .dark
            // 这里可以根据系统外观自动切换主题
        }
    }

    // MARK: - 输入处理
    private func insertText(_ text: String) {
        var finalText = text

        // 处理大小写
        if isShiftEnabled || isCapsLockEnabled {
            finalText = text.uppercased()

            // 如果是单次Shift，输入后关闭
            if isShiftEnabled && !isCapsLockEnabled {
                isShiftEnabled = false
            }
        }

        textDocumentProxy.insertText(finalText)
    }

    private func deleteBackward() {
        textDocumentProxy.deleteBackward()
    }

    private func insertSpace() {
        textDocumentProxy.insertText(" ")
    }

    private func insertReturn() {
        textDocumentProxy.insertText("\n")
    }

    private func toggleShift() {
        if isShiftEnabled {
            // 如果已经是Shift状态，再次点击变成CapsLock
            isCapsLockEnabled = true
            isShiftEnabled = false
        } else {
            // 启用Shift
            isShiftEnabled = true
            isCapsLockEnabled = false
        }

        // 这里可以更新Shift键的视觉状态
        // keyboardLayoutView.updateShiftState(isShiftEnabled, isCapsLockEnabled)
    }
}

// MARK: - KeyboardLayoutDelegate
extension KeyboardViewController: KeyboardLayoutDelegate {
    func keyboardDidTapKey(_ key: String) {
        insertText(key)
    }

    func keyboardDidTapSpace() {
        insertSpace()
    }

    func keyboardDidTapDelete() {
        deleteBackward()
    }

    func keyboardDidTapShift() {
        toggleShift()
    }

    func keyboardDidTapEnter() {
        insertReturn()
    }

    func keyboardDidTapEmoji() {
        showEmojiPicker()
    }

    func keyboardDidSwitchToNumbers() {
        // 切换到数字/符号键盘的逻辑已在KeyboardLayoutView中实现
        print("切换到数字/符号键盘")
    }

    func keyboardDidSwitchToLetters() {
        // 切换到字母键盘的逻辑已在KeyboardLayoutView中实现
        print("切换到字母键盘")
    }

    func keyboardDidSwitchToSymbols() {
        // 切换到更多符号键盘的逻辑已在KeyboardLayoutView中实现
        print("切换到更多符号键盘")
    }
}

// MARK: - EmojiPickerDelegate
extension KeyboardViewController: EmojiPickerDelegate {
    func emojiPickerDidSelectEmoji(_ emoji: String) {
        // 插入选中的表情符号
        textDocumentProxy.insertText(emoji)

        // 隐藏表情符号选择器
        hideEmojiPicker()
    }

    func emojiPickerDidClose() {
        print("🔄 表情键盘关闭，当前主键盘状态: \(getCurrentKeyboardState()?.rawValue ?? "未知")")
        hideEmojiPicker()
    }

    // 工具栏功能实现
    func emojiPickerDidTapSpace() {
        insertSpace()
    }

    func emojiPickerDidTapEnter() {
        insertReturn()
    }

    func emojiPickerDidTapDelete() {
        deleteBackward()
    }

    func emojiPickerDidSwitchToNumbers() {
        print("🔄 表情选择器请求切换到数字键盘")
        // 更新主键盘状态
        keyboardLayoutView.switchToKeyboardState(.numbers)
        // 更新表情选择器工具栏状态
        emojiPickerView?.setKeyboardState(.numbers)
        print("✅ 表情选择器切换到数字/符号键盘完成")
    }

    func emojiPickerDidSwitchToLetters() {
        print("🔄 表情选择器请求切换到字母键盘")
        // 更新主键盘状态
        keyboardLayoutView.switchToKeyboardState(.letters)
        // 更新表情选择器工具栏状态
        emojiPickerView?.setKeyboardState(.letters)
        print("✅ 表情选择器切换到字母键盘完成")
    }

    func emojiPickerDidSwitchToSymbols() {
        print("🔄 表情选择器请求切换到符号键盘")
        // 更新主键盘状态
        keyboardLayoutView.switchToKeyboardState(.symbols)
        // 更新表情选择器工具栏状态
        emojiPickerView?.setKeyboardState(.symbols)
        print("✅ 表情选择器切换到更多符号键盘完成")
    }
}
