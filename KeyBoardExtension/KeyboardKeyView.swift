//
//  KeyboardKeyView.swift
//  KeyBoardExtension
//
//  Created by yj<PERSON><PERSON> on 2025/5/23.
//

import UIKit

/// 键盘按键类型（键盘扩展本地版本）
enum KeyboardKeyType {
    case letter(String)
    case number(String)
    case symbol(String)
    case space
    case delete
    case shift
    case enter
    case emoji           // 表情符号按钮
    case switchToNumbers  // 123按钮
    case switchToLetters  // ABC按钮
    case switchToSymbols  // #+=按钮
}

/// 键盘按键视图
class KeyboardKeyView: UIButton {

    // MARK: - 属性
    private var keyType: KeyboardKeyType
    private var theme: KeyboardTheme
    private var advancedTheme: AdvancedKeyboardTheme?
    private var isPressed: Bool = false

    // MARK: - 初始化
    init(keyType: KeyboardKeyType, theme: KeyboardTheme, advancedTheme: AdvancedKeyboardTheme? = nil) {
        self.keyType = keyType
        self.theme = theme
        self.advancedTheme = advancedTheme
        super.init(frame: .zero)
        setupButton()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 设置
    private func setupButton() {
        // 确保按钮可以接收用户交互
        isUserInteractionEnabled = true

        // 设置按键文本
        setupKeyText()

        // 设置按键样式
        setupKeyAppearance()

        // 设置触摸事件
        setupTouchEvents()
    }

    private func setupKeyText() {
        let keyValue: String
        switch keyType {
        case .letter(let char):
            keyValue = char.uppercased()
            setTitle(keyValue, for: .normal)
        case .number(let num):
            keyValue = num
            setTitle(keyValue, for: .normal)
        case .symbol(let sym):
            keyValue = sym
            setTitle(keyValue, for: .normal)
        case .space:
            keyValue = "空格"
            setTitle("空格", for: .normal) // 临时显示文字以便调试
        case .delete:
            keyValue = "删除"
            setTitle("⌫", for: .normal)
        case .shift:
            keyValue = "⇧"
            setTitle("⇧", for: .normal)
        case .enter:
            keyValue = "换行"
            setTitle("回车", for: .normal)
        case .emoji:
            keyValue = "表情符号"
            setTitle("😀", for: .normal) // 恢复正确的表情符号
        case .switchToNumbers:
            keyValue = "123"
            setTitle("123", for: .normal)
        case .switchToLetters:
            keyValue = "ABC"
            setTitle("ABC", for: .normal)
        case .switchToSymbols:
            keyValue = "#+="
            setTitle("#+=", for: .normal)
        }

        // 获取高级配置
        let advancedConfig = getAdvancedKeyConfig(for: keyValue)

        // 检查是否应该隐藏文字（当有自定义图片且配置为隐藏文字时）
        let shouldHideText = advancedConfig?.hasCustomImage == true && advancedConfig?.hideTextWhenImageExists == true

        if shouldHideText {
            // 隐藏文字：设置透明颜色
            setTitleColor(UIColor.clear, for: .normal)
            setTitleColor(UIColor.clear, for: .highlighted)
            print("🔇 隐藏按键文字: \(keyValue) (图片已包含文字)")
        } else {
            // 显示文字：设置正常颜色
            let textColor = advancedConfig?.textColor.uiColor ?? theme.textColor.uiColor
            setTitleColor(textColor, for: .normal)
            setTitleColor(textColor.withAlphaComponent(0.7), for: .highlighted)
            print("📝 显示按键文字: \(keyValue), 颜色: \(textColor)")
        }

        // 设置字体
        let fontSize = advancedConfig?.fontSize ?? theme.fontSize
        let fontWeight = advancedConfig?.fontWeight.uiKitFontWeight ?? theme.fontWeight.uiKitFontWeight
        let font = UIFont.systemFont(ofSize: CGFloat(fontSize), weight: fontWeight)
        titleLabel?.font = font
    }

    // 获取高级按键配置
    private func getAdvancedKeyConfig(for keyValue: String) -> KeyConfig? {
        guard let advancedTheme = advancedTheme else { return nil }

        let keyboardKeyType = convertToAdvancedKeyType()
        return KeyboardThemeDataManager.shared.getEffectiveKeyConfig(
            for: keyValue,
            keyType: keyboardKeyType,
            advancedTheme: advancedTheme
        )
    }

    // 转换按键类型
    private func convertToAdvancedKeyType() -> KeyType {
        switch keyType {
        case .letter:
            return .letter
        case .number:
            return .number
        case .symbol:
            return .symbol
        case .space:
            return .space
        case .delete, .shift, .enter, .emoji, .switchToNumbers, .switchToLetters, .switchToSymbols:
            return .function
        }
    }

    private func setupKeyAppearance() {
        // 获取当前按键值用于高级配置
        let keyValue = getCurrentKeyValue()
        let advancedConfig = getAdvancedKeyConfig(for: keyValue)

        // 设置背景
        setupKeyBackground()

        // 设置圆角
        let cornerRadius = advancedConfig?.cornerRadius ?? theme.keyStyle.cornerRadius
        layer.cornerRadius = CGFloat(cornerRadius)
        layer.masksToBounds = !theme.enableShadow // 如果有阴影则不裁剪

        // 设置边框
        let borderWidth = advancedConfig?.borderWidth ?? theme.borderWidth
        let borderColor = advancedConfig?.borderColor.uiColor ?? theme.borderColor.uiColor
        if theme.showBorder || advancedConfig != nil {
            layer.borderWidth = CGFloat(borderWidth)
            layer.borderColor = borderColor.cgColor
        }

        // 设置阴影
        if theme.enableShadow || (advancedConfig?.shadowEnabled == true) {
            let shadowColor = advancedConfig?.shadowColor.uiColor ?? theme.shadowColor.uiColor
            let shadowRadius = advancedConfig?.shadowRadius ?? theme.shadowRadius
            layer.shadowColor = shadowColor.cgColor
            layer.shadowOffset = advancedConfig?.shadowOffset ?? CGSize(width: 0, height: 1)
            layer.shadowRadius = CGFloat(shadowRadius)
            layer.shadowOpacity = 0.3
        }
    }

    // 获取当前按键值
    private func getCurrentKeyValue() -> String {
        switch keyType {
        case .letter(let char):
            return char.uppercased()
        case .number(let num):
            return num
        case .symbol(let sym):
            return sym
        case .space:
            return "空格"
        case .delete:
            return "删除"
        case .shift:
            return "⇧"
        case .enter:
            return "换行"
        case .emoji:
            return "表情符号"
        case .switchToNumbers:
            return "123"
        case .switchToLetters:
            return "ABC"
        case .switchToSymbols:
            return "#+="
        }
    }

    private func setupKeyBackground() {
        // 先设置基础背景颜色
        backgroundColor = getKeyBackgroundColor()

        // 如果有按键图片，则设置图片背景
        if theme.hasKeyImage {
            setupKeyImageBackground()
        }
    }

    private func setupKeyImageBackground() {
        let imageLoader = KeyboardImageLoader.shared
        let keyValue = getCurrentKeyValue()
        let advancedConfig = getAdvancedKeyConfig(for: keyValue)

        var keyImage: UIImage?
        var imageOpacity: Double = theme.imageOpacity
        var cornerRadius: CGFloat = theme.keyStyle.cornerRadius

        // 优先尝试加载个性化按键图片
        if let config = advancedConfig, config.hasCustomImage {
            print("🔍 尝试加载个性化按键图片: \(keyValue)")
            print("  - 配置存在: true")
            print("  - 有自定义图片: \(config.hasCustomImage)")
            print("  - 图片路径: \(config.normalImagePath ?? "nil")")
            print("  - 主题ID: \(theme.id)")

            keyImage = imageLoader.loadIndividualKeyImage(
                for: keyValue,
                keyConfig: config,
                themeId: theme.id,
                state: .normal,
                isBuiltIn: theme.isBuiltInImageTheme
            )
            imageOpacity = config.imageOpacity
            cornerRadius = CGFloat(config.cornerRadius)

            if keyImage != nil {
                print("✅ 成功加载个性化按键图片: \(keyValue)")
            } else {
                print("❌ 个性化按键图片加载失败: \(keyValue)")
            }
        } else {
            if advancedConfig == nil {
                print("⚠️ 没有找到高级配置: \(keyValue)")
            } else if let config = advancedConfig, !config.hasCustomImage {
                print("⚠️ 配置存在但没有自定义图片: \(keyValue)")
            }
        }

        // 如果没有个性化图片，使用主题默认图片
        if keyImage == nil {
            keyImage = imageLoader.loadKeyImage(for: theme)
            if keyImage != nil {
                print("✅ 使用主题默认按键图片: \(keyValue)")
            }
        }

        guard let finalKeyImage = keyImage else {
            print("⚠️ 无法加载任何按键图片，使用颜色背景: \(keyValue)")
            return
        }

        // 创建带透明度的图片
        let processedImage: UIImage
        if imageOpacity < 1.0 {
            processedImage = imageLoader.createImageWithOpacity(finalKeyImage, opacity: imageOpacity) ?? finalKeyImage
        } else {
            processedImage = finalKeyImage
        }

        // 创建圆角图片
        let roundedImage = imageLoader.createRoundedImage(processedImage, cornerRadius: cornerRadius) ?? processedImage

        // 设置为背景图片
        DispatchQueue.main.async {
            self.setBackgroundImage(roundedImage, for: .normal)

            // 为按下状态尝试加载专门的图片
            if let config = advancedConfig, config.hasCustomImage {
                let pressedImage = imageLoader.loadIndividualKeyImage(
                    for: keyValue,
                    keyConfig: config,
                    themeId: self.theme.id,
                    state: .pressed,
                    isBuiltIn: self.theme.isBuiltInImageTheme
                )

                if let pressedImg = pressedImage {
                    let processedPressedImage = imageLoader.createImageWithOpacity(pressedImg, opacity: imageOpacity) ?? pressedImg
                    let roundedPressedImage = imageLoader.createRoundedImage(processedPressedImage, cornerRadius: cornerRadius) ?? processedPressedImage
                    self.setBackgroundImage(roundedPressedImage, for: .highlighted)
                } else {
                    // 如果没有专门的按下状态图片，使用正常状态图片
                    self.setBackgroundImage(roundedImage, for: .highlighted)
                }
            } else {
                self.setBackgroundImage(roundedImage, for: .highlighted)
            }
        }
    }

    private func setupTouchEvents() {
        addTarget(self, action: #selector(keyPressed), for: .touchDown)
        addTarget(self, action: #selector(keyReleased), for: [.touchUpInside, .touchUpOutside, .touchCancel])
    }

    private func getKeyBackgroundColor() -> UIColor {
        switch keyType {
        case .letter, .number, .symbol:
            return theme.keyBackgroundColor.uiColor
        case .space, .delete, .shift, .enter, .emoji, .switchToNumbers, .switchToLetters, .switchToSymbols:
            return theme.specialKeyColor.uiColor
        }
    }

    // MARK: - 触摸事件
    @objc private func keyPressed() {
        isPressed = true

        // 视觉反馈
        UIView.animate(withDuration: 0.1) {
            self.backgroundColor = self.theme.keyPressedColor.uiColor
            self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }

        // 触觉反馈
        if theme.enableHaptic {
            let impactFeedback = UIImpactFeedbackGenerator(style: .light)
            impactFeedback.impactOccurred()
        }

        // 声音反馈
        if theme.enableSound {
            // 这里可以添加按键音效
        }
    }

    @objc private func keyReleased() {
        isPressed = false

        // 恢复视觉状态
        UIView.animate(withDuration: 0.1) {
            self.backgroundColor = self.getKeyBackgroundColor()
            self.transform = CGAffineTransform.identity
        }
    }

    // MARK: - 更新主题
    func updateTheme(_ newTheme: KeyboardTheme, advancedTheme: AdvancedKeyboardTheme? = nil) {
        theme = newTheme
        self.advancedTheme = advancedTheme
        setupKeyText()
        setupKeyAppearance()
    }

    // MARK: - 获取按键值
    func getKeyValue() -> String? {
        switch keyType {
        case .letter(let char):
            return char
        case .number(let num):
            return num
        case .symbol(let sym):
            return sym
        case .space:
            return " "
        default:
            return nil
        }
    }

    // MARK: - 获取按键类型
    func getKeyType() -> KeyboardKeyType {
        return keyType
    }
}

// MARK: - 键盘状态枚举
enum KeyboardState: String {
    case letters = "letters"
    case numbers = "numbers"
    case symbols = "symbols"
}

// MARK: - 键盘布局配置
struct KeyboardLayoutConfig {
    // 字母键盘布局
    static let letterRows: [[String]] = [
        ["q", "w", "e", "r", "t", "y", "u", "i", "o", "p"],
        ["a", "s", "d", "f", "g", "h", "j", "k", "l"],
        ["z", "x", "c", "v", "b", "n", "m"]
    ]

    // 数字/符号键盘布局
    static let numberSymbolRows: [[String]] = [
        ["1", "2", "3", "4", "5", "6", "7", "8", "9", "0"],
        ["-", "/", ":", ";", "(", ")", "¥", "&", "@", "\""],
        [".", ",", "?", "!", "'"]
    ]

    // 更多符号键盘布局
    static let moreSymbolRows: [[String]] = [
        ["[", "]", "{", "}", "#", "%", "^", "*", "+", "="],
        ["_", "\\", "|", "~", "<", ">", "€", "£", "$", "•"],
        [".", ",", "?", "!", "'"]
    ]

    static let keySpacing: CGFloat = 6
    static let rowSpacing: CGFloat = 12
}

// MARK: - 扩展：从MyWidgetKit导入主题类型
extension KeyboardKeyView {
    // 这里需要确保能够访问MyWidgetKit中的KeyboardTheme类型
    // 如果无法直接导入，可能需要创建一个简化的主题结构
}
