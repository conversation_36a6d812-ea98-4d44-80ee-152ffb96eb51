//
//  KeyboardLayoutView.swift
//  KeyBoardExtension
//
//  Created by yjzheng on 2025/5/23.
//

import UIKit

/// 键盘布局视图
class KeyboardLayoutView: UIView {

    // MARK: - 属性
    private var theme: KeyboardTheme
    private var advancedTheme: AdvancedKeyboardTheme?
    private var keyViews: [KeyboardKeyView] = []
    private weak var delegate: KeyboardLayoutDelegate?

    // 键盘状态
    private var currentKeyboardState: KeyboardState = .letters

    // 布局相关
    private var mainStackView: UIStackView!
    private var currentLayoutStackViews: [UIStackView] = []

    // MARK: - 初始化
    init(theme: KeyboardTheme, advancedTheme: AdvancedKeyboardTheme? = nil, delegate: KeyboardLayoutDelegate? = nil) {
        self.theme = theme
        self.advancedTheme = advancedTheme
        self.delegate = delegate
        super.init(frame: .zero)
        setupLayout()
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    // MARK: - 布局设置
    private func setupLayout() {
        // 设置背景
        setupBackground()

        // 创建主要的垂直堆栈视图
        mainStackView = UIStackView()
        mainStackView.axis = .vertical
        mainStackView.spacing = CGFloat(theme.keySpacing)
        mainStackView.distribution = .fillEqually
        mainStackView.translatesAutoresizingMaskIntoConstraints = false
        addSubview(mainStackView)

        // 设置约束
        NSLayoutConstraint.activate([
            mainStackView.topAnchor.constraint(equalTo: topAnchor, constant: 8),
            mainStackView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 8),
            mainStackView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -8),
            mainStackView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -8)
        ])

        // 根据当前状态创建布局
        setupKeyboardLayout(for: currentKeyboardState)
    }

    // MARK: - 动态键盘布局
    private func setupKeyboardLayout(for state: KeyboardState) {
        // 清除现有布局
        clearCurrentLayout()

        switch state {
        case .letters:
            setupLettersLayout()
        case .numbers:
            setupNumbersLayout()
        case .symbols:
            setupSymbolsLayout()
        }

        // 总是添加底部功能行
        setupBottomRow(for: state)
    }

    private func clearCurrentLayout() {
        // 移除所有现有的堆栈视图
        for stackView in currentLayoutStackViews {
            stackView.removeFromSuperview()
        }
        currentLayoutStackViews.removeAll()

        // 清除按键视图
        keyViews.removeAll()
    }

    // MARK: - 字母键盘布局
    private func setupLettersLayout() {
        for (rowIndex, row) in KeyboardLayoutConfig.letterRows.enumerated() {
            let rowStackView = UIStackView()
            rowStackView.axis = .horizontal
            rowStackView.spacing = CGFloat(theme.keySpacing)
            rowStackView.distribution = .fillEqually

            // 为第二行和第三行添加适当的缩进
            if rowIndex == 1 {
                // 第二行（asdf...）稍微缩进
                let spacer1 = UIView()
                spacer1.widthAnchor.constraint(equalToConstant: 20).isActive = true
                rowStackView.addArrangedSubview(spacer1)
            } else if rowIndex == 2 {
                // 第三行添加Shift键
                let shiftKey = KeyboardKeyView(keyType: .shift, theme: theme, advancedTheme: advancedTheme)
                shiftKey.addTarget(self, action: #selector(shiftTapped(_:)), for: .touchUpInside)
                keyViews.append(shiftKey)
                rowStackView.addArrangedSubview(shiftKey)
            }

            // 添加字母键
            for letter in row {
                let keyView = KeyboardKeyView(keyType: .letter(letter), theme: theme, advancedTheme: advancedTheme)
                keyView.addTarget(self, action: #selector(keyTapped(_:)), for: .touchUpInside)
                keyViews.append(keyView)
                rowStackView.addArrangedSubview(keyView)
            }

            // 为第三行添加删除键
            if rowIndex == 2 {
                let deleteKey = KeyboardKeyView(keyType: .delete, theme: theme, advancedTheme: advancedTheme)
                deleteKey.addTarget(self, action: #selector(deleteTapped(_:)), for: .touchUpInside)
                keyViews.append(deleteKey)
                rowStackView.addArrangedSubview(deleteKey)
            } else if rowIndex == 1 {
                // 第二行添加右侧间距
                let spacer2 = UIView()
                spacer2.widthAnchor.constraint(equalToConstant: 20).isActive = true
                rowStackView.addArrangedSubview(spacer2)
            }

            mainStackView.addArrangedSubview(rowStackView)
            currentLayoutStackViews.append(rowStackView)
        }
    }

    // MARK: - 数字/符号键盘布局
    private func setupNumbersLayout() {
        for (rowIndex, row) in KeyboardLayoutConfig.numberSymbolRows.enumerated() {
            let rowStackView = UIStackView()
            rowStackView.axis = .horizontal
            rowStackView.spacing = CGFloat(theme.keySpacing)
            rowStackView.distribution = .fillEqually

            if rowIndex == 2 {
                // 第三行添加#+= 切换键
                let symbolsKey = KeyboardKeyView(keyType: .switchToSymbols, theme: theme, advancedTheme: advancedTheme)
                symbolsKey.addTarget(self, action: #selector(switchToSymbolsTapped(_:)), for: .touchUpInside)
                keyViews.append(symbolsKey)
                rowStackView.addArrangedSubview(symbolsKey)
            }

            // 添加数字或符号键
            for item in row {
                let keyType: KeyboardKeyType = rowIndex == 0 ? .number(item) : .symbol(item)
                let keyView = KeyboardKeyView(keyType: keyType, theme: theme, advancedTheme: advancedTheme)
                keyView.addTarget(self, action: #selector(keyTapped(_:)), for: .touchUpInside)
                keyViews.append(keyView)
                rowStackView.addArrangedSubview(keyView)
            }

            if rowIndex == 2 {
                // 第三行添加删除键
                let deleteKey = KeyboardKeyView(keyType: .delete, theme: theme, advancedTheme: advancedTheme)
                deleteKey.addTarget(self, action: #selector(deleteTapped(_:)), for: .touchUpInside)
                keyViews.append(deleteKey)
                rowStackView.addArrangedSubview(deleteKey)
            }

            mainStackView.addArrangedSubview(rowStackView)
            currentLayoutStackViews.append(rowStackView)
        }
    }

    // MARK: - 更多符号键盘布局
    private func setupSymbolsLayout() {
        for (rowIndex, row) in KeyboardLayoutConfig.moreSymbolRows.enumerated() {
            let rowStackView = UIStackView()
            rowStackView.axis = .horizontal
            rowStackView.spacing = CGFloat(theme.keySpacing)
            rowStackView.distribution = .fillEqually

            if rowIndex == 2 {
                // 第三行添加123切换键
                let numbersKey = KeyboardKeyView(keyType: .switchToNumbers, theme: theme, advancedTheme: advancedTheme)
                numbersKey.addTarget(self, action: #selector(switchToNumbersTapped(_:)), for: .touchUpInside)
                keyViews.append(numbersKey)
                rowStackView.addArrangedSubview(numbersKey)
            }

            // 添加符号键
            for symbol in row {
                let keyView = KeyboardKeyView(keyType: .symbol(symbol), theme: theme, advancedTheme: advancedTheme)
                keyView.addTarget(self, action: #selector(keyTapped(_:)), for: .touchUpInside)
                keyViews.append(keyView)
                rowStackView.addArrangedSubview(keyView)
            }

            if rowIndex == 2 {
                // 第三行添加删除键
                let deleteKey = KeyboardKeyView(keyType: .delete, theme: theme, advancedTheme: advancedTheme)
                deleteKey.addTarget(self, action: #selector(deleteTapped(_:)), for: .touchUpInside)
                keyViews.append(deleteKey)
                rowStackView.addArrangedSubview(deleteKey)
            }

            mainStackView.addArrangedSubview(rowStackView)
            currentLayoutStackViews.append(rowStackView)
        }
    }

    private func setupBottomRow(for state: KeyboardState) {
        let bottomRowStackView = UIStackView()
        bottomRowStackView.axis = .horizontal
        bottomRowStackView.spacing = CGFloat(theme.keySpacing)
        bottomRowStackView.distribution = .fill
        bottomRowStackView.alignment = .fill
        print("🔧 创建底部行StackView，distribution: .fill")

        // 根据当前状态决定左侧切换键
        let switchKey: KeyboardKeyView
        switch state {
        case .letters:
            switchKey = KeyboardKeyView(keyType: .switchToNumbers, theme: theme, advancedTheme: advancedTheme)
            switchKey.addTarget(self, action: #selector(switchToNumbersTapped(_:)), for: .touchUpInside)
        case .numbers, .symbols:
            switchKey = KeyboardKeyView(keyType: .switchToLetters, theme: theme, advancedTheme: advancedTheme)
            switchKey.addTarget(self, action: #selector(switchToLettersTapped(_:)), for: .touchUpInside)
        }
        keyViews.append(switchKey)
        bottomRowStackView.addArrangedSubview(switchKey)

        // 表情符号键
        let emojiKey = KeyboardKeyView(keyType: .emoji, theme: theme, advancedTheme: advancedTheme)
        emojiKey.addTarget(self, action: #selector(emojiTapped(_:)), for: .touchUpInside)
        keyViews.append(emojiKey)
        bottomRowStackView.addArrangedSubview(emojiKey)

        // 空格键
        let spaceKey = KeyboardKeyView(keyType: .space, theme: theme, advancedTheme: advancedTheme)
        spaceKey.addTarget(self, action: #selector(spaceTapped(_:)), for: .touchUpInside)
        keyViews.append(spaceKey)
        bottomRowStackView.addArrangedSubview(spaceKey)
        print("🔧 添加空格键到底部行，当前子视图数量: \(bottomRowStackView.arrangedSubviews.count)")

        // 回车键
        let enterKey = KeyboardKeyView(keyType: .enter, theme: theme, advancedTheme: advancedTheme)
        enterKey.addTarget(self, action: #selector(enterTapped(_:)), for: .touchUpInside)
        keyViews.append(enterKey)
        bottomRowStackView.addArrangedSubview(enterKey)
        print("🔧 添加回车键到底部行，当前子视图数量: \(bottomRowStackView.arrangedSubviews.count)")

        // 设置空格键占用更多空间
        spaceKey.setContentHuggingPriority(.defaultLow, for: .horizontal)
        spaceKey.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)

        switchKey.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        switchKey.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)

        emojiKey.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        emojiKey.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)

        enterKey.setContentHuggingPriority(.defaultHigh, for: .horizontal)
        enterKey.setContentCompressionResistancePriority(.defaultHigh, for: .horizontal)

        // 设置精确的宽度约束
        NSLayoutConstraint.activate([
            switchKey.widthAnchor.constraint(equalToConstant: 50),  // 123按钮更小
            emojiKey.widthAnchor.constraint(equalToConstant: 50),   // 表情按钮更小
            enterKey.widthAnchor.constraint(equalToConstant: 60),   // 回车按钮适中
            // 空格键自动占用剩余空间
        ])

        print("🔧 底部行设置完成，包含按键: 切换键(\(switchKey.titleLabel?.text ?? "无")), 表情键(\(emojiKey.titleLabel?.text ?? "无")), 空格键, 回车键(\(enterKey.titleLabel?.text ?? "无"))")

        mainStackView.addArrangedSubview(bottomRowStackView)
        currentLayoutStackViews.append(bottomRowStackView)
    }

    // MARK: - 按键事件处理
    @objc private func keyTapped(_ sender: KeyboardKeyView) {
        if let value = sender.getKeyValue() {
            delegate?.keyboardDidTapKey(value)
        }
    }

    @objc private func spaceTapped(_ sender: KeyboardKeyView) {
        delegate?.keyboardDidTapSpace()
    }

    @objc private func deleteTapped(_ sender: KeyboardKeyView) {
        delegate?.keyboardDidTapDelete()
    }

    @objc private func shiftTapped(_ sender: KeyboardKeyView) {
        delegate?.keyboardDidTapShift()
    }

    @objc private func enterTapped(_ sender: KeyboardKeyView) {
        delegate?.keyboardDidTapEnter()
    }

    @objc private func emojiTapped(_ sender: KeyboardKeyView) {
        delegate?.keyboardDidTapEmoji()
    }

    @objc private func switchToNumbersTapped(_ sender: KeyboardKeyView) {
        switchToKeyboard(.numbers)
        delegate?.keyboardDidSwitchToNumbers()
    }

    @objc private func switchToLettersTapped(_ sender: KeyboardKeyView) {
        switchToKeyboard(.letters)
        delegate?.keyboardDidSwitchToLetters()
    }

    @objc private func switchToSymbolsTapped(_ sender: KeyboardKeyView) {
        switchToKeyboard(.symbols)
        delegate?.keyboardDidSwitchToSymbols()
    }

    // MARK: - 键盘切换
    private func switchToKeyboard(_ state: KeyboardState) {
        currentKeyboardState = state
        setupKeyboardLayout(for: state)
    }

    // 公共方法：外部切换键盘状态
    func switchToKeyboardState(_ state: KeyboardState) {
        switchToKeyboard(state)
    }

    // 公共方法：获取当前键盘状态
    func getCurrentKeyboardState() -> KeyboardState {
        return currentKeyboardState
    }

    // MARK: - 背景设置
    private func setupBackground() {
        // 先设置基础背景颜色
        backgroundColor = theme.backgroundColor.uiColor

        // 如果有背景图片，则设置图片背景
        if theme.hasBackgroundImage {
            setupBackgroundImage()
        }
    }

    private func setupBackgroundImage() {
        let imageLoader = KeyboardImageLoader.shared

        guard let backgroundImage = imageLoader.loadBackgroundImage(for: theme) else {
            print("⚠️ 无法加载键盘背景图片，使用颜色背景")
            return
        }

        // 创建背景图片视图
        let backgroundImageView = UIImageView()
        backgroundImageView.image = backgroundImage
        backgroundImageView.contentMode = .scaleAspectFill
        backgroundImageView.clipsToBounds = true
        backgroundImageView.alpha = CGFloat(theme.imageOpacity)
        backgroundImageView.translatesAutoresizingMaskIntoConstraints = false

        // 插入到最底层
        insertSubview(backgroundImageView, at: 0)

        // 设置约束
        NSLayoutConstraint.activate([
            backgroundImageView.topAnchor.constraint(equalTo: topAnchor),
            backgroundImageView.leadingAnchor.constraint(equalTo: leadingAnchor),
            backgroundImageView.trailingAnchor.constraint(equalTo: trailingAnchor),
            backgroundImageView.bottomAnchor.constraint(equalTo: bottomAnchor)
        ])
    }

    // MARK: - 主题更新
    func updateTheme(_ newTheme: KeyboardTheme, advancedTheme: AdvancedKeyboardTheme? = nil) {
        theme = newTheme
        self.advancedTheme = advancedTheme

        // 重新设置背景
        setupBackground()

        // 更新所有按键的主题
        for keyView in keyViews {
            keyView.updateTheme(newTheme, advancedTheme: advancedTheme)
        }

        // 更新间距
        mainStackView.spacing = CGFloat(theme.keySpacing)

        // 更新所有布局堆栈视图的间距
        for stackView in currentLayoutStackViews {
            stackView.spacing = CGFloat(theme.keySpacing)
        }
    }
}

// MARK: - 键盘布局代理
protocol KeyboardLayoutDelegate: AnyObject {
    func keyboardDidTapKey(_ key: String)
    func keyboardDidTapSpace()
    func keyboardDidTapDelete()
    func keyboardDidTapShift()
    func keyboardDidTapEnter()
    func keyboardDidTapEmoji()
    func keyboardDidSwitchToNumbers()
    func keyboardDidSwitchToLetters()
    func keyboardDidSwitchToSymbols()
}
