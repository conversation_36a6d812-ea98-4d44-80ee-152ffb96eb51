//
//  IntentHandler.swift
//  WidgetIntentExtension
//
//  Created by yjzheng on 2025/5/13.
//

import Intents

class IntentHandler: INExtension {
    override func handler(for intent: INIntent) -> Any {
        // This is the default implementation.  If you want different objects to handle different intents,
        // you can override this and return the handler you want for that particular intent.

        return self
    }
}

extension IntentHandler: DynamicConfigIntentHandling {
    func provideSelectButtonOptionsCollection(for intent: DynamicConfigIntent, searchTerm: String?, with completion: @escaping (INObjectCollection<CustomButtonItem>?, (any Error)?) -> Void) {
        let typeList: [StaticConfigBtnType] = [.scan, .dailyQuote, .todoList, .timeClock, .waterIntake, .passwordGenerator, .pomodoro, .note, .moonPhase, .appLauncher, .deviceInfo]
        let itemList = generateItemList(from: typeList)
        completion(INObjectCollection(items: itemList), nil)
    }

    private func defaultSelectButtons(for intent: DynamicConfigIntent) -> [CustomButtonItem]? {
        let defaultBtnTypeList: [StaticConfigBtnType] = [.scan, .dailyQuote, .todoList, .timeClock, .waterIntake, .passwordGenerator, .pomodoro, .note, .moonPhase, .appLauncher, .deviceInfo]
        let defaultItemList = generateItemList(from: defaultBtnTypeList)
        return defaultItemList
    }

    fileprivate func generateItemList(from typeList: [StaticConfigBtnType]) -> [CustomButtonItem] {
        let defaultItemList = typeList.map {
            let formatBtnType = buttonType(from: $0)
            let item = CustomButtonItem(identifier: formatBtnType.id,
                                        display: formatBtnType.displayName)
            item.buttonType = $0
            item.urlStr = formatBtnType.urlStr
            item.imageName = formatBtnType.imageName
            item.displayImage = INImage(named: "widget_scan")
            return item
        }
        return defaultItemList
    }

    // 将Intent中定义的按钮类型转为Widget中的按钮类型使用
    func buttonType(from configurationType: StaticConfigBtnType) -> WidgetButtonType {
        switch configurationType {
        case .scan:
            return .scan
        case .dailyQuote:
            return .dailyQuote
        case .unknown:
            return .unknown
        case .todoList:
            return .todoList
        case .timeClock:
            return .timeClock
        case .waterIntake:
            return .waterIntake
        case .passwordGenerator:
            return .passwordGenerator
        case .pomodoro:
            return .pomodoro
        case .note:
            return .note
        case .moonPhase:
            return .moonPhase
        case .appLauncher:
            return .appLauncher
        case .deviceInfo:
            return .deviceInfo
        }
    }
}
