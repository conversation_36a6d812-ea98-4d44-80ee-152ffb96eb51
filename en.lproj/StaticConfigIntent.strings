"25z1q2" = "daily quote";

"5QJ2sT" = "time clock";

"D78TrN" = "Custom Button Item";

"EyQRTp" = "Static Config Btn Type";

"GDOn1Z" = "password generator";

"H2dGqu" = "water intake";

"IwGXsL" = "Button Type";

"KoVD2G" = "note";

"Me2V8Y" = "Please add a widget in the app before selecting.";

"TYqP9A" = "Please add a widget in the app before selecting.";

"UmpY3N" = "select widget";

"Ymazyc" = "device info";

"crY2KA" = "select widget";

"dT1HXv" = "Image Name";

"iEBixT" = "StaticConfig";

"kp8kEu" = "Btn Type";

"lUZpdl-25z1q2" = "Just to confirm, you wanted ‘daily quote’?";

"lUZpdl-5QJ2sT" = "Just to confirm, you wanted ‘time clock’?";

"lUZpdl-GDOn1Z" = "Just to confirm, you wanted ‘password generator’?";

"lUZpdl-H2dGqu" = "Just to confirm, you wanted ‘water intake’?";

"lUZpdl-KoVD2G" = "Just to confirm, you wanted ‘note’?";

"lUZpdl-Ymazyc" = "Just to confirm, you wanted ‘device info’?";

"lUZpdl-pOWHat" = "Just to confirm, you wanted ‘moon phase’?";

"lUZpdl-qxP70q" = "Just to confirm, you wanted ‘scan’?";

"lUZpdl-rDTU38" = "Just to confirm, you wanted ‘pomodoro’?";

"lUZpdl-tRLnBD" = "Just to confirm, you wanted ‘app launcher’?";

"lUZpdl-vCMUNb" = "Just to confirm, you wanted ‘todo list’?";

"pOWHat" = "moon phase";

"qxP70q" = "scan";

"rDTU38" = "pomodoro";

"rrKEx5" = "StaticConfig";

"tRLnBD" = "app launcher";

"tdqNlz-25z1q2" = "There are ${count} options matching ‘daily quote’.";

"tdqNlz-5QJ2sT" = "There are ${count} options matching ‘time clock’.";

"tdqNlz-GDOn1Z" = "There are ${count} options matching ‘password generator’.";

"tdqNlz-H2dGqu" = "There are ${count} options matching ‘water intake’.";

"tdqNlz-KoVD2G" = "There are ${count} options matching ‘note’.";

"tdqNlz-Ymazyc" = "There are ${count} options matching ‘device info’.";

"tdqNlz-pOWHat" = "There are ${count} options matching ‘moon phase’.";

"tdqNlz-qxP70q" = "There are ${count} options matching ‘scan’.";

"tdqNlz-rDTU38" = "There are ${count} options matching ‘pomodoro’.";

"tdqNlz-tRLnBD" = "There are ${count} options matching ‘app launcher’.";

"tdqNlz-vCMUNb" = "There are ${count} options matching ‘todo list’.";

"vCMUNb" = "todo list";

"xLIuwg" = "Url Str";

