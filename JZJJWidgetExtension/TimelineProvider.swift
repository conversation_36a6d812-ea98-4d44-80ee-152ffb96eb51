import WidgetKit

import Foundation
import Intents
import SwiftUI
import WidgetKit

// struct Provider: TimelineProvider {
//    typealias Entry = SimpleEntry
//
//    func placeholder(in context: Context) -> SimpleEntry {
//        SimpleEntry(date: Date(), widgetData: WidgetData.placeholder)
//    }
//
//    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> Void) {
//        let entry = SimpleEntry(date: Date(), widgetData: WidgetData.placeholder)
//        completion(entry)
//    }
//
//    func getTimeline(in context: Context, completion: @escaping (Timeline<SimpleEntry>) -> Void) {
//        // 从 UserDefaults 加载数据
//        let widgetData = WidgetDataManager.shared.loadWidgetData() ?? WidgetData.placeholder
//        let entry = SimpleEntry(date: Date(), widgetData: widgetData)
//
//        // 设置更新策略
//        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 15, to: Date())!
//        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))
//
//        completion(timeline)
//    }
// }

struct TimelineProvider: IntentTimelineProvider {
    typealias Entry = SimpleEntry

    typealias Intent = StaticConfigIntent

    // 将Intent中定义的按钮类型转为Widget中的按钮类型使用
    func buttonType(from configuration: Intent) -> WidgetButtonType {
        switch configuration.btnType {
        case .scan:
            return .scan
        case .dailyQuote:
            return .dailyQuote
        case .todoList:
            return .todoList
        case .timeClock:
            return .timeClock
        case .waterIntake:
            return .waterIntake
        case .passwordGenerator:
            return .passwordGenerator
        case .pomodoro:
            return .pomodoro
        case .note:
            return .note
        case .moonPhase:
            return .moonPhase
        case .appLauncher:
            return .appLauncher
        case .deviceInfo:
            return .deviceInfo
        case .unknown:
            return .unknown
        }
    }

    func placeholder(in _: Context) -> Entry {
        Entry(date: Date(), widgetData: WidgetData.placeholder)
    }

    func getSnapshot(for _: Intent, in _: Context, completion: @escaping (Entry) -> Void) {
//        let buttonType = buttonType(from: configuration)

        let entry = SimpleEntry(date: Date(), widgetData: WidgetData.placeholder)
        completion(entry)
    }

    func getTimeline(for configuration: Intent, in _: Context, completion: @escaping (Timeline<Entry>) -> Void) {
        // 从 UserDefaults 加载数据
        let buttonType = buttonType(from: configuration)

        var widgetData: WidgetData
        switch buttonType {
        case .unknown,
             .scan:
            widgetData = WidgetData(type: .dailyQuote, content: "")
        case .dailyQuote:
            widgetData = WidgetData(type: .dailyQuote)
        case .todoList:
            widgetData = WidgetData(type: .todoList, content: "")
        case .timeClock:
            widgetData = WidgetData(type: .timeClock, content: "")
        case .waterIntake:
            widgetData = WidgetData(type: .waterIntake, content: "")
        case .passwordGenerator:
            widgetData = WidgetData(type: .passwordGenerator, content: "")
        case .pomodoro:
            widgetData = WidgetData(type: .pomodoro, content: "")
        case .note:
            widgetData = WidgetData(type: .note, content: "")
        case .moonPhase:
            widgetData = WidgetData(type: .moonPhase, content: "")
        case .appLauncher:
            widgetData = WidgetData(type: .appLauncher, content: "")
        case .deviceInfo:
            widgetData = WidgetData(type: .deviceInfo, content: "")
        }

//        let widgetData = WidgetDataManager.shared.loadWidgetData() ?? WidgetData.placeholder
        let entry = SimpleEntry(date: Date(), widgetData: widgetData)

        // 设置更新策略
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 15, to: Date())!
        let timeline = Timeline(entries: [entry], policy: .after(nextUpdate))

        completion(timeline)
    }
}

// struct StaticIntentWidgetEntry: TimelineEntry {
//    let date: Date
//
// }
//
// struct StaticIntentWidgetEntryView: View {
//    var entry: StaticIntentWidgetProvider.Entry
//    let mediumItem = AliPayWidgetMediumItem()
//
//    var body: some View {
//        AlipayWidgetMeidumView(mediumItem: mediumItem)
//    }
// }
//
// struct StaticIntentWidget: Widget {
//
//    let kind: String = "StaticIntentWidget"
//
//    var title: String = "StaticIntentWidget"
//    var desc: String = "StaticIntentWidget描述"
//
//    var body: some WidgetConfiguration {
//        IntentConfiguration(kind: kind,
//                            intent: StaticConfigurationIntent.self,
//                            provider: StaticIntentWidgetProvider()) { entry in
//            StaticIntentWidgetEntryView(entry: entry)
//        }
//        .configurationDisplayName(title)
//        .description(desc)
//        .supportedFamilies([.systemMedium])
//    }
// }
