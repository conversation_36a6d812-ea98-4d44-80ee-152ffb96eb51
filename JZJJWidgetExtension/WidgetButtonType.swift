//
//  ButtonType.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/13.
//

import Foundation

public enum WidgetButtonType: String {
    case unknown = ""
    case scan = "二维码"
    case dailyQuote = "每日一言"
    case todoList = "任务列表"
    case timeClock = "时钟"
    case waterIntake = "水分摄入"
    case passwordGenerator = "随机密码生成器"
    case pomodoro = "番茄时钟"
    case note = "快速笔记"
    case moonPhase = "月相日历"
    case appLauncher = "快捷启动"
    case deviceInfo = "设备信息"
}

extension WidgetButtonType: Identifiable {
    public var id: String {
        return rawValue
    }

    public var displayName: String {
        return rawValue
    }

    public var urlStr: String {
        let imageUrl: (image: String, url: String) = imageAndUrl(from: self)
        return imageUrl.url
    }

    public var imageName: String {
        let imageUrl: (image: String, url: String) = imageAndUrl(from: self)
        return imageUrl.image
    }

    /// return (image, url)
    func imageAndUrl(from type: WidgetButtonType) -> (String, String) {
        switch self {
        case .scan:
            return ("widget_scan", "https://fastly.picsum.photos/id/365/300/300.jpg?hmac=kkeehcpfbd5GfZlsNJPx19Xry-PASDMJKjh2wRbYpBM")
        case .dailyQuote:
            return ("widget_pay", "https://www.baidu.com/")
        case .unknown:
            return ("", "")
        case .todoList:
            return ("widget_pay", "https://www.baidu.com/")
        case .timeClock:
            return ("widget_pay", "https://www.baidu.com/")
        case .waterIntake:
            return ("widget_pay", "https://www.baidu.com/")
        case .passwordGenerator:
            return ("widget_pay", "https://www.baidu.com/")
        case .pomodoro:
            return ("widget_pay", "https://www.baidu.com/")
        case .note:
            return ("widget_pay", "https://www.baidu.com/")
        case .moonPhase:
            return ("widget_pay", "https://www.baidu.com/")
        case .appLauncher:
            return ("square.grid.3x2", "https://www.baidu.com/")
        case .deviceInfo:
            return ("info.circle", "https://www.baidu.com/")
        }
    }
}
