//
//  DynamicIntentWidgetProvider.swift
//  JZJJWidget
//
//  Created by yj<PERSON><PERSON> on 2025/5/13.
//

import MyWidgetKit
import SwiftUI
import WidgetKit

// 导入配置模型
import struct MyWidgetKit.DailyQuoteWidgetConfig
import enum MyWidgetKit.PriorityFilter
import struct MyWidgetKit.Task
import struct MyWidgetKit.TaskCategory
import struct MyWidgetKit.TodoWidgetConfig
import struct MyWidgetKit.TodoWidgetData
import struct MyWidgetKit.TimeWidgetData
import struct MyWidgetKit.WaterIntakeWidgetData
import struct MyWidgetKit.PasswordGeneratorConfig
import struct MyWidgetKit.PomodoroWidgetData
import struct MyWidgetKit.NoteWidgetData
import struct MyWidgetKit.NoteItem
import struct MyWidgetKit.MoonPhaseWidgetData
import struct MyWidgetKit.AppLauncherWidgetData
import struct MyWidgetKit.AppLauncherItem
import struct MyWidgetKit.DeviceInfoWidgetData

struct DynamicIntentWidgetProvider: IntentTimelineProvider {
    typealias Entry = DynamicIntentWidgetEntry
    typealias Intent = DynamicConfigIntent

    func placeholder(in _: Context) -> DynamicIntentWidgetEntry {
        DynamicIntentWidgetEntry(date: Date(), widgetData: .placeholder)
    }

    func getSnapshot(for configuration: Intent, in _: Context, completion: @escaping (DynamicIntentWidgetEntry) -> Void) {
        let entry = DynamicIntentWidgetEntry(date: Date(), selectButtonItem: configuration.selectButton, widgetData: .placeholder)
        completion(entry)
    }

    func getTimeline(for configuration: Intent, in _: Context, completion: @escaping (Timeline<DynamicIntentWidgetEntry>) -> Void) {
        // 创建默认数据
        var widgetData = WidgetData(type: .dailyQuote, content: "")

        // 根据选择的按钮类型确定小组件类型
        if let buttonType = configuration.selectButton?.buttonType {
            switch buttonType {
            case .dailyQuote:
                // 每日一言小组件
                if let widgetConfig = AppGroupDataManager.shared.read(DailyQuoteWidgetConfig.self, for: .dailyQuote, property: .config) {
                    // 由于 content 是常量，我们需要创建一个新的 WidgetData 实例
                    widgetData = WidgetData(
                        type: .dailyQuote,
                        content: widgetConfig.content,
                        backgroundColor: widgetConfig.fontColor, // 使用字体颜色作为背景色
                        textColor: widgetConfig.fontColor,
                        fontSize: widgetConfig.fontSize,
                        showBorder: widgetConfig.showBorder,
                        borderColor: widgetConfig.borderSwiftUIColor
                    )

                    // 创建 DailyQuoteData 对象（兼容性处理）
                    let style = DailyQuoteData.QuoteStyle(
                        background: .color(ColorWrapper(color: widgetConfig.fontColor)),
                        textColor: widgetConfig.fontColor,
                        fontSize: widgetConfig.fontSize,
                        showBorder: widgetConfig.showBorder,
                        borderColor: widgetConfig.borderSwiftUIColor
                    )

                    let quoteData = DailyQuoteData(
                        content: widgetConfig.content,
                        style: style,
                        lastUpdated: widgetConfig.lastUpdated
                    )

                    widgetData.dailyQuote = quoteData
                }

            case .todoList:
                // 任务清单小组件
                widgetData = WidgetData(type: .todoList, content: "")

                // 加载任务数据
                if let _ = AppGroupDataManager.shared.read(TodoWidgetConfig.self, for: .todoList, property: .config) {
                    // 配置已存在，不需要在这里处理，将在视图中处理
                } else {
                    // 创建默认配置
                    let defaultCategory = TaskCategory(name: "示例分类", color: .blue, icon: "list.bullet")
                    let sampleTasks = Task.createSampleTasks(for: defaultCategory)

                    // 保存默认分类和任务
                    AppGroupDataManager.shared.save([defaultCategory], for: .todoList, property: .categories)
                    AppGroupDataManager.shared.save(sampleTasks, for: .todoList, property: .tasks)

                    // 保存默认配置 - 确保显示所有任务
                    let defaultConfig = TodoWidgetConfig(
                        categoryId: nil, // 不指定分类，显示所有任务
                        showCompleted: true, // 显示已完成任务
                        maxTaskCount: 10, // 显示更多任务
                        priorityFilter: .all, // 显示所有优先级
                        background: .color(WidgetColor.fromColor(.white)),
                        fontName: "SF Pro",
                        fontSize: 15,
                        fontColor: .black,
                        lastUpdated: Date()
                    )

                    AppGroupDataManager.shared.save(defaultConfig, for: .todoList, property: .config)
                }

            case .scan:
                // 二维码小组件
                widgetData = WidgetData(type: .qrImage, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(QRWidgetViewData.self, for: .qrImage, property: .config) == nil {
                    // 创建默认二维码配置
                    let defaultQRConfig = QRWidgetViewData(
                        content: "https://www.example.com",
                        foreground: .color(WidgetColor.fromColor(.black)),
                        background: .color(WidgetColor.fromColor(.white)),
                        foregroundColor: WidgetColor.fromColor(.black)
                    )

                    AppGroupDataManager.shared.save(defaultQRConfig, for: .qrImage, property: .config)
                }


            case .timeClock:
                // 时间小组件
                widgetData = WidgetData(type: .timeClock, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(TimeWidgetData.self, for: .timeWidget, property: .config) == nil {
                    // 创建默认时间小组件配置
                    let defaultConfig = TimeWidgetData(
                        background: .color(WidgetColor(red: 0, green: 0, blue: 0, alpha: 1)),
                        fontColor: WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
                        fontName: "SF Pro",
                        use12HourFormat: false,
                        showDate: true,
                        showSeconds: true
                    )

                    AppGroupDataManager.shared.save(defaultConfig, for: .timeWidget, property: .config)
                }

            case .waterIntake:
                // 水分摄入追踪器小组件
                widgetData = WidgetData(type: .waterIntake, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(WaterIntakeWidgetData.self, for: .waterIntake, property: .config) == nil {
                    // 创建默认水分摄入追踪器配置
                    let defaultConfig = WaterIntakeWidgetData(
                        currentIntake: 800,
                        targetIntake: 2000,
                        quickAddOptions: [100, 200, 300, 500],
                        background: .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
                        fontName: "SF Pro",
                        fontSize: 14,
                        fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
                        accentColor: WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
                        useHealthKit: false,
                        lastUpdated: Date()
                    )

                    AppGroupDataManager.shared.save(defaultConfig, for: .waterIntake, property: .config)
                }

            case .passwordGenerator:
                // 随机密码生成器小组件
                widgetData = WidgetData(type: .passwordGenerator, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(PasswordGeneratorConfig.self, for: .passwordGenerator, property: .config) == nil {
                    // 创建默认密码生成器配置
                    let defaultConfig = PasswordGeneratorConfig.createDefault()
                    AppGroupDataManager.shared.save(defaultConfig, for: .passwordGenerator, property: .config)
                }

            case .pomodoro:
                // 番茄时钟小组件
                widgetData = WidgetData(type: .pomodoro, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) == nil {
                    // 创建默认番茄时钟配置
                    let defaultConfig = PomodoroWidgetData(
                        background: .color(WidgetColor(red: 0, green: 0, blue: 0, alpha: 1)),
                        fontColor: WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
                        fontName: "SF Pro",
                        workDuration: 25,
                        shortBreakDuration: 5,
                        longBreakDuration: 15,
                        longBreakInterval: 4,
                        currentState: .idle,
                        remainingSeconds: 25 * 60,
                        startTime: nil,
                        completedPomodoros: 0,
                        autoStartNextPhase: false,
                        lastUpdated: Date()
                    )

                    AppGroupDataManager.shared.save(defaultConfig, for: .pomodoro, property: .config)
                }

            case .note:
                // 快速笔记小组件
                widgetData = WidgetData(type: .note, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(NoteWidgetData.self, for: .note, property: .config) == nil {
                    // 创建默认笔记配置
                    let defaultConfig = NoteWidgetData(
                        notes: NoteItem.createSampleNotes(),
                        background: .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
                        fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
                        fontName: "SF Pro",
                        fontSize: 14,
                        displayMode: .all,
                        maxNoteCount: 3,
                        showCreationTime: true,
                        showTypeIcon: true,
                        lastUpdated: Date()
                    )

                    AppGroupDataManager.shared.save(defaultConfig, for: .note, property: .config)
                }

            case .moonPhase:
                // 月相日历小组件
                widgetData = WidgetData(type: .moonPhase, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(MoonPhaseWidgetData.self, for: .moonPhase, property: .config) == nil {
                    // 创建默认月相日历配置
                    let defaultConfig = MoonPhaseWidgetData(
                        background: .color(WidgetColor(red: 0.05, green: 0.05, blue: 0.2, alpha: 1)),
                        fontColor: WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
                        fontName: "SF Pro",
                        displayMode: .detailed,
                        showLunarDate: true
                    )

                    AppGroupDataManager.shared.save(defaultConfig, for: .moonPhase, property: .config)
                }

            case .appLauncher:
                // 应用快捷启动器小组件
                widgetData = WidgetData(type: .appLauncher, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(AppLauncherWidgetData.self, for: .appLauncher, property: .config) == nil {
                    // 创建默认应用项目
                    let defaultItems = [
                        AppLauncherItem(name: "微信", iconName: "message.fill", urlScheme: "weixin://", color: .green, position: 0),
                        AppLauncherItem(name: "支付宝", iconName: "creditcard.fill", urlScheme: "alipay://", color: .blue, position: 1),
                        AppLauncherItem(name: "淘宝", iconName: "cart.fill", urlScheme: "taobao://", color: .orange, position: 2),
                        AppLauncherItem(name: "抖音", iconName: "music.note", urlScheme: "snssdk1128://", color: .pink, position: 3),
                        AppLauncherItem(name: "微博", iconName: "flame.fill", urlScheme: "sinaweibo://", color: .red, position: 4),
                        AppLauncherItem(name: "QQ", iconName: "bubble.left.fill", urlScheme: "mqq://", color: .blue, position: 5),
                        AppLauncherItem(name: "京东", iconName: "bag.fill", urlScheme: "openapp.jdmobile://", color: .red, position: 6),
                        AppLauncherItem(name: "美团", iconName: "fork.knife", urlScheme: "meituanwaimai://", color: .yellow, position: 7)
                    ]

                    // 创建默认配置
                    let defaultConfig = AppLauncherWidgetData(
                        items: defaultItems,
                        iconStyle: .rounded,
                        layoutType: .grid,
                        background: .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
                        fontName: "SF Pro",
                        fontSize: 12,
                        fontColor: .black,
                        showLabels: true,
                        columns: 4,
                        rows: 2,
                        spacing: 10
                    )

                    AppGroupDataManager.shared.save(defaultConfig, for: .appLauncher, property: .config)
                }

            case .deviceInfo:
                // 设备信息小组件
                widgetData = WidgetData(type: .deviceInfo, content: "")

                // 如果没有配置，可以在这里创建默认配置
                if AppGroupDataManager.shared.read(DeviceInfoWidgetData.self, for: .deviceInfo, property: .config) == nil {
                    // 获取设备信息
                    let deviceName = UIDevice.current.name

                    // 获取存储信息
                    let fileManager = FileManager.default
                    var totalStorage: Int64 = 0
                    var freeStorage: Int64 = 0
                    var usedStorage: Int64 = 0
                    var storagePercentage: Double = 0

                    if let path = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first?.path,
                       let attributes = try? fileManager.attributesOfFileSystem(forPath: path),
                       let freeSize = attributes[.systemFreeSize] as? NSNumber,
                       let totalSize = attributes[.systemSize] as? NSNumber
                    {
                        totalStorage = totalSize.int64Value
                        freeStorage = freeSize.int64Value
                        usedStorage = totalStorage - freeStorage
                        storagePercentage = Double(usedStorage) / Double(totalStorage) * 100
                    }

                    // 创建默认配置
                    let defaultConfig = DeviceInfoWidgetData(
                        deviceName: UIDevice.current.name,
                        totalStorage: totalStorage,
                        usedStorage: usedStorage,
                        freeStorage: freeStorage,
                        storagePercentage: storagePercentage,
                        background: .color(WidgetColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1)),
                        fontName: "SF Pro",
                        fontSize: 14,
                        fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
                        accentColor: WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
                        showDeviceName: true,
                        showOSInfo: true,
                        showBatteryInfo: true,
                        showStorageInfo: true,
                        progressStyle: .bar,
                        textAlignment: .leading,
                        lastUpdated: Date()
                    )

                    AppGroupDataManager.shared.save(defaultConfig, for: .deviceInfo, property: .config)
                }



            case .unknown:
                // 未知类型，使用默认配置
                break
            }
        }

        let entry = DynamicIntentWidgetEntry(date: Date(), selectButtonItem: configuration.selectButton, widgetData: widgetData)

        // 设置更新策略
        if configuration.selectButton?.buttonType == .timeClock {
            // 时钟小组件需要特殊处理
            if let timeWidgetData = AppGroupDataManager.shared.read(TimeWidgetData.self, for: .timeWidget, property: .config) {
                if timeWidgetData.showSeconds {
                    // 显示秒的时钟小组件 - 尝试实现接近秒级的更新
                    // 注意：iOS限制了小组件的最小更新间隔，我们尽可能接近秒级更新

                    var entries: [DynamicIntentWidgetEntry] = []
                    let currentDate = Date()
                    let calendar = Calendar.current

                    // 创建接下来60秒的条目，每秒一个
                    // 这是一个尝试，实际上系统可能会限制更新频率
                    for second in 0..<60 {
                        if let date = calendar.date(byAdding: .second, value: second, to: currentDate) {
                            let entry = DynamicIntentWidgetEntry(
                                date: date,
                                selectButtonItem: configuration.selectButton,
                                widgetData: widgetData
                            )
                            entries.append(entry)
                        }
                    }

                    // 使用.atEnd策略，在最后一个条目显示完后请求新的时间线
                    let timeline = Timeline(entries: entries, policy: .atEnd)
                    completion(timeline)
                } else {
                    // 不显示秒的时钟小组件 - 每分钟更新一次
                    var entries: [DynamicIntentWidgetEntry] = []
                    let currentDate = Date()

                    // 创建未来30分钟的条目，每分钟一个
                    for minute in 0..<30 {
                        if let date = Calendar.current.date(byAdding: .minute, value: minute, to: currentDate) {
                            let entry = DynamicIntentWidgetEntry(
                                date: date,
                                selectButtonItem: configuration.selectButton,
                                widgetData: widgetData
                            )
                            entries.append(entry)
                        }
                    }

                    // 设置为每分钟更新一次
                    let timeline = Timeline(entries: entries, policy: .atEnd)
                    completion(timeline)
                }
            } else {
                // 没有配置数据，使用默认更新策略
                let expireDate = Calendar.current.date(byAdding: .minute, value: 5, to: Date()) ?? Date()
                let timeline = Timeline(entries: [entry], policy: .after(expireDate))
                completion(timeline)
            }
        } else if configuration.selectButton?.buttonType == .pomodoro {
            // 番茄时钟小组件需要特殊处理
            if let pomodoroData = AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) {
                // 根据番茄时钟的状态决定更新频率
                if pomodoroData.currentState == .working || pomodoroData.currentState == .shortBreak || pomodoroData.currentState == .longBreak {
                    // 活动状态 - 每秒更新
                    var entries: [DynamicIntentWidgetEntry] = []
                    let currentDate = Date()
                    let calendar = Calendar.current

                    // 如果有开始时间，计算剩余时间
                    var remainingSeconds = pomodoroData.remainingSeconds
                    if let startTime = pomodoroData.startTime {
                        let elapsedSeconds = Int(currentDate.timeIntervalSince(startTime))
                        remainingSeconds = max(0, pomodoroData.remainingSeconds - elapsedSeconds)
                    }

                    // 创建接下来的条目，每秒一个，最多创建剩余时间的条目
                    for second in 0..<min(remainingSeconds, 60) {
                        if let date = calendar.date(byAdding: .second, value: second, to: currentDate) {
                            let entry = DynamicIntentWidgetEntry(
                                date: date,
                                selectButtonItem: configuration.selectButton,
                                widgetData: widgetData
                            )
                            entries.append(entry)
                        }
                    }

                    // 使用.atEnd策略，在最后一个条目显示完后请求新的时间线
                    let timeline = Timeline(entries: entries, policy: .atEnd)
                    completion(timeline)
                } else {
                    // 非活动状态 - 每5分钟更新一次
                    let expireDate = Calendar.current.date(byAdding: .minute, value: 5, to: Date()) ?? Date()
                    let timeline = Timeline(entries: [entry], policy: .after(expireDate))
                    completion(timeline)
                }
            } else {
                // 没有配置数据，使用默认更新策略
                let expireDate = Calendar.current.date(byAdding: .minute, value: 5, to: Date()) ?? Date()
                let timeline = Timeline(entries: [entry], policy: .after(expireDate))
                completion(timeline)
            }
        } else {
            // 其他小组件每小时更新一次
            let expireDate = Calendar.current.date(byAdding: .hour, value: 1, to: Date()) ?? Date()
            let timeline = Timeline(entries: [entry], policy: .after(expireDate))
            completion(timeline)
        }
    }
}

struct DynamicIntentWidgetEntry: TimelineEntry {
    let date: Date
    var selectButtonItem: CustomButtonItem?

//    let configuration: DynamicConfigIntent?
    let widgetData: WidgetData
}

// struct DynamicIntentWidgetEntryView: View {
//    var entry: DynamicIntentWidgetProvider.Entry
//
//    var body: some View {
//        AlipayWidgetMeidumView(mediumItem: AliPayWidgetMediumItem(with: entry.groupBtns))
//    }
// }
//
// struct DynamicQQIntentWidgetEntryView: View {
//    var entry: DynamicIntentWidgetProvider.Entry
//
//    var body: some View {
//        QQSyncWidgetView(dateShareItem: QQSyncWidgetDateShareItem(), quoteShareItem: QQSyncWidgetQuoteShareItem.init())
//    }
// }

// struct DynamicQQIntentWidget: Widget {
//
//    let kind: String = "DynamicIntentWidgetQQ"
//
//    var title: String = "!!DynamicIntentWidget"
//    var desc: String = "QQ快捷功能直达，且新增了自定义场景展示"
//
//    var body: some WidgetConfiguration {
//        IntentConfiguration(kind: kind,
//                            intent: DynamicConfigurationIntent.self,
//                            provider: DynamicIntentWidgetProvider()) { entry in
//            DynamicQQIntentWidgetEntryView(entry: entry)
//        }
//        .configurationDisplayName(title)
//        .description(desc)
//        .contentMarginsDisabled()
//    }
// }
//
//
//
// struct DynamicIntentWidget: Widget {
//
//    let kind: String = "DynamicIntentWidget"
//
//    var title: String = "DynamicIntentWidget"
//    var desc: String = "快捷功能直达，且新增了自定义场景展示"
//
//    var body: some WidgetConfiguration {
//        IntentConfiguration(kind: kind,
//                            intent: DynamicConfigurationIntent.self,
//                            provider: DynamicIntentWidgetProvider()) { entry in
//            DynamicIntentWidgetEntryView(entry: entry)
//        }
//        .configurationDisplayName(title)
//        .description(desc)
//        .supportedFamilies([.systemMedium])
//        .contentMarginsDisabled()
//    }
// }
//
