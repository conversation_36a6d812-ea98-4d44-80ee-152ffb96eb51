//
//  JZJJWidgetExtensionLiveActivity.swift
//  JZJJWidgetExtension
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/8.
//

import ActivityKit
import WidgetKit
import SwiftUI

// iOS 16.1+ 实时活动功能
@available(iOS 16.1, *)
struct JZJJWidgetExtensionAttributes: ActivityAttributes {
    public struct ContentState: Codable, Hashable {
        // Dynamic stateful properties about your activity go here!
        var emoji: String
    }

    // Fixed non-changing properties about your activity go here!
    var name: String
}

@available(iOS 16.1, *)
struct JZJJWidgetExtensionLiveActivity: Widget {
    var body: some WidgetConfiguration {
        ActivityConfiguration(for: JZJJWidgetExtensionAttributes.self) { context in
            // Lock screen/banner UI goes here
            VStack {
                Text("Hello \(context.state.emoji)")
            }
            .activityBackgroundTint(Color.cyan)
            .activitySystemActionForegroundColor(Color.black)

        } dynamicIsland: { context in
            DynamicIsland {
                // Expanded UI goes here.  Compose the expanded UI through
                // various regions, like leading/trailing/center/bottom
                DynamicIslandExpandedRegion(.leading) {
                    Text("Leading")
                }
                DynamicIslandExpandedRegion(.trailing) {
                    Text("Trailing")
                }
                DynamicIslandExpandedRegion(.bottom) {
                    Text("Bottom \(context.state.emoji)")
                    // more content
                }
            } compactLeading: {
                Text("L")
            } compactTrailing: {
                Text("T \(context.state.emoji)")
            } minimal: {
                Text(context.state.emoji)
            }
            .widgetURL(URL(string: "http://www.apple.com"))
            .keylineTint(Color.red)
        }
    }
}

@available(iOS 16.1, *)
extension JZJJWidgetExtensionAttributes {
    fileprivate static var preview: JZJJWidgetExtensionAttributes {
        JZJJWidgetExtensionAttributes(name: "World")
    }
}

@available(iOS 16.1, *)
extension JZJJWidgetExtensionAttributes.ContentState {
    fileprivate static var smiley: JZJJWidgetExtensionAttributes.ContentState {
        JZJJWidgetExtensionAttributes.ContentState(emoji: "😀")
     }

     fileprivate static var starEyes: JZJJWidgetExtensionAttributes.ContentState {
         JZJJWidgetExtensionAttributes.ContentState(emoji: "🤩")
     }
}

//#if DEBUG
//@available(iOS 16.1, *)
//#Preview("Notification", as: .content, using: JZJJWidgetExtensionAttributes.preview) {
//   JZJJWidgetExtensionLiveActivity()
//} contentStates: {
//    JZJJWidgetExtensionAttributes.ContentState.smiley
//    JZJJWidgetExtensionAttributes.ContentState.starEyes
//}
//#endif
