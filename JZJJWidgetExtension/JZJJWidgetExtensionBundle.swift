//
//  JZJJWidgetExtensionBundle.swift
//  JZJJWidgetExtension
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/8.
//

import WidgetKit
import SwiftUI
import MyWidgetKit

@main
struct JZJJWidgetExtensionBundle: WidgetBundle {
    var body: some Widget {
        JZJJWidgetExtension()

        // iOS 18.0+ 控制小组件
        if #available(iOS 18.0, *) {
            JZJJWidgetExtensionControl()
        }

        // iOS 16.1+ 实时活动
        if #available(iOS 16.1, *) {
            JZJJWidgetExtensionLiveActivity()
        }
    }
}
