import MyWidgetKit
import SwiftUI
import WidgetKit
#if os(iOS)
import AppIntents
#endif

// 导入配置模型
import struct MyWidgetKit.DailyQuoteWidgetConfig
import enum MyWidgetKit.PriorityFilter
import struct MyWidgetKit.Task
import struct MyWidgetKit.TaskCategory
import struct MyWidgetKit.TodoWidgetConfig
import struct MyWidgetKit.TodoWidgetData

import struct MyWidgetKit.TimeWidgetData
import struct MyWidgetKit.WaterIntakeWidgetData
import struct MyWidgetKit.PasswordGeneratorConfig
import struct MyWidgetKit.PomodoroWidgetData
import struct MyWidgetKit.NoteWidgetData
import struct MyWidgetKit.NoteItem
import struct MyWidgetKit.MoonPhaseWidgetData
import struct MyWidgetKit.AppLauncherWidgetData
import struct MyWidgetKit.AppLauncherItem

import struct MyWidgetKit.DeviceInfoWidgetData

struct SimpleEntry: TimelineEntry {
    let date: Date
    let widgetData: WidgetData
}

struct JZJJWidgetExtensionEntryView: View {
    var entry: DynamicIntentWidgetEntry
    @Environment(\.widgetFamily) var family

    var body: some View {
        Group {
            widgetView
        }
        // 设置为小组件环境
        .inWidgetEnvironment(true)
    }

    // 将复杂逻辑移到计算属性中
    @ViewBuilder
    private var widgetView: some View {
        Group {
            switch entry.selectButtonItem?.buttonType {
            case .dailyQuote:
                dailyQuoteView
                    .inWidgetEnvironment(true)
            case .scan:
                scanView
            case .todoList:
                todoListView
            case .timeClock:
                timeClockView
            case .waterIntake:
                waterIntakeView
            case .passwordGenerator:
                passwordGeneratorView
            case .pomodoro:
                pomodoroView
            case .note:
                noteView
            case .moonPhase:
                moonPhaseView
            case .appLauncher:
                appLauncherView
            case .deviceInfo:
                deviceInfoView
            case .none, .unknown:
                PlaceholderWidgetView()
            }
        }
    }

    // 每日一言视图
    @ViewBuilder
    private var dailyQuoteView: some View {
        let widgetConfig = AppGroupDataManager.shared.read(DailyQuoteWidgetConfig.self, for: .dailyQuote, property: .config)
        let background = getDailyQuoteBackground(config: widgetConfig)
        let viewData = DailyQuoteWidgetViewData(
            quote: widgetConfig?.content ?? entry.widgetData.content,
            background: background,
            date: entry.date,
            config: nil
        )

        DailyQuoteWidgetView(
            data: viewData,
            fontName: widgetConfig?.fontName ?? "PingFangSC",
            fontSize: widgetConfig?.fontSize ?? 16,
            fontColor: widgetConfig?.fontColor ?? .black
        )
    }

    // 获取每日一言背景
    private func getDailyQuoteBackground(config: DailyQuoteWidgetConfig?) -> WidgetBackground {
        if let config = config {
            return config.background
        } else if let imageData = AppGroupDataManager.shared.readAuto(Data.self, for: .dailyQuote, property: .backgroundImage),
                  UIImage(data: imageData) != nil
        {
            return .imageData(imageData)
        } else {
            return .color(WidgetColor.fromColor(.white))
        }
    }

    // 二维码视图
    @ViewBuilder
    private var scanView: some View {
        if let config = AppGroupDataManager.shared.read(QRWidgetViewData.self, for: .qrImage, property: .config) {
            QRWidgetView(data: config)
        } else {
            PlaceholderWidgetView()
        }
    }

    // 任务清单视图
    @ViewBuilder
    private var todoListView: some View {
        if let widgetConfig = AppGroupDataManager.shared.read(TodoWidgetConfig.self, for: .todoList, property: .config) {
            let (category, tasks) = getTodoListData(config: widgetConfig)
            let widgetData = TodoWidgetData(
                category: category,
                showCompleted: widgetConfig.showCompleted,
                maxTaskCount: widgetConfig.maxTaskCount,
                priorityFilter: widgetConfig.priorityFilter,
                background: widgetConfig.background,
                fontName: widgetConfig.fontName,
                fontSize: widgetConfig.fontSize,
                fontColor: widgetConfig.fontColor,
                tasks: tasks
            )

            TodoWidgetView(data: widgetData, family: family)
        } else {
            let defaultCategory = TaskCategory(name: "示例分类", color: .blue, icon: "list.bullet")
            let sampleTasks = Task.createSampleTasks(for: defaultCategory)
            let widgetData = TodoWidgetData(
                category: defaultCategory,
                tasks: sampleTasks
            )

            TodoWidgetView(data: widgetData, family: family)
        }
    }



    // 时间小组件视图
    @ViewBuilder
    private var timeClockView: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(TimeWidgetData.self, for: .timeWidget, property: .config) {
                TimeWidgetView(data: config, date: entry.date)
            } else {
                // 创建默认配置并显示
                let defaultConfig = createDefaultTimeWidgetConfig()
                TimeWidgetView(data: defaultConfig, date: entry.date)
            }
        }
    }

    // 水分摄入追踪器视图
    @ViewBuilder
    private var waterIntakeView: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(WaterIntakeWidgetData.self, for: .waterIntake, property: .config) {
                WaterIntakeWidgetView(data: config, family: family)
            } else {
                // 创建默认配置并显示
                let defaultConfig = createDefaultWaterIntakeConfig()
                WaterIntakeWidgetView(data: defaultConfig, family: family)
            }
        }
    }

    // 随机密码生成器视图
    @ViewBuilder
    private var passwordGeneratorView: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(PasswordGeneratorConfig.self, for: .passwordGenerator, property: .config) {
                PasswordGeneratorWidgetView(config: config, family: family)
            } else {
                // 创建默认配置并显示
                let defaultConfig = PasswordGeneratorConfig.createDefault()
                PasswordGeneratorWidgetView(config: defaultConfig, family: family)
            }
        }
    }

    // 番茄时钟视图
    @ViewBuilder
    private var pomodoroView: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(PomodoroWidgetData.self, for: .pomodoro, property: .config) {
                PomodoroWidgetView(data: config, family: family, date: entry.date)
            } else {
                // 创建默认配置并显示
                let defaultConfig = createDefaultPomodoroConfig()
                PomodoroWidgetView(data: defaultConfig, family: family, date: entry.date)
            }
        }
    }

    // 快速笔记视图
    @ViewBuilder
    private var noteView: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(NoteWidgetData.self, for: .note, property: .config) {
                NoteWidgetView(data: config, family: family)
            } else {
                // 创建默认配置并显示
                let defaultConfig = createDefaultNoteConfig()
                NoteWidgetView(data: defaultConfig, family: family)
            }
        }
    }

    // 月相日历视图
    @ViewBuilder
    private var moonPhaseView: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(MoonPhaseWidgetData.self, for: .moonPhase, property: .config) {
                MoonPhaseWidgetView(data: config, date: entry.date)
            } else {
                // 创建默认配置并显示
                let defaultConfig = createDefaultMoonPhaseConfig()
                MoonPhaseWidgetView(data: defaultConfig, date: entry.date)
            }
        }
    }

    // 应用快捷启动器视图
    @ViewBuilder
    private var appLauncherView: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(AppLauncherWidgetData.self, for: .appLauncher, property: .config) {
                AppLauncherWidgetView(data: config, family: family)
            } else {
                // 创建默认配置并显示
                let defaultConfig = createDefaultAppLauncherConfig()
                AppLauncherWidgetView(data: defaultConfig, family: family)
            }
        }
    }

    // 设备信息视图
    @ViewBuilder
    private var deviceInfoView: some View {
        Group {
            if let config = AppGroupDataManager.shared.read(DeviceInfoWidgetData.self, for: .deviceInfo, property: .config) {
                DeviceInfoWidgetView(data: config, family: family)
            } else {
                // 创建默认配置并显示
                let defaultConfig = createDefaultDeviceInfoConfig()
                DeviceInfoWidgetView(data: defaultConfig, family: family)
            }
        }
    }





    // 创建默认时间小组件配置
    private func createDefaultTimeWidgetConfig() -> TimeWidgetData {
        // 创建默认配置
        let defaultConfig = TimeWidgetData(
            background: .color(WidgetColor(red: 0, green: 0, blue: 0, alpha: 1)),
            fontColor: WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
            fontName: "SF Pro",
            use12HourFormat: false,
            showDate: true,
            showSeconds: true
        )

        // 保存默认配置
        AppGroupDataManager.shared.save(defaultConfig, for: .timeWidget, property: .config)

        return defaultConfig
    }

    // 创建默认水分摄入追踪器配置
    private func createDefaultWaterIntakeConfig() -> WaterIntakeWidgetData {
        // 创建默认配置
        let defaultConfig = WaterIntakeWidgetData(
            currentIntake: 800,
            targetIntake: 2000,
            quickAddOptions: [100, 200, 300, 500],
            background: .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
            fontName: "SF Pro",
            fontSize: 14,
            fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
            accentColor: WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
            useHealthKit: false,
            lastUpdated: Date()
        )

        // 保存默认配置
        AppGroupDataManager.shared.save(defaultConfig, for: .waterIntake, property: .config)

        return defaultConfig
    }

    // 创建默认番茄时钟配置
    private func createDefaultPomodoroConfig() -> PomodoroWidgetData {
        // 创建默认配置
        let defaultConfig = PomodoroWidgetData(
            background: .color(WidgetColor(red: 0, green: 0, blue: 0, alpha: 1)),
            fontColor: WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
            fontName: "SF Pro",
            workDuration: 25,
            shortBreakDuration: 5,
            longBreakDuration: 15,
            longBreakInterval: 4,
            currentState: .idle,
            remainingSeconds: 25 * 60,
            startTime: nil,
            completedPomodoros: 0,
            autoStartNextPhase: false,
            lastUpdated: Date()
        )

        // 保存默认配置
        AppGroupDataManager.shared.save(defaultConfig, for: .pomodoro, property: .config)

        return defaultConfig
    }

    // 创建默认笔记配置
    private func createDefaultNoteConfig() -> NoteWidgetData {
        // 创建默认配置
        let defaultConfig = NoteWidgetData(
            notes: NoteItem.createSampleNotes(),
            background: .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
            fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
            fontName: "SF Pro",
            fontSize: 14,
            displayMode: .all,
            maxNoteCount: 3,
            showCreationTime: true,
            showTypeIcon: true,
            lastUpdated: Date()
        )

        // 保存默认配置
        AppGroupDataManager.shared.save(defaultConfig, for: .note, property: .config)

        return defaultConfig
    }

    // 创建默认月相日历配置
    private func createDefaultMoonPhaseConfig() -> MoonPhaseWidgetData {
        // 创建默认配置
        let defaultConfig = MoonPhaseWidgetData(
            background: .color(WidgetColor(red: 0.05, green: 0.05, blue: 0.2, alpha: 1)),
            fontColor: WidgetColor(red: 1, green: 1, blue: 1, alpha: 1),
            fontName: "SF Pro",
            displayMode: .detailed,
            showLunarDate: true
        )

        // 保存默认配置
        AppGroupDataManager.shared.save(defaultConfig, for: .moonPhase, property: .config)

        return defaultConfig
    }

    // 创建默认应用快捷启动器配置
    private func createDefaultAppLauncherConfig() -> AppLauncherWidgetData {
        // 创建默认应用项目
        let defaultItems = [
            AppLauncherItem(name: "微信", iconName: "message.fill", urlScheme: "weixin://", color: .green, position: 0),
            AppLauncherItem(name: "支付宝", iconName: "creditcard.fill", urlScheme: "alipay://", color: .blue, position: 1),
            AppLauncherItem(name: "淘宝", iconName: "cart.fill", urlScheme: "taobao://", color: .orange, position: 2),
            AppLauncherItem(name: "抖音", iconName: "music.note", urlScheme: "snssdk1128://", color: .pink, position: 3),
            AppLauncherItem(name: "微博", iconName: "flame.fill", urlScheme: "sinaweibo://", color: .red, position: 4),
            AppLauncherItem(name: "QQ", iconName: "bubble.left.fill", urlScheme: "mqq://", color: .blue, position: 5),
            AppLauncherItem(name: "京东", iconName: "bag.fill", urlScheme: "openapp.jdmobile://", color: .red, position: 6),
            AppLauncherItem(name: "美团", iconName: "fork.knife", urlScheme: "meituanwaimai://", color: .yellow, position: 7)
        ]

        // 创建默认配置
        let defaultConfig = AppLauncherWidgetData(
            items: defaultItems,
            iconStyle: .rounded,
            layoutType: .grid,
            background: .color(WidgetColor(red: 1, green: 1, blue: 1, alpha: 1)),
            fontName: "SF Pro",
            fontSize: 12,
            fontColor: .black,
            showLabels: true,
            columns: 4,
            rows: 2,
            spacing: 10
        )

        // 保存默认配置
        AppGroupDataManager.shared.save(defaultConfig, for: .appLauncher, property: .config)

        return defaultConfig
    }



    // 创建默认设备信息配置
    private func createDefaultDeviceInfoConfig() -> DeviceInfoWidgetData {
        // 获取设备信息
        let deviceName = UIDevice.current.name

        // 获取存储信息
        let fileManager = FileManager.default
        var totalStorage: Int64 = 0
        var freeStorage: Int64 = 0
        var usedStorage: Int64 = 0
        var storagePercentage: Double = 0

        if let path = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first?.path,
           let attributes = try? fileManager.attributesOfFileSystem(forPath: path),
           let freeSize = attributes[.systemFreeSize] as? NSNumber,
           let totalSize = attributes[.systemSize] as? NSNumber
        {
            totalStorage = totalSize.int64Value
            freeStorage = freeSize.int64Value
            usedStorage = totalStorage - freeStorage
            storagePercentage = Double(usedStorage) / Double(totalStorage) * 100
        }

        // 创建默认配置
        let defaultConfig = DeviceInfoWidgetData(
            deviceName: UIDevice.current.name,
            totalStorage: totalStorage,
            usedStorage: usedStorage,
            freeStorage: freeStorage,
            storagePercentage: storagePercentage,
            background: .color(WidgetColor(red: 0.95, green: 0.95, blue: 0.97, alpha: 1)),
            fontName: "SF Pro",
            fontSize: 14,
            fontColor: WidgetColor(red: 0, green: 0, blue: 0, alpha: 1),
            accentColor: WidgetColor(red: 0, green: 0.5, blue: 1, alpha: 1),
            showDeviceName: true,
            showOSInfo: true,
            showBatteryInfo: true,
            showStorageInfo: true,
            progressStyle: .bar,
            textAlignment: .leading,
            lastUpdated: Date()
        )

        // 保存默认配置
        AppGroupDataManager.shared.save(defaultConfig, for: .deviceInfo, property: .config)

        return defaultConfig
    }

    // 获取任务清单数据
    private func getTodoListData(config: TodoWidgetConfig) -> (TaskCategory?, [Task]?) {
        var category: TaskCategory? = nil
        var tasks: [Task]? = nil

        // 创建一个修改后的配置，强制使用"all"优先级筛选
        var modifiedConfig = config
        // 如果优先级筛选不是"all"，则修改为"all"
        if config.priorityFilter != .all {
            print("检测到优先级筛选不是'all'，自动修改为'all'以显示所有优先级任务")
            modifiedConfig.priorityFilter = .all
            // 保存修改后的配置
            AppGroupDataManager.shared.save(modifiedConfig, for: .todoList, property: .config)
        }

        // 调试信息：打印配置
        print("小组件配置: 分类ID=\(modifiedConfig.categoryId ?? "无"), 显示已完成=\(modifiedConfig.showCompleted), 最大任务数=\(modifiedConfig.maxTaskCount), 优先级筛选=\(modifiedConfig.priorityFilter)")

        // 读取所有任务进行调试
        if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            print("小组件读取到的任务总数: \(allTasks.count)")
            for task in allTasks {
                print("任务: \(task.title), 完成状态: \(task.isCompleted), 分类ID: \(task.categoryId), 优先级: \(task.priority.rawValue)")
            }
        } else {
            print("小组件未读取到任何任务")
        }

        if let categoryIdString = config.categoryId, let categoryId = UUID(uuidString: categoryIdString) {
            if let categories = AppGroupDataManager.shared.read([TaskCategory].self, for: .todoList, property: .categories) {
                category = categories.first { $0.id == categoryId }
                if category != nil {
                    print("找到分类: \(category!.name)")
                } else {
                    print("未找到指定分类ID: \(categoryId)")
                }
            }

            if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
                if let categoryId = category?.id {
                    tasks = allTasks.filter { $0.categoryId == categoryId }
                    print("筛选后的任务数量(按分类): \(tasks?.count ?? 0)")
                } else {
                    tasks = allTasks
                    print("使用所有任务(分类未找到): \(tasks?.count ?? 0)")
                }
            }
        } else if let allTasks = AppGroupDataManager.shared.read([Task].self, for: .todoList, property: .tasks) {
            tasks = allTasks
            print("使用所有任务(未指定分类): \(tasks?.count ?? 0)")
        }

        return (category, tasks)
    }
}

struct JZJJWidgetExtension: Widget {
    let kind: String = "JZJJWidgetExtension"

    var body: some WidgetConfiguration {
        #if os(iOS)
        if #available(iOS 16.0, *) {
            return IntentConfiguration(
                kind: kind,
                intent: DynamicConfigIntent.self,
                provider: DynamicIntentWidgetProvider()
            ) { entry in
                JZJJWidgetExtensionEntryView(entry: entry)
            }
            .contentMarginsDisabled()
            .configurationDisplayName("小组件")
            .description("选择您要添加的组件尺寸到桌面")
            .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
            .containerBackgroundRemovable(false)
        } else {
            return IntentConfiguration(
                kind: kind,
                intent: DynamicConfigIntent.self,
                provider: DynamicIntentWidgetProvider()
            ) { entry in
                JZJJWidgetExtensionEntryView(entry: entry)
            }
            .contentMarginsDisabled()
            .configurationDisplayName("小组件")
            .description("选择您要添加的组件尺寸到桌面")
            .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
        }
        #else
        return IntentConfiguration(
            kind: kind,
            intent: DynamicConfigIntent.self,
            provider: DynamicIntentWidgetProvider()
        ) { entry in
            JZJJWidgetExtensionEntryView(entry: entry)
        }
        .contentMarginsDisabled()
        .configurationDisplayName("小组件")
        .description("选择您要添加的组件尺寸到桌面")
        .supportedFamilies([.systemSmall, .systemMedium, .systemLarge])
        #endif
    }
}
