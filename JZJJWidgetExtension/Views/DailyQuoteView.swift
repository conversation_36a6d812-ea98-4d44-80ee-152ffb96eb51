import SwiftUI
import MyWidgetKit

struct DailyQuoteView: View {
    let quote: String
    let style: DailyQuoteData.QuoteStyle

    init(quote: String, style: DailyQuoteData.QuoteStyle) {
        self.quote = quote
        self.style = style
    }

    var body: some View {
        ZStack {
            Group {
                if let imageData = AppGroupDataManager.shared.readAuto(Data.self, for: .dailyQuote, property: .backgroundImage),
                   let uiImage = UIImage(data: imageData) {
                    Image(uiImage: uiImage)
                        .resizable()
                        .aspectRatio(contentMode: .fill)
                } else if let color = style.background.color {
                    color
                }
            }
            .ignoresSafeArea()

            VStack(alignment: .leading, spacing: 8) {
                Text(quote)
                    .font(.system(size: style.fontSize, design: .serif))
                    .foregroundColor(style.textSwiftUIColor)
                    .lineLimit(3)
                    .minimumScaleFactor(0.8)

                Spacer()

                Text(Date().formatted(.dateTime.month().day()))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .padding()
        }
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(style.showBorder ? style.borderSwiftUIColor : .clear, lineWidth: 1)
        )
    }
}
