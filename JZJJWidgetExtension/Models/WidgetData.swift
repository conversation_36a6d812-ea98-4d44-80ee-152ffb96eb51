import SwiftUI

struct ColorWrapper: Codable {
    let red: CGFloat
    let green: CGFloat
    let blue: CGFloat
    let alpha: CGFloat

    init(color: Color) {
        let uiColor = UIColor(color)
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        uiColor.getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        self.red = red
        self.green = green
        self.blue = blue
        self.alpha = alpha
    }

    var color: Color {
        Color(.sRGB, red: red, green: green, blue: blue, opacity: alpha)
    }
}

struct DailyQuoteData: Codable {
    var content: String
    var style: QuoteStyle
    var lastUpdated: Date

    struct QuoteStyle: Codable {
        enum Background: Codable {
            case color(ColorWrapper)
            case image(Data)

            var isImage: Bool {
                switch self {
                case .image: return true
                case .color: return false
                }
            }

            var color: Color? {
                switch self {
                case let .color(wrapper): return wrapper.color
                case .image: return nil
                }
            }

            var imageData: Data? {
                switch self {
                case let .image(data): return data
                case .color: return nil
                }
            }
        }

        private(set) var background: Background
        private(set) var textColor: ColorWrapper
        var fontSize: Double
        var showBorder: Bool
        private(set) var borderColor: ColorWrapper

        var textSwiftUIColor: Color {
            get { textColor.color }
            set { textColor = ColorWrapper(color: newValue) }
        }

        var borderSwiftUIColor: Color {
            get { borderColor.color }
            set { borderColor = ColorWrapper(color: newValue) }
        }

        init(background: Background = .color(ColorWrapper(color: .white)),
             textColor: Color = .black,
             fontSize: Double = 16,
             showBorder: Bool = false,
             borderColor: Color = .gray)
        {
            self.background = background
            self.textColor = ColorWrapper(color: textColor)
            self.fontSize = fontSize
            self.showBorder = showBorder
            self.borderColor = ColorWrapper(color: borderColor)
        }

        static let `default` = QuoteStyle()
    }

    static func createDefault() -> DailyQuoteData {
        DailyQuoteData(
            content: "",
            style: .default,
            lastUpdated: Date()
        )
    }
}

public struct WidgetData: Codable {
    public enum WidgetType: String, Codable {
        case dailyQuote
        case todoList
        case qrImage
        case timeClock
        case waterIntake
        case passwordGenerator
        case pomodoro
        case note
        case moonPhase
        case appLauncher
        case deviceInfo
    }

    let type: WidgetType
    var dailyQuote: DailyQuoteData?
    let content: String
    private(set) var backgroundColor: ColorWrapper
    private(set) var textColor: ColorWrapper
    var fontSize: Double
    var showBorder: Bool
    private(set) var borderColor: ColorWrapper
    let lastUpdated: Date

    var backgroundSwiftUIColor: Color {
        get { backgroundColor.color }
        set { backgroundColor = ColorWrapper(color: newValue) }
    }

    var textSwiftUIColor: Color {
        get { textColor.color }
        set { textColor = ColorWrapper(color: newValue) }
    }

    var borderSwiftUIColor: Color {
        get { borderColor.color }
        set { borderColor = ColorWrapper(color: newValue) }
    }

    init(type: WidgetType, content: String,
         backgroundColor: Color = .white,
         textColor: Color = .black,
         fontSize: Double = 16,
         showBorder: Bool = false,
         borderColor: Color = .gray,
         lastUpdated: Date = Date())
    {
        self.type = type
        self.content = content
        self.backgroundColor = ColorWrapper(color: backgroundColor)
        self.textColor = ColorWrapper(color: textColor)
        self.fontSize = fontSize
        self.showBorder = showBorder
        self.borderColor = ColorWrapper(color: borderColor)
        self.lastUpdated = lastUpdated
    }

    init(type: WidgetType) {
        self.type = type
        content = ""
        backgroundColor = ColorWrapper(color: .white)
        textColor = ColorWrapper(color: .black)
        fontSize = 16
        showBorder = false
        borderColor = ColorWrapper(color: .gray)
        lastUpdated = Date()

        switch type {
        case .dailyQuote:
            dailyQuote = DailyQuoteData.createDefault()
        case .todoList, .qrImage, .timeClock, .waterIntake, .passwordGenerator, .pomodoro, .note, .moonPhase, .appLauncher, .deviceInfo:
            dailyQuote = nil
        }
    }

    static let placeholder = WidgetData(
        type: .dailyQuote,
        content: "每日一句占位内容",
        backgroundColor: .white,
        textColor: .black,
        fontSize: 16,
        showBorder: false,
        borderColor: .gray
    )
}


