// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 63;
	objects = {

/* Begin PBXBuildFile section */
		10ECCA64BF9FAB3ED8FF18E8 /* Pods_JZJJWidget.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 5446C61596A92E126E02CBF1 /* Pods_JZJJWidget.framework */; };
		39077BF52DD5D0CE00C44DFE /* TodoWidgetConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39077BF42DD5D0CE00C44DFE /* TodoWidgetConfigView.swift */; };
		390BDE1B2DD1CBD600CDC312 /* QRWidgetConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 390BDE1A2DD1CBD600CDC312 /* QRWidgetConfigView.swift */; };
		3943BB552DD5E3FF0067560C /* TaskListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3943BB542DD5E3FF0067560C /* TaskListView.swift */; };
		3943BB562DD5E3FF0067560C /* EditTaskView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3943BB532DD5E3FF0067560C /* EditTaskView.swift */; };
		3943BB6C2DD6D2300067560C /* CalendarPickerView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3943BB6B2DD6D2300067560C /* CalendarPickerView.swift */; };
		3943BB782DD6E0460067560C /* CategoryButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3943BB752DD6E0460067560C /* CategoryButton.swift */; };
		3943BB792DD6E0460067560C /* ConfigSectionContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3943BB762DD6E0460067560C /* ConfigSectionContainer.swift */; };
		3943BB7A2DD6E0460067560C /* EnhancedWidgetCard.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3943BB772DD6E0460067560C /* EnhancedWidgetCard.swift */; };
		3943BB862DD6ED300067560C /* LearningProgressWidgetView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3943BB822DD6ED300067560C /* LearningProgressWidgetView.swift */; };
		3943BB8D2DD6ED300067560C /* LearningProgressConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3943BB812DD6ED300067560C /* LearningProgressConfigView.swift */; };
		394990A92DD87276004AE1C5 /* DropdownSizeSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394990A72DD87276004AE1C5 /* DropdownSizeSelector.swift */; };
		394990AA2DD87276004AE1C5 /* DropdownMenuManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394990A62DD87276004AE1C5 /* DropdownMenuManager.swift */; };
		394990AD2DD87284004AE1C5 /* DailyQuotePreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394990AB2DD87284004AE1C5 /* DailyQuotePreviewView.swift */; };
		394990B32DD894A0004AE1C5 /* SizeSelectorPopoverView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394990B12DD894A0004AE1C5 /* SizeSelectorPopoverView.swift */; };
		394990B42DD894A0004AE1C5 /* TopDropdownSizeSelector.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394990B22DD894A0004AE1C5 /* TopDropdownSizeSelector.swift */; };
		394990B52DD894A0004AE1C5 /* SizeSelectorButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394990B02DD894A0004AE1C5 /* SizeSelectorButton.swift */; };
		394990B62DD894A0004AE1C5 /* PopoverButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394990AE2DD894A0004AE1C5 /* PopoverButton.swift */; };
		394990BA2DDB0463004AE1C5 /* TimeWidgetConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394990B82DDB0463004AE1C5 /* TimeWidgetConfigView.swift */; };
		394991042DDB2401004AE1C5 /* WaterIntakeConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991022DDB2401004AE1C5 /* WaterIntakeConfigView.swift */; };
		394991312DDB380E004AE1C5 /* PasswordGeneratorConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3949912F2DDB380E004AE1C5 /* PasswordGeneratorConfigView.swift */; };
		394991342DDB5E63004AE1C5 /* PomodoroWidgetConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991322DDB5E63004AE1C5 /* PomodoroWidgetConfigView.swift */; };
		394991372DDB6A47004AE1C5 /* NoteWidgetConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991352DDB6A47004AE1C5 /* NoteWidgetConfigView.swift */; };
		394991452DDC120C004AE1C5 /* MoonPhaseConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991442DDC120C004AE1C5 /* MoonPhaseConfigView.swift */; };
		394991592DDC5B45004AE1C5 /* AppLauncherConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991572DDC5B45004AE1C5 /* AppLauncherConfigView.swift */; };
		3949915D2DDC7C95004AE1C5 /* ItemPreviewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3949915A2DDC7C95004AE1C5 /* ItemPreviewController.swift */; };
		3949915E2DDC7C95004AE1C5 /* WidgetPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3949915B2DDC7C95004AE1C5 /* WidgetPreviewView.swift */; };
		394991602DDC872C004AE1C5 /* AppLauncherConfigViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3949915F2DDC872C004AE1C5 /* AppLauncherConfigViewController.swift */; };
		394991622DDD56D9004AE1C5 /* PopularAppsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991612DDD56D9004AE1C5 /* PopularAppsView.swift */; };
		394991662DDD6619004AE1C5 /* WidgetHistoryItem.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991632DDD6619004AE1C5 /* WidgetHistoryItem.swift */; };
		394991672DDD6619004AE1C5 /* WidgetHistoryManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991642DDD6619004AE1C5 /* WidgetHistoryManager.swift */; };
		394991692DDD6BB2004AE1C5 /* ThemeSelectorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394991682DDD6BB2004AE1C5 /* ThemeSelectorView.swift */; };
		394ACD092DCC9529007AA42D /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACCFF2DCC9529007AA42D /* AppDelegate.swift */; };
		394ACD0A2DCC9529007AA42D /* SceneDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD062DCC9529007AA42D /* SceneDelegate.swift */; };
		394ACD0C2DCC9529007AA42D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 394ACD002DCC9529007AA42D /* Assets.xcassets */; };
		394ACD0E2DCC9529007AA42D /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 394ACD032DCC9529007AA42D /* LaunchScreen.storyboard */; };
		394ACD152DCC9ACD007AA42D /* ForumView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD102DCC9ACD007AA42D /* ForumView.swift */; };
		394ACD182DCC9ACD007AA42D /* WallpaperView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD122DCC9ACD007AA42D /* WallpaperView.swift */; };
		394ACD1F2DCC9F52007AA42D /* WidgetKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 394ACD1E2DCC9F52007AA42D /* WidgetKit.framework */; };
		394ACD212DCC9F52007AA42D /* SwiftUI.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 394ACD202DCC9F52007AA42D /* SwiftUI.framework */; };
		394ACD322DCC9F54007AA42D /* JZJJWidgetExtensionExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 394ACD1D2DCC9F52007AA42D /* JZJJWidgetExtensionExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		394ACD402DCC9F5F007AA42D /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 394ACD392DCC9F5F007AA42D /* Assets.xcassets */; };
		394ACD432DCC9F5F007AA42D /* JZJJWidgetExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD3B2DCC9F5F007AA42D /* JZJJWidgetExtension.swift */; };
		394ACD442DCC9F5F007AA42D /* JZJJWidgetExtensionBundle.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD3C2DCC9F5F007AA42D /* JZJJWidgetExtensionBundle.swift */; };
		394ACD452DCC9F5F007AA42D /* JZJJWidgetExtensionControl.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD3D2DCC9F5F007AA42D /* JZJJWidgetExtensionControl.swift */; };
		394ACD462DCC9F5F007AA42D /* JZJJWidgetExtensionLiveActivity.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD3E2DCC9F5F007AA42D /* JZJJWidgetExtensionLiveActivity.swift */; };
		394ACD5E2DCCA6CB007AA42D /* WidgetDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD5A2DCCA6CB007AA42D /* WidgetDataManager.swift */; };
		394ACD5F2DCCA6CB007AA42D /* WidgetConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD5C2DCCA6CB007AA42D /* WidgetConfigView.swift */; };
		394ACD602DCCA6CB007AA42D /* WidgetDataManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD5A2DCCA6CB007AA42D /* WidgetDataManager.swift */; };
		394ACD612DCCA6CB007AA42D /* WidgetConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD5C2DCCA6CB007AA42D /* WidgetConfigView.swift */; };
		394ACD702DCCB567007AA42D /* TimelineProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD692DCCB567007AA42D /* TimelineProvider.swift */; };
		394ACD712DCCB567007AA42D /* DailyQuoteView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD6B2DCCB567007AA42D /* DailyQuoteView.swift */; };
		394ACD722DCCB567007AA42D /* WidgetData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD672DCCB567007AA42D /* WidgetData.swift */; };
		394ACD732DCCB767007AA42D /* WidgetData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 394ACD672DCCB567007AA42D /* WidgetData.swift */; };
		3966E07A2DE15416001234F4 /* ImageThemeCardViews.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E0782DE15416001234F4 /* ImageThemeCardViews.swift */; };
		3966E07B2DE15416001234F4 /* ImageThemeConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E0792DE15416001234F4 /* ImageThemeConfigView.swift */; };
		3966E0822DE15AC2001234F4 /* AdvancedThemeCardViews.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E07D2DE15AC2001234F4 /* AdvancedThemeCardViews.swift */; };
		3966E0832DE15AC2001234F4 /* AdvancedThemeCreatorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E07F2DE15AC2001234F4 /* AdvancedThemeCreatorView.swift */; };
		3966E0842DE15AC2001234F4 /* AdvancedKeyboardPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E07C2DE15AC2001234F4 /* AdvancedKeyboardPreviewView.swift */; };
		3966E0852DE15AC2001234F4 /* ColorPickerComponents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E0812DE15AC2001234F4 /* ColorPickerComponents.swift */; };
		3966E0862DE15AC2001234F4 /* BaseThemeSelectorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E0802DE15AC2001234F4 /* BaseThemeSelectorView.swift */; };
		3966E0872DE15AC2001234F4 /* AdvancedThemeConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E07E2DE15AC2001234F4 /* AdvancedThemeConfigView.swift */; };
		3966E08D2DE15AD8001234F4 /* KeyTypeConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E08C2DE15AD8001234F4 /* KeyTypeConfigView.swift */; };
		3966E08E2DE15AD8001234F4 /* IndividualKeyConfigEditorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E0892DE15AD8001234F4 /* IndividualKeyConfigEditorView.swift */; };
		3966E08F2DE15AD8001234F4 /* IndividualKeyConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E08A2DE15AD8001234F4 /* IndividualKeyConfigView.swift */; };
		3966E0902DE15AD8001234F4 /* ImportExportView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E0882DE15AD8001234F4 /* ImportExportView.swift */; };
		3966E0912DE15AD8001234F4 /* KeyTypeConfigEditorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E08B2DE15AD8001234F4 /* KeyTypeConfigEditorView.swift */; };
		3966E0932DE195FA001234F4 /* ThemePackSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 3966E0922DE195FA001234F4 /* ThemePackSelectionView.swift */; };
		3966E0EC2DE19C8A001234F4 /* BuiltInThemePacks in Resources */ = {isa = PBXBuildFile; fileRef = 3966E0EB2DE19C8A001234F4 /* BuiltInThemePacks */; };
		3983D1172DEE7F1300588C6C /* StaticConfigIntent.intentdefinition in Sources */ = {isa = PBXBuildFile; fileRef = 3983D1162DEE7F1300588C6C /* StaticConfigIntent.intentdefinition */; };
		3983D1182DEE7F1300588C6C /* StaticConfigIntent.intentdefinition in Sources */ = {isa = PBXBuildFile; fileRef = 3983D1162DEE7F1300588C6C /* StaticConfigIntent.intentdefinition */; };
		3983D1192DEE7F1300588C6C /* StaticConfigIntent.intentdefinition in Sources */ = {isa = PBXBuildFile; fileRef = 3983D1162DEE7F1300588C6C /* StaticConfigIntent.intentdefinition */; };
		39ABF74C2DD31DA700D37EAB /* DailyQuoteConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF74B2DD31DA700D37EAB /* DailyQuoteConfigView.swift */; };
		39ABF7692DD33E0600D37EAB /* Intents.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 39ABF7682DD33E0600D37EAB /* Intents.framework */; };
		39ABF7702DD33E0600D37EAB /* WidgetIntentExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 39ABF7672DD33E0600D37EAB /* WidgetIntentExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		39ABF7782DD33EA200D37EAB /* WidgetButtonType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF7772DD33EA200D37EAB /* WidgetButtonType.swift */; };
		39ABF7792DD33EA200D37EAB /* WidgetButtonType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF7772DD33EA200D37EAB /* WidgetButtonType.swift */; };
		39ABF77A2DD33EB100D37EAB /* WidgetButtonType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF7772DD33EA200D37EAB /* WidgetButtonType.swift */; };
		39ABF77C2DD3406F00D37EAB /* DynamicIntentWidgetProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF77B2DD3406F00D37EAB /* DynamicIntentWidgetProvider.swift */; };
		39ABF77D2DD3406F00D37EAB /* DynamicIntentWidgetProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF77B2DD3406F00D37EAB /* DynamicIntentWidgetProvider.swift */; };
		39ABF7832DD4909E00D37EAB /* EnhancedViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF7822DD4909E00D37EAB /* EnhancedViewController.swift */; };
		39ABF7872DD490A700D37EAB /* EnhancedWidgetsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF7862DD490A700D37EAB /* EnhancedWidgetsView.swift */; };
		39ABF7882DD490A700D37EAB /* EnhancedProfileView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF7842DD490A700D37EAB /* EnhancedProfileView.swift */; };
		39ABF7892DD490A700D37EAB /* EnhancedWallpaperView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF7852DD490A700D37EAB /* EnhancedWallpaperView.swift */; };
		39ABF78B2DD5759000D37EAB /* SearchViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39ABF78A2DD5759000D37EAB /* SearchViewController.swift */; };
		39B9676E2DCF8D7100847831 /* MyWidgetKit in Frameworks */ = {isa = PBXBuildFile; productRef = 39B9676D2DCF8D7100847831 /* MyWidgetKit */; };
		39DC8A792DE06959009529B2 /* KeyBoardExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 39DC8A722DE06959009529B2 /* KeyBoardExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		39DC8A882DE0751D009529B2 /* CustomKeyboardThemeEditorView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39DC8A842DE0751D009529B2 /* CustomKeyboardThemeEditorView.swift */; };
		39DC8A892DE0751D009529B2 /* KeyboardPreviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39DC8A852DE0751D009529B2 /* KeyboardPreviewView.swift */; };
		39DC8A8A2DE0751D009529B2 /* KeyboardThemeConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39DC8A862DE0751D009529B2 /* KeyboardThemeConfigView.swift */; };
		39E08A8B2DE5B25700FA3C9F /* KeyboardThemeTabView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39E08A8A2DE5B25700FA3C9F /* KeyboardThemeTabView.swift */; };
		39E22A6B2DDDC0F50071C72D /* DeviceInfoWidgetConfigView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39E22A692DDDC0F50071C72D /* DeviceInfoWidgetConfigView.swift */; };
		39E22A6E2DDDC2580071C72D /* FontSelectionView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39E22A6C2DDDC2580071C72D /* FontSelectionView.swift */; };
		39E22A6F2DDDC2580071C72D /* SuccessToast.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39E22A6D2DDDC2580071C72D /* SuccessToast.swift */; };
		39E22A7A2DDDD7CE0071C72D /* SharedComponents.swift in Sources */ = {isa = PBXBuildFile; fileRef = 39E22A782DDDD7CE0071C72D /* SharedComponents.swift */; };
		39EAC4EB2DCF7BCF00F0CEBE /* MyWidgetKit in Frameworks */ = {isa = PBXBuildFile; productRef = 39EAC4EA2DCF7BCF00F0CEBE /* MyWidgetKit */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		394ACD302DCC9F54007AA42D /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 394ACCDF2DCC9520007AA42D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 394ACD1C2DCC9F52007AA42D;
			remoteInfo = JZJJWidgetExtensionExtension;
		};
		39ABF76E2DD33E0600D37EAB /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 394ACCDF2DCC9520007AA42D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 39ABF7662DD33E0600D37EAB;
			remoteInfo = WidgetIntentExtension;
		};
		39DC8A772DE06959009529B2 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 394ACCDF2DCC9520007AA42D /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 39DC8A712DE06959009529B2;
			remoteInfo = KeyBoardExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		394ACD372DCC9F54007AA42D /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = **********;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				39ABF7702DD33E0600D37EAB /* WidgetIntentExtension.appex in Embed Foundation Extensions */,
				39DC8A792DE06959009529B2 /* KeyBoardExtension.appex in Embed Foundation Extensions */,
				394ACD322DCC9F54007AA42D /* JZJJWidgetExtensionExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		1BAAB0A1C77EE60A92C2D28C /* Pods-JZJJWidget.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JZJJWidget.debug.xcconfig"; path = "Target Support Files/Pods-JZJJWidget/Pods-JZJJWidget.debug.xcconfig"; sourceTree = "<group>"; };
		39077BF42DD5D0CE00C44DFE /* TodoWidgetConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TodoWidgetConfigView.swift; sourceTree = "<group>"; };
		390BDE1A2DD1CBD600CDC312 /* QRWidgetConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = QRWidgetConfigView.swift; sourceTree = "<group>"; };
		3943BB532DD5E3FF0067560C /* EditTaskView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EditTaskView.swift; sourceTree = "<group>"; };
		3943BB542DD5E3FF0067560C /* TaskListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TaskListView.swift; sourceTree = "<group>"; };
		3943BB6B2DD6D2300067560C /* CalendarPickerView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarPickerView.swift; sourceTree = "<group>"; };
		3943BB752DD6E0460067560C /* CategoryButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CategoryButton.swift; sourceTree = "<group>"; };
		3943BB762DD6E0460067560C /* ConfigSectionContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfigSectionContainer.swift; sourceTree = "<group>"; };
		3943BB772DD6E0460067560C /* EnhancedWidgetCard.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnhancedWidgetCard.swift; sourceTree = "<group>"; };
		3943BB812DD6ED300067560C /* LearningProgressConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LearningProgressConfigView.swift; sourceTree = "<group>"; };
		3943BB822DD6ED300067560C /* LearningProgressWidgetView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LearningProgressWidgetView.swift; sourceTree = "<group>"; };
		394990A62DD87276004AE1C5 /* DropdownMenuManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DropdownMenuManager.swift; sourceTree = "<group>"; };
		394990A72DD87276004AE1C5 /* DropdownSizeSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DropdownSizeSelector.swift; sourceTree = "<group>"; };
		394990AB2DD87284004AE1C5 /* DailyQuotePreviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DailyQuotePreviewView.swift; sourceTree = "<group>"; };
		394990AE2DD894A0004AE1C5 /* PopoverButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PopoverButton.swift; sourceTree = "<group>"; };
		394990B02DD894A0004AE1C5 /* SizeSelectorButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SizeSelectorButton.swift; sourceTree = "<group>"; };
		394990B12DD894A0004AE1C5 /* SizeSelectorPopoverView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SizeSelectorPopoverView.swift; sourceTree = "<group>"; };
		394990B22DD894A0004AE1C5 /* TopDropdownSizeSelector.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopDropdownSizeSelector.swift; sourceTree = "<group>"; };
		394990B82DDB0463004AE1C5 /* TimeWidgetConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimeWidgetConfigView.swift; sourceTree = "<group>"; };
		394991022DDB2401004AE1C5 /* WaterIntakeConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WaterIntakeConfigView.swift; sourceTree = "<group>"; };
		3949912F2DDB380E004AE1C5 /* PasswordGeneratorConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PasswordGeneratorConfigView.swift; sourceTree = "<group>"; };
		394991322DDB5E63004AE1C5 /* PomodoroWidgetConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PomodoroWidgetConfigView.swift; sourceTree = "<group>"; };
		394991352DDB6A47004AE1C5 /* NoteWidgetConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NoteWidgetConfigView.swift; sourceTree = "<group>"; };
		394991442DDC120C004AE1C5 /* MoonPhaseConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MoonPhaseConfigView.swift; sourceTree = "<group>"; };
		394991572DDC5B45004AE1C5 /* AppLauncherConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppLauncherConfigView.swift; sourceTree = "<group>"; };
		3949915A2DDC7C95004AE1C5 /* ItemPreviewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ItemPreviewController.swift; sourceTree = "<group>"; };
		3949915B2DDC7C95004AE1C5 /* WidgetPreviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetPreviewView.swift; sourceTree = "<group>"; };
		3949915F2DDC872C004AE1C5 /* AppLauncherConfigViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppLauncherConfigViewController.swift; sourceTree = "<group>"; };
		394991612DDD56D9004AE1C5 /* PopularAppsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PopularAppsView.swift; sourceTree = "<group>"; };
		394991632DDD6619004AE1C5 /* WidgetHistoryItem.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetHistoryItem.swift; sourceTree = "<group>"; };
		394991642DDD6619004AE1C5 /* WidgetHistoryManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetHistoryManager.swift; sourceTree = "<group>"; };
		394991682DDD6BB2004AE1C5 /* ThemeSelectorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThemeSelectorView.swift; sourceTree = "<group>"; };
		394ACCE72DCC9520007AA42D /* JZJJWidget.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = JZJJWidget.app; sourceTree = BUILT_PRODUCTS_DIR; };
		394ACCFF2DCC9529007AA42D /* AppDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		394ACD002DCC9529007AA42D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		394ACD012DCC9529007AA42D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		394ACD022DCC9529007AA42D /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		394ACD062DCC9529007AA42D /* SceneDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SceneDelegate.swift; sourceTree = "<group>"; };
		394ACD102DCC9ACD007AA42D /* ForumView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ForumView.swift; sourceTree = "<group>"; };
		394ACD122DCC9ACD007AA42D /* WallpaperView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WallpaperView.swift; sourceTree = "<group>"; };
		394ACD1D2DCC9F52007AA42D /* JZJJWidgetExtensionExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = JZJJWidgetExtensionExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		394ACD1E2DCC9F52007AA42D /* WidgetKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = WidgetKit.framework; path = System/Library/Frameworks/WidgetKit.framework; sourceTree = SDKROOT; };
		394ACD202DCC9F52007AA42D /* SwiftUI.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = SwiftUI.framework; path = System/Library/Frameworks/SwiftUI.framework; sourceTree = SDKROOT; };
		394ACD392DCC9F5F007AA42D /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		394ACD3A2DCC9F5F007AA42D /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		394ACD3B2DCC9F5F007AA42D /* JZJJWidgetExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JZJJWidgetExtension.swift; sourceTree = "<group>"; };
		394ACD3C2DCC9F5F007AA42D /* JZJJWidgetExtensionBundle.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JZJJWidgetExtensionBundle.swift; sourceTree = "<group>"; };
		394ACD3D2DCC9F5F007AA42D /* JZJJWidgetExtensionControl.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JZJJWidgetExtensionControl.swift; sourceTree = "<group>"; };
		394ACD3E2DCC9F5F007AA42D /* JZJJWidgetExtensionLiveActivity.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JZJJWidgetExtensionLiveActivity.swift; sourceTree = "<group>"; };
		394ACD582DCCA4D4007AA42D /* JZJJWidget.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = JZJJWidget.entitlements; sourceTree = "<group>"; };
		394ACD592DCCA53B007AA42D /* JZJJWidgetExtensionExtension.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = JZJJWidgetExtensionExtension.entitlements; sourceTree = "<group>"; };
		394ACD5A2DCCA6CB007AA42D /* WidgetDataManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetDataManager.swift; sourceTree = "<group>"; };
		394ACD5C2DCCA6CB007AA42D /* WidgetConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetConfigView.swift; sourceTree = "<group>"; };
		394ACD672DCCB567007AA42D /* WidgetData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetData.swift; sourceTree = "<group>"; };
		394ACD692DCCB567007AA42D /* TimelineProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimelineProvider.swift; sourceTree = "<group>"; };
		394ACD6B2DCCB567007AA42D /* DailyQuoteView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DailyQuoteView.swift; sourceTree = "<group>"; };
		3966E0782DE15416001234F4 /* ImageThemeCardViews.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageThemeCardViews.swift; sourceTree = "<group>"; };
		3966E0792DE15416001234F4 /* ImageThemeConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImageThemeConfigView.swift; sourceTree = "<group>"; };
		3966E07C2DE15AC2001234F4 /* AdvancedKeyboardPreviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvancedKeyboardPreviewView.swift; sourceTree = "<group>"; };
		3966E07D2DE15AC2001234F4 /* AdvancedThemeCardViews.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvancedThemeCardViews.swift; sourceTree = "<group>"; };
		3966E07E2DE15AC2001234F4 /* AdvancedThemeConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvancedThemeConfigView.swift; sourceTree = "<group>"; };
		3966E07F2DE15AC2001234F4 /* AdvancedThemeCreatorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AdvancedThemeCreatorView.swift; sourceTree = "<group>"; };
		3966E0802DE15AC2001234F4 /* BaseThemeSelectorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BaseThemeSelectorView.swift; sourceTree = "<group>"; };
		3966E0812DE15AC2001234F4 /* ColorPickerComponents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColorPickerComponents.swift; sourceTree = "<group>"; };
		3966E0882DE15AD8001234F4 /* ImportExportView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImportExportView.swift; sourceTree = "<group>"; };
		3966E0892DE15AD8001234F4 /* IndividualKeyConfigEditorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndividualKeyConfigEditorView.swift; sourceTree = "<group>"; };
		3966E08A2DE15AD8001234F4 /* IndividualKeyConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IndividualKeyConfigView.swift; sourceTree = "<group>"; };
		3966E08B2DE15AD8001234F4 /* KeyTypeConfigEditorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeyTypeConfigEditorView.swift; sourceTree = "<group>"; };
		3966E08C2DE15AD8001234F4 /* KeyTypeConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeyTypeConfigView.swift; sourceTree = "<group>"; };
		3966E0922DE195FA001234F4 /* ThemePackSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ThemePackSelectionView.swift; sourceTree = "<group>"; };
		3966E0EB2DE19C8A001234F4 /* BuiltInThemePacks */ = {isa = PBXFileReference; lastKnownFileType = folder; path = BuiltInThemePacks; sourceTree = "<group>"; };
		3983D0E42DED73FB00588C6C /* JZJJWidgetRelease.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = JZJJWidgetRelease.entitlements; sourceTree = "<group>"; };
		3983D1152DEE7F1300588C6C /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.intentdefinition; name = Base; path = Base.lproj/StaticConfigIntent.intentdefinition; sourceTree = "<group>"; };
		3983D11B2DEE7F1900588C6C /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/StaticConfigIntent.strings; sourceTree = "<group>"; };
		39ABF74B2DD31DA700D37EAB /* DailyQuoteConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DailyQuoteConfigView.swift; sourceTree = "<group>"; };
		39ABF7672DD33E0600D37EAB /* WidgetIntentExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = WidgetIntentExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		39ABF7682DD33E0600D37EAB /* Intents.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = Intents.framework; path = System/Library/Frameworks/Intents.framework; sourceTree = SDKROOT; };
		39ABF7772DD33EA200D37EAB /* WidgetButtonType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = WidgetButtonType.swift; sourceTree = "<group>"; };
		39ABF77B2DD3406F00D37EAB /* DynamicIntentWidgetProvider.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DynamicIntentWidgetProvider.swift; sourceTree = "<group>"; };
		39ABF7822DD4909E00D37EAB /* EnhancedViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnhancedViewController.swift; sourceTree = "<group>"; };
		39ABF7842DD490A700D37EAB /* EnhancedProfileView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnhancedProfileView.swift; sourceTree = "<group>"; };
		39ABF7852DD490A700D37EAB /* EnhancedWallpaperView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnhancedWallpaperView.swift; sourceTree = "<group>"; };
		39ABF7862DD490A700D37EAB /* EnhancedWidgetsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EnhancedWidgetsView.swift; sourceTree = "<group>"; };
		39ABF78A2DD5759000D37EAB /* SearchViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SearchViewController.swift; sourceTree = "<group>"; };
		39DC8A722DE06959009529B2 /* KeyBoardExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = KeyBoardExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		39DC8A842DE0751D009529B2 /* CustomKeyboardThemeEditorView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomKeyboardThemeEditorView.swift; sourceTree = "<group>"; };
		39DC8A852DE0751D009529B2 /* KeyboardPreviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeyboardPreviewView.swift; sourceTree = "<group>"; };
		39DC8A862DE0751D009529B2 /* KeyboardThemeConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeyboardThemeConfigView.swift; sourceTree = "<group>"; };
		39E08A8A2DE5B25700FA3C9F /* KeyboardThemeTabView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = KeyboardThemeTabView.swift; sourceTree = "<group>"; };
		39E22A692DDDC0F50071C72D /* DeviceInfoWidgetConfigView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DeviceInfoWidgetConfigView.swift; sourceTree = "<group>"; };
		39E22A6C2DDDC2580071C72D /* FontSelectionView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FontSelectionView.swift; sourceTree = "<group>"; };
		39E22A6D2DDDC2580071C72D /* SuccessToast.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SuccessToast.swift; sourceTree = "<group>"; };
		39E22A782DDDD7CE0071C72D /* SharedComponents.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SharedComponents.swift; sourceTree = "<group>"; };
		5446C61596A92E126E02CBF1 /* Pods_JZJJWidget.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_JZJJWidget.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6043DAD684AF05A1E1AD1290 /* Pods-JZJJWidget.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-JZJJWidget.release.xcconfig"; path = "Target Support Files/Pods-JZJJWidget/Pods-JZJJWidget.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		39ABF7712DD33E0600D37EAB /* Exceptions for "WidgetIntentExtension" folder in "WidgetIntentExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 39ABF7662DD33E0600D37EAB /* WidgetIntentExtension */;
		};
		39DC8A7A2DE06959009529B2 /* Exceptions for "KeyBoardExtension" folder in "KeyBoardExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 39DC8A712DE06959009529B2 /* KeyBoardExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		3966E0EE2DE1AD2F001234F4 /* ThemePack */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = ThemePack;
			sourceTree = "<group>";
		};
		39ABF76A2DD33E0600D37EAB /* WidgetIntentExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				39ABF7712DD33E0600D37EAB /* Exceptions for "WidgetIntentExtension" folder in "WidgetIntentExtension" target */,
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = WidgetIntentExtension;
			sourceTree = "<group>";
		};
		39DC8A732DE06959009529B2 /* KeyBoardExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				39DC8A7A2DE06959009529B2 /* Exceptions for "KeyBoardExtension" folder in "KeyBoardExtension" target */,
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = KeyBoardExtension;
			sourceTree = "<group>";
		};
		39E08AC32DE8612300FA3C9F /* Health */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
			);
			explicitFileTypes = {
			};
			explicitFolders = (
			);
			path = Health;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		394ACCE42DCC9520007AA42D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				10ECCA64BF9FAB3ED8FF18E8 /* Pods_JZJJWidget.framework in Frameworks */,
				39B9676E2DCF8D7100847831 /* MyWidgetKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		394ACD1A2DCC9F52007AA42D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				394ACD212DCC9F52007AA42D /* SwiftUI.framework in Frameworks */,
				39EAC4EB2DCF7BCF00F0CEBE /* MyWidgetKit in Frameworks */,
				394ACD1F2DCC9F52007AA42D /* WidgetKit.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39ABF7642DD33E0600D37EAB /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				39ABF7692DD33E0600D37EAB /* Intents.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39DC8A6F2DE06959009529B2 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		022074CBE0FB00F2D391085F /* Pods */ = {
			isa = PBXGroup;
			children = (
				1BAAB0A1C77EE60A92C2D28C /* Pods-JZJJWidget.debug.xcconfig */,
				6043DAD684AF05A1E1AD1290 /* Pods-JZJJWidget.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		3949909D2DD71DF3004AE1C5 /* Todolist */ = {
			isa = PBXGroup;
			children = (
				3943BB532DD5E3FF0067560C /* EditTaskView.swift */,
				3943BB542DD5E3FF0067560C /* TaskListView.swift */,
			);
			path = Todolist;
			sourceTree = "<group>";
		};
		394990A12DD71E2B004AE1C5 /* Learning */ = {
			isa = PBXGroup;
			children = (
				3943BB812DD6ED300067560C /* LearningProgressConfigView.swift */,
				3943BB822DD6ED300067560C /* LearningProgressWidgetView.swift */,
			);
			path = Learning;
			sourceTree = "<group>";
		};
		394990A82DD87276004AE1C5 /* Common */ = {
			isa = PBXGroup;
			children = (
				39E22A6C2DDDC2580071C72D /* FontSelectionView.swift */,
				39E22A6D2DDDC2580071C72D /* SuccessToast.swift */,
				394990AE2DD894A0004AE1C5 /* PopoverButton.swift */,
				394990B02DD894A0004AE1C5 /* SizeSelectorButton.swift */,
				394990B12DD894A0004AE1C5 /* SizeSelectorPopoverView.swift */,
				394990B22DD894A0004AE1C5 /* TopDropdownSizeSelector.swift */,
				394990A62DD87276004AE1C5 /* DropdownMenuManager.swift */,
				394990A72DD87276004AE1C5 /* DropdownSizeSelector.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		394990AC2DD87284004AE1C5 /* DailyQuote */ = {
			isa = PBXGroup;
			children = (
				39ABF74B2DD31DA700D37EAB /* DailyQuoteConfigView.swift */,
				394990AB2DD87284004AE1C5 /* DailyQuotePreviewView.swift */,
			);
			path = DailyQuote;
			sourceTree = "<group>";
		};
		394990B92DDB0463004AE1C5 /* Time */ = {
			isa = PBXGroup;
			children = (
				394990B82DDB0463004AE1C5 /* TimeWidgetConfigView.swift */,
			);
			path = Time;
			sourceTree = "<group>";
		};
		394991032DDB2401004AE1C5 /* WaterIntake */ = {
			isa = PBXGroup;
			children = (
				394991022DDB2401004AE1C5 /* WaterIntakeConfigView.swift */,
			);
			path = WaterIntake;
			sourceTree = "<group>";
		};
		394991302DDB380E004AE1C5 /* PasswordGenerator */ = {
			isa = PBXGroup;
			children = (
				3949912F2DDB380E004AE1C5 /* PasswordGeneratorConfigView.swift */,
			);
			path = PasswordGenerator;
			sourceTree = "<group>";
		};
		394991332DDB5E63004AE1C5 /* Pomodoro */ = {
			isa = PBXGroup;
			children = (
				394991322DDB5E63004AE1C5 /* PomodoroWidgetConfigView.swift */,
			);
			path = Pomodoro;
			sourceTree = "<group>";
		};
		394991362DDB6A47004AE1C5 /* Note */ = {
			isa = PBXGroup;
			children = (
				394991352DDB6A47004AE1C5 /* NoteWidgetConfigView.swift */,
			);
			path = Note;
			sourceTree = "<group>";
		};
		394991582DDC5B45004AE1C5 /* AppLauncher */ = {
			isa = PBXGroup;
			children = (
				394991612DDD56D9004AE1C5 /* PopularAppsView.swift */,
				3949915F2DDC872C004AE1C5 /* AppLauncherConfigViewController.swift */,
				394991572DDC5B45004AE1C5 /* AppLauncherConfigView.swift */,
			);
			path = AppLauncher;
			sourceTree = "<group>";
		};
		3949915C2DDC7C95004AE1C5 /* PreviewController */ = {
			isa = PBXGroup;
			children = (
				3949915A2DDC7C95004AE1C5 /* ItemPreviewController.swift */,
				3949915B2DDC7C95004AE1C5 /* WidgetPreviewView.swift */,
			);
			path = PreviewController;
			sourceTree = "<group>";
		};
		394991652DDD6619004AE1C5 /* Models */ = {
			isa = PBXGroup;
			children = (
				394991632DDD6619004AE1C5 /* WidgetHistoryItem.swift */,
				394991642DDD6619004AE1C5 /* WidgetHistoryManager.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		394ACCDE2DCC9520007AA42D = {
			isa = PBXGroup;
			children = (
				3966E0EB2DE19C8A001234F4 /* BuiltInThemePacks */,
				394ACD592DCCA53B007AA42D /* JZJJWidgetExtensionExtension.entitlements */,
				3983D1162DEE7F1300588C6C /* StaticConfigIntent.intentdefinition */,
				394ACD5B2DCCA6CB007AA42D /* Shared */,
				394ACD5D2DCCA6CB007AA42D /* Views */,
				394ACD082DCC9529007AA42D /* JZJJWidget */,
				394ACD3F2DCC9F5F007AA42D /* JZJJWidgetExtension */,
				39ABF76A2DD33E0600D37EAB /* WidgetIntentExtension */,
				39DC8A732DE06959009529B2 /* KeyBoardExtension */,
				394ACCE82DCC9520007AA42D /* Products */,
				022074CBE0FB00F2D391085F /* Pods */,
				EBF070EC4187E4E3DDB52E0B /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		394ACCE82DCC9520007AA42D /* Products */ = {
			isa = PBXGroup;
			children = (
				394ACCE72DCC9520007AA42D /* JZJJWidget.app */,
				394ACD1D2DCC9F52007AA42D /* JZJJWidgetExtensionExtension.appex */,
				39ABF7672DD33E0600D37EAB /* WidgetIntentExtension.appex */,
				39DC8A722DE06959009529B2 /* KeyBoardExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		394ACD082DCC9529007AA42D /* JZJJWidget */ = {
			isa = PBXGroup;
			children = (
				3983D0E42DED73FB00588C6C /* JZJJWidgetRelease.entitlements */,
				3949915C2DDC7C95004AE1C5 /* PreviewController */,
				394ACD582DCCA4D4007AA42D /* JZJJWidget.entitlements */,
				394991652DDD6619004AE1C5 /* Models */,
				394ACD142DCC9ACD007AA42D /* Views */,
				394ACCFF2DCC9529007AA42D /* AppDelegate.swift */,
				394ACD002DCC9529007AA42D /* Assets.xcassets */,
				394ACD012DCC9529007AA42D /* Info.plist */,
				394ACD032DCC9529007AA42D /* LaunchScreen.storyboard */,
				394ACD062DCC9529007AA42D /* SceneDelegate.swift */,
				39ABF7822DD4909E00D37EAB /* EnhancedViewController.swift */,
				39ABF78A2DD5759000D37EAB /* SearchViewController.swift */,
			);
			path = JZJJWidget;
			sourceTree = "<group>";
		};
		394ACD142DCC9ACD007AA42D /* Views */ = {
			isa = PBXGroup;
			children = (
				39E08AC32DE8612300FA3C9F /* Health */,
				3966E0EE2DE1AD2F001234F4 /* ThemePack */,
				39DC8A872DE0751D009529B2 /* Keyboard */,
				39E22A792DDDD7CE0071C72D /* Shared */,
				39E22A6A2DDDC0F50071C72D /* DeviceInfo */,
				394991682DDD6BB2004AE1C5 /* ThemeSelectorView.swift */,
				394991582DDC5B45004AE1C5 /* AppLauncher */,
				394991442DDC120C004AE1C5 /* MoonPhaseConfigView.swift */,
				394991362DDB6A47004AE1C5 /* Note */,
				394991332DDB5E63004AE1C5 /* Pomodoro */,
				394991302DDB380E004AE1C5 /* PasswordGenerator */,
				394991032DDB2401004AE1C5 /* WaterIntake */,
				394990B92DDB0463004AE1C5 /* Time */,
				394990AC2DD87284004AE1C5 /* DailyQuote */,
				394990A82DD87276004AE1C5 /* Common */,
				394990A12DD71E2B004AE1C5 /* Learning */,
				3949909D2DD71DF3004AE1C5 /* Todolist */,
				3943BB752DD6E0460067560C /* CategoryButton.swift */,
				3943BB762DD6E0460067560C /* ConfigSectionContainer.swift */,
				3943BB772DD6E0460067560C /* EnhancedWidgetCard.swift */,
				3943BB6B2DD6D2300067560C /* CalendarPickerView.swift */,
				39077BF42DD5D0CE00C44DFE /* TodoWidgetConfigView.swift */,
				39ABF7842DD490A700D37EAB /* EnhancedProfileView.swift */,
				39ABF7852DD490A700D37EAB /* EnhancedWallpaperView.swift */,
				39ABF7862DD490A700D37EAB /* EnhancedWidgetsView.swift */,
				394ACD102DCC9ACD007AA42D /* ForumView.swift */,
				394ACD122DCC9ACD007AA42D /* WallpaperView.swift */,
				390BDE1A2DD1CBD600CDC312 /* QRWidgetConfigView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		394ACD3F2DCC9F5F007AA42D /* JZJJWidgetExtension */ = {
			isa = PBXGroup;
			children = (
				394ACD6D2DCCB567007AA42D /* Views */,
				394ACD682DCCB567007AA42D /* Models */,
				394ACD692DCCB567007AA42D /* TimelineProvider.swift */,
				39ABF77B2DD3406F00D37EAB /* DynamicIntentWidgetProvider.swift */,
				39ABF7772DD33EA200D37EAB /* WidgetButtonType.swift */,
				394ACD392DCC9F5F007AA42D /* Assets.xcassets */,
				394ACD3A2DCC9F5F007AA42D /* Info.plist */,
				394ACD3B2DCC9F5F007AA42D /* JZJJWidgetExtension.swift */,
				394ACD3C2DCC9F5F007AA42D /* JZJJWidgetExtensionBundle.swift */,
				394ACD3D2DCC9F5F007AA42D /* JZJJWidgetExtensionControl.swift */,
				394ACD3E2DCC9F5F007AA42D /* JZJJWidgetExtensionLiveActivity.swift */,
			);
			path = JZJJWidgetExtension;
			sourceTree = "<group>";
		};
		394ACD5B2DCCA6CB007AA42D /* Shared */ = {
			isa = PBXGroup;
			children = (
				394ACD5A2DCCA6CB007AA42D /* WidgetDataManager.swift */,
			);
			path = Shared;
			sourceTree = "<group>";
		};
		394ACD5D2DCCA6CB007AA42D /* Views */ = {
			isa = PBXGroup;
			children = (
				394ACD5C2DCCA6CB007AA42D /* WidgetConfigView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		394ACD682DCCB567007AA42D /* Models */ = {
			isa = PBXGroup;
			children = (
				394ACD672DCCB567007AA42D /* WidgetData.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		394ACD6D2DCCB567007AA42D /* Views */ = {
			isa = PBXGroup;
			children = (
				394ACD6B2DCCB567007AA42D /* DailyQuoteView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		39DC8A872DE0751D009529B2 /* Keyboard */ = {
			isa = PBXGroup;
			children = (
				39E08A8A2DE5B25700FA3C9F /* KeyboardThemeTabView.swift */,
				3966E0922DE195FA001234F4 /* ThemePackSelectionView.swift */,
				3966E0882DE15AD8001234F4 /* ImportExportView.swift */,
				3966E0892DE15AD8001234F4 /* IndividualKeyConfigEditorView.swift */,
				3966E08A2DE15AD8001234F4 /* IndividualKeyConfigView.swift */,
				3966E08B2DE15AD8001234F4 /* KeyTypeConfigEditorView.swift */,
				3966E08C2DE15AD8001234F4 /* KeyTypeConfigView.swift */,
				3966E07C2DE15AC2001234F4 /* AdvancedKeyboardPreviewView.swift */,
				3966E07D2DE15AC2001234F4 /* AdvancedThemeCardViews.swift */,
				3966E07E2DE15AC2001234F4 /* AdvancedThemeConfigView.swift */,
				3966E07F2DE15AC2001234F4 /* AdvancedThemeCreatorView.swift */,
				3966E0802DE15AC2001234F4 /* BaseThemeSelectorView.swift */,
				3966E0812DE15AC2001234F4 /* ColorPickerComponents.swift */,
				39DC8A842DE0751D009529B2 /* CustomKeyboardThemeEditorView.swift */,
				39DC8A852DE0751D009529B2 /* KeyboardPreviewView.swift */,
				39DC8A862DE0751D009529B2 /* KeyboardThemeConfigView.swift */,
				3966E0782DE15416001234F4 /* ImageThemeCardViews.swift */,
				3966E0792DE15416001234F4 /* ImageThemeConfigView.swift */,
			);
			path = Keyboard;
			sourceTree = "<group>";
		};
		39E22A6A2DDDC0F50071C72D /* DeviceInfo */ = {
			isa = PBXGroup;
			children = (
				39E22A692DDDC0F50071C72D /* DeviceInfoWidgetConfigView.swift */,
			);
			path = DeviceInfo;
			sourceTree = "<group>";
		};
		39E22A792DDDD7CE0071C72D /* Shared */ = {
			isa = PBXGroup;
			children = (
				39E22A782DDDD7CE0071C72D /* SharedComponents.swift */,
			);
			path = Shared;
			sourceTree = "<group>";
		};
		EBF070EC4187E4E3DDB52E0B /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				5446C61596A92E126E02CBF1 /* Pods_JZJJWidget.framework */,
				394ACD1E2DCC9F52007AA42D /* WidgetKit.framework */,
				394ACD202DCC9F52007AA42D /* SwiftUI.framework */,
				39ABF7682DD33E0600D37EAB /* Intents.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		394ACCE62DCC9520007AA42D /* JZJJWidget */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 394ACCFA2DCC9522007AA42D /* Build configuration list for PBXNativeTarget "JZJJWidget" */;
			buildPhases = (
				688A2B0FC54D04AE0D127558 /* [CP] Check Pods Manifest.lock */,
				394ACCE32DCC9520007AA42D /* Sources */,
				394ACCE42DCC9520007AA42D /* Frameworks */,
				394ACCE52DCC9520007AA42D /* Resources */,
				394ACD372DCC9F54007AA42D /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				394ACD312DCC9F54007AA42D /* PBXTargetDependency */,
				39ABF76F2DD33E0600D37EAB /* PBXTargetDependency */,
				39DC8A782DE06959009529B2 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				3966E0EE2DE1AD2F001234F4 /* ThemePack */,
				39E08AC32DE8612300FA3C9F /* Health */,
			);
			name = JZJJWidget;
			productName = JZJJWidget;
			productReference = 394ACCE72DCC9520007AA42D /* JZJJWidget.app */;
			productType = "com.apple.product-type.application";
		};
		394ACD1C2DCC9F52007AA42D /* JZJJWidgetExtensionExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 394ACD342DCC9F54007AA42D /* Build configuration list for PBXNativeTarget "JZJJWidgetExtensionExtension" */;
			buildPhases = (
				394ACD192DCC9F52007AA42D /* Sources */,
				394ACD1A2DCC9F52007AA42D /* Frameworks */,
				394ACD1B2DCC9F52007AA42D /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = JZJJWidgetExtensionExtension;
			packageProductDependencies = (
				39EAC4EA2DCF7BCF00F0CEBE /* MyWidgetKit */,
			);
			productName = JZJJWidgetExtensionExtension;
			productReference = 394ACD1D2DCC9F52007AA42D /* JZJJWidgetExtensionExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		39ABF7662DD33E0600D37EAB /* WidgetIntentExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 39ABF7722DD33E0600D37EAB /* Build configuration list for PBXNativeTarget "WidgetIntentExtension" */;
			buildPhases = (
				39ABF7632DD33E0600D37EAB /* Sources */,
				39ABF7642DD33E0600D37EAB /* Frameworks */,
				39ABF7652DD33E0600D37EAB /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				39ABF76A2DD33E0600D37EAB /* WidgetIntentExtension */,
			);
			name = WidgetIntentExtension;
			productName = WidgetIntentExtension;
			productReference = 39ABF7672DD33E0600D37EAB /* WidgetIntentExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		39DC8A712DE06959009529B2 /* KeyBoardExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 39DC8A7B2DE06959009529B2 /* Build configuration list for PBXNativeTarget "KeyBoardExtension" */;
			buildPhases = (
				39DC8A6E2DE06959009529B2 /* Sources */,
				39DC8A6F2DE06959009529B2 /* Frameworks */,
				39DC8A702DE06959009529B2 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				39DC8A732DE06959009529B2 /* KeyBoardExtension */,
			);
			name = KeyBoardExtension;
			productName = KeyBoardExtension;
			productReference = 39DC8A722DE06959009529B2 /* KeyBoardExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		394ACCDF2DCC9520007AA42D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					394ACCE62DCC9520007AA42D = {
						CreatedOnToolsVersion = 16.3;
					};
					394ACD1C2DCC9F52007AA42D = {
						CreatedOnToolsVersion = 16.3;
					};
					39ABF7662DD33E0600D37EAB = {
						CreatedOnToolsVersion = 16.3;
					};
					39DC8A712DE06959009529B2 = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 394ACCE22DCC9520007AA42D /* Build configuration list for PBXProject "JZJJWidget" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 394ACCDE2DCC9520007AA42D;
			minimizedProjectReferenceProxies = 1;
			productRefGroup = 394ACCE82DCC9520007AA42D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				394ACCE62DCC9520007AA42D /* JZJJWidget */,
				394ACD1C2DCC9F52007AA42D /* JZJJWidgetExtensionExtension */,
				39ABF7662DD33E0600D37EAB /* WidgetIntentExtension */,
				39DC8A712DE06959009529B2 /* KeyBoardExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		394ACCE52DCC9520007AA42D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3966E0EC2DE19C8A001234F4 /* BuiltInThemePacks in Resources */,
				394ACD0C2DCC9529007AA42D /* Assets.xcassets in Resources */,
				394ACD0E2DCC9529007AA42D /* LaunchScreen.storyboard in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		394ACD1B2DCC9F52007AA42D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				394ACD402DCC9F5F007AA42D /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39ABF7652DD33E0600D37EAB /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39DC8A702DE06959009529B2 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		688A2B0FC54D04AE0D127558 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-JZJJWidget-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		394ACCE32DCC9520007AA42D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3949915D2DDC7C95004AE1C5 /* ItemPreviewController.swift in Sources */,
				3949915E2DDC7C95004AE1C5 /* WidgetPreviewView.swift in Sources */,
				394ACD092DCC9529007AA42D /* AppDelegate.swift in Sources */,
				39ABF7832DD4909E00D37EAB /* EnhancedViewController.swift in Sources */,
				39E22A7A2DDDD7CE0071C72D /* SharedComponents.swift in Sources */,
				394ACD152DCC9ACD007AA42D /* ForumView.swift in Sources */,
				3983D1172DEE7F1300588C6C /* StaticConfigIntent.intentdefinition in Sources */,
				394991042DDB2401004AE1C5 /* WaterIntakeConfigView.swift in Sources */,
				3966E07A2DE15416001234F4 /* ImageThemeCardViews.swift in Sources */,
				3966E07B2DE15416001234F4 /* ImageThemeConfigView.swift in Sources */,
				394ACD182DCC9ACD007AA42D /* WallpaperView.swift in Sources */,
				394991662DDD6619004AE1C5 /* WidgetHistoryItem.swift in Sources */,
				394991672DDD6619004AE1C5 /* WidgetHistoryManager.swift in Sources */,
				39ABF7872DD490A700D37EAB /* EnhancedWidgetsView.swift in Sources */,
				39077BF52DD5D0CE00C44DFE /* TodoWidgetConfigView.swift in Sources */,
				39ABF7882DD490A700D37EAB /* EnhancedProfileView.swift in Sources */,
				39ABF7892DD490A700D37EAB /* EnhancedWallpaperView.swift in Sources */,
				394990B32DD894A0004AE1C5 /* SizeSelectorPopoverView.swift in Sources */,
				3966E0822DE15AC2001234F4 /* AdvancedThemeCardViews.swift in Sources */,
				3966E0832DE15AC2001234F4 /* AdvancedThemeCreatorView.swift in Sources */,
				3966E0842DE15AC2001234F4 /* AdvancedKeyboardPreviewView.swift in Sources */,
				3966E0852DE15AC2001234F4 /* ColorPickerComponents.swift in Sources */,
				3966E0862DE15AC2001234F4 /* BaseThemeSelectorView.swift in Sources */,
				3966E0872DE15AC2001234F4 /* AdvancedThemeConfigView.swift in Sources */,
				394991602DDC872C004AE1C5 /* AppLauncherConfigViewController.swift in Sources */,
				394990B42DD894A0004AE1C5 /* TopDropdownSizeSelector.swift in Sources */,
				394990B52DD894A0004AE1C5 /* SizeSelectorButton.swift in Sources */,
				394990B62DD894A0004AE1C5 /* PopoverButton.swift in Sources */,
				3943BB552DD5E3FF0067560C /* TaskListView.swift in Sources */,
				394991312DDB380E004AE1C5 /* PasswordGeneratorConfigView.swift in Sources */,
				3943BB782DD6E0460067560C /* CategoryButton.swift in Sources */,
				3943BB792DD6E0460067560C /* ConfigSectionContainer.swift in Sources */,
				3943BB7A2DD6E0460067560C /* EnhancedWidgetCard.swift in Sources */,
				3943BB562DD5E3FF0067560C /* EditTaskView.swift in Sources */,
				394990AD2DD87284004AE1C5 /* DailyQuotePreviewView.swift in Sources */,
				394991592DDC5B45004AE1C5 /* AppLauncherConfigView.swift in Sources */,
				39ABF77D2DD3406F00D37EAB /* DynamicIntentWidgetProvider.swift in Sources */,
				394991692DDD6BB2004AE1C5 /* ThemeSelectorView.swift in Sources */,
				394ACD0A2DCC9529007AA42D /* SceneDelegate.swift in Sources */,
				394991342DDB5E63004AE1C5 /* PomodoroWidgetConfigView.swift in Sources */,
				39DC8A882DE0751D009529B2 /* CustomKeyboardThemeEditorView.swift in Sources */,
				39DC8A892DE0751D009529B2 /* KeyboardPreviewView.swift in Sources */,
				39DC8A8A2DE0751D009529B2 /* KeyboardThemeConfigView.swift in Sources */,
				394ACD602DCCA6CB007AA42D /* WidgetDataManager.swift in Sources */,
				394990A92DD87276004AE1C5 /* DropdownSizeSelector.swift in Sources */,
				394991372DDB6A47004AE1C5 /* NoteWidgetConfigView.swift in Sources */,
				394990AA2DD87276004AE1C5 /* DropdownMenuManager.swift in Sources */,
				3966E0932DE195FA001234F4 /* ThemePackSelectionView.swift in Sources */,
				39E08A8B2DE5B25700FA3C9F /* KeyboardThemeTabView.swift in Sources */,
				39ABF78B2DD5759000D37EAB /* SearchViewController.swift in Sources */,
				3943BB862DD6ED300067560C /* LearningProgressWidgetView.swift in Sources */,
				394991622DDD56D9004AE1C5 /* PopularAppsView.swift in Sources */,
				39E22A6B2DDDC0F50071C72D /* DeviceInfoWidgetConfigView.swift in Sources */,
				394991452DDC120C004AE1C5 /* MoonPhaseConfigView.swift in Sources */,
				3943BB8D2DD6ED300067560C /* LearningProgressConfigView.swift in Sources */,
				39E22A6E2DDDC2580071C72D /* FontSelectionView.swift in Sources */,
				3966E08D2DE15AD8001234F4 /* KeyTypeConfigView.swift in Sources */,
				3966E08E2DE15AD8001234F4 /* IndividualKeyConfigEditorView.swift in Sources */,
				3966E08F2DE15AD8001234F4 /* IndividualKeyConfigView.swift in Sources */,
				3966E0902DE15AD8001234F4 /* ImportExportView.swift in Sources */,
				3966E0912DE15AD8001234F4 /* KeyTypeConfigEditorView.swift in Sources */,
				39E22A6F2DDDC2580071C72D /* SuccessToast.swift in Sources */,
				3943BB6C2DD6D2300067560C /* CalendarPickerView.swift in Sources */,
				39ABF7792DD33EA200D37EAB /* WidgetButtonType.swift in Sources */,
				394ACD732DCCB767007AA42D /* WidgetData.swift in Sources */,
				394990BA2DDB0463004AE1C5 /* TimeWidgetConfigView.swift in Sources */,
				394ACD612DCCA6CB007AA42D /* WidgetConfigView.swift in Sources */,
				390BDE1B2DD1CBD600CDC312 /* QRWidgetConfigView.swift in Sources */,
				39ABF74C2DD31DA700D37EAB /* DailyQuoteConfigView.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		394ACD192DCC9F52007AA42D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				394ACD432DCC9F5F007AA42D /* JZJJWidgetExtension.swift in Sources */,
				39ABF7782DD33EA200D37EAB /* WidgetButtonType.swift in Sources */,
				39ABF77C2DD3406F00D37EAB /* DynamicIntentWidgetProvider.swift in Sources */,
				394ACD442DCC9F5F007AA42D /* JZJJWidgetExtensionBundle.swift in Sources */,
				394ACD452DCC9F5F007AA42D /* JZJJWidgetExtensionControl.swift in Sources */,
				394ACD5E2DCCA6CB007AA42D /* WidgetDataManager.swift in Sources */,
				3983D1182DEE7F1300588C6C /* StaticConfigIntent.intentdefinition in Sources */,
				394ACD5F2DCCA6CB007AA42D /* WidgetConfigView.swift in Sources */,
				394ACD462DCC9F5F007AA42D /* JZJJWidgetExtensionLiveActivity.swift in Sources */,
				394ACD702DCCB567007AA42D /* TimelineProvider.swift in Sources */,
				394ACD712DCCB567007AA42D /* DailyQuoteView.swift in Sources */,
				394ACD722DCCB567007AA42D /* WidgetData.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39ABF7632DD33E0600D37EAB /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				3983D1192DEE7F1300588C6C /* StaticConfigIntent.intentdefinition in Sources */,
				39ABF77A2DD33EB100D37EAB /* WidgetButtonType.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		39DC8A6E2DE06959009529B2 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		394ACD312DCC9F54007AA42D /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 394ACD1C2DCC9F52007AA42D /* JZJJWidgetExtensionExtension */;
			targetProxy = 394ACD302DCC9F54007AA42D /* PBXContainerItemProxy */;
		};
		39ABF76F2DD33E0600D37EAB /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 39ABF7662DD33E0600D37EAB /* WidgetIntentExtension */;
			targetProxy = 39ABF76E2DD33E0600D37EAB /* PBXContainerItemProxy */;
		};
		39DC8A782DE06959009529B2 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 39DC8A712DE06959009529B2 /* KeyBoardExtension */;
			targetProxy = 39DC8A772DE06959009529B2 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		394ACD032DCC9529007AA42D /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				394ACD022DCC9529007AA42D /* Base */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		3983D1162DEE7F1300588C6C /* StaticConfigIntent.intentdefinition */ = {
			isa = PBXVariantGroup;
			children = (
				3983D1152DEE7F1300588C6C /* Base */,
				3983D11B2DEE7F1900588C6C /* en */,
			);
			name = StaticConfigIntent.intentdefinition;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		394ACCFB2DCC9522007AA42D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1BAAB0A1C77EE60A92C2D28C /* Pods-JZJJWidget.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = JZJJWidget/JZJJWidget.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = ZNJDK9YN64;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = JZJJWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Keyboard Widgets-我的桌面万能小组件主题商店";
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.ort.JZJJWidgetAPP;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = JZJJWidgetAPP;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		394ACCFC2DCC9522007AA42D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6043DAD684AF05A1E1AD1290 /* Pods-JZJJWidget.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = JZJJWidget/JZJJWidgetRelease.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = ZNJDK9YN64;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = JZJJWidget/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "Keyboard Widgets-我的桌面万能小组件主题商店";
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.ort.JZJJWidgetAPP;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = JZJJWidgetAPP;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		394ACCFD2DCC9522007AA42D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 7XB5JLK236;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		394ACCFE2DCC9522007AA42D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 7XB5JLK236;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		394ACD352DCC9F54007AA42D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = JZJJWidgetExtensionExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = JZJJWidgetExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = JZJJWidgetExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.ort.JZJJWidgetAPP.Widgets;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		394ACD362DCC9F54007AA42D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				ASSETCATALOG_COMPILER_WIDGET_BACKGROUND_COLOR_NAME = WidgetBackground;
				CODE_SIGN_ENTITLEMENTS = JZJJWidgetExtensionExtension.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = ZNJDK9YN64;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = JZJJWidgetExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = JZJJWidgetExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.ort.JZJJWidgetAPP.Widgets;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = widget;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		39ABF7732DD33E0600D37EAB /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WidgetIntentExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = WidgetIntentExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.ort.JZJJWidgetAPP.intent;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		39ABF7742DD33E0600D37EAB /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = ZNJDK9YN64;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = WidgetIntentExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = WidgetIntentExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.ort.JZJJWidgetAPP.intent;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = intent;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		39DC8A7C2DE06959009529B2 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = KeyBoardExtension/KeyBoardExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = KeyBoardExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "自定义键盘";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.ort.JZJJWidgetAPP.keybaord;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		39DC8A7D2DE06959009529B2 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = KeyBoardExtension/KeyBoardExtension.entitlements;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Distribution";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 2;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = ZNJDK9YN64;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = KeyBoardExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "自定义键盘";
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 16.6;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0.1;
				PRODUCT_BUNDLE_IDENTIFIER = com.ort.JZJJWidgetAPP.keybaord;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = keyboard;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		394ACCE22DCC9520007AA42D /* Build configuration list for PBXProject "JZJJWidget" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				394ACCFD2DCC9522007AA42D /* Debug */,
				394ACCFE2DCC9522007AA42D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		394ACCFA2DCC9522007AA42D /* Build configuration list for PBXNativeTarget "JZJJWidget" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				394ACCFB2DCC9522007AA42D /* Debug */,
				394ACCFC2DCC9522007AA42D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		394ACD342DCC9F54007AA42D /* Build configuration list for PBXNativeTarget "JZJJWidgetExtensionExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				394ACD352DCC9F54007AA42D /* Debug */,
				394ACD362DCC9F54007AA42D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		39ABF7722DD33E0600D37EAB /* Build configuration list for PBXNativeTarget "WidgetIntentExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				39ABF7732DD33E0600D37EAB /* Debug */,
				39ABF7742DD33E0600D37EAB /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		39DC8A7B2DE06959009529B2 /* Build configuration list for PBXNativeTarget "KeyBoardExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				39DC8A7C2DE06959009529B2 /* Debug */,
				39DC8A7D2DE06959009529B2 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCSwiftPackageProductDependency section */
		39B9676D2DCF8D7100847831 /* MyWidgetKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MyWidgetKit;
		};
		39EAC4EA2DCF7BCF00F0CEBE /* MyWidgetKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = MyWidgetKit;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 394ACCDF2DCC9520007AA42D /* Project object */;
}
