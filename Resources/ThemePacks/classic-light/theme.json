{"schemaVersion": "1.0.0", "themeInfo": {"id": "classic-light", "name": "经典浅色", "description": "内置主题包 - 经典浅色"}, "baseTheme": {"id": "classic-light-base", "name": "经典浅色基础", "type": "light", "keyStyle": "rounded", "colors": {"background": {"red": 0.961, "green": 0.961, "blue": 0.969, "alpha": 1.0}, "keyBackground": {"red": 1.0, "green": 1.0, "blue": 1.0, "alpha": 1.0}, "keyPressed": {"red": 0.9, "green": 0.9, "blue": 0.9, "alpha": 1.0}, "text": {"red": 0.0, "green": 0.0, "blue": 0.0, "alpha": 1.0}, "specialKey": {"red": 0.8, "green": 0.8, "blue": 0.8, "alpha": 1.0}, "border": {"red": 0.7, "green": 0.7, "blue": 0.7, "alpha": 1.0}}, "images": {"hasBackgroundImage": true, "hasKeyImage": true, "backgroundImagePath": "resources/backgrounds/keyboard-bg.png", "keyImagePath": "resources/keys/letter-key.png", "keyImages": {"letter": {"normal": "resources/keys/letter-key.png", "pressed": "resources/keys/letter-key-pressed.png"}, "number": {"normal": "resources/keys/number-key.png", "pressed": "resources/keys/number-key-pressed.png"}, "function": {"normal": "resources/keys/function-key.png", "pressed": "resources/keys/function-key-pressed.png"}, "space": {"normal": "resources/keys/space-key.png", "pressed": "resources/keys/space-key-pressed.png"}, "shift": {"normal": "resources/keys/shift-key.png", "pressed": "resources/keys/shift-key-pressed.png"}, "symbol": {"normal": "resources/keys/symbol-key.png", "pressed": "resources/keys/symbol-key-pressed.png"}, "punctuation": {"normal": "resources/keys/punctuation-key.png", "pressed": "resources/keys/punctuation-key-pressed.png"}}, "isBuiltInImageTheme": true, "imageOpacity": 0.8, "imageBlendMode": "normal"}, "typography": {"fontName": "SF Pro", "fontSize": 16, "fontWeight": "medium"}, "layout": {"keySpacing": 6, "keyHeight": 44, "showBorder": true, "borderWidth": 1}, "effects": {"enableShadow": true, "shadowColor": {"red": 0.0, "green": 0.0, "blue": 0.0, "alpha": 0.2}, "shadowRadius": 2, "enableHaptic": true, "enableSound": true}}, "advancedConfig": {"globalSettings": {"keySpacing": 6, "keyHeight": 44, "enableHapticFeedback": true, "enableSoundFeedback": true, "enableKeyAnimations": true, "animationDuration": 0.1, "enableGradientEffects": false, "enableParallaxEffect": false}, "keyTypeConfigs": {}, "individualKeyConfigs": {}, "createdAt": "2025-01-27T10:00:00Z", "updatedAt": "2025-01-27T10:00:00Z"}, "validation": {"checksum": "auto-generated", "fileCount": 16, "totalSize": 1024768}}