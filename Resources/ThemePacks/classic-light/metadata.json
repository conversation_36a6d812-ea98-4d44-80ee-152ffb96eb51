{"id": "classic-light", "name": "经典浅色", "description": "内置主题包 - 经典浅色", "version": "1.0.0", "category": "classic", "style": "light", "author": "JZJJWidget Team", "createdAt": "2025-01-27T10:00:00Z", "updatedAt": "2025-01-27T10:00:00Z", "compatibility": {"minIOSVersion": "13.0", "minAppVersion": "1.0.0", "supportedDevices": ["iPhone", "iPad"]}, "resources": {"previewImage": "preview.png", "backgroundImages": ["resources/backgrounds/keyboard-bg.png"], "keyImages": ["resources/keys/letter-key.png", "resources/keys/number-key.png", "resources/keys/function-key.png", "resources/keys/space-key.png", "resources/keys/shift-key.png", "resources/keys/symbol-key.png", "resources/keys/punctuation-key.png"], "totalSize": 1024768, "compressedSize": 512384}, "features": {"supportsDarkMode": false, "hasAnimations": true, "hasSounds": true, "hasHaptics": true, "customFonts": false}, "tags": ["classic", "light", "内置"], "rating": 4.8, "downloadCount": 0, "isPremium": false, "isBuiltIn": true}