{"schema_version": "1.0.0", "theme_info": {"id": "neon-cyber", "name": "霓虹赛博", "description": "未来科技感的霓虹主题"}, "base_theme": {"id": "neon-cyber-base", "name": "霓虹赛博基础", "type": "dark", "key_style": "rounded", "colors": {"background": {"red": 0.05, "green": 0.05, "blue": 0.1, "alpha": 1.0}, "key_background": {"red": 0.1, "green": 0.1, "blue": 0.2, "alpha": 0.8}, "key_pressed": {"red": 0.0, "green": 1.0, "blue": 1.0, "alpha": 1.0}, "text": {"red": 0.0, "green": 1.0, "blue": 1.0, "alpha": 1.0}, "special_key": {"red": 1.0, "green": 0.0, "blue": 1.0, "alpha": 0.8}, "border": {"red": 0.0, "green": 1.0, "blue": 1.0, "alpha": 0.6}}, "images": {"has_background_image": true, "has_key_image": true, "background_image_path": "resources/backgrounds/keyboard-bg.png", "key_image_path": "resources/keys/neon-key.png", "key_images": {"letter": {"normal": "resources/keys/letter-key.png", "pressed": "resources/keys/letter-key-pressed.png"}, "number": {"normal": "resources/keys/number-key.png", "pressed": "resources/keys/number-key-pressed.png"}, "function": {"normal": "resources/keys/function-key.png", "pressed": "resources/keys/function-key-pressed.png"}, "space": {"normal": "resources/keys/space-key.png", "pressed": "resources/keys/space-key-pressed.png"}, "shift": {"normal": "resources/keys/shift-key.png", "pressed": "resources/keys/shift-key-pressed.png"}, "symbol": {"normal": "resources/keys/symbol-key.png", "pressed": "resources/keys/symbol-key-pressed.png"}, "punctuation": {"normal": "resources/keys/punctuation-key.png", "pressed": "resources/keys/punctuation-key-pressed.png"}}, "is_built_in_image_theme": true, "image_opacity": 0.7, "image_blend_mode": "overlay"}, "typography": {"font_name": "SF Pro", "font_size": 16, "font_weight": "semibold"}, "layout": {"key_spacing": 8, "key_height": 46, "show_border": true, "border_width": 2}, "effects": {"enable_shadow": true, "shadow_color": {"red": 0.0, "green": 1.0, "blue": 1.0, "alpha": 0.3}, "shadow_radius": 4, "enable_haptic": true, "enable_sound": true}}, "advanced_config": {"global_settings": {"key_spacing": 8, "key_height": 46, "enable_haptic_feedback": true, "enable_sound_feedback": true, "enable_key_animations": true, "animation_duration": 0.2, "enable_gradient_effects": true, "enable_parallax_effect": true}, "key_type_configs": {"symbol": {"id": "symbol-config", "key_type": "symbol", "name": "符号键", "description": "@#$%等符号按键配置", "default_background_color": {"red": 1.0, "green": 0.0, "blue": 1.0, "alpha": 0.3}, "default_pressed_color": {"red": 1.0, "green": 0.0, "blue": 1.0, "alpha": 1.0}, "default_text_color": {"red": 1.0, "green": 0.0, "blue": 1.0, "alpha": 1.0}, "default_border_color": {"red": 1.0, "green": 0.0, "blue": 1.0, "alpha": 0.8}, "default_font_size": 16, "default_font_weight": "bold", "default_corner_radius": 10, "default_border_width": 2, "affected_keys": ["@", "#", "$", "%", "&", "*", "+", "="], "updated_at": "2025-01-01T00:00:00Z"}, "punctuation": {"id": "punctuation-config", "key_type": "punctuation", "name": "标点键", "description": ".,?!等标点按键配置", "default_background_color": {"red": 0.0, "green": 1.0, "blue": 0.0, "alpha": 0.3}, "default_pressed_color": {"red": 0.0, "green": 1.0, "blue": 0.0, "alpha": 1.0}, "default_text_color": {"red": 0.0, "green": 1.0, "blue": 0.0, "alpha": 1.0}, "default_border_color": {"red": 0.0, "green": 1.0, "blue": 0.0, "alpha": 0.8}, "default_font_size": 16, "default_font_weight": "bold", "default_corner_radius": 10, "default_border_width": 2, "affected_keys": [".", ",", "?", "!", ";", ":", "'", "\""], "updated_at": "2025-01-01T00:00:00Z"}}, "individual_key_configs": {}, "created_at": "2025-01-01T00:00:00Z", "updated_at": "2025-01-01T00:00:00Z"}, "validation": {"checksum": "def456ghi789", "file_count": 4, "total_size": 3145728}}