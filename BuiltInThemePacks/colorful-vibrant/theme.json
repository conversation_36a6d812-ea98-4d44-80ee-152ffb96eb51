{"schema_version": "1.0.0", "theme_info": {"id": "colorful-vibrant", "name": "彩色活泼", "description": "活泼明亮的彩色主题，充满活力的渐变色彩"}, "base_theme": {"id": "colorful-vibrant-base", "name": "彩色活泼基础", "type": "light", "key_style": "rounded", "colors": {"background": {"red": 0.98, "green": 0.95, "blue": 1.0, "alpha": 1.0}, "key_background": {"red": 1.0, "green": 0.98, "blue": 0.95, "alpha": 1.0}, "key_pressed": {"red": 1.0, "green": 0.3, "blue": 0.6, "alpha": 1.0}, "text": {"red": 0.2, "green": 0.2, "blue": 0.3, "alpha": 1.0}, "special_key": {"red": 0.4, "green": 0.8, "blue": 1.0, "alpha": 1.0}, "border": {"red": 0.8, "green": 0.6, "blue": 1.0, "alpha": 0.6}}, "images": {"has_background_image": true, "has_key_image": true, "background_image_path": "resources/backgrounds/keyboard-bg.png", "key_image_path": null, "key_images": {"letter": {"normal": "resources/keys/letter-key.png", "pressed": "resources/keys/letter-key.png"}, "number": {"normal": "resources/keys/number-key.png", "pressed": "resources/keys/number-key.png"}, "function": {"normal": "resources/keys/function-key.png", "pressed": "resources/keys/function-key.png"}, "space": {"normal": "resources/keys/space-key.png", "pressed": "resources/keys/space-key.png"}, "shift": {"normal": "resources/keys/shift-key.png", "pressed": "resources/keys/shift-key.png"}, "symbol": {"normal": "resources/keys/symbol-key.png", "pressed": "resources/keys/symbol-key.png"}, "punctuation": {"normal": "resources/keys/punctuation-key.png", "pressed": "resources/keys/punctuation-key.png"}}, "is_built_in_image_theme": true, "image_opacity": 0.9, "image_blend_mode": "normal"}, "typography": {"font_name": "SF Pro", "font_size": 16, "font_weight": "semibold"}, "layout": {"key_spacing": 6, "key_height": 44, "show_border": true, "border_width": 2}, "effects": {"enable_shadow": true, "shadow_color": {"red": 1.0, "green": 0.4, "blue": 0.8, "alpha": 0.2}, "shadow_radius": 3, "enable_haptic": true, "enable_sound": true}}, "advanced_config": {"global_settings": {"key_spacing": 6, "key_height": 44, "enable_haptic_feedback": true, "enable_sound_feedback": true, "enable_key_animations": true, "animation_duration": 0.15, "enable_gradient_effects": true, "enable_parallax_effect": false}, "key_type_configs": {"symbol": {"id": "symbol-config", "key_type": "symbol", "name": "符号键", "description": "@#$%等符号按键配置", "default_background_color": {"red": 0.2, "green": 0.9, "blue": 0.7, "alpha": 1.0}, "default_pressed_color": {"red": 1.0, "green": 0.3, "blue": 0.6, "alpha": 1.0}, "default_text_color": {"red": 0.1, "green": 0.1, "blue": 0.2, "alpha": 1.0}, "default_border_color": {"red": 0.3, "green": 0.7, "blue": 1.0, "alpha": 0.8}, "default_font_size": 16, "default_font_weight": "semibold", "default_corner_radius": 10, "default_border_width": 2, "affected_keys": ["@", "#", "$", "%", "&", "*", "+", "="], "updated_at": "2025-01-27T00:00:00Z"}, "punctuation": {"id": "punctuation-config", "key_type": "punctuation", "name": "标点键", "description": ".,?!等标点按键配置", "default_background_color": {"red": 1.0, "green": 0.6, "blue": 0.3, "alpha": 1.0}, "default_pressed_color": {"red": 1.0, "green": 0.3, "blue": 0.6, "alpha": 1.0}, "default_text_color": {"red": 0.1, "green": 0.1, "blue": 0.2, "alpha": 1.0}, "default_border_color": {"red": 0.9, "green": 0.4, "blue": 0.8, "alpha": 0.8}, "default_font_size": 16, "default_font_weight": "semibold", "default_corner_radius": 10, "default_border_width": 2, "affected_keys": [".", ",", "?", "!", ";", ":", "'", "\""], "updated_at": "2025-01-27T00:00:00Z"}}, "individual_key_configs": {"A": {"id": "key-A", "key_type": "letter", "key_value": "A", "background_color": {"red": 1.0, "green": 0.0, "blue": 0.0, "alpha": 1.0}, "pressed_color": {"red": 1.0, "green": 0.3, "blue": 0.6, "alpha": 1.0}, "text_color": {"red": 1.0, "green": 0.1, "blue": 0.2, "alpha": 0.0}, "border_color": {"red": 0.9, "green": 0.4, "blue": 0.8, "alpha": 0.8}, "font_name": "SF Pro", "font_size": 18, "font_weight": "bold", "corner_radius": 12, "border_width": 2, "shadow_enabled": true, "shadow_color": {"red": 1.0, "green": 0.4, "blue": 0.8, "alpha": 0.3}, "shadow_radius": 4, "shadow_offset": {"width": 0, "height": 2}, "width_multiplier": 1.0, "height_multiplier": 1.0, "has_custom_image": true, "normal_image_path": "resources/keys/custom/A-key.png", "pressed_image_path": "resources/keys/custom/A-key-pressed.png", "hover_image_path": null, "image_opacity": 0.9, "image_blend_mode": "normal", "created_at": "2025-01-27T00:00:00Z", "updated_at": "2025-01-27T00:00:00Z"}, "1": {"id": "key-1", "key_type": "number", "key_value": "1", "background_color": {"red": 0.2, "green": 0.9, "blue": 0.7, "alpha": 1.0}, "pressed_color": {"red": 0.1, "green": 0.8, "blue": 0.6, "alpha": 1.0}, "text_color": {"red": 0.0, "green": 0.2, "blue": 0.1, "alpha": 0.0}, "border_color": {"red": 0.3, "green": 0.7, "blue": 1.0, "alpha": 0.8}, "font_name": "SF Pro", "font_size": 20, "font_weight": "semibold", "corner_radius": 10, "border_width": 2, "shadow_enabled": true, "shadow_color": {"red": 0.2, "green": 0.9, "blue": 0.7, "alpha": 0.2}, "shadow_radius": 3, "shadow_offset": {"width": 1, "height": 1}, "width_multiplier": 1.0, "height_multiplier": 1.0, "has_custom_image": true, "normal_image_path": "resources/keys/custom/1-key.png", "pressed_image_path": "resources/keys/custom/1-key-pressed.png", "hover_image_path": null, "image_opacity": 0.85, "image_blend_mode": "normal", "created_at": "2025-01-27T00:00:00Z", "updated_at": "2025-01-27T00:00:00Z"}, "空格": {"id": "key-space", "key_type": "space", "key_value": "空格", "background_color": {"red": 0.9, "green": 0.6, "blue": 1.0, "alpha": 1.0}, "pressed_color": {"red": 0.8, "green": 0.4, "blue": 0.9, "alpha": 1.0}, "text_color": {"red": 0.3, "green": 0.1, "blue": 0.4, "alpha": 1.0}, "border_color": {"red": 0.7, "green": 0.3, "blue": 0.9, "alpha": 0.8}, "font_name": "SF Pro", "font_size": 14, "font_weight": "medium", "corner_radius": 15, "border_width": 3, "shadow_enabled": true, "shadow_color": {"red": 0.9, "green": 0.6, "blue": 1.0, "alpha": 0.25}, "shadow_radius": 5, "shadow_offset": {"width": 0, "height": 3}, "width_multiplier": 3.0, "height_multiplier": 1.0, "has_custom_image": true, "normal_image_path": "resources/keys/custom/space-key.png", "pressed_image_path": "resources/keys/custom/space-key-pressed.png", "hover_image_path": null, "image_opacity": 0.8, "image_blend_mode": "multiply", "created_at": "2025-01-27T00:00:00Z", "updated_at": "2025-01-27T00:00:00Z"}, "⇧": {"id": "key-shift", "key_type": "shift", "key_value": "⇧", "background_color": {"red": 1.0, "green": 0.3, "blue": 0.6, "alpha": 1.0}, "pressed_color": {"red": 0.9, "green": 0.2, "blue": 0.5, "alpha": 1.0}, "text_color": {"red": 1.0, "green": 1.0, "blue": 1.0, "alpha": 1.0}, "border_color": {"red": 0.8, "green": 0.1, "blue": 0.4, "alpha": 0.9}, "font_name": "SF Pro", "font_size": 16, "font_weight": "bold", "corner_radius": 8, "border_width": 2, "shadow_enabled": true, "shadow_color": {"red": 1.0, "green": 0.3, "blue": 0.6, "alpha": 0.4}, "shadow_radius": 3, "shadow_offset": {"width": 1, "height": 2}, "width_multiplier": 1.5, "height_multiplier": 1.0, "has_custom_image": true, "normal_image_path": "resources/keys/custom/shift-key.png", "pressed_image_path": "resources/keys/custom/shift-key-pressed.png", "hover_image_path": null, "image_opacity": 0.9, "image_blend_mode": "overlay", "created_at": "2025-01-27T00:00:00Z", "updated_at": "2025-01-27T00:00:00Z"}}, "created_at": "2025-01-27T00:00:00Z", "updated_at": "2025-01-27T00:00:00Z"}, "validation": {"checksum": "colorful123vibrant456", "file_count": 15, "total_size": 2048576}}